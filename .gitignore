# Node.js
node_modules/
npm-debug.log
yarn-error.log
package-lock.json

# Python
__pycache__/
*.py[cod]
*.pyo
*.pyd
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
pip-log.txt
pip-delete-this-directory.txt

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
.DS_Store

# Environment variables
./backend/.env


# Logs
logs
*.log

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# pytest
.pytest_cache/

# Coverage reports
htmlcov/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# mypy
.mypy_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# dotenv
# virtualenv
.venv
venv/
ENV/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# Pytest
.pytest_cache/


# some personal files
backend/data/originalQB.json
backend/magicPdfTry
backend/seleniumParser.py
backend/store.txt
manim

frontend/dist
math_questions.json
Maths-SQP.pdf
structuredVision.json
structuredVisionUnique.json
Mathematics.pdf 
OCRoutput 
MS XII MATH - 65-2-1-2024.pdf
MathsQuestionBankClass10
backend/data

finetuningOcr.ipynb
magic-pdf.json
backend/mathspdfs
backend/data
.idea