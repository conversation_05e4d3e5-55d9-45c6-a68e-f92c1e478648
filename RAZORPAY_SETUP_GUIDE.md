# Razorpay Setup Guide for AegisScholar Credit System

## Overview
This guide will help you set up Razorpay integration for the AegisScholar credit-based billing system.

## Step 1: Create Razorpay Account

1. Go to [https://razorpay.com](https://razorpay.com)
2. Sign up for a new account or log in to existing account
3. Complete the KYC verification process (required for live payments)
4. Navigate to the Dashboard

## Step 2: Get API Credentials

### For Testing (Test Mode)
1. In Razorpay Dashboard, ensure you're in **Test Mode** (toggle in top-left)
2. Go to **Settings** → **API Keys**
3. Click **Generate Test Key**
4. Copy the **Key ID** and **Key Secret**

### For Production (Live Mode)
1. Switch to **Live Mode** in Razorpay Dashboard
2. Complete business verification if not done
3. Go to **Settings** → **API Keys**
4. Click **Generate Live Key**
5. Copy the **Key ID** and **Key Secret**

## Step 3: Configure Environment Variables

### Backend Configuration
Add these variables to your `backend/.env` file:

```env
# Razorpay Configuration
RAZORPAY_KEY_ID=rzp_test_xxxxxxxxxx  # Replace with your Key ID
RAZORPAY_KEY_SECRET=your_key_secret_here  # Replace with your Key Secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret  # We'll set this up in Step 4
```

### Frontend Configuration
Add this variable to your `frontend/.env` file:

```env
# Razorpay Configuration
VITE_RAZORPAY_KEY_ID=rzp_test_xxxxxxxxxx  # Same Key ID as backend
```

## Step 4: Set Up Webhooks

1. In Razorpay Dashboard, go to **Settings** → **Webhooks**
2. Click **Create New Webhook**
3. Configure the webhook:
   - **URL**: `https://yourdomain.com/api/credits/webhook`
   - **Secret**: Generate a random string (save this as `RAZORPAY_WEBHOOK_SECRET`)
   - **Events**: Select these events:
     - `payment.captured`
     - `payment.failed`
     - `order.paid`

4. Click **Create Webhook**

## Step 5: Test the Integration

### Test Credit Purchase Flow
1. Start your backend server
2. Check console logs for "Razorpay initialized successfully"
3. Open your frontend application
4. Navigate to AegisGrader or Credit Dashboard
5. Try purchasing credits with test card details:
   - **Card Number**: 4111 1111 1111 1111
   - **Expiry**: Any future date
   - **CVV**: Any 3 digits
   - **Name**: Any name

### Test Payment Failure
1. Use card number: 4000 0000 0000 0002 (always fails)
2. Verify that the transaction is marked as failed
3. Check that no credits are added to the account

## Step 6: Production Deployment

### Before Going Live
1. Switch to Live Mode in Razorpay Dashboard
2. Update environment variables with live credentials
3. Update webhook URL to production domain
4. Test with small amounts first

### Security Checklist
- [ ] Environment variables are secure and not committed to git
- [ ] Webhook secret is properly configured
- [ ] HTTPS is enabled for webhook endpoint
- [ ] Payment verification is working correctly
- [ ] Error handling is in place

## Troubleshooting

### Common Issues

#### 1. "Payment service is not configured"
- **Cause**: Missing or incorrect Razorpay credentials
- **Solution**: Check that `RAZORPAY_KEY_ID` and `RAZORPAY_KEY_SECRET` are set correctly

#### 2. "Invalid signature" error
- **Cause**: Webhook secret mismatch
- **Solution**: Ensure `RAZORPAY_WEBHOOK_SECRET` matches the secret in Razorpay Dashboard

#### 3. Payment successful but credits not added
- **Cause**: Webhook not receiving events or processing failing
- **Solution**: Check webhook logs in Razorpay Dashboard and server logs

#### 4. Frontend shows "Payment failed" immediately
- **Cause**: Frontend `VITE_RAZORPAY_KEY_ID` is incorrect or missing
- **Solution**: Verify the frontend environment variable

### Testing Commands

```bash
# Test backend API endpoints
curl -X GET http://localhost:8080/api/credits/packages

# Check if Razorpay is initialized (check server logs)
# Should see: "Razorpay initialized successfully"
```

## Support

### Razorpay Support
- Documentation: [https://razorpay.com/docs](https://razorpay.com/docs)
- Support: [https://razorpay.com/support](https://razorpay.com/support)

### AegisScholar Integration
- Check server logs for detailed error messages
- Verify all environment variables are set
- Test with Razorpay's test credentials first

## Credit Packages Configuration

The system comes with pre-configured packages:
- **Professional**: 500-5000 credits at ₹200 per credit
- **Premium**: 5001-10000 credits at ₹175 per credit (12.5% discount)
- **Enterprise**: 10000+ credits with custom pricing

All amounts are stored and processed in rupees throughout the system. Razorpay conversion to paisa happens only at the payment gateway level.

To modify packages, edit the `CREDIT_PACKAGES` object in `backend/controllers/creditController.js`.

## Next Steps

1. Complete Razorpay setup following this guide
2. Test the payment flow thoroughly
3. Monitor transactions in Razorpay Dashboard
4. Set up alerts for failed payments
5. Consider setting up automatic reconciliation

Your credit system is now ready for production use!
