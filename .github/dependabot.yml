# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/code-security/dependabot/dependabot-version-updates/configuration-options-for-the-dependabot.yml-file

version: 2
updates:
  - package-ecosystem: "npm" # Assumes a JavaScript project using npm
    directory: "/" # Location of package manifests
    schedule:
      interval: "weekly"
    open-pull-requests-limit: 10 # Limit the number of open PRs at any time

  # You can add more ecosystems if needed, for example:
  # - package-ecosystem: "docker"
  #   directory: "/"
  #   schedule:
  #     interval: "weekly"

  # - package-ecosystem: "github-actions"
  #   directory: "/"
  #   schedule:
  #     interval: "weekly"
