name: CI/CD Pipeline

on:
  push:
    branches:
      - main

jobs:
  build:
    name: Build and Push
    runs-on: ubuntu-latest
    outputs:
      image_tag: ${{ steps.build-image.outputs.image_tag }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v1

      - name: Get commit hash
        id: get-commit-hash
        run: echo "commit-hash=$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT

      - name: Get timestamp
        id: get-timestamp
        run: echo "timestamp=$(date +'%Y-%m-%d-%H-%M')" >> $GITHUB_OUTPUT

      - name: Build, tag, and push image to Amazon ECR
        id: build-image
        env:
          ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
          ECR_REPOSITORY: ${{ secrets.REPO_NAME }}
          IMAGE_TAG: ${{ steps.get-commit-hash.outputs.commit-hash }}-${{ steps.get-timestamp.outputs.timestamp }}
        run: |
          docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG ./backend
          docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
          echo "image_tag=$IMAGE_TAG" >> $GITHUB_OUTPUT

  deploy:
    name: Deploy to EC2
    runs-on: ubuntu-latest
    needs: build
    environment: production
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ap-south-1

      - name: Deploy to EC2 Instance
        env:
          ECR_REGISTRY: 588738568415.dkr.ecr.ap-south-1.amazonaws.com
          ECR_REPOSITORY: aegisscholar-platform-web
          IMAGE_TAG: ${{ needs.build.outputs.image_tag }}
          EC2_INSTANCE_ID: ${{ secrets.EC2_INSTANCE_ID }}
          CONTAINER_NAME: aegisscholar-backend  # New container name
        run: |
          FULL_IMAGE_URI="$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"
          
          # Send deployment commands via SSM
          aws ssm send-command \
            --instance-ids "$EC2_INSTANCE_ID" \
            --document-name "AWS-RunShellScript" \
            --parameters "{\"commands\":[\"aws ecr get-login-password --region ap-south-1 | sudo docker login --username AWS --password-stdin 588738568415.dkr.ecr.ap-south-1.amazonaws.com\",\"sudo docker ps -a -q -f name=$CONTAINER_NAME | grep -q . && sudo docker stop $CONTAINER_NAME || true\",\"sudo docker ps -a -q -f name=$CONTAINER_NAME | grep -q . && sudo docker rm $CONTAINER_NAME || true\",\"sudo docker system prune -a -f\",\"sudo docker pull $FULL_IMAGE_URI\",\"sudo docker run --name $CONTAINER_NAME --env-file /home/<USER>/.env -p 443:443 -v /etc/letsencrypt:/etc/letsencrypt:ro $FULL_IMAGE_URI\"]}"
