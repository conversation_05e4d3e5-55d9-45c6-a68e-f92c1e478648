interface StudentInfoProps {
    name?: string;
    rollNumber?: string;
}

export const StudentInfo: React.FC<StudentInfoProps> = ({ name, rollNumber }) => (
    <div className="min-w-0 flex-1">
        <p className="font-medium text-foreground text-sm sm:text-base truncate">
            {name || "N/A"}
        </p>
        <p className="text-xs sm:text-sm text-muted-foreground">
            Roll: {rollNumber || "N/A"}
        </p>
    </div>
);