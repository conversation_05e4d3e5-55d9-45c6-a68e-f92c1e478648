#!/bin/bash

# Comprehensive Test Execution Script for AegisScholar Platform
# This script runs all tests for the test workflow and generates a detailed report

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Create reports directory
REPORTS_DIR="test-reports"
mkdir -p $REPORTS_DIR

# Initialize report file
REPORT_FILE="$REPORTS_DIR/comprehensive-test-report-$(date +%Y%m%d-%H%M%S).md"

cat > $REPORT_FILE << EOF
# Comprehensive Test Report - AegisScholar Platform

**Generated:** $(date)
**Test Scope:** Complete Test Workflow End-to-End Testing

## Executive Summary

This report covers comprehensive testing of the AegisScholar platform's test workflow functionality, including:
- Test creation for all three types (personalized, generic, diagnostic)
- Test scheduling and status management
- Student test access and execution
- Test submission and result processing
- Error handling and edge cases

---

EOF

log "Starting comprehensive test execution for AegisScholar platform..."

# Function to run frontend tests
run_frontend_tests() {
    log "Running frontend tests..."
    
    cd frontend
    
    echo "## Frontend Test Results" >> ../$REPORT_FILE
    echo "" >> ../$REPORT_FILE
    
    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        log "Installing frontend dependencies..."
        npm install
    fi
    
    # Run unit tests with coverage
    log "Running frontend unit tests..."
    if npm run test:coverage > ../test-output-frontend.log 2>&1; then
        success "Frontend unit tests passed"
        echo "### Unit Tests: ✅ PASSED" >> ../$REPORT_FILE
    else
        error "Frontend unit tests failed"
        echo "### Unit Tests: ❌ FAILED" >> ../$REPORT_FILE
        echo "\`\`\`" >> ../$REPORT_FILE
        tail -20 ../test-output-frontend.log >> ../$REPORT_FILE
        echo "\`\`\`" >> ../$REPORT_FILE
    fi
    
    # Run specific test workflow tests
    log "Running test workflow specific tests..."
    
    # Test creation tests
    if npm run test -- ScheduleTestPage.test.tsx > ../test-output-schedule.log 2>&1; then
        success "Schedule test page tests passed"
        echo "### Test Creation Tests: ✅ PASSED" >> ../$REPORT_FILE
    else
        error "Schedule test page tests failed"
        echo "### Test Creation Tests: ❌ FAILED" >> ../$REPORT_FILE
    fi
    
    # Review page tests
    if npm run test -- ReviewTestPage.test.tsx > ../test-output-review.log 2>&1; then
        success "Review test page tests passed"
        echo "### Test Review Tests: ✅ PASSED" >> ../$REPORT_FILE
    else
        error "Review test page tests failed"
        echo "### Test Review Tests: ❌ FAILED" >> ../$REPORT_FILE
    fi
    
    # Student test tests
    if npm run test -- StudentTestPage.comprehensive.test.tsx > ../test-output-student.log 2>&1; then
        success "Student test page tests passed"
        echo "### Student Test Execution Tests: ✅ PASSED" >> ../$REPORT_FILE
    else
        error "Student test page tests failed"
        echo "### Student Test Execution Tests: ❌ FAILED" >> ../$REPORT_FILE
    fi
    
    # Integration tests
    if npm run test -- TestWorkflowIntegration.test.tsx > ../test-output-integration.log 2>&1; then
        success "Integration tests passed"
        echo "### Integration Tests: ✅ PASSED" >> ../$REPORT_FILE
    else
        error "Integration tests failed"
        echo "### Integration Tests: ❌ FAILED" >> ../$REPORT_FILE
    fi
    
    echo "" >> ../$REPORT_FILE
    
    cd ..
}

# Function to run backend tests
run_backend_tests() {
    log "Running backend tests..."
    
    cd backend
    
    echo "## Backend Test Results" >> ../$REPORT_FILE
    echo "" >> ../$REPORT_FILE
    
    # Check if dependencies are installed
    if [ ! -d "node_modules" ]; then
        log "Installing backend dependencies..."
        npm install
    fi
    
    # Run controller tests
    log "Running backend controller tests..."
    if npm run test > ../test-output-backend.log 2>&1; then
        success "Backend tests passed"
        echo "### Controller Tests: ✅ PASSED" >> ../$REPORT_FILE
    else
        error "Backend tests failed"
        echo "### Controller Tests: ❌ FAILED" >> ../$REPORT_FILE
        echo "\`\`\`" >> ../$REPORT_FILE
        tail -20 ../test-output-backend.log >> ../$REPORT_FILE
        echo "\`\`\`" >> ../$REPORT_FILE
    fi
    
    # Run specific workflow tests
    if npm run test -- submitTestController.test.js > ../test-output-submit.log 2>&1; then
        success "Submit test controller tests passed"
        echo "### Test Submission Tests: ✅ PASSED" >> ../$REPORT_FILE
    else
        error "Submit test controller tests failed"
        echo "### Test Submission Tests: ❌ FAILED" >> ../$REPORT_FILE
    fi
    
    if npm run test -- testHistoryController.test.js > ../test-output-history.log 2>&1; then
        success "Test history controller tests passed"
        echo "### Test History Tests: ✅ PASSED" >> ../$REPORT_FILE
    else
        error "Test history controller tests failed"
        echo "### Test History Tests: ❌ FAILED" >> ../$REPORT_FILE
    fi
    
    echo "" >> ../$REPORT_FILE
    
    cd ..
}

# Function to run integration tests
run_integration_tests() {
    log "Running end-to-end integration tests..."
    
    echo "## End-to-End Integration Tests" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    
    # Simulate complete workflow tests
    log "Testing complete teacher workflow..."
    echo "### Teacher Workflow Tests" >> $REPORT_FILE
    echo "- Test creation for all three types" >> $REPORT_FILE
    echo "- Question selection and review process" >> $REPORT_FILE
    echo "- Test scheduling and activation" >> $REPORT_FILE
    echo "- Status: ✅ SIMULATED (requires full environment)" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    
    log "Testing complete student workflow..."
    echo "### Student Workflow Tests" >> $REPORT_FILE
    echo "- Test discovery in dashboard" >> $REPORT_FILE
    echo "- Instructions and agreement process" >> $REPORT_FILE
    echo "- Test execution and navigation" >> $REPORT_FILE
    echo "- Answer submission and processing" >> $REPORT_FILE
    echo "- Status: ✅ SIMULATED (requires full environment)" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
}

# Function to check critical components
check_critical_components() {
    log "Checking critical component implementations..."
    
    echo "## Critical Component Analysis" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    
    # Check if critical files exist
    CRITICAL_FILES=(
        "frontend/src/pages/ScheduleTestPage.tsx"
        "frontend/src/pages/ReviewTestPage.tsx"
        "frontend/src/pages/TestInstructionsPage.tsx"
        "frontend/src/pages/StudentTestPage.tsx"
        "frontend/src/components/ViewUpcomingTest.tsx"
        "backend/controllers/submitTestController.js"
        "backend/controllers/testHistoryController.js"
        "backend/models/TestHistory.js"
        "backend/models/TestConfig.js"
    )
    
    echo "### Critical Files Check" >> $REPORT_FILE
    for file in "${CRITICAL_FILES[@]}"; do
        if [ -f "$file" ]; then
            echo "- ✅ $file" >> $REPORT_FILE
        else
            echo "- ❌ $file (MISSING)" >> $REPORT_FILE
            error "Critical file missing: $file"
        fi
    done
    echo "" >> $REPORT_FILE
}

# Function to generate recommendations
generate_recommendations() {
    log "Generating recommendations..."
    
    echo "## Recommendations and Next Steps" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    
    echo "### Immediate Actions Required" >> $REPORT_FILE
    echo "1. **Run Full Test Suite**: Execute all tests in a complete environment" >> $REPORT_FILE
    echo "2. **Performance Testing**: Conduct load testing with multiple concurrent users" >> $REPORT_FILE
    echo "3. **Security Testing**: Validate authentication and authorization flows" >> $REPORT_FILE
    echo "4. **Browser Compatibility**: Test across different browsers and devices" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    
    echo "### Long-term Improvements" >> $REPORT_FILE
    echo "1. **Automated E2E Testing**: Implement Cypress or Playwright tests" >> $REPORT_FILE
    echo "2. **Performance Monitoring**: Add real-time performance tracking" >> $REPORT_FILE
    echo "3. **Error Tracking**: Implement comprehensive error logging" >> $REPORT_FILE
    echo "4. **Test Data Management**: Create automated test data generation" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    
    echo "### Success Metrics" >> $REPORT_FILE
    echo "- **Test Coverage**: Target 90%+ for critical paths" >> $REPORT_FILE
    echo "- **Performance**: Sub-2-second response times" >> $REPORT_FILE
    echo "- **Reliability**: 99.9% uptime for test submission" >> $REPORT_FILE
    echo "- **User Experience**: Zero critical bugs in production" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
}

# Main execution
main() {
    log "Starting comprehensive test execution..."
    
    # Check critical components first
    check_critical_components
    
    # Run frontend tests
    run_frontend_tests
    
    # Run backend tests
    run_backend_tests
    
    # Run integration tests
    run_integration_tests
    
    # Generate recommendations
    generate_recommendations
    
    # Finalize report
    echo "## Test Execution Summary" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    echo "**Execution completed:** $(date)" >> $REPORT_FILE
    echo "**Total duration:** $SECONDS seconds" >> $REPORT_FILE
    echo "" >> $REPORT_FILE
    echo "For detailed logs, check the test-output-*.log files in the project root." >> $REPORT_FILE
    
    success "Comprehensive test execution completed!"
    log "Report generated: $REPORT_FILE"
    
    # Display summary
    echo ""
    echo "=== TEST EXECUTION SUMMARY ==="
    echo "Report file: $REPORT_FILE"
    echo "Duration: $SECONDS seconds"
    echo "Check the report for detailed results and recommendations."
    echo ""
}

# Cleanup function
cleanup() {
    log "Cleaning up temporary files..."
    rm -f test-output-*.log
}

# Set trap for cleanup
trap cleanup EXIT

# Run main function
main "$@"
