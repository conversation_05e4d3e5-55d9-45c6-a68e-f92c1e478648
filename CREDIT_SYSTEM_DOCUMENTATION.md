# AegisScholar Credit-Based Billing System

## Overview

The AegisScholar platform implements a comprehensive credit-based billing system specifically for the AegisGrader feature. This system provides users with a complete billing dashboard, payment history tracking, usage analytics, and transparent credit management for AI-powered exam grading.

## Key Features

### Core Billing Features
- **Free Credits for New Users**: 10 free credits upon registration
- **Frictionless Onboarding**: No payment setup required during signup
- **Credit-per-Evaluation**: 1 credit deducted per answer sheet evaluation
- **Razorpay Integration**: Secure payment processing for credit purchases
- **No Expiration**: Credits never expire
- **Scope Limited**: Only applies to AegisGrader, not other features

### Billing Dashboard Features
- **Comprehensive Payment History**: Complete transaction tracking with filtering and search
- **Usage Analytics**: Detailed credit consumption patterns and insights
- **Receipt Management**: Downloadable/printable receipts for all transactions
- **Real-time Balance Updates**: Live credit balance tracking
- **Advanced Filtering**: Date ranges, transaction types, and status filters
- **Mobile-Responsive Design**: Optimized for all device sizes
- **Dark Mode Support**: Full liquid glass morphism UI design

## Architecture

### Frontend Components

#### Billing Dashboard (`/billing`)
The main billing interface provides a comprehensive view of user's credit management:

**Main Components:**
- `BillingDashboard.tsx` - Main dashboard page with tabbed interface
- `BillingStats.tsx` - Credit balance and billing statistics overview
- `PaymentHistory.tsx` - Transaction list with advanced filtering
- `CreditUsageAnalytics.tsx` - Usage patterns and consumption analytics
- `TransactionFilters.tsx` - Advanced filtering component
- `ReceiptModal.tsx` - Professional receipt display and download
- `BillingErrorBoundary.tsx` - Error handling and recovery
- `BillingSkeleton.tsx` - Loading state components

**Navigation Integration:**
- Accessible via `/billing` route
- Integrated into main sidebar navigation
- Quick access link from AegisGrader page
- Breadcrumb navigation for easy return

#### Dashboard Tabs
1. **Overview Tab**: Credit balance, quick stats, and recent activity
2. **Payment History Tab**: Complete transaction history with filtering
3. **Usage Analytics Tab**: Credit consumption patterns and insights

### Database Models

#### User Models (Student/Teacher)
```javascript
credits: {
    balance: { type: Number, default: 10 },
    totalEarned: { type: Number, default: 10 },
    totalSpent: { type: Number, default: 0 },
    lastUpdated: { type: Date, default: Date.now }
},
billing: {
    customerId: { type: String },
    lastPurchaseDate: { type: Date },
    totalAmountSpent: { type: Number, default: 0 }
}
```

#### CreditTransaction Model
- Tracks all credit-related transactions
- Supports PURCHASE, USAGE, REFUND, BONUS, INITIAL_GRANT types
- Maintains transaction status and payment details
- Includes audit trail with timestamps

### Services

#### CreditService
- `getCreditBalance(userId, userType)`: Get current balance
- `hasSufficientCredits(userId, userType, required)`: Check availability
- `deductCredits(userId, userType, amount, details)`: Consume credits
- `addCredits(userId, userType, amount, details)`: Add credits
- `grantInitialCredits(userId, userType, amount)`: Welcome bonus

### API Endpoints

#### Credit Management
- `GET /api/credits/packages`: Available credit packages
- `GET /api/credits/balance`: User's credit balance and stats
- `POST /api/credits/order`: Create Razorpay order
- `POST /api/credits/verify`: Verify payment and add credits
- `GET /api/credits/transactions`: Enhanced transaction history with filtering
- `GET /api/credits/analytics`: Usage and payment analytics
- `GET /api/credits/dashboard`: Comprehensive billing dashboard data
- `POST /api/credits/webhook`: Razorpay webhook handler

#### Enhanced API Features
- **Advanced Filtering**: Date ranges, transaction types, status filters
- **Pagination Support**: Efficient handling of large transaction histories
- **Analytics Endpoints**: Usage patterns, payment analytics, consumption insights
- **Real-time Data**: Live credit balance and transaction updates

#### AegisGrader Integration
- Credit check middleware on `/api/aegisGrader/submit`
- Automatic credit deduction after successful evaluation

## Billing Dashboard

### Overview
The billing dashboard (`/billing`) provides users with complete transparency and control over their credit usage and payment history. It features a modern, responsive design with dark mode support and liquid glass morphism UI elements.

### Dashboard Features

#### 1. Payment History Section
**Complete Transaction Tracking:**
- All Razorpay transactions with detailed information
- Transaction ID, payment ID, order ID for each purchase
- Date, amount, payment method, and status tracking
- Credit purchase details (package type, credits added)

**Advanced Filtering:**
- Date range filters (custom dates, quick filters)
- Transaction type filters (Purchase, Usage, Refund, Bonus, Initial Grant)
- Transaction status filters (Completed, Pending, Failed, Cancelled)
- Feature-based filtering (AegisGrader usage)

**Quick Filter Options:**
- Last 7 days, 30 days, 3 months
- Purchases only, Usage only
- Custom date range selection

**Receipt Management:**
- Professional receipt modal for all transactions
- Downloadable/printable receipts with company branding
- Complete transaction details including customer information
- PDF generation for record keeping

#### 2. Credit Usage Section
**Current Balance Display:**
- Prominent credit balance with visual indicators
- Low credit warnings (yellow for <5 credits, red for <2 credits)
- Real-time balance updates after purchases or usage

**Detailed Usage History:**
- Date and time of each credit consumption
- Exam details (subject, class, evaluation count)
- Credits consumed per transaction
- Remaining balance after each transaction

**Usage Analytics:**
- Credits used per day/week/month trends
- Average credits consumed per day
- Most used features breakdown
- Usage patterns and consumption insights

**Feature Breakdown:**
- Usage by feature with percentage distribution
- Visual progress bars for usage patterns
- Total evaluations count
- Credit efficiency metrics

#### 3. Billing Statistics
**Key Metrics:**
- Total amount spent (in INR)
- Total credits purchased
- Total credits used
- Average credits per day
- Average order value

**Payment Analytics:**
- Most popular credit package
- Purchase frequency patterns
- Monthly spending trends
- Package preference analysis

**Usage Summary:**
- Total evaluations performed
- Most used features
- Credits remaining
- Usage efficiency metrics

### User Experience Features

#### Responsive Design
- **Desktop**: Full-featured dashboard with detailed analytics
- **Tablet**: Optimized layout with collapsible sections
- **Mobile**: Touch-friendly interface with bottom navigation

#### Dark Mode Support
- **Liquid Glass Morphism**: Semi-transparent backgrounds with blur effects
- **Consistent Theming**: Proper contrast ratios and color schemes
- **Smooth Transitions**: Animated theme switching

#### Loading States
- **Skeleton Loaders**: Professional loading animations
- **Progressive Loading**: Data loads in sections for better performance
- **Error Boundaries**: Graceful error handling with retry options

#### Accessibility
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: Proper ARIA labels and descriptions
- **High Contrast**: Accessible color combinations
- **Touch Targets**: Minimum 44px touch targets for mobile

## Technical Implementation

### TypeScript Interfaces

#### Billing Types (`frontend/src/types/billing.ts`)
```typescript
interface PaymentTransaction {
    _id: string;
    transactionId: string;
    type: 'PURCHASE' | 'USAGE' | 'REFUND' | 'BONUS' | 'INITIAL_GRANT';
    status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';
    creditAmount: number;
    balanceBefore: number;
    balanceAfter: number;
    description: string;
    createdAt: string;
    payment?: PaymentDetails;
    usage?: UsageDetails;
}

interface UsageAnalytics {
    totalCreditsUsed: number;
    totalEvaluations: number;
    averageCreditsPerDay: number;
    mostUsedFeature: string;
    usageByFeature: Record<string, FeatureUsage>;
    dailyUsage: DailyUsage[];
    weeklyUsage: WeeklyUsage[];
    monthlyUsage: MonthlyUsage[];
}

interface PaymentAnalytics {
    totalAmountSpent: number;
    totalCreditspurchased: number;
    averageOrderValue: number;
    mostPopularPackage: string;
    purchasesByPackage: Record<string, PackageAnalytics>;
    monthlyPurchases: MonthlyPurchase[];
}
```

### Backend Services

#### Enhanced Credit Service (`backend/services/creditService.js`)
**New Methods:**
- `getTransactionHistory()` - Enhanced with filtering and pagination
- `getUsageAnalytics()` - Comprehensive usage pattern analysis
- `getPaymentAnalytics()` - Payment and purchase pattern analysis
- `aggregateUsageByPeriod()` - Time-based usage aggregation
- `aggregatePurchasesByMonth()` - Monthly purchase analysis

**Analytics Features:**
- Time range filtering (week, month, quarter, year)
- Feature-wise usage breakdown
- Purchase pattern analysis
- Consumption trend calculation

#### Enhanced API Endpoints

**GET /api/credits/transactions**
```javascript
// Enhanced with filtering parameters
Query Parameters:
- page: number (pagination)
- limit: number (results per page)
- type: string (PURCHASE|USAGE|REFUND|BONUS|INITIAL_GRANT)
- status: string (PENDING|COMPLETED|FAILED|CANCELLED)
- dateFrom: string (ISO date)
- dateTo: string (ISO date)
- feature: string (filter by feature)

Response:
{
    success: boolean,
    data: {
        transactions: PaymentTransaction[],
        pagination: {
            page: number,
            limit: number,
            total: number,
            totalPages: number
        }
    }
}
```

**GET /api/credits/analytics**
```javascript
// Usage and payment analytics
Query Parameters:
- timeRange: string (week|month|quarter|year)

Response:
{
    success: boolean,
    data: {
        usageAnalytics: UsageAnalytics,
        paymentAnalytics: PaymentAnalytics
    }
}
```

**GET /api/credits/dashboard**
```javascript
// Comprehensive dashboard data
Response:
{
    success: boolean,
    data: {
        creditBalance: CreditBalance,
        billingInfo: BillingInfo,
        recentTransactions: PaymentTransaction[],
        usageAnalytics: UsageAnalytics,
        paymentAnalytics: PaymentAnalytics,
        totalTransactions: number
    }
}
```

### Error Handling

#### Frontend Error Management
- **Error Boundaries**: Comprehensive error catching and recovery
- **Loading States**: Skeleton loaders and progressive loading
- **Retry Mechanisms**: Automatic retry for failed requests
- **User Feedback**: Toast notifications for all actions

#### Backend Error Handling
- **Validation**: Input validation for all API endpoints
- **Rate Limiting**: Protection against abuse
- **Transaction Safety**: Atomic operations for credit management
- **Logging**: Comprehensive error logging and monitoring

### Security Features

#### Data Protection
- **Authentication**: JWT-based authentication for all endpoints
- **Authorization**: User-specific data access controls
- **Input Validation**: Comprehensive input sanitization
- **Rate Limiting**: API endpoint protection

#### Payment Security
- **Razorpay Integration**: Secure payment processing
- **Webhook Verification**: Cryptographic signature verification
- **Transaction Integrity**: Atomic credit operations
- **Audit Trail**: Complete transaction logging

## Credit Packages

| Package | Credits | Price (INR) | Price per Credit |
|---------|---------|-------------|------------------|
| Basic   | 10      | ₹2499.00      | ₹249.90           |
| Standard| 50      | ₹11249.00     | ₹224.90 (10% off) |
| Premium | 100     | ₹19999.00     | ₹199.90 (20% off) |

## Frontend Components

### CreditBalance
- Displays current credit balance
- Shows low credit warnings
- Provides purchase button

### CreditPurchaseModal
- Package selection interface
- Razorpay payment integration
- Success/failure handling

### TransactionHistory
- Lists all credit transactions
- Filters by type and date
- Pagination support

## Deployment Guide

### Frontend Deployment
1. **Build the application:**
   ```bash
   cd frontend
   npm run build
   ```

2. **Environment Variables:**
   ```env
   VITE_RAZORPAY_KEY_ID=your_razorpay_key_id
   VITE_API_BASE_URL=your_backend_url
   ```

3. **Routing Configuration:**
   - Ensure `/billing` route is properly configured
   - Update sidebar navigation to include billing link
   - Configure proper authentication guards

### Backend Deployment
1. **Database Setup:**
   - Ensure CreditTransaction collection is properly indexed
   - Set up proper user permissions for credit operations
   - Configure transaction logging

2. **API Endpoints:**
   - All credit endpoints are under `/api/credits/`
   - Ensure proper CORS configuration
   - Set up rate limiting for payment endpoints

### Monitoring and Analytics
1. **Transaction Monitoring:**
   - Monitor credit transaction success rates
   - Track payment processing times
   - Alert on failed transactions

2. **Usage Analytics:**
   - Track user engagement with billing dashboard
   - Monitor credit consumption patterns
   - Analyze payment conversion rates

## User Guide

### Accessing the Billing Dashboard
1. **Navigation:**
   - Click "Billing" in the main sidebar
   - Or access directly via `/billing` URL
   - Quick access link available on AegisGrader page

2. **Dashboard Tabs:**
   - **Overview**: Quick stats and recent activity
   - **Payment History**: Complete transaction history
   - **Usage Analytics**: Credit consumption insights

### Managing Payment History
1. **Viewing Transactions:**
   - All transactions displayed with full details
   - Real-time status updates
   - Sortable by date, amount, type

2. **Filtering Options:**
   - Date range selection (custom or quick filters)
   - Transaction type filtering
   - Status-based filtering

3. **Receipt Management:**
   - Click any transaction to view receipt
   - Download/print professional receipts
   - All receipts include complete transaction details

### Understanding Usage Analytics
1. **Credit Consumption:**
   - View daily, weekly, monthly usage patterns
   - Track average credits per day
   - Identify peak usage periods

2. **Feature Breakdown:**
   - See which features consume most credits
   - Percentage distribution of usage
   - Efficiency metrics and insights

3. **Spending Analysis:**
   - Total amount spent over time
   - Average order value tracking
   - Package preference analysis

## Environment Variables

### Backend (.env)
```
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_webhook_secret
```

### Frontend (.env)
```
VITE_RAZORPAY_KEY_ID=your_razorpay_key_id
```

## Security Considerations

1. **Payment Verification**: All payments verified using Razorpay signatures
2. **Transaction Atomicity**: Database transactions ensure consistency
3. **Credit Check Middleware**: Prevents unauthorized usage
4. **Webhook Security**: Signature verification for webhook endpoints
5. **Error Handling**: Graceful failure handling with rollbacks

## Usage Flow

### New User Registration
1. User registers (Student/Teacher)
2. System automatically grants 10 free credits
3. Transaction recorded as INITIAL_GRANT type
4. User can immediately start using AegisGrader

### Credit Purchase
1. User selects credit package
2. Razorpay order created
3. User completes payment
4. Payment verified via signature
5. Credits added to user account
6. Transaction recorded as PURCHASE type

### Credit Consumption
1. User submits answer sheets for grading
2. Middleware checks sufficient credits
3. If insufficient, shows purchase modal
4. If sufficient, proceeds with evaluation
5. Credits deducted after successful grading
6. Transaction recorded as USAGE type

## Error Handling

### Payment Failures
- Failed payments marked in transaction history
- Webhook handles payment failure events
- User notified of payment issues
- No credits added for failed payments

### Concurrent Access
- Database transactions prevent race conditions
- Atomic credit deduction operations
- Consistent balance updates

### System Failures
- Graceful degradation on service errors
- Transaction rollbacks on failures
- Detailed error logging for debugging

## Monitoring and Analytics

### Transaction Tracking
- All credit movements logged
- Payment success/failure rates
- User spending patterns
- Credit usage analytics

### Business Metrics
- Revenue per user
- Credit package popularity
- Conversion rates
- Churn analysis

## Testing

### Unit Tests
- CreditService methods
- Payment verification logic
- Transaction handling
- Error scenarios

### Integration Tests
- End-to-end payment flow
- Database consistency
- Webhook processing
- Concurrent operations

## Deployment Notes

1. **Database Migration**: Update existing users with credit fields
2. **Environment Setup**: Configure Razorpay credentials
3. **Webhook Configuration**: Set up Razorpay webhook endpoints
4. **Monitoring**: Set up alerts for payment failures
5. **Backup Strategy**: Regular transaction data backups

## Future Enhancements

1. **Subscription Plans**: Monthly/yearly unlimited plans
2. **Credit Gifting**: Transfer credits between users
3. **Bulk Discounts**: Enterprise pricing tiers
4. **Usage Analytics**: Detailed credit usage reports
5. **Auto-Refill**: Automatic credit purchases when low

## Support and Troubleshooting

### Common Issues
1. **Payment Failures**: Check Razorpay configuration
2. **Credit Not Added**: Verify webhook processing
3. **Insufficient Credits**: Guide users to purchase flow
4. **Transaction Disputes**: Review transaction logs

### Contact Information
- Technical Support: <EMAIL>
- Payment Issues: <EMAIL>
- Documentation: https://docs.aegisscholar.com

## Feature Summary

### ✅ Implemented Features

#### Billing Dashboard
- [x] Comprehensive payment history with advanced filtering
- [x] Real-time credit balance tracking
- [x] Usage analytics with time-based insights
- [x] Professional receipt generation and download
- [x] Mobile-responsive design with dark mode support
- [x] Error handling with graceful recovery
- [x] Loading states with skeleton animations

#### Payment Management
- [x] Razorpay integration for secure payments
- [x] Multiple credit packages (10, 50, 100 credits)
- [x] Automatic credit deduction for AegisGrader usage
- [x] Transaction status tracking and updates
- [x] Webhook handling for payment verification
- [x] Receipt generation with company branding

#### Analytics & Insights
- [x] Usage patterns by day/week/month
- [x] Feature-wise credit consumption breakdown
- [x] Payment analytics and spending insights
- [x] Average usage calculations
- [x] Most popular package tracking
- [x] Credit efficiency metrics

#### User Experience
- [x] Intuitive tabbed interface
- [x] Advanced filtering with quick presets
- [x] Real-time balance updates
- [x] Professional receipt modal
- [x] Error boundaries and retry mechanisms
- [x] Accessibility compliance (WCAG 2.1)

### 🔧 Technical Implementation

#### Frontend Architecture
- [x] TypeScript interfaces for type safety
- [x] Modular component architecture
- [x] Reusable UI components
- [x] Responsive design patterns
- [x] Dark mode support
- [x] Error boundary implementation

#### Backend Services
- [x] Enhanced credit service with analytics
- [x] Advanced filtering and pagination
- [x] Transaction aggregation services
- [x] Real-time data synchronization
- [x] Comprehensive error handling
- [x] Security and rate limiting

#### Database Design
- [x] Optimized transaction schema
- [x] Efficient indexing for queries
- [x] Atomic credit operations
- [x] Audit trail maintenance
- [x] Data integrity constraints
- [x] Performance optimization

## Troubleshooting

### Common Issues

#### Frontend Issues
**Problem**: Billing dashboard not loading
- **Solution**: Check network connectivity and API endpoints
- **Debug**: Open browser console for error messages
- **Fallback**: Use error boundary retry mechanism

**Problem**: Credit balance not updating
- **Solution**: Refresh the page or check real-time sync
- **Debug**: Verify WebSocket connections
- **Fallback**: Manual balance refresh button

#### Backend Issues
**Problem**: Payment verification failing
- **Solution**: Check Razorpay webhook configuration
- **Debug**: Verify webhook signatures and endpoints
- **Fallback**: Manual transaction verification

**Problem**: Analytics data missing
- **Solution**: Check database aggregation services
- **Debug**: Verify transaction data integrity
- **Fallback**: Rebuild analytics cache

#### Payment Issues
**Problem**: Razorpay payment not completing
- **Solution**: Check payment gateway configuration
- **Debug**: Verify API keys and webhook setup
- **Fallback**: Contact Razorpay support

**Problem**: Credits not added after payment
- **Solution**: Check webhook processing logs
- **Debug**: Verify transaction status in database
- **Fallback**: Manual credit addition

### Performance Optimization

#### Frontend Optimization
- Implement lazy loading for large transaction lists
- Use virtual scrolling for better performance
- Optimize bundle size with code splitting
- Cache frequently accessed data

#### Backend Optimization
- Index database queries for faster retrieval
- Implement caching for analytics data
- Use connection pooling for database access
- Optimize aggregation pipelines

### Security Considerations

#### Data Protection
- All sensitive data encrypted in transit and at rest
- User authentication required for all billing endpoints
- Input validation and sanitization implemented
- Rate limiting to prevent abuse

#### Payment Security
- Razorpay PCI DSS compliance
- Webhook signature verification
- Secure API key management
- Transaction audit logging

## Changelog

### Version 2.0.0 (Current)
- ✅ Complete billing dashboard implementation
- ✅ Advanced payment history with filtering
- ✅ Comprehensive usage analytics
- ✅ Professional receipt system
- ✅ Mobile-responsive design
- ✅ Dark mode support
- ✅ Error handling and recovery

### Version 1.0.0 (Previous)
- ✅ Basic credit system implementation
- ✅ Razorpay payment integration
- ✅ Simple transaction tracking
- ✅ Credit deduction for AegisGrader
- ✅ Basic balance display

## Future Enhancements

### Planned Features
- [ ] Credit usage predictions and recommendations
- [ ] Bulk credit purchase discounts
- [ ] Subscription-based credit plans
- [ ] Advanced analytics with charts and graphs
- [ ] Export functionality for transaction data
- [ ] Multi-currency support
- [ ] Credit sharing between team members
- [ ] Usage alerts and notifications

### Technical Improvements
- [ ] Real-time WebSocket updates
- [ ] Advanced caching strategies
- [ ] Machine learning for usage predictions
- [ ] Enhanced mobile app integration
- [ ] API rate limiting improvements
- [ ] Advanced security features
