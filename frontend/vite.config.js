import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';
import svgr from 'vite-plugin-svgr';
// import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [
    react(),
    svgr({
      exportAsDefault: true,
      svgrOptions: {
        icon: true,
      },
    }),
    // tailwindcss(),
  ],
  optimizeDeps: {
    include: ['pdfjs-dist/build/pdf.worker.entry'],
  },
  resolve: {
    alias: {
      // Match with tsconfig paths
      '@': path.resolve(__dirname, './src'),
      '@/lib': path.resolve(__dirname, './src/shadcn/components/lib'),
      '@/shadcn/components': path.resolve(__dirname, './src/shadcn/components'),
      '@/ui': path.resolve(__dirname, './src/shadcn/components/ui')
    },
  },
  css: {
    postcss: {
      plugins: [require('@tailwindcss/postcss')],
    },
  },
});