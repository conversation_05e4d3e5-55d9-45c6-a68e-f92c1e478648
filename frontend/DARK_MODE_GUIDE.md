# Dark Mode Implementation Guide

## Overview

This guide explains the dark mode implementation for AegisScholar platform. The dark mode feature provides users with two theme options:
- **Light Mode**: Traditional light theme (default)
- **Dark Mode**: Dark theme for better viewing in low-light conditions

## Implementation Details

### 1. Theme Context (`src/contexts/themeContext.tsx`)

The theme system is built around a React Context that manages:
- Current theme setting (`light` or `dark`)
- Theme persistence in localStorage
- Automatic application of theme classes to the document

### 2. Theme Toggle Component (`src/components/ThemeToggle.tsx`)

A simple toggle button that switches between light and dark themes:

**Props:**
- `size`: `'sm'` | `'default'` | `'lg'` (default: `'default'`)
- `showLabel`: boolean (default: `false`)

**Usage Examples:**
```tsx
// Default toggle button
<ThemeToggle />

// Toggle button with label
<ThemeToggle showLabel={true} />

// Small size toggle
<ThemeToggle size="sm" />
```

**Behavior:**
- Click to toggle: Light ↔ Dark
- Shows sun icon (☀️) for light theme, moon icon (🌙) for dark theme
- Tooltip indicates current theme and toggle action

### 3. CSS Variables and Styling

The dark mode uses CSS custom properties defined in `src/index.css`:

**Light Mode Variables:**
```css
:root {
  --background: 0 0% 100%;
  --foreground: 230 60% 10%;
  --card: 220 30% 99%;
  --primary: 230 60% 20%;
  /* ... other variables */
}
```

**Dark Mode Variables:**
```css
.dark {
  --background: 230 30% 10%;
  --foreground: 0 0% 98%;
  --card: 230 30% 12%;
  --primary: 230 60% 20%;
  /* ... other variables */
}
```

### 4. Integration

The theme system is integrated at the application root level:

```tsx
// src/index.tsx
<ThemeProvider>
  <UserProvider>
    <App />
  </UserProvider>
</ThemeProvider>
```

The theme toggle is placed in the sidebar for easy access:

```tsx
// src/components/Sidebar.tsx
<ThemeToggle size="default" />
```

## Features

### ✅ Persistent Settings
- Saves user's theme preference in localStorage
- Remembers setting across browser sessions
- Defaults to 'light' for new users

### ✅ Smooth Transitions
- CSS transitions for theme changes
- Consistent styling across all components
- No flash of unstyled content

### ✅ Accessibility
- Proper ARIA labels and screen reader support
- Keyboard navigation support
- High contrast ratios in both themes

## Usage

### For Users
1. Look for the theme toggle button in the sidebar (sun/moon icon)
2. Click the button to toggle between themes:
   - **Light** ↔ **Dark**
3. The icon changes to reflect the current theme:
   - ☀️ Sun icon for Light theme
   - 🌙 Moon icon for Dark theme

### For Developers

#### Using the Theme Context
```tsx
import { useTheme } from '../contexts/themeContext';

function MyComponent() {
  const { theme, setTheme } = useTheme();

  return (
    <div>
      <p>Current theme: {theme}</p>
      <button onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}>
        Toggle Theme
      </button>
    </div>
  );
}
```

#### Adding Theme-Aware Styling
Use Tailwind's dark mode classes:
```tsx
<div className="bg-background text-foreground dark:bg-background dark:text-foreground">
  <h1 className="text-primary dark:text-primary">Title</h1>
</div>
```

Or use CSS custom properties:
```css
.my-component {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}
```

## Testing

The implementation includes comprehensive tests:
- Theme context functionality
- Theme toggle component behavior
- System preference detection
- localStorage persistence

Run tests with:
```bash
npm test ThemeToggle
npm test themeContext
```

## Browser Support

- **Modern browsers**: Full support with system detection
- **Older browsers**: Graceful fallback to light mode
- **No JavaScript**: Defaults to light mode

## Customization

### Adding New Theme Variables
1. Add the variable to both `:root` and `.dark` selectors in `index.css`
2. Use HSL format for consistency
3. Update TypeScript types if needed

### Creating Custom Theme Variants
1. Extend the `Theme` type in `themeContext.tsx`
2. Add new CSS classes for the theme
3. Update the theme toggle component

## Troubleshooting

### Theme Not Applying
- Check that ThemeProvider wraps your app
- Verify CSS custom properties are defined
- Ensure the `dark` class is applied to `<html>`

### System Detection Not Working
- Check browser support for `prefers-color-scheme`
- Verify media query listeners are properly attached
- Test with browser dev tools theme simulation

### Performance Issues
- Theme changes are optimized with CSS custom properties
- No re-rendering of components on theme change
- Minimal JavaScript execution for theme switching
