# Analytics System Implementation Guide

## Quick Start

### 1. Backend Setup
The analytics system is already integrated into your backend. The following files have been created:

- `backend/models/UserAnalytics.js` - Database models for analytics data
- `backend/services/analyticsService.js` - Core analytics processing service
- `backend/routes/analyticsRoutes.js` - API endpoints for analytics
- Analytics routes added to `backend/index.js`

### 2. Frontend Setup
The frontend analytics system includes:

- `frontend/src/contexts/analyticsContext.tsx` - Analytics context provider
- `frontend/src/hooks/useTracking.ts` - Tracking hooks for components
- `frontend/src/components/AnalyticsDashboard.tsx` - Admin analytics dashboard
- Analytics provider added to `frontend/src/App.tsx`
- Enhanced admin page with analytics tab

### 3. Usage in Components

#### Basic Tracking
```typescript
import { useTracking } from '@/hooks/useTracking';

function MyComponent() {
  const { trackClick, trackFeatureUsage } = useTracking();
  
  const handleButtonClick = () => {
    trackClick('my-button', 'Click Me', 'dashboard');
    // Your button logic
  };
  
  return <button onClick={handleButtonClick}>Click Me</button>;
}
```

#### Form Tracking
```typescript
import { useFormTracking } from '@/hooks/useTracking';

function LoginForm() {
  const { trackFormStart, trackFormSubmit, trackFieldInteraction } = useFormTracking('login-form', 'authentication');
  
  useEffect(() => {
    trackFormStart();
  }, []);
  
  const handleSubmit = (formData) => {
    trackFormSubmit(formData);
    // Submit logic
  };
  
  return (
    <form onSubmit={handleSubmit}>
      <input 
        onFocus={() => trackFieldInteraction('email')}
        type="email" 
        name="email" 
      />
      <button type="submit">Login</button>
    </form>
  );
}
```

#### Test Tracking
```typescript
import { useTracking } from '@/hooks/useTracking';

function TestInterface() {
  const { trackTestAction } = useTracking();
  
  const startTest = () => {
    trackTestAction('start', testId, null, { 
      testType: 'practice',
      subject: 'mathematics' 
    });
  };
  
  const answerQuestion = (questionId, answer) => {
    trackTestAction('question_answered', testId, questionId, { 
      answer,
      timeSpent: getTimeSpent() 
    });
  };
}
```

#### AegisAI Chat Tracking
```typescript
import { useTracking } from '@/hooks/useTracking';

function ChatInterface() {
  const { trackChatAction } = useTracking();
  
  const sendMessage = (message) => {
    trackChatAction('message_sent', conversationId, { 
      messageLength: message.length,
      subject: currentSubject 
    });
  };
  
  const switchSubject = (newSubject) => {
    trackChatAction('subject_switched', conversationId, { 
      fromSubject: currentSubject,
      toSubject: newSubject 
    });
  };
}
```

## Key Features Implemented

### 1. Comprehensive Data Collection
- **User Sessions**: Track login/logout, session duration, device info
- **Page Views**: Automatic tracking of route changes and page visits
- **User Interactions**: Button clicks, form submissions, feature usage
- **Performance Metrics**: Page load times, response times
- **Device Analytics**: Browser, OS, screen resolution, mobile/desktop

### 2. Privacy-Compliant Design
- **Opt-out capability**: Users can disable analytics tracking
- **Data minimization**: Only essential data is collected
- **Automatic cleanup**: Old data is automatically removed
- **No PII tracking**: Personal information is not stored in analytics

### 3. Real-time Analytics Dashboard
- **Live user count**: See active users in real-time
- **Page views**: Current page activity
- **Feature usage**: Most popular features
- **User type breakdown**: Students vs Teachers vs Admins
- **Export capabilities**: CSV and JSON export options

### 4. Performance Optimized
- **Non-blocking**: Analytics never impact user experience
- **Offline support**: Events queued when offline, sent when online
- **Throttled events**: Scroll and resize events are throttled
- **Efficient storage**: Aggregated data reduces database load

## Database Collections Created

1. **userSessions** - User session tracking
2. **userInteractions** - Individual user interactions
3. **pageAnalytics** - Aggregated page-level metrics
4. **featureUsage** - Feature usage statistics
5. **userBehavior** - User behavior patterns

## API Endpoints Available

### Session Management
- `POST /api/analytics/session/start` - Start user session
- `POST /api/analytics/session/end` - End user session

### Interaction Tracking
- `POST /api/analytics/interaction` - Track user interaction

### Analytics Data
- `GET /api/analytics/dashboard` - Dashboard data
- `GET /api/analytics/user/:userId` - User-specific analytics
- `GET /api/analytics/realtime` - Real-time metrics
- `GET /api/analytics/export` - Export data

## Admin Dashboard Features

Access the analytics dashboard at `/platform-admin` → Analytics Dashboard tab:

1. **Real-time Metrics**
   - Active sessions count
   - Active users count
   - Current page views

2. **Overview Cards**
   - Total users (last 30 days)
   - Total sessions
   - Average session duration
   - Date range selector

3. **Visualizations**
   - Daily activity trend (line chart)
   - User type distribution (pie chart)
   - Most visited pages (bar chart)
   - Feature usage (bar chart)

4. **Export Options**
   - Sessions data (CSV/JSON)
   - Interactions data (CSV/JSON)
   - Page analytics (CSV/JSON)
   - Feature usage (CSV/JSON)

## Privacy and Compliance

### GDPR/DPDP Act Compliance
- **Explicit consent**: Analytics can be disabled by users
- **Data minimization**: Only necessary data is collected
- **Right to erasure**: Data cleanup functionality
- **Data portability**: Export functionality available
- **Transparency**: Clear documentation of data collection

### Implementation
```typescript
// Disable analytics for a user
const { setAnalyticsEnabled } = useAnalytics();
setAnalyticsEnabled(false);

// Data is automatically cleaned up after 1 year
// Configurable retention period
```

## Next Steps

### 1. Enable Analytics in Components
Add tracking to your existing components using the provided hooks:

```typescript
// In your existing components
import { useTracking } from '@/hooks/useTracking';

// Add tracking calls to important user actions
const { trackClick, trackFeatureUsage } = useTracking();
```

### 2. Configure Data Retention
Set up automated data cleanup based on your requirements:

```javascript
// In your backend, schedule cleanup job
// Clean up data older than 365 days
await analyticsService.cleanupOldData(365);
```

### 3. Monitor Performance
Use the real-time dashboard to monitor:
- User engagement patterns
- Feature adoption rates
- Performance bottlenecks
- Error rates

### 4. Privacy Compliance
- Add consent banners if required
- Implement user data deletion requests
- Regular privacy audits

## Troubleshooting

### Common Issues

1. **Analytics not tracking**
   - Ensure AnalyticsProvider wraps your app
   - Check if user is logged in (tracking requires user context)
   - Verify analytics is enabled (`isEnabled` state)

2. **Dashboard not loading**
   - Check backend analytics routes are properly imported
   - Verify MongoDB connection
   - Check browser console for API errors

3. **Performance issues**
   - Analytics should not impact performance
   - Check for excessive tracking calls
   - Verify throttling is working for scroll events

### Debug Mode
Enable debug logging in development:

```typescript
// In analyticsContext.tsx, add console.log statements
console.log('Tracking interaction:', interactionData);
```

## Future Enhancements

1. **Advanced Analytics**
   - User journey mapping
   - Conversion funnel analysis
   - Cohort analysis
   - A/B testing framework

2. **Machine Learning**
   - Predictive user behavior
   - Anomaly detection
   - Personalized recommendations

3. **Enhanced Visualizations**
   - Heat maps
   - User flow diagrams
   - Geographic analytics

4. **Integration Options**
   - Google Analytics integration
   - Custom webhook notifications
   - Third-party analytics tools
