# User Analytics and Interaction Monitoring System

## Overview

The User Analytics and Interaction Monitoring System provides comprehensive tracking and insights into user behavior patterns across the AegisScholar platform. This system helps developers and founders understand platform usage, optimize user experience, and make data-driven decisions.

## Architecture

```mermaid
graph TB
    A[Frontend Components] --> B[Analytics Context]
    B --> C[Analytics Service]
    C --> D[Backend API]
    D --> E[MongoDB Collections]
    E --> F[Analytics Dashboard]
    
    G[User Interactions] --> H[Tracking Hooks]
    H --> B
    
    I[Real-time Events] --> J[Event Listeners]
    J --> B
    
    K[Session Management] --> L[Session Tracking]
    L --> C
```

## Features

### 1. User Interaction Tracking
- **Button clicks and UI element interactions**
- **Page/route navigation patterns and user flow analysis**
- **Session duration and time spent on each page/tab**
- **Form submissions and input field interactions**
- **Feature usage frequency (AegisAI, test taking, dashboard navigation)**

### 2. Data Collection
- **Real-time event tracking without performance impact**
- **User session management and unique visitor identification**
- **Timestamp logging for all interactions**
- **Device/browser information for responsive design insights**
- **User type differentiation (students vs teachers vs admins)**

### 3. Analytics Dashboard
- **Display analytics data on the admin page**
- **Visualizations for user behavior patterns**
- **Filtering by date ranges, user types, and specific features**
- **Key metrics like daily/monthly active users, feature adoption rates**
- **Export capabilities for detailed analysis**

## Database Schema

### Collections

#### 1. UserSessions
```javascript
{
  userId: ObjectId,
  userType: String, // 'Student', 'Teacher', 'Admin'
  sessionId: String,
  startTime: Date,
  endTime: Date,
  duration: Number, // in seconds
  deviceInfo: {
    userAgent: String,
    browser: String,
    os: String,
    device: String, // mobile, tablet, desktop
    screenResolution: String,
    timezone: String
  },
  ipAddress: String,
  location: {
    country: String,
    region: String,
    city: String
  },
  isActive: Boolean,
  lastActivity: Date
}
```

#### 2. UserInteractions
```javascript
{
  userId: ObjectId,
  sessionId: String,
  userType: String,
  interactionType: String, // 'page_view', 'button_click', 'form_submit', etc.
  page: String,
  previousPage: String,
  elementType: String,
  elementId: String,
  elementClass: String,
  elementText: String,
  feature: String, // 'aegis_ai', 'test_taking', 'dashboard', etc.
  metadata: Object,
  loadTime: Number,
  responseTime: Number,
  timestamp: Date
}
```

#### 3. PageAnalytics
```javascript
{
  page: String,
  date: Date,
  totalViews: Number,
  uniqueVisitors: Number,
  userTypeBreakdown: {
    students: Number,
    teachers: Number,
    admins: Number
  },
  averageTimeOnPage: Number,
  bounceRate: Number,
  exitRate: Number,
  averageLoadTime: Number,
  deviceBreakdown: {
    mobile: Number,
    tablet: Number,
    desktop: Number
  }
}
```

#### 4. FeatureUsage
```javascript
{
  feature: String,
  date: Date,
  totalUsage: Number,
  uniqueUsers: Number,
  userTypeUsage: {
    students: Number,
    teachers: Number,
    admins: Number
  },
  averageSessionDuration: Number,
  averageInteractionsPerSession: Number,
  successMetrics: {
    completionRate: Number,
    errorRate: Number,
    retryRate: Number
  }
}
```

#### 5. UserBehavior
```javascript
{
  userId: ObjectId,
  userType: String,
  mostUsedFeatures: [{
    feature: String,
    usageCount: Number,
    lastUsed: Date
  }],
  commonPaths: [{
    path: [String],
    frequency: Number,
    averageDuration: Number
  }],
  activityPatterns: {
    peakHours: [Number],
    peakDays: [Number],
    sessionFrequency: Number,
    averageSessionDuration: Number
  },
  engagementScore: Number,
  retentionMetrics: {
    firstVisit: Date,
    lastVisit: Date,
    totalSessions: Number,
    totalTimeSpent: Number
  }
}
```

## API Endpoints

### Session Management
- `POST /api/analytics/session/start` - Start a new user session
- `POST /api/analytics/session/end` - End a user session

### Interaction Tracking
- `POST /api/analytics/interaction` - Track user interaction

### Analytics Data
- `GET /api/analytics/dashboard` - Get dashboard analytics data
- `GET /api/analytics/user/:userId` - Get user-specific analytics
- `GET /api/analytics/pages` - Get page analytics
- `GET /api/analytics/features` - Get feature usage analytics
- `GET /api/analytics/realtime` - Get real-time analytics

### Data Export
- `GET /api/analytics/export` - Export analytics data (JSON/CSV)

### Data Management
- `DELETE /api/analytics/cleanup` - Clean up old data (admin only)

## Frontend Implementation

### Analytics Context
The `AnalyticsProvider` wraps the entire application and provides tracking capabilities:

```typescript
import { AnalyticsProvider } from './contexts/analyticsContext';

function App() {
  return (
    <AnalyticsProvider>
      <RouterProvider router={router} />
    </AnalyticsProvider>
  );
}
```

### Tracking Hooks

#### useTracking
Basic tracking functionality:
```typescript
const { trackClick, trackForm, trackFeatureUsage } = useTracking();

// Track button click
trackClick('submit-button', 'Submit Test', 'test_taking');

// Track form submission
trackForm('login-form', 'submit', formData, 'authentication');

// Track feature usage
trackFeatureUsage('aegis_ai', 'message_sent', { conversationId });
```

#### useComponentTracking
Component-specific tracking:
```typescript
const { trackComponentInteraction } = useComponentTracking('TestCard', 'test_taking');

// Automatically tracks mount/unmount
// Manual interaction tracking
trackComponentInteraction('card_clicked', { testId });
```

#### useFormTracking
Enhanced form tracking:
```typescript
const { 
  trackFormStart, 
  trackFieldInteraction, 
  trackFormSubmit, 
  trackFormError 
} = useFormTracking('registration-form', 'authentication');
```

## Privacy and Compliance

### GDPR/DPDP Act Compliance
- **Explicit consent mechanism** for analytics tracking
- **Data minimization** - only collect necessary data
- **User control** - ability to opt-out of tracking
- **Data retention policies** with automatic cleanup
- **Anonymization** of sensitive data

### Implementation
```typescript
// User can disable analytics
const { setAnalyticsEnabled } = useAnalytics();
setAnalyticsEnabled(false);

// Data retention (configurable)
await analyticsService.cleanupOldData(365); // Keep data for 1 year
```

## Performance Considerations

### Non-blocking Tracking
- All analytics calls are asynchronous and non-blocking
- Failed requests are queued for retry when online
- Throttled event listeners for scroll/resize events

### Efficient Data Storage
- Indexed MongoDB collections for fast queries
- Aggregated daily data to reduce storage
- TTL indexes for automatic data cleanup

### Caching Strategy
- Real-time data cached for 30 seconds
- Dashboard data cached for 5 minutes
- Export data generated on-demand

## Usage Examples

### Track Page Views (Automatic)
```typescript
// Automatically tracked on route changes
// No manual implementation needed
```

### Track Button Clicks
```typescript
function SubmitButton() {
  const { trackClick } = useTracking();
  
  return (
    <button 
      onClick={(e) => {
        trackClick(e.currentTarget, { testId: '123' }, 'test_taking');
        handleSubmit();
      }}
    >
      Submit Test
    </button>
  );
}
```

### Track AegisAI Interactions
```typescript
function ChatInterface() {
  const { trackChatAction } = useTracking();
  
  const sendMessage = (message) => {
    trackChatAction('message_sent', conversationId, { 
      messageLength: message.length,
      subject: currentSubject 
    });
    // Send message logic
  };
}
```

### Track Test Actions
```typescript
function TestInterface() {
  const { trackTestAction } = useTracking();
  
  const startTest = () => {
    trackTestAction('start', testId, null, { 
      testType: 'practice',
      subject: 'mathematics' 
    });
  };
  
  const answerQuestion = (questionId, answer) => {
    trackTestAction('question_answered', testId, questionId, { 
      answer,
      timeSpent: getTimeSpent() 
    });
  };
}
```

## Monitoring and Alerts

### Real-time Monitoring
- Active user count
- Current page views
- Error rates
- Performance metrics

### Automated Alerts
- Unusual traffic patterns
- High error rates
- Performance degradation
- Data storage limits

## Future Enhancements

1. **Machine Learning Integration**
   - Predictive user behavior analysis
   - Personalized recommendations
   - Anomaly detection

2. **Advanced Visualizations**
   - Heat maps for UI interactions
   - User journey flow diagrams
   - Cohort analysis

3. **A/B Testing Framework**
   - Feature flag integration
   - Conversion tracking
   - Statistical significance testing

4. **Enhanced Privacy Controls**
   - Granular consent management
   - Data portability features
   - Right to be forgotten implementation
