# Frontend Testing Documentation

This document provides information about the testing setup for the frontend application.

## Testing Stack

- **Vitest**: Fast test runner compatible with Vite
- **React Testing Library**: For testing React components
- **JSDOM**: For simulating a browser environment

## Running Tests

### Running All Tests

```bash
# Run all tests in watch mode (interactive)
npm test

# Run all tests once with coverage report
npm run test:coverage
```

### Running Specific Tests

```bash
# Run tests matching a specific pattern
npm test -- -t "component name"

# Run tests in a specific file
npm test -- path/to/test/file.test.tsx
```

## Test Structure

Our tests follow a structured organization pattern to make them easy to maintain:

```
src/
  __tests__/                     # App-level test files
    App.test.tsx
  components/
    __tests__/                   # Component test files
      ComponentName.test.tsx
    ComponentName.tsx
  hooks/
    __tests__/                   # Hook test files
      useHookName.test.ts
    useHookName.ts
  utils/
    __tests__/                   # Utility test files
      utilityName.test.ts
    utilityName.ts
  api/
    __tests__/                   # API test files
      api.test.ts
    api.ts
```

We maintain a consistent file structure with tests placed in `__tests__` folders at each module level, making it easy to find tests related to specific components or functionality.

## Test Utils

We have a dedicated test utilities setup in `src/utils/test-utils.tsx` that includes:

- Custom render functions with appropriate providers
- Helpful test utilities and mocks
- Type-safe testing helpers

## Writing Tests

### Component Tests

Component tests should verify:
- The component renders without crashing
- Props are properly passed and displayed
- Interactive elements work as expected
- Conditional rendering logic works

Example:
```tsx
import { describe, it, expect } from 'vitest';
import { render, screen, fireEvent } from '../../utils/test-utils';
import Component from '../Component';

describe('Component', () => {
  it('renders correctly', () => {
    render(<Component />);
    // Add assertions
  });
});
```

### Hook Tests

Hook tests should verify:
- The hook initializes with correct initial state
- The hook responds correctly to function calls
- Side effects work as expected

Example:
```tsx
import { renderHook, act } from '@testing-library/react';
import { useHook } from '../useHook';

describe('useHook', () => {
  it('initializes with correct state', () => {
    const { result } = renderHook(() => useHook());
    // Add assertions
  });
});
```

### Utility Tests

Utility tests should verify:
- Functions return expected results for various inputs
- Edge cases are handled properly

Example:
```tsx
import { describe, it, expect } from 'vitest';
import { utilFunction } from '../util';

describe('utilFunction', () => {
  it('returns expected result', () => {
    const result = utilFunction(input);
    expect(result).toBe(expectedOutput);
  });
});
```

## Mocking

### Mocking Components

Use the vi.mock function to mock components:

```tsx
vi.mock('../../components/ComponentToMock', () => ({
  default: () => <div data-testid="mocked-component" />
}));
```

### Mocking Hooks

Mock React hooks:

```tsx
vi.mock('react', async () => {
  const actual = await vi.importActual('react');
  return {
    ...actual,
    useState: vi.fn().mockImplementation(() => [initialState, setState])
  };
});
```

### Mocking API Calls

Mock API responses:

```tsx
vi.mock('../../axios', () => ({
  axiosDefault: vi.fn().mockResolvedValue({ data: mockData })
}));
```

## Code Coverage

View code coverage reports in the console when running:

```bash
npm run test:coverage
```

The detailed HTML coverage report can be found in:
```
frontend/coverage/index.html
```

## Best Practices

1. **Keep Tests Isolated**: Each test should be independent and not rely on other tests
2. **Use Data-testid Attributes**: Use data-testid attributes for reliable element selection
3. **Test Behavior, Not Implementation**: Focus on what the component does, not how it does it
4. **Mock External Dependencies**: Always mock external API calls or utilities
5. **Use Appropriate Assertions**: Choose assertions that make failures easy to understand 