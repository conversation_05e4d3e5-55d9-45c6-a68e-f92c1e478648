# AegisScholar Authentication System Documentation

## 📋 Table of Contents

1. [Overview](#overview)
2. [Architecture](#architecture)
3. [Authentication Flow](#authentication-flow)
4. [Components](#components)
5. [Backend Implementation](#backend-implementation)
6. [Frontend Implementation](#frontend-implementation)
7. [Security Features](#security-features)
8. [API Endpoints](#api-endpoints)
9. [Configuration](#configuration)
10. [Troubleshooting](#troubleshooting)
11. [Best Practices](#best-practices)

---

## 🎯 Overview

AegisScholar implements a robust JWT-based authentication system with refresh token rotation, supporting multiple user types (Students, Teachers) and OAuth integration. The system ensures secure session management with automatic token refresh and comprehensive error handling.

### Key Features
- **JWT Access & Refresh Tokens**: Secure token-based authentication
- **Multi-User Support**: Students, Teachers, and Admin roles
- **Google OAuth Integration**: Social login capability
- **Automatic Token Refresh**: Seamless session management
- **Email Verification**: Account security enhancement
- **Password Reset**: Self-service password recovery
- **Session Persistence**: Maintains login state across browser refreshes

---

## 🏗️ Architecture

```mermaid
graph TB
    A[Client Browser] --> B[Frontend React App]
    B --> C[Authentication Context]
    C --> D[Protected Routes]
    B --> E[Axios Interceptors]
    E --> F[Backend API]
    F --> G[JWT Middleware]
    G --> H[Auth Controllers]
    H --> I[Database Models]
    F --> J[Refresh Token Controller]
    J --> K[Cookie Storage]
    
    subgraph "Frontend Components"
        B
        C
        D
        E
    end
    
    subgraph "Backend Services"
        F
        G
        H
        I
        J
        K
    end
```

---

## 🔄 Authentication Flow

### 1. Initial Login Process

```mermaid
sequenceDiagram
    participant U as User
    participant F as Frontend
    participant B as Backend
    participant DB as Database
    
    U->>F: Enter credentials
    F->>B: POST /api/auth/loginStudent|Teacher
    B->>DB: Validate credentials
    DB-->>B: User data
    B->>B: Generate JWT tokens
    B->>DB: Store refresh token
    B-->>F: Access token + Set refresh cookie
    F->>F: Store user data in context
    F->>F: Redirect to dashboard
```

### 2. Page Refresh Authentication

```mermaid
sequenceDiagram
    participant B as Browser
    participant F as Frontend
    participant BE as Backend
    participant DB as Database
    
    B->>F: Page refresh
    F->>F: Check sessionStorage
    F->>BE: POST /api/refresh (with cookie)
    BE->>DB: Validate refresh token
    DB-->>BE: User found
    BE->>BE: Generate new access token
    BE-->>F: New access token
    F->>BE: GET /api/details/getDetailStudent|Teacher
    BE-->>F: User profile data
    F->>F: Restore user context
```

### 3. Automatic Token Refresh

```mermaid
sequenceDiagram
    participant F as Frontend
    participant API as API Request
    participant BE as Backend
    
    F->>API: Request with expired token
    API-->>F: 403 Forbidden
    F->>F: Trigger refresh interceptor
    F->>BE: POST /api/refresh
    BE-->>F: New access token
    F->>API: Retry original request
    API-->>F: Success response
```

---

## 🧩 Components

### Frontend Components

#### 1. **UserContext** (`frontend/src/contexts/userContext.tsx`)
Central state management for user authentication data.

```typescript
interface User {
    usertype?: string;
    id?: string;
    email?: string;
    username?: string;
    role?: string;
    accessToken?: string;
    // ... other fields
}

interface UserContextType {
    user: User | null;
    setUser: (user: User | null) => void;
}
```

**Key Features:**
- Manages user state across the application
- Automatically syncs with sessionStorage
- Provides type-safe user data access

#### 2. **usePageRefresh Hook** (`frontend/src/hooks/usePageRefresh.jsx`)
Handles authentication restoration on page refresh.

```javascript
export const usePageRefresh = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user, setUser } = useUser();
  const [initializationState, setInitializationState] = useState('idle');
  
  useEffect(() => {
    const initializeUser = async () => {
      // Check sessionStorage for user data
      // Attempt token refresh
      // Fetch user details
      // Handle authentication failure
    };
  }, []);
};
```

**Responsibilities:**
- Checks sessionStorage for user credentials
- Attempts automatic token refresh
- Fetches complete user profile
- Redirects to login on failure

#### 3. **useAxiosPrivate Hook** (`frontend/src/hooks/useAxiosPrivate.jsx`)
Provides authenticated HTTP client with automatic token refresh.

```javascript
export const useAxiosPrivate = () => {
  const refresh = useRefreshToken();
  const { user } = useUser();

  useEffect(() => {
    const requestIntercept = axiosPrivate.interceptors.request.use(
      config => {
        if (!config.headers['Authorization']) {
          config.headers['Authorization'] = `Bearer ${user?.accessToken}`;
        }
        return config;
      }
    );

    const responseIntercept = axiosPrivate.interceptors.response.use(
      response => response,
      async(error) => {
        if (error?.response.status === 403 && !prevRequest?.sent) {
          const newAccessToken = await refresh();
          prevRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;
          return axiosPrivate(prevRequest);
        }
        return Promise.reject(error);
      }
    );
  }, [user, refresh]);
};
```

#### 4. **Layout Component** (`frontend/src/components/Layout.tsx`)
Main layout wrapper that initializes authentication.

```typescript
const Layout: React.FC = () => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Initialize authentication on layout mount
  usePageRefresh();

  return (
    <div className="flex flex-col sm:flex-row">
      <Sidebar isExpanded={isExpanded} onToggle={() => setIsExpanded(!isExpanded)} />
      <main>
        <Outlet />
      </main>
    </div>
  );
};
```

---

## 🔧 Backend Implementation

### 1. **Authentication Controller** (`backend/controllers/authController.js`)

#### Student Login
```javascript
export const loginStudent = async (req, res) => {
    const { email, password } = req.body;
    try {
        const student = await Student.findOne({
            $or: [{ email: email }, { username: email }]
        });
        
        if (!student || !(await student.comparePassword(password))) {
            return res.status(400).json({ message: 'Invalid credentials' });
        }

        if (!student.isEmailVerified) {
            return res.status(401).json({
                message: 'Email not verified',
                verified: false,
                needsVerification: true
            });
        }

        const [accessToken, refreshToken] = genTokens(student);
        student.set({ securityToken: refreshToken });
        await student.save();

        res.cookie('refresh', refreshToken, { 
            httpOnly: true, 
            maxAge: 24 * 60 * 60 * 1000,
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax',
            secure: process.env.NODE_ENV === 'production'
        });
        
        res.json({
            accessToken: accessToken,
            success: true,
            verified: true
        });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
};
```

### 2. **Refresh Token Controller** (`backend/controllers/refreshTokenController.js`)

```javascript
export const handleRefresh = async (req, res) => {
  const cookies = req.cookies;
  
  if (!cookies?.refresh) {
    return res.status(401).json({ error: "No refresh token" });
  }

  const refreshToken = cookies.refresh;
  let user;
  
  if (req.body.role === 'Student') {
    user = await Student.findOne({securityToken: refreshToken});
  } else {
    user = await Teacher.findOne({securityToken: refreshToken});
  }
  
  if (!user) {
    return res.status(403).json({ error: "Invalid refresh token" });
  }

  jwt.verify(refreshToken, process.env.REFRESH_TOKEN_SECRET, (err, decoded) => {
    if (err || user._id != decoded.id) {
      return res.sendStatus(403);
    }
    
    const accessToken = jwt.sign(
      {"id" : user._id},
      process.env.ACCESS_TOKEN_SECRET,
      {expiresIn: process.env.ACCESS_EXPIRES_IN}
    );
    
    res.json({ accessToken });
  });
}
```

### 3. **JWT Verification Middleware** (`backend/middleware/verifyJWT.js`)

```javascript
export const verifyJWT = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  
  if (!authHeader) {
    return res.sendStatus(401);
  }

  const token = authHeader.split(' ')[1];

  jwt.verify(token, process.env.ACCESS_TOKEN_SECRET, (err, decoded) => {
    if (err) {
      return res.sendStatus(403);
    }
    req.id = decoded.id;
    next();
  });
}
```

### 4. **Token Generation Service** (`backend/services/genTokenService.js`)

```javascript
export const genTokens = (user) => {
  const accessToken = jwt.sign(
    {"id": user._id},
    process.env.ACCESS_TOKEN_SECRET,
    { expiresIn: process.env.ACCESS_EXPIRES_IN }
  );
  
  const refreshToken = jwt.sign(
    {"id": user._id},
    process.env.REFRESH_TOKEN_SECRET,
    { expiresIn: process.env.REFRESH_EXPIRES_IN }
  );

  return [accessToken, refreshToken];
}
```

---

## 🔒 Security Features

### 1. **Token Security**
- **Access Tokens**: Short-lived (15 minutes), stored in memory
- **Refresh Tokens**: Long-lived (24 hours), stored in httpOnly cookies
- **Token Rotation**: New refresh token on each refresh request
- **Secure Cookies**: httpOnly, secure, sameSite attributes

### 2. **Password Security**
- **Bcrypt Hashing**: Passwords hashed with salt rounds
- **Password Validation**: Minimum length requirements
- **Account Lockout**: Protection against brute force attacks

### 3. **CORS Configuration**
```javascript
const corsOptions = {
    origin: process.env.NODE_ENV === 'development' 
        ? ['http://localhost:3000', 'http://localhost:8080']
        : ['https://aegisscholar.com', 'https://www.aegisscholar.com'],
    methods: 'GET,POST,PUT,DELETE',
    credentials: true,
    optionsSuccessStatus: 200
};

app.use(cors(corsOptions));
```

### 4. **Email Verification**
- **Account Activation**: Email verification required for new accounts
- **Verification Tokens**: Time-limited verification links
- **Resend Capability**: Users can request new verification emails

---

## 🌐 API Endpoints

### Authentication Endpoints

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/auth/loginStudent` | Student login | No |
| POST | `/api/auth/loginTeacher` | Teacher login | No |
| POST | `/api/auth/registerStudent` | Student registration | No |
| POST | `/api/auth/registerTeacher` | Teacher registration | No |
| POST | `/api/auth/google-auth` | Google OAuth login | No |
| GET | `/api/auth/verify-email` | Email verification | No |
| POST | `/api/auth/resend-verification-email` | Resend verification | No |

### Token Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/refresh/` | Refresh access token | Refresh Token |

### User Details

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/details/getDetailStudent` | Get student details | Yes |
| POST | `/api/details/getDetailTeacher` | Get teacher details | Yes |

### Password Management

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| POST | `/api/password/forgot-password` | Request password reset | No |
| POST | `/api/password/reset-password` | Reset password | No |

---

## ⚙️ Configuration

### Environment Variables

#### Backend (.env)
```bash
# JWT Configuration
ACCESS_TOKEN_SECRET=your_access_token_secret
REFRESH_TOKEN_SECRET=your_refresh_token_secret
ACCESS_EXPIRES_IN=15m
REFRESH_EXPIRES_IN=24h

# Database
MONGO_URI_DEV=mongodb://localhost:27017/aegisscholar_dev
MONGO_URI_PROD=mongodb://your_production_uri

# Google OAuth
GOOGLE_CLIENT_ID=your_google_client_id

# Email Service
EMAIL_SERVICE_API_KEY=your_email_service_key

# Environment
NODE_ENV=development|production
```

#### Frontend (.env)
```bash
# API Configuration
VITE_API_URL=http://localhost:8080
VITE_GOOGLE_CLIENT_ID=your_google_client_id

# Environment
NODE_ENV=development|production
```

### Cookie Configuration

```javascript
// Development
{
  httpOnly: true,
  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  sameSite: 'lax',
  secure: false
}

// Production
{
  httpOnly: true,
  maxAge: 24 * 60 * 60 * 1000, // 24 hours
  sameSite: 'none',
  secure: true
}
```

---

## 🐛 Troubleshooting

### Common Issues

#### 1. **"Getting logged out on refresh"**
**Symptoms:** User redirected to login after page refresh
**Causes:**
- Missing refresh token cookie
- CORS configuration issues
- Cookie sameSite/secure settings

**Solutions:**
```javascript
// Check browser cookies
document.cookie

// Verify CORS settings
// Ensure credentials: true in axios config
// Check cookie attributes match environment
```

#### 2. **"Token refresh fails"**
**Symptoms:** 403 errors on token refresh
**Causes:**
- Expired refresh token
- Token not found in database
- Invalid token signature

**Debug Steps:**
```javascript
// Backend logs
console.log('Refresh token from cookie:', refreshToken);
console.log('User found for token:', !!user);

// Frontend logs
console.log('Refresh response:', response.data);
```

#### 3. **"CORS errors"**
**Symptoms:** Network errors, blocked requests
**Solutions:**
- Verify origin in CORS configuration
- Ensure credentials: true
- Check preflight OPTIONS requests

#### 4. **"Email verification not working"**
**Symptoms:** Verification emails not sent/received
**Causes:**
- Email service configuration
- Invalid email templates
- Spam filters

---

## 📋 Best Practices

### 1. **Token Management**
- Keep access tokens short-lived (15 minutes)
- Store refresh tokens securely (httpOnly cookies)
- Implement token rotation
- Clear tokens on logout

### 2. **Error Handling**
- Provide clear error messages
- Log security events
- Implement rate limiting
- Handle network failures gracefully

### 3. **User Experience**
- Show loading states during authentication
- Persist user sessions across refreshes
- Provide clear feedback on auth failures
- Implement auto-logout on token expiry

### 4. **Security**
- Use HTTPS in production
- Implement CSRF protection
- Validate all inputs
- Monitor for suspicious activity

### 5. **Testing**
```javascript
// Test authentication flows
describe('Authentication', () => {
  test('should login successfully', async () => {
    // Test login flow
  });

  test('should refresh token automatically', async () => {
    // Test token refresh
  });

  test('should logout on invalid token', async () => {
    // Test error handling
  });
});
```

---

## 📊 Monitoring & Analytics

### Key Metrics to Track
- Login success/failure rates
- Token refresh frequency
- Session duration
- Authentication errors
- Password reset requests

### Logging Strategy
```javascript
// Authentication events
logger.info('User login attempt', { email, userType, ip });
logger.info('Token refresh successful', { userId, ip });
logger.warn('Invalid login attempt', { email, ip });
logger.error('Token verification failed', { token, error });
```

---

## 🔄 Future Enhancements

### Planned Features
1. **Multi-Factor Authentication (MFA)**
2. **Social Login Expansion** (Facebook, GitHub)
3. **Session Management Dashboard**
4. **Advanced Security Monitoring**
5. **Biometric Authentication**
6. **Single Sign-On (SSO)**

### Performance Optimizations
1. **Token Caching**: Implement Redis for token storage
2. **Connection Pooling**: Optimize database connections
3. **Rate Limiting**: Implement sophisticated rate limiting
4. **Monitoring**: Add comprehensive authentication metrics

---

## 📝 Implementation Checklist

### Backend Setup
- [ ] Configure JWT secrets
- [ ] Set up database models
- [ ] Implement authentication controllers
- [ ] Add JWT middleware
- [ ] Configure CORS properly
- [ ] Set up email service
- [ ] Add logging and monitoring

### Frontend Setup
- [ ] Set up user context
- [ ] Implement authentication hooks
- [ ] Add axios interceptors
- [ ] Create protected routes
- [ ] Handle loading states
- [ ] Add error boundaries
- [ ] Implement logout functionality

### Security Checklist
- [ ] Use HTTPS in production
- [ ] Configure secure cookies
- [ ] Implement CSRF protection
- [ ] Add rate limiting
- [ ] Validate all inputs
- [ ] Set up monitoring
- [ ] Regular security audits

### Testing
- [ ] Unit tests for auth functions
- [ ] Integration tests for auth flow
- [ ] E2E tests for user journeys
- [ ] Security penetration testing
- [ ] Performance testing
- [ ] Cross-browser testing

---

## 📞 Support & Maintenance

### Regular Maintenance Tasks
- Monitor authentication logs
- Update dependencies regularly
- Review security configurations
- Backup user data
- Performance optimization
- Security audits

### Emergency Procedures
- Token compromise response
- Database breach protocol
- Service outage handling
- User account recovery
- Security incident response

---

*Last Updated: December 2024*
*Version: 1.0*
*Maintainer: AegisScholar Development Team*
