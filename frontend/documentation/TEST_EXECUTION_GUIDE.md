# Test Execution Guide for AegisScholar Platform

## Overview
This guide provides comprehensive instructions for executing the complete test suite for the AegisScholar platform's test workflow functionality.

## Test Structure

### Frontend Tests
```
frontend/src/
├── pages/__tests__/
│   ├── ScheduleTestPage.test.tsx          # Test creation form tests
│   ├── ReviewTestPage.test.tsx            # Generic test review tests
│   ├── TestInstructionsPage.test.tsx     # Pre-test instructions tests
│   └── StudentTestPage.comprehensive.test.tsx # Test execution tests
├── components/__tests__/
│   └── ViewUpcomingTest.test.tsx          # Test display tests
└── __tests__/
    └── TestWorkflowIntegration.test.tsx   # End-to-end workflow tests
```

### Backend Tests
```
backend/controllers/__tests__/
├── submitTestController.test.js           # Test submission processing
└── testHistoryController.test.js         # Test CRUD operations
```

## Running Tests

### Prerequisites
1. Ensure all dependencies are installed:
   ```bash
   cd frontend && npm install
   cd backend && npm install
   ```

2. Set up test environment variables:
   ```bash
   # Frontend
   cp .env.example .env.test
   
   # Backend
   cp .env.example .env.test
   ```

### Frontend Tests

#### Run All Frontend Tests
```bash
cd frontend
npm run test
```

#### Run Specific Test Suites
```bash
# Test creation workflow
npm run test -- ScheduleTestPage.test.tsx

# Test review process
npm run test -- ReviewTestPage.test.tsx

# Student test experience
npm run test -- StudentTestPage.comprehensive.test.tsx

# Integration tests
npm run test -- TestWorkflowIntegration.test.tsx
```

#### Run Tests with Coverage
```bash
npm run test:coverage
```

#### Watch Mode for Development
```bash
npm run test:watch
```

### Backend Tests

#### Run All Backend Tests
```bash
cd backend
npm run test
```

#### Run Specific Test Files
```bash
# Test submission controller
npm run test -- submitTestController.test.js

# Test history controller
npm run test -- testHistoryController.test.js
```

#### Run Tests with Coverage
```bash
npm run test:coverage
```

## Test Categories and Coverage

### 1. Unit Tests
**Coverage Target: 90%+**

#### Frontend Components
- [x] ScheduleTestPage form validation
- [x] ReviewTestPage question management
- [x] TestInstructionsPage agreement flow
- [x] StudentTestPage test execution
- [x] ViewUpcomingTest display logic

#### Backend Controllers
- [x] submitTestController response processing
- [x] testHistoryController CRUD operations
- [x] Test status management
- [x] Error handling

### 2. Integration Tests
**Coverage Target: 85%+**

#### API Integration
- [x] Test creation API calls
- [x] Question recommendation API
- [x] Test submission API
- [x] Test retrieval API

#### Component Integration
- [x] Form submission to API
- [x] Navigation between components
- [x] State management across workflow
- [x] Error propagation

### 3. End-to-End Tests
**Coverage Target: 80%+**

#### Complete Workflows
- [x] Teacher test creation (all types)
- [x] Student test discovery
- [x] Test instructions and agreement
- [x] Test execution and submission
- [x] Error handling throughout workflow

## Critical Test Scenarios

### 1. Test Creation Scenarios
```bash
# Run these specific tests
npm run test -- --testNamePattern="Test Creation"
```

**Covered Scenarios:**
- ✅ Personalized test creation with AI recommendations
- ✅ Generic test creation through review process
- ✅ Diagnostic test creation with automatic questions
- ✅ Form validation for all required fields
- ✅ Date/time validation for scheduling
- ✅ Error handling for API failures

### 2. Test Status Management
```bash
# Run these specific tests
npm run test -- --testNamePattern="Test Status"
```

**Covered Scenarios:**
- ✅ Automatic test activation based on date/time
- ✅ Test visibility for eligible students
- ✅ Status transitions (draft → scheduled → active → completed)
- ✅ Cron job functionality simulation
- ✅ Edge cases for timezone handling

### 3. Student Test Experience
```bash
# Run these specific tests
npm run test -- --testNamePattern="Student Test"
```

**Covered Scenarios:**
- ✅ Test discovery in student dashboard
- ✅ Instructions page and agreement process
- ✅ Question navigation and answer selection
- ✅ Timer functionality and auto-submission
- ✅ LaTeX equation rendering
- ✅ Test submission and result processing

### 4. Error Handling
```bash
# Run these specific tests
npm run test -- --testNamePattern="Error"
```

**Covered Scenarios:**
- ✅ Network failures during test creation
- ✅ API timeouts during test loading
- ✅ Submission failures and retry logic
- ✅ Invalid test data handling
- ✅ Authentication/authorization errors

## Performance Testing

### Load Testing Setup
```bash
# Install k6 for load testing
npm install -g k6

# Run load tests
k6 run scripts/load-test-submission.js
```

### Performance Benchmarks
- **Test Creation**: < 2 seconds
- **Question Loading**: < 1 second
- **Test Submission**: < 3 seconds
- **Concurrent Users**: 100+ simultaneous test takers

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Test Workflow CI
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: |
          cd frontend && npm ci
          cd backend && npm ci
      - name: Run frontend tests
        run: cd frontend && npm run test:coverage
      - name: Run backend tests
        run: cd backend && npm run test:coverage
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

## Test Data Management

### Mock Data Setup
```bash
# Setup test database
npm run test:db:setup

# Seed test data
npm run test:db:seed

# Cleanup after tests
npm run test:db:cleanup
```

### Test Data Files
- `frontend/src/__mocks__/testData.ts` - Mock test objects
- `backend/test/fixtures/` - Database fixtures
- `backend/test/helpers/` - Test utilities

## Debugging Tests

### Frontend Test Debugging
```bash
# Run tests in debug mode
npm run test:debug

# Run specific test with verbose output
npm run test -- --verbose ScheduleTestPage.test.tsx
```

### Backend Test Debugging
```bash
# Run with debug output
DEBUG=* npm run test

# Run specific test with logging
npm run test -- --verbose submitTestController.test.js
```

## Test Maintenance

### Regular Maintenance Tasks
1. **Weekly**: Review test coverage reports
2. **Monthly**: Update test data and mocks
3. **Quarterly**: Performance benchmark review
4. **Release**: Full regression test suite

### Adding New Tests
1. Follow existing test patterns
2. Include both positive and negative test cases
3. Mock external dependencies appropriately
4. Update this documentation

## Success Criteria

### Test Coverage Requirements
- **Unit Tests**: 90%+ line coverage
- **Integration Tests**: 85%+ feature coverage
- **E2E Tests**: 80%+ user journey coverage

### Performance Requirements
- All tests complete in < 5 minutes
- No memory leaks in test execution
- Consistent test results across environments

### Quality Gates
- Zero critical bugs in test workflow
- All tests pass in CI/CD pipeline
- Performance benchmarks met
- Security tests pass

## Troubleshooting

### Common Issues
1. **Test timeouts**: Increase timeout values in test config
2. **Mock failures**: Verify mock data matches API responses
3. **Async issues**: Use proper async/await patterns
4. **State pollution**: Ensure proper test cleanup

### Getting Help
- Check test logs for detailed error messages
- Review existing test patterns for guidance
- Consult team documentation for specific scenarios
- Use debugging tools for complex issues
