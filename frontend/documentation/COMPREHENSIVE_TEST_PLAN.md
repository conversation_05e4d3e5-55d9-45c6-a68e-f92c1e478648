# Comprehensive Test Plan for Frontend

This document outlines the comprehensive testing strategy for the Aegis Scholar frontend application.

## Testing Coverage Goals

- **Unit Tests**: 80%+ coverage for utility functions, hooks, and context providers
- **Component Tests**: 70%+ coverage for all UI components
- **Integration Tests**: Key user flows should be tested
- **End-to-End Tests**: Critical user journeys (login, registration, course navigation, test-taking)

## Test File Organization

We maintain a consistent structure with tests located in `__tests__` directories at each module level:

```
src/
  __tests__/                # App-level tests
  components/
    __tests__/              # Component tests
  hooks/
    __tests__/              # Hook tests
  utils/
    __tests__/              # Utility function tests
  api/
    __tests__/              # API service tests
  contexts/
    __tests__/              # Context provider tests
  pages/
    __tests__/              # Page component tests
```

## Component Testing Priorities

### High Priority Components

These components form the core of our application and should have comprehensive test coverage:

- `CourseCard.tsx`
- `TaskList.tsx`
- `QuestionCard.tsx`
- `Sidebar.tsx`
- `AegisAiChatbot.tsx`
- `QuestionNavigator.tsx`
- `RenderLatexContent.tsx`

### Medium Priority Components

These components are important but less central to our application:

- `LoadingSpinner.tsx`
- `RecentActivity.tsx`
- `StudentStatsSection.tsx`
- `ActivityHeatmap.tsx`
- `Layout.tsx`

### Low Priority Components

These are simple or less frequently used components:

- `Background.tsx`
- `Logout.tsx`
- `DemoVideos.tsx`

## Page Component Testing Priorities

### High Priority Pages

These pages represent critical user journeys:

- `Login.tsx`
- `Register.tsx`
- `StudentDashboard.tsx`
- `TeacherDashboard.tsx`
- `StudentTestPage.tsx`
- `TestResults.tsx`

### Medium Priority Pages

These pages are important but less central:

- `ProfilePage.tsx`
- `ClassDetails.tsx`
- `ReviewTestPage.tsx`
- `Homepage.tsx`

## API Service Tests

All API service functions should be tested with mocked responses for:

1. Successful responses
2. Error responses (401, 403, 404, 500)
3. Network errors
4. Timeout errors

## Hook Testing Priorities

### High Priority Hooks

- `useQuizState.ts`
- `useAxiosPrivate.jsx`
- `useRefreshToken.jsx`

### Medium Priority Hooks

- `usePracticeTestSetupState.ts`
- `usePageRefresh.jsx`

## Context Testing Priorities

- `userContext.tsx` - Test all user-related functionality (login, logout, auth state)

## Testing Custom Utilities

All utility functions should have comprehensive test coverage:

- `cacheUtil.tsx`
- API utility functions 
- Test helper utilities

## Mocking Strategy

- **API Calls**: Mock `axios` responses
- **Auth State**: Mock localStorage/sessionStorage for auth tokens
- **Router**: Mock `react-router-dom` functions
- **Complex Components**: Mock sub-components when testing container components

## Test Environment Setup

Each test should set up its own isolated environment:

```tsx
beforeEach(() => {
  vi.clearAllMocks();
  
  // Mock relevant browser APIs
  Storage.prototype.getItem = vi.fn();
  Storage.prototype.setItem = vi.fn();
  
  // Reset DOM
  document.body.innerHTML = '';
});
```

## Testing User Interactions

For interactive components, test the following patterns:

1. Initial render state
2. State after user interaction
3. API calls triggered by interaction
4. Error states display
5. Loading states

## Recommended Test Cases by Component Type

### Form Components

- Valid input submission
- Input validation (required fields, formats)
- Error message display
- Form reset functionality
- Submission handling (success, error)

### Data Display Components

- Correct rendering with valid data
- Empty state handling
- Loading state display
- Error state display
- Data formatting

### Interactive Components

- Click/touch handlers
- Keyboard navigation
- Accessibility requirements
- State changes on interaction

## Running Tests

```bash
# Run all tests
npm test

# Run tests with coverage report
npm run test:coverage

# Run tests for a specific file
npm test -- ComponentName

# Run only tests that match a pattern
npm test -- -t "pattern"
```

## Continuous Integration

Tests should be automatically run on each pull request. The pipeline should:

1. Run linting
2. Run unit and integration tests
3. Generate and store coverage report
4. Fail if coverage drops below threshold

## Test Maintenance Guidelines

1. Update tests when changing component interfaces
2. Write tests before fixing bugs
3. Keep mock data in sync with API contracts
4. Review test coverage regularly
5. Avoid testing implementation details; focus on behavior

## Accessibility Testing

Include specific tests for:

- Keyboard navigation
- Screen reader compatibility
- Color contrast
- Focus management

## Performance Testing

For critical components, include tests for:

- Render performance
- Memory usage
- Re-render optimization

## Example Test File Structure

```tsx
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '../../utils/test-utils';
import Component from '../Component';

describe('Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders successfully with default props', () => {
    render(<Component />);
    // assertions
  });

  it('handles user interaction correctly', () => {
    render(<Component />);
    fireEvent.click(screen.getByRole('button'));
    // assertions about state change
  });

  it('displays error states appropriately', () => {
    render(<Component hasError={true} errorMessage="Test error" />);
    // assertions about error display
  });
});
```

## Action Items for Testing Implementation

1. Complete test coverage for high-priority components
2. Implement integration tests for critical user flows
3. Set up testing threshold enforcement in CI pipeline
4. Document component-specific test cases for complex components
5. Create reusable test utilities for common patterns 