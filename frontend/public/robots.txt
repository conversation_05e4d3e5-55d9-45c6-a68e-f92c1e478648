# AegisScholar - AI-Native Teaching Assistant Platform
# Robots.txt for optimal SEO and security
# https://www.robotstxt.org/robotstxt.html

# =============================================================================
# SEARCH ENGINE CRAWLERS - ALLOW ALL MAJOR BOTS
# =============================================================================

# Google Search Bot
User-agent: Googlebot
Allow: /
Allow: /privacy-policy
Allow: /login
Allow: /register
Disallow: /student-dashboard*
Disallow: /teacher-dashboard*
Disallow: /platform-admin*
Disallow: /profile*
Disallow: /test*
Disallow: /practice-setup*
Disallow: /schedule-test*
Disallow: /classDetails/*
Disallow: /reviewTest*
Disallow: /analytics*
Disallow: /subjectDetails/*
Disallow: /test-results*
Disallow: /aegis-grader*
Disallow: /gradingDetails/*
Disallow: /aegis-ai*
Disallow: /verify-email*
Disallow: /reset-password*
Disallow: /api/*
Crawl-delay: 1

# Bing Search Bot
User-agent: Bingbot
Allow: /
Allow: /privacy-policy
Allow: /login
Allow: /register
Disallow: /student-dashboard*
Disallow: /teacher-dashboard*
Disallow: /platform-admin*
Disallow: /profile*
Disallow: /test*
Disallow: /practice-setup*
Disallow: /schedule-test*
Disallow: /classDetails/*
Disallow: /reviewTest*
Disallow: /analytics*
Disallow: /subjectDetails/*
Disallow: /test-results*
Disallow: /aegis-grader*
Disallow: /gradingDetails/*
Disallow: /aegis-ai*
Disallow: /verify-email*
Disallow: /reset-password*
Disallow: /api/*
Crawl-delay: 1

# Yahoo Search Bot
User-agent: Slurp
Allow: /
Allow: /privacy-policy
Allow: /login
Allow: /register
Disallow: /student-dashboard*
Disallow: /teacher-dashboard*
Disallow: /platform-admin*
Disallow: /profile*
Disallow: /test*
Disallow: /practice-setup*
Disallow: /schedule-test*
Disallow: /classDetails/*
Disallow: /reviewTest*
Disallow: /analytics*
Disallow: /subjectDetails/*
Disallow: /test-results*
Disallow: /aegis-grader*
Disallow: /gradingDetails/*
Disallow: /aegis-ai*
Disallow: /verify-email*
Disallow: /reset-password*
Disallow: /api/*
Crawl-delay: 2

# DuckDuckGo Search Bot
User-agent: DuckDuckBot
Allow: /
Allow: /privacy-policy
Allow: /login
Allow: /register
Disallow: /student-dashboard*
Disallow: /teacher-dashboard*
Disallow: /platform-admin*
Disallow: /profile*
Disallow: /test*
Disallow: /practice-setup*
Disallow: /schedule-test*
Disallow: /classDetails/*
Disallow: /reviewTest*
Disallow: /analytics*
Disallow: /subjectDetails/*
Disallow: /test-results*
Disallow: /aegis-grader*
Disallow: /gradingDetails/*
Disallow: /aegis-ai*
Disallow: /verify-email*
Disallow: /reset-password*
Disallow: /api/*
Crawl-delay: 1

# =============================================================================
# SOCIAL MEDIA CRAWLERS - ALLOW FOR SOCIAL SHARING
# =============================================================================

# Facebook/Meta Crawler
User-agent: facebookexternalhit
Allow: /
Allow: /privacy-policy
Disallow: /student-dashboard*
Disallow: /teacher-dashboard*
Disallow: /platform-admin*
Disallow: /profile*
Disallow: /test*
Disallow: /api/*

# Twitter/X Crawler
User-agent: Twitterbot
Allow: /
Allow: /privacy-policy
Disallow: /student-dashboard*
Disallow: /teacher-dashboard*
Disallow: /platform-admin*
Disallow: /profile*
Disallow: /test*
Disallow: /api/*

# LinkedIn Crawler
User-agent: LinkedInBot
Allow: /
Allow: /privacy-policy
Disallow: /student-dashboard*
Disallow: /teacher-dashboard*
Disallow: /platform-admin*
Disallow: /profile*
Disallow: /test*
Disallow: /api/*

# =============================================================================
# AGGRESSIVE/UNWANTED BOTS - BLOCK COMPLETELY
# =============================================================================

# Block aggressive crawlers and scrapers
User-agent: AhrefsBot
Disallow: /

User-agent: SemrushBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: DotBot
Disallow: /

User-agent: BLEXBot
Disallow: /

User-agent: YandexBot
Disallow: /

User-agent: SeznamBot
Disallow: /

User-agent: CCBot
Disallow: /

User-agent: ChatGPT-User
Disallow: /

User-agent: GPTBot
Disallow: /

User-agent: Google-Extended
Disallow: /

User-agent: Claude-Web
Disallow: /

User-agent: anthropic-ai
Disallow: /

User-agent: PerplexityBot
Disallow: /

# =============================================================================
# DEFAULT RULES FOR ALL OTHER BOTS
# =============================================================================

User-agent: *
# Allow public pages
Allow: /
Allow: /privacy-policy
Allow: /login
Allow: /register

# Allow static assets for proper rendering
Allow: /static/
Allow: /assets/
Allow: /logo*.png
Allow: /logo*.ico
Allow: /manifest.json
Allow: /favicon.ico
Allow: /*.css
Allow: /*.js
Allow: /*.png
Allow: /*.jpg
Allow: /*.jpeg
Allow: /*.gif
Allow: /*.svg
Allow: /*.webp
Allow: /*.ico
Allow: /subjectIcons/

# Block all private/authenticated areas
Disallow: /student-dashboard*
Disallow: /teacher-dashboard*
Disallow: /platform-admin*
Disallow: /profile*
Disallow: /test*
Disallow: /practice-setup*
Disallow: /schedule-test*
Disallow: /classDetails/*
Disallow: /reviewTest*
Disallow: /analytics*
Disallow: /subjectDetails/*
Disallow: /test-results*
Disallow: /aegis-grader*
Disallow: /gradingDetails/*
Disallow: /aegis-ai*

# Block authentication and security-related pages
Disallow: /verify-email*
Disallow: /reset-password*

# Block all API endpoints
Disallow: /api/

# Block temporary/development files
Disallow: /tmp/
Disallow: /temp/
Disallow: /_next/
Disallow: /node_modules/
Disallow: /.git/
Disallow: /.env*
Disallow: /package*.json
Disallow: /tsconfig*.json
Disallow: /vite.config.*
Disallow: /webpack.config.*

# Block common admin/config paths (security)
Disallow: /admin/
Disallow: /config/
Disallow: /wp-admin/
Disallow: /wp-content/
Disallow: /wp-includes/
Disallow: /phpmyadmin/
Disallow: /cpanel/
Disallow: /.well-known/

# Set crawl delay to be respectful
Crawl-delay: 1

# =============================================================================
# SITEMAP LOCATION
# =============================================================================

# Sitemap location - Update with your actual domain when deploying
Sitemap: https://aegisscholar.com/sitemap.xml

# For development/testing (uncomment if needed):
# Sitemap: http://localhost:3000/sitemap.xml
