import { StudentAnalytics } from './student';

export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  admissionNumber?: string;
  profileImage?: string;
  subjects?: Subject[];
  learningPreferences?: {
    preferredLearningStyle: string;
    preferredTimeOfDay: string;
    preferredDuration: number;
    preferredEnvironment: string;
  };
  learningGoals?: {
    shortTerm: string[];
    longTerm: string[];
    targetScore: number;
  };
  timeAvailability?: {
    weekdays: {
      morning: boolean;
      afternoon: boolean;
      evening: boolean;
    };
    weekends: {
      morning: boolean;
      afternoon: boolean;
      evening: boolean;
    };
    preferredStudyDuration: number;
  };
}

export interface Subject {
  subjectName: string;
  subjectClassId: string;
  analytics?: StudentAnalytics;
}