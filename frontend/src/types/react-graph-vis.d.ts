declare module "react-graph-vis" {
    import { Component } from "react";
  
    export interface GraphNode {
      id: number | string;
      label: string;
      title?: string;
    }
  
    export interface GraphEdge {
      from: number | string;
      to: number | string;
    }
  
    export interface GraphData {
      nodes: GraphNode[];
      edges: GraphEdge[];
    }
  
    export interface GraphOptions {
      layout?: {
        hierarchical?: boolean;
      };
      edges?: {
        color?: string;
      };
      height?: string;
      width?: string;
    }
  
    export interface GraphEvents {
      select?: (event: { nodes: number[]; edges: number[] }) => void;
    }
  
    export interface GraphProps {
      graph: GraphData;
      options?: GraphOptions;
      events?: GraphEvents;
      getNetwork?: (network: any) => void; // Vis.js network instance
    }
  
    export default class Graph extends Component<GraphProps> {}
  }
  