export interface Response {
    questionId: string;
    selectedAnswer: number | null;
    isCorrect: boolean;
    intuition?: string;
    timeSpent: number;
}

export interface AttemptedTest {
    testId: string;
    startTime?: Date;
    endTime?: Date;
    totalTimeTaken?: number;
    responses: Response[];
    totalScore: number;
    proficiencyBefore?: number;
    proficiencyAfter?: number;
    metadata?: {
        device?: string;
        browser?: string;
        ipAddress?: string;
    };
    flaggedForReview?: boolean;
}

export interface StudentAnalytics {
    performanceMetrics: {
        averageTimePerQuestion: number;
        averageScore: number;
        currentProficiency: number;
        improvementRate: number;
        totalTestsTaken: number;
        completionRate: number;
    };
    
    progressTracking: {
        dailyActivity: Array<{
            date: Date;
            minutesSpent: number;
            questionsAnswered: number;
            consistencyScore: number;
            averageSessionLength: number;
        }>;
    };
    
    knowledgeAnalysis: {
        strengths: Array<{
            subtopicId: string;
            proficiency: number;
            confidenceScore: number;
            lastAssessed: Date;
        }>;
        weaknesses: Array<{
            subtopicId: string;
            proficiency: number;
            recommendedPractice: string;
            lastAssessed: Date;
        }>;
    };
    
    learningBehavior: {
        errorPatterns: Array<{
            patternType: string;
            frequency: number;
            commonMistakes: string[];
        }>;
        learningStyle: {
            speedVsAccuracy: number;
            reflectionTime: number;
            retryFrequency: number;
        };
    };
    
    personalRecommendations: {
        practiceSuggestions: Array<{
            questionType: string;
            targetSkill: string;
            difficulty: number;
        }>;
        estimatedMasteryTimeline: Array<{
            topicId: string;
            estimatedDays: number;
        }>;
    };
    
    lastUpdated: Date;
}

export interface Subject {
    subjectClassId: string;
    subjectName: string;
    overallProficiency: number;
    knowledgeGraphId: string;
    testHistory: string[];
    attemptedTests: AttemptedTest[];
    analytics: StudentAnalytics;
}

export interface Task {
    _id?: string;
    title: string;
    due: Date;
    completed: boolean;
    priority: 'low' | 'medium' | 'high';
}

export interface Student {
    _id: string;
    username: string;
    firstName: string;
    lastName: string;
    email: string;
    password?: string;
    isEmailVerified?: boolean;
    registeredAt?: Date;
    subjects: Subject[];
    role: string;
    securityToken?: string;
    schoolCode: string;
    tasks: Task[];
    classes: string[];
}