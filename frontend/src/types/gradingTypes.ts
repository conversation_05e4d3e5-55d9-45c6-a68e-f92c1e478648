import { Status } from './aegisGrader';

export interface GradingResult {
    questionNumber: number;
    maxMarks: number;
    marksAwarded: number;
    feedback: string;
}

export interface AnswerSheetResult {
    id: string;
    studentName: string;
    rollNumber: string;
    totalMarks: number;
    maxMarks: number;
    percentage: number;
    results: GradingResult[];
    detailedBreakdown: any;
    pdfUrl?: string;
}

export interface AnswerSheetData {
    id: string;
    studentName: string;
    rollNumber: string;
    pdfUrl?: string;
    evaluationResult?: any;
    status?: 'pending' | 'processing' | 'completed' | 'error';
    processedAt?: Date;
}

export interface SubmissionData {
    id: string;
    status: Status | string;
    testDetails: {
        subject: string;
        className: string;
        date: string;
    };
    questionPaper?: { pdfUrl?: string };
    rubric?: { pdfUrl?: string };
    gradingProgress?: number;
    answerSheets: AnswerSheetData[];
}

export interface ClassStats {
    average: number;
    highest: number;
    lowest: number;
    totalStudents: number;
    totalSubmissions: number;
}
