import { ChevronRight, History, Clock, Trophy, <PERSON><PERSON><PERSON><PERSON>, AlertCircle } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useUser } from '../contexts/userContext';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { useNavigate } from 'react-router-dom';
import { getCache, setCache } from '../utils/cacheUtil';
import { AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline';

interface Response {
  questionId: string;
  selectedAnswer: number | null;
  intuition: string;
  timeSpent?: number;
  isCorrect?: boolean;
}

interface AttemptedTest {
  testId: string;
  startTime?: Date;
  endTime?: Date;
  totalTimeTaken: number;
  score?: number;
  totalScore?: number;
  responses: Response[];
  proficiencyBefore?: number;
  proficiencyAfter?: number;
  metadata?: {
    device: string;
    browser: string;
    ipAddress: string;
  };
  flaggedForReview?: boolean;
}

interface TestDetails {
  id: string;
  subject: string;
  topic: string; // Concatenated topics from the Topics array.
  date: Date;
  duration: number;
  numOfQuestions: number;
  instructions: string;
  totalMarks: number;
  startTime?: string;
}

const RecentActivity = () => {
  const { user } = useUser();
  const axiosPrivate = useAxiosPrivate();
  const [attemptedTests, setAttemptedTests] = useState<AttemptedTest[]>([]);
  const [testDetailsMap, setTestDetailsMap] = useState<Record<string, TestDetails>>({});
  const [loading, setLoading] = useState(false);
  const [sortOption, setSortOption] = useState<'date-latest' | 'date-oldest' | 'subject-az' | 'subject-za' | 'score-high' | 'score-low'>('date-latest');
  const [showSortMenu, setShowSortMenu] = useState(false);
  const navigate = useNavigate();

  // Format each test data from the user's testHistory.
  const formatTestData = (test: any): AttemptedTest => ({
    testId: test._id,
    startTime: new Date(test.testDate),
    endTime: new Date(test.testDate),
    totalTimeTaken: test.duration,
    responses: [],
    totalScore: test.totalScore,
    metadata: {
      device: 'Chrome',
      browser: 'Chrome',
      ipAddress: '',
    },
    flaggedForReview: false,
  });

  // New bulk function to fetch multiple test details efficiently
  const getBulkTestDetails = async (testIds: string[]) => {
    try {
      if (testIds.length === 0) return {};

      // Use cache key for bulk requests
      const cacheKey = `/api/test/bulk-${testIds.sort().join(',')}`;
      const cachedData = getCache(cacheKey);
      if (cachedData) {
        console.log('Using cached bulk test details');
        return cachedData;
      }

      console.log('Fetching bulk test details for', testIds.length, 'tests');
      const response = await axiosPrivate.post('/api/test/bulk', { testIds });
      if (response.data && response.data.tests) {
        // Cache for 10 minutes
        setCache(cacheKey, response.data.tests, 600000);
        console.log('Successfully fetched and cached bulk test details');
        return response.data.tests;
      }
      console.log('Failed to fetch bulk test details');
      return {};
    } catch (error) {
      console.error('Failed to fetch bulk test details, this may be expected if the bulk endpoint is not available:', error);
      // Return empty object to trigger fallback logic if needed
      throw error;
    }
  };

  // Build attemptedTests from user subjects - optimized version
  useEffect(() => {
    if (user?.subjects) {
      // Create a Set of attempted test IDs for faster lookup
      const attemptedTestIds = new Set();
      user.subjects.forEach(subject => {
        subject.attemptedTests?.forEach(attempt => {
          attemptedTestIds.add(attempt.testId);
        });
      });

      // Get unique tests from testHistory that were actually attempted
      const uniqueTests = new Map();
      user.subjects.forEach(subject => {
        subject.testHistory?.forEach((test: any) => {
          if (attemptedTestIds.has(test._id) && !uniqueTests.has(test._id)) {
            uniqueTests.set(test._id, test);
          }
        });
      });

      const tests = Array.from(uniqueTests.values())
        .map(formatTestData)
        .sort((a, b) => (b.startTime!.getTime() || 0) - (a.startTime!.getTime() || 0));

      setAttemptedTests(tests);
    }
  }, [user?.subjects]);

  // Fetch test details using the new bulk endpoint for maximum efficiency
  useEffect(() => {
    const fetchTestDetails = async () => {
      if (attemptedTests.length === 0) return;

      setLoading(true);

      try {
        // Extract all test IDs
        const testIds = attemptedTests.map(test => test.testId);

        // Fetch all test details in a single bulk request
        const bulkTestData = await getBulkTestDetails(testIds);

        // Transform the data to match our TestDetails interface
        const detailsMap: Record<string, TestDetails> = {};

        Object.entries(bulkTestData).forEach(([testId, testData]: [string, any]) => {
          if (testData && testData._id && testData.subject && testData.topics) {
            detailsMap[testId] = {
              id: testData._id,
              subject: testData.subject,
              topic: Array.isArray(testData.topics)
                ? testData.topics.join(', ')
                : testData.topics,
              date: new Date(testData.testDate),
              duration: testData.duration,
              numOfQuestions: testData.numberOfQuestions,
              instructions: testData.testInstructions,
              totalMarks: testData.totalMarks,
              startTime: testData.startTime,
            };
          }
        });

        setTestDetailsMap(detailsMap);
      } catch (error) {
        console.error('Failed to fetch test details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTestDetails();
  }, [attemptedTests]);

  // Helper function to format date and time in IST.
  const formatDateTimeIST = (date: Date, startTime?: string): string => {
    const testDate = new Date(date);
    if (startTime) {
      const [hours, minutes] = startTime.split(':');
      testDate.setHours(parseInt(hours), parseInt(minutes));
    }
    const options: Intl.DateTimeFormatOptions = {
      timeZone: 'Asia/Kolkata',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    };
    return new Intl.DateTimeFormat('en-US', options).format(testDate);
  };

  const sortTests = (tests: AttemptedTest[]) => {
    return [...tests].sort((a, b) => {
      const detailsA = testDetailsMap[a.testId];
      const detailsB = testDetailsMap[b.testId];
      switch (sortOption) {
        case 'date-oldest':
          return (a.startTime?.getTime() || 0) - (b.startTime?.getTime() || 0);
        case 'subject-az':
          return (detailsA?.subject || '').localeCompare(detailsB?.subject || '');
        case 'subject-za':
          return (detailsB?.subject || '').localeCompare(detailsA?.subject || '');
        case 'score-high':
          return (b.score || 0) - (a.score || 0);
        case 'score-low':
          return (a.score || 0) - (b.score || 0);
        case 'date-latest':
        default:
          return (b.startTime?.getTime() || 0) - (a.startTime?.getTime() || 0);
      }
    });
  };

  const sortOptions = [
    { value: 'date-latest', label: 'Date (Latest First)' },
    { value: 'date-oldest', label: 'Date (Oldest First)' },
    { value: 'subject-az', label: 'Subject (A-Z)' },
    { value: 'subject-za', label: 'Subject (Z-A)' },
    // { value: 'score-high', label: 'Score (High to Low)' },
    // { value: 'score-low', label: 'Score (Low to High)' },
  ];

  // Close sort menu on outside click
  useEffect(() => {
    if (!showSortMenu) return;
    const handleClick = (e: MouseEvent) => {
      const menu = document.getElementById('recent-activity-sort-menu');
      if (menu && !menu.contains(e.target as Node)) {
        setShowSortMenu(false);
      }
    };
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [showSortMenu]);

  return (
    <div className="flex flex-col h-full">
      <div className="p-2 border-b border-border mb-2 flex justify-between items-center shrink-0">
        <div className="flex items-center gap-2">
          <History className="h-4 w-4 text-accent" />
          <h2 className="text-lg font-['Space_Grotesk'] font-bold text-primary">Recent Activities</h2>
        </div>
        <div className="relative">
          <button
            className="flex items-center border border-border rounded-md p-2 bg-background text-foreground focus:outline-none focus:ring-1 focus:ring-accent transition-all duration-150 hover:border-accent"
            onClick={() => setShowSortMenu((prev) => !prev)}
            title="Filter/Sort"
            type="button"
          >
            <AdjustmentsHorizontalIcon className='h-4 w-4' />
          </button>
          {showSortMenu && (
            <div id="recent-activity-sort-menu" className="absolute right-0 mt-2 w-48 bg-card border border-border rounded shadow-lg z-50">
              {sortOptions.map(option => (
                <button
                  key={option.value}
                  className={`w-full text-left px-4 py-2 text-xs hover:bg-accent/10 ${sortOption === option.value ? 'font-bold text-accent' : ''}`}
                  onClick={() => {
                    setSortOption(option.value as typeof sortOption);
                    setShowSortMenu(false);
                  }}
                >
                  {option.label}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="flex flex-col gap-4 px-2 overflow-auto max-h-[300px] lg:max-h-full scrollbar-thin scrollbar-thumb-muted scrollbar-track-muted/50">
        {attemptedTests.length > 0 ? (
          sortTests(attemptedTests).map((test) => {
            const details = testDetailsMap[test.testId];
            const isLoading = loading && !details;

            return (
              <div
                key={test.testId}
                className={`bg-card hover:bg-accent/5 rounded-lg border border-border hover:shadow-sm transition-all duration-200 group relative ${isLoading ? 'animate-pulse' : ''
                  }`}
              >
                <div className="p-4">
                  <div className="border-b border-border mb-3 pb-2">
                    <div className="flex justify-between items-center">
                      <div className="text-md font-['Space_Grotesk'] font-semibold text-primary">
                        {details ? details.subject : (
                          <div className="h-5 bg-muted-foreground/30 rounded w-24 animate-pulse"></div>
                        )}
                      </div>
                      <button
                        onClick={() =>
                          navigate('/test-results', {
                            state: { studentId: user?.id, testId: test.testId },
                          })
                        }
                        className="flex items-center text-xs text-accent hover:text-accent/80"
                        disabled={!details}
                      >
                        View Results
                        <ChevronRight className="h-3 w-3 ml-1" />
                      </button>
                    </div>
                    <div className="text-sm text-foreground/70">
                      {details ? details.topic : (
                        <div className="h-4 bg-muted-foreground/30 rounded w-32 animate-pulse"></div>
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    {details && details.date ? (
                      <span className="text-xs text-muted-foreground flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {formatDateTimeIST(new Date(details.date), details.startTime)}
                      </span>
                    ) : (
                      <div className="h-3 bg-muted-foreground/30 rounded w-20 animate-pulse"></div>
                    )}
                  </div>
                </div>
              </div>
            );
          })
        ) : loading ? (
          // Show loading skeleton when initially loading
          Array.from({ length: 3 }).map((_, index) => (
            <div
              key={`skeleton-${index}`}
              className="bg-card rounded-lg border border-border animate-pulse"
            >
              <div className="p-4">
                <div className="border-b border-border mb-3 pb-2">
                  <div className="flex justify-between items-center">
                    <div className="h-5 bg-muted-foreground/30 rounded w-24"></div>
                    <div className="h-4 bg-muted-foreground/30 rounded w-16"></div>
                  </div>
                  <div className="h-4 bg-muted-foreground/30 rounded w-32 mt-2"></div>
                </div>
                <div className="h-3 bg-muted-foreground/30 rounded w-20"></div>
              </div>
            </div>
          ))
        ) : (
          <div className="flex flex-col items-center justify-center h-full py-8 px-4 text-center">
            <div className="bg-accent/10 rounded-full p-4 mb-4">
              <AlertCircle className="h-5 w-5 text-accent" />
            </div>
            <h3 className="text-lg font-['Space_Grotesk'] font-medium text-primary mb-2">No Recent Activity</h3>
            <p className="text-sm text-foreground/70 max-w-xs">
              Complete your first test to see your activity history here.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default RecentActivity;