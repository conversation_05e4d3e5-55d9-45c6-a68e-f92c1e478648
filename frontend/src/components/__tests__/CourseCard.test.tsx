import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '../../utils/test-utils';
import CourseCard from '../CourseCard';

describe('CourseCard', () => {
  const mockProps = {
    title: 'Test Course',
    category: 'IT & Software',
    students: 1000,
    rating: 4.5,
    color: 'bg-secondary'
  };

  it('renders the course title', () => {
    render(<CourseCard {...mockProps} />);
    expect(screen.getByText('Test Course')).toBeInTheDocument();
  });

  it('renders the category with appropriate icon', () => {
    render(<CourseCard {...mockProps} />);
    const categoryElement = screen.getByText(/IT & Software/);
    expect(categoryElement).toBeInTheDocument();
    expect(categoryElement.textContent).toContain('💻');
  });

  it('renders the number of students', () => {
    render(<CourseCard {...mockProps} />);
    expect(screen.getByText('1,000 students')).toBeInTheDocument();
  });

  it('renders the rating', () => {
    render(<CourseCard {...mockProps} />);
    expect(screen.getByText('4.5')).toBeInTheDocument();
  });

  it('applies the correct background color', () => {
    render(<CourseCard {...mockProps} />);
    const card = screen.getByText('Test Course').closest('div[class*="bg-secondary"]');
    expect(card).toHaveClass('bg-secondary');
  });

  it('renders different category icons based on category', () => {
    // Business category
    render(<CourseCard {...mockProps} category="Business" />);
    expect(screen.getByText(/Business/).textContent).toContain('💼');
    
    // Re-render with Media Training
    render(<CourseCard {...mockProps} category="Media Training" />);
    expect(screen.getByText(/Media Training/).textContent).toContain('🎥');
    
    // Re-render with Interior
    render(<CourseCard {...mockProps} category="Interior" />);
    expect(screen.getByText(/Interior/).textContent).toContain('🏠');
  });
}); 