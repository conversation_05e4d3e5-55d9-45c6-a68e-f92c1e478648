import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '../../utils/test-utils';
import LoadingSpinner from '../LoadingSpinner';

// Mock the logo component since we're only testing if LoadingSpinner renders
vi.mock('../../assets/AegisScholarLogoIcon', () => ({
  default: () => <div data-testid="mock-logo" />
}));

describe('LoadingSpinner', () => {
  it('renders without crashing', () => {
    render(<LoadingSpinner />);
    expect(screen.getByTestId('mock-logo')).toBeInTheDocument();
  });

  it('renders with proper styling', () => {
    render(<LoadingSpinner />);
    const container = screen.getByTestId('mock-logo').parentElement;
    expect(container).toHaveClass('flex');
    expect(container).toHaveClass('items-center');
    expect(container).toHaveClass('justify-center');
    expect(container).toHaveClass('h-screen');
  });
}); 