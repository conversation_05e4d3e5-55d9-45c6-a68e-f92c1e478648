import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '../../utils/test-utils';
import TeacherTestHistory from '../TeacherTestHistory';

// Mock the hooks and utilities
vi.mock('../../contexts/userContext', () => ({
  useUser: vi.fn(),
}));

vi.mock('../../hooks/useAxiosPrivate', () => ({
  useAxiosPrivate: vi.fn(),
}));

vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

vi.mock('../../utils/cacheUtil', () => ({
  fetchWithCache: vi.fn(),
}));

describe('TeacherTestHistory', () => {
  const mockUser = {
    id: 'teacher1',
    role: 'Teacher',
    testHistory: [
      {
        _id: 'test1',
        subject: 'Mathematics',
        topics: ['Algebra', 'Geometry'],
        testDate: new Date('2024-01-15'),
        startTime: '10:00',
        duration: 60,
        numberOfQuestions: 20,
        totalMarks: 100,
        testType: 'regular',
        class: {
          className: 'Class 10-A',
          classStd: '10',
          students: ['student1', 'student2']
        }
      }
    ]
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component with correct title', () => {
    const { useUser } = require('../../contexts/userContext');
    const { useAxiosPrivate } = require('../../hooks/useAxiosPrivate');
    const { useNavigate } = require('react-router-dom');

    useUser.mockReturnValue({ user: mockUser });
    useAxiosPrivate.mockReturnValue({});
    useNavigate.mockReturnValue(vi.fn());

    render(<TeacherTestHistory />);

    expect(screen.getByText('Test History')).toBeInTheDocument();
  });

  it('shows loading state initially', () => {
    const { useUser } = require('../../contexts/userContext');
    const { useAxiosPrivate } = require('../../hooks/useAxiosPrivate');
    const { useNavigate } = require('react-router-dom');

    useUser.mockReturnValue({ user: mockUser });
    useAxiosPrivate.mockReturnValue({});
    useNavigate.mockReturnValue(vi.fn());

    render(<TeacherTestHistory />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('shows empty state when no completed tests', () => {
    const { useUser } = require('../../contexts/userContext');
    const { useAxiosPrivate } = require('../../hooks/useAxiosPrivate');
    const { useNavigate } = require('react-router-dom');

    useUser.mockReturnValue({ user: { ...mockUser, testHistory: [] } });
    useAxiosPrivate.mockReturnValue({});
    useNavigate.mockReturnValue(vi.fn());

    render(<TeacherTestHistory />);

    // Wait for loading to complete
    setTimeout(() => {
      expect(screen.getByText('No Completed Tests')).toBeInTheDocument();
    }, 100);
  });
});
