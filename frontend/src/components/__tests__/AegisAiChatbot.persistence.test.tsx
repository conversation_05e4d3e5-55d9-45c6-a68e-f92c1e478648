import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../utils/test-utils';
import AegisAiChatbot from '../AegisAiChatbot';

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock the axios hook
vi.mock('../../hooks/useAxiosPrivate', () => ({
  useAxiosPrivate: () => ({
    post: vi.fn().mockResolvedValue({ 
      data: { 
        message: 'This is a test response from the AI assistant.' 
      } 
    })
  })
}));

// Mock the user context
vi.mock('../../contexts/userContext', () => ({
  useUser: () => ({
    user: {
      id: 'test-user-id',
      username: 'Test User',
      email: '<EMAIL>'
    }
  })
}));

describe('AegisAiChatbot - Chat Persistence', () => {
  const mockProps = {
    subject: 'Mathematics',
    studentId: 'test-student-id',
    availableSubjects: [
      { subjectName: 'Mathematics' },
      { subjectName: 'Physics' }
    ],
    onSubjectChange: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    localStorageMock.clear();
  });

  it('saves messages to localStorage when messages change', async () => {
    render(<AegisAiChatbot {...mockProps} />);
    
    const input = screen.getByRole('textbox');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    fireEvent.change(input, { target: { value: 'Test message' } });
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        'aegis_chat_test-student-id_Mathematics',
        expect.stringContaining('Test message')
      );
    });
  });

  it('loads messages from localStorage on mount', () => {
    const storedMessages = JSON.stringify([
      {
        id: 'test-1',
        type: 'user',
        content: 'Previous message',
        timestamp: new Date().toISOString()
      },
      {
        id: 'test-2',
        type: 'assistant',
        content: 'Previous response',
        timestamp: new Date().toISOString()
      }
    ]);

    localStorageMock.getItem.mockReturnValue(storedMessages);

    render(<AegisAiChatbot {...mockProps} />);
    
    expect(screen.getByText('Previous message')).toBeInTheDocument();
    expect(screen.getByText('Previous response')).toBeInTheDocument();
  });

  it('shows message count in header when messages exist', () => {
    const storedMessages = JSON.stringify([
      {
        id: 'welcome',
        type: 'assistant',
        content: 'Welcome message',
        timestamp: new Date().toISOString()
      },
      {
        id: 'test-1',
        type: 'user',
        content: 'User message',
        timestamp: new Date().toISOString()
      }
    ]);

    localStorageMock.getItem.mockReturnValue(storedMessages);

    render(<AegisAiChatbot {...mockProps} />);
    
    expect(screen.getByText('1 messages')).toBeInTheDocument();
  });

  it('clears chat history when clear button is clicked', async () => {
    const storedMessages = JSON.stringify([
      {
        id: 'test-1',
        type: 'user',
        content: 'Message to be cleared',
        timestamp: new Date().toISOString()
      }
    ]);

    localStorageMock.getItem.mockReturnValue(storedMessages);

    // Mock window.confirm
    window.confirm = vi.fn().mockReturnValue(true);

    render(<AegisAiChatbot {...mockProps} />);
    
    const clearButton = screen.getByText('Clear Chat');
    fireEvent.click(clearButton);
    
    expect(localStorageMock.removeItem).toHaveBeenCalledWith(
      'aegis_chat_test-student-id_Mathematics'
    );
  });

  it('generates correct storage key for different subjects', () => {
    const mathProps = { ...mockProps, subject: 'Mathematics' };
    const physicsProps = { ...mockProps, subject: 'Physics' };

    const { rerender } = render(<AegisAiChatbot {...mathProps} />);
    expect(localStorageMock.getItem).toHaveBeenCalledWith('aegis_chat_test-student-id_Mathematics');

    rerender(<AegisAiChatbot {...physicsProps} />);
    expect(localStorageMock.getItem).toHaveBeenCalledWith('aegis_chat_test-student-id_Physics');
  });
});
