import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '../../utils/test-utils';
import TaskList from '../TaskList';
import React from 'react';

// Mock data for tests
const mockTasks = [
  { _id: '1', title: 'Complete Math Assignment', due: new Date('2023-05-15'), completed: false, priority: 'medium' },
  { _id: '2', title: 'Study for Science Quiz', due: new Date('2023-05-18'), completed: true, priority: 'high' },
  { _id: '3', title: 'Read History Chapter', due: new Date('2023-05-20'), completed: false, priority: 'low' },
];

// Mock useUser hook implementation
const useUserWithTasks = vi.fn().mockReturnValue({
  user: { id: 'user1', role: 'Student', tasks: mockTasks },
  setUser: vi.fn(),
});

const useUserWithoutTasks = vi.fn().mockReturnValue({
  user: { id: 'user1', role: 'Student', tasks: [] },
  setUser: vi.fn(),
});

// Define our mock implementations
const userContextMocks = {
  withTasks: useUserWithTasks,
  withoutTasks: useUserWithoutTasks,
};

// Track which implementation to use
let currentMock: 'withTasks' | 'withoutTasks' = 'withTasks';

// Mock the required hooks and context
vi.mock('../../contexts/userContext', () => ({
  useUser: () => userContextMocks[currentMock](),
  UserProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>
}));

vi.mock('react-router-dom', () => ({
  useNavigate: () => vi.fn(),
  useLocation: () => ({ pathname: '/dashboard' }),
  BrowserRouter: ({ children }: { children: React.ReactNode }) => <>{children}</>
}));

vi.mock('../../hooks/useAxiosPrivate', () => ({
  useAxiosPrivate: () => ({
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  }),
}));

vi.mock('react-toastify', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('TaskList', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Default to tasks being present
    currentMock = 'withTasks';
  });

  it('renders correctly and displays tasks', () => {
    render(<TaskList />);
    
    // Check that the header is displayed
    expect(screen.getByText('Tasks')).toBeInTheDocument();
    
    // Check that task titles are displayed
    expect(screen.getByText('Complete Math Assignment')).toBeInTheDocument();
    expect(screen.getByText('Study for Science Quiz')).toBeInTheDocument();
    expect(screen.getByText('Read History Chapter')).toBeInTheDocument();
  });

  it('displays priority labels for tasks', () => {
    render(<TaskList />);
    
    // Check that priority labels are displayed
    expect(screen.getByText('medium')).toBeInTheDocument();
    expect(screen.getByText('high')).toBeInTheDocument();
    expect(screen.getByText('low')).toBeInTheDocument();
  });
  
  it('displays a message when there are no tasks', () => {
    // Use the version with no tasks
    currentMock = 'withoutTasks';
    
    render(<TaskList />);
    
    // Check for a "no tasks" message
    expect(screen.getByText('No Tasks Found')).toBeInTheDocument();
  });
}); 