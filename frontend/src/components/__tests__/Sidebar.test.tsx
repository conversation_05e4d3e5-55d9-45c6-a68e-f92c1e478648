import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent } from '../../utils/test-utils';
import Sidebar from '../Sidebar';
import { useNavigate } from 'react-router-dom';
import React from 'react';

// Mock SVG import first
vi.mock('../../assets/logos/logo.svg?react', () => ({
  default: (props: React.SVGProps<SVGSVGElement>) => <svg data-testid="mock-svg-logo" {...props} />
}));

// Then mock the component that uses it
vi.mock('../../assets/AegisScholarLogoIcon', () => ({
  default: ({ className, style }: { className?: string; style?: React.CSSProperties }) => (
    <div data-testid="mock-logo" className={className} style={style}>Logo Mock</div>
  )
}));

// Mock navigation
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: vi.fn(() => navigateMock),
    useLocation: () => ({ pathname: '/student-dashboard' }),
  };
});

// Create mock for navigation
const navigateMock = vi.fn();

// Mock the user context
vi.mock('../../contexts/userContext', () => ({
  useUser: () => ({
    user: { role: 'Student' },
    setUser: vi.fn(),
  }),
  // Export UserProvider to be used by test-utils
  UserProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>
}));

// Mock useAxiosPrivate hook
vi.mock('../../hooks/useAxiosPrivate', () => ({
  useAxiosPrivate: () => ({
    put: vi.fn(),
    delete: vi.fn(),
  }),
}));

// Mock toast
vi.mock('react-toastify', () => ({
  toast: {
    loading: vi.fn(),
    update: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
  },
  ToastContainer: () => <div data-testid="toast-container" />,
}));

describe('Sidebar', () => {
  const renderSidebar = () => {
    return render(
      <Sidebar isExpanded={true} onToggle={vi.fn()} />
    );
  };

  it('renders correctly for student users', () => {
    renderSidebar();
    
    // Check that student-specific navigation items are present using more specific selectors
    expect(screen.getByRole('button', { name: /home/<USER>
    expect(screen.getByRole('button', { name: /profile/i })).toBeInTheDocument();
    
    // Verify that Create and Analytics aren't shown for students
    expect(screen.queryByRole('button', { name: /create/i })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: /analytics/i })).not.toBeInTheDocument();
  });

  it('handles navigation item clicks', () => {
    // Reset the mock before this test
    navigateMock.mockClear();
    
    renderSidebar();
    
    // Simulate clicking a navigation item using a more specific selector
    const homeLink = screen.getByRole('button', { name: /home/<USER>
    fireEvent.click(homeLink);
    
    // Verify navigation was called
    expect(navigateMock).toHaveBeenCalled();
  });

  it('displays toggle button for collapsing sidebar', () => {
    const toggleMock = vi.fn();
    
    render(
      <Sidebar isExpanded={true} onToggle={toggleMock} />
    );
    
    // We need to find a more specific way to target the toggle button
    // In the component it's at the bottom of the nav
    const buttons = screen.getAllByRole('button');
    
    // Find the button that handles toggle
    // This is a simplified approach - might need to be more specific
    const toggleButton = buttons[buttons.length - 1]; // Assuming it's the last button
    fireEvent.click(toggleButton);
    
    // Verify toggle function was called
    expect(toggleMock).toHaveBeenCalled();
  });
}); 