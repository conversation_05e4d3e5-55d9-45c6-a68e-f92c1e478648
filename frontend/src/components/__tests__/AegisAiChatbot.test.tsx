import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../utils/test-utils';
import AegisAiChatbot from '../AegisAiChatbot';

// Mock the axios hook
vi.mock('../../hooks/useAxiosPrivate', () => ({
  useAxiosPrivate: () => ({
    post: vi.fn().mockResolvedValue({ 
      data: { 
        message: 'This is a test response from the AI assistant.' 
      } 
    })
  })
}));

// Mock the user context
vi.mock('../../contexts/userContext', () => ({
  useUser: () => ({
    user: {
      id: 'test-user-id',
      username: 'Test User',
      email: '<EMAIL>'
    }
  })
}));

describe('AegisAiChatbot', () => {
  const mockProps = {
    subject: 'Mathematics',
    studentId: 'test-student-id'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders welcome message on mount', () => {
    render(<AegisAiChatbot {...mockProps} />);
    
    expect(screen.getByText(/Hi Test User!/)).toBeInTheDocument();
    expect(screen.getByText(/I'm your personal AI learning assistant for Mathematics/)).toBeInTheDocument();
  });

  it('shows suggested questions when no conversation history', () => {
    render(<AegisAiChatbot {...mockProps} />);
    
    expect(screen.getByText('Try asking:')).toBeInTheDocument();
    expect(screen.getByText('What are my weakest areas in mathematics?')).toBeInTheDocument();
    expect(screen.getByText('How can I improve my problem-solving skills?')).toBeInTheDocument();
  });

  it('has subject-specific placeholder text', () => {
    render(<AegisAiChatbot {...mockProps} />);
    
    const input = screen.getByPlaceholderText(/Ask me about quadratic equations/);
    expect(input).toBeInTheDocument();
  });

  it('allows typing and sending messages', async () => {
    render(<AegisAiChatbot {...mockProps} />);
    
    const input = screen.getByRole('textbox');
    const sendButton = screen.getByRole('button');
    
    fireEvent.change(input, { target: { value: 'What is algebra?' } });
    expect(input).toHaveValue('What is algebra?');
    
    fireEvent.click(sendButton);
    
    await waitFor(() => {
      expect(screen.getByText('What is algebra?')).toBeInTheDocument();
    });
  });

  it('shows loading state when sending message', async () => {
    render(<AegisAiChatbot {...mockProps} />);
    
    const input = screen.getByRole('textbox');
    const sendButton = screen.getByRole('button');
    
    fireEvent.change(input, { target: { value: 'Test question' } });
    fireEvent.click(sendButton);
    
    expect(screen.getByText('Thinking...')).toBeInTheDocument();
  });

  it('handles suggested question clicks', async () => {
    render(<AegisAiChatbot {...mockProps} />);
    
    const suggestedQuestion = screen.getByText('What are my weakest areas in mathematics?');
    fireEvent.click(suggestedQuestion);
    
    await waitFor(() => {
      expect(screen.getByText('What are my weakest areas in mathematics?')).toBeInTheDocument();
    });
  });
});
