import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '../../utils/test-utils';
import { renderLatexContent, EnhancedMarkdown } from '../RenderLatexContent';

// Mock ReactKatex component
vi.mock('@pkasila/react-katex', () => ({
  default: ({ children }: { children: string }) => (
    <span data-testid="latex-formula">{children}</span>
  )
}));

describe('renderLatexContent', () => {
  it('should render regular text without latex', () => {
    const text = 'This is a regular text without any formulas';
    const result = renderLatexContent(text);
    
    // Create a container to render the result
    render(<div>{result}</div>);
    
    // Regular text should be rendered as is
    expect(screen.getByText(text)).toBeInTheDocument();
    
    // No latex formulas should be rendered
    expect(screen.queryByTestId('latex-formula')).not.toBeInTheDocument();
  });

  it('should render text with inline latex formula', () => {
    const text = 'The formula $E=mc^2$ represents energy-mass equivalence';
    const result = renderLatexContent(text);
    
    render(<div>{result}</div>);
    
    // Text parts should be rendered using more specific text matching
    expect(screen.getByText('The formula', { exact: false })).toBeInTheDocument();
    expect(screen.getByText('represents energy-mass equivalence', { exact: false })).toBeInTheDocument();
    
    // Latex formula should be rendered via ReactKatex
    expect(screen.getByTestId('latex-formula')).toBeInTheDocument();
    expect(screen.getByTestId('latex-formula').textContent).toBe('E=mc^2');
  });

  it('should render text with multiple latex formulas', () => {
    const text = 'In physics, $E=mc^2$ and $F=ma$ are important equations';
    const result = renderLatexContent(text);
    
    render(<div>{result}</div>);
    
    // Check text parts with exact: false to find text within larger content
    expect(screen.getByText(/In physics,/)).toBeInTheDocument();
    expect(screen.getByText(/and/)).toBeInTheDocument();
    expect(screen.getByText(/are important equations/)).toBeInTheDocument();
    
    // Check both formulas
    const formulas = screen.getAllByTestId('latex-formula');
    expect(formulas).toHaveLength(2);
    expect(formulas[0].textContent).toBe('E=mc^2');
    expect(formulas[1].textContent).toBe('F=ma');
  });

  it('should handle undefined or empty input', () => {
    // Test with undefined
    const result1 = renderLatexContent(undefined as unknown as string);
    render(<div>{result1}</div>);

    // Check rendering doesn't crash
    expect(document.body.textContent).toBe('');

    // Clear previous render
    document.body.innerHTML = '';

    // Test with empty string
    const result2 = renderLatexContent('');
    render(<div>{result2}</div>);

    // Should render an empty string without errors
    expect(document.body.textContent).toBe('');
  });
});

describe('EnhancedMarkdown', () => {
  it('should render markdown bold text', () => {
    const text = 'This is **bold text** and this is __also bold__';

    render(<EnhancedMarkdown text={text} />);

    // Check that bold elements are rendered with enhanced styling
    const boldElements = screen.getAllByText('bold text');
    expect(boldElements[0]).toBeInTheDocument();

    const alsoBoldElements = screen.getAllByText('also bold');
    expect(alsoBoldElements[0]).toBeInTheDocument();
  });

  it('should render markdown italic text', () => {
    const text = 'This is *italic text* and this is _also italic_';

    render(<EnhancedMarkdown text={text} />);

    // Check that italic elements are rendered
    const italicElements = screen.getAllByText('italic text');
    expect(italicElements[0]).toBeInTheDocument();

    const alsoItalicElements = screen.getAllByText('also italic');
    expect(alsoItalicElements[0]).toBeInTheDocument();
  });

  it('should render inline code', () => {
    const text = 'Use the `console.log()` function for debugging';

    render(<EnhancedMarkdown text={text} />);

    const codeElement = screen.getByText('console.log()');
    expect(codeElement).toBeInTheDocument();
  });

  it('should render bullet lists', () => {
    const text = `Here are some items:
- First item
- Second item
* Third item`;

    render(<EnhancedMarkdown text={text} />);

    // Check that list items are rendered
    expect(screen.getByText('First item')).toBeInTheDocument();
    expect(screen.getByText('Second item')).toBeInTheDocument();
    expect(screen.getByText('Third item')).toBeInTheDocument();
  });

  it('should render numbered lists', () => {
    const text = `Steps to follow:
1. First step
2. Second step
3. Third step`;

    render(<EnhancedMarkdown text={text} />);

    // Check that numbered list items are rendered
    expect(screen.getByText('First step')).toBeInTheDocument();
    expect(screen.getByText('Second step')).toBeInTheDocument();
    expect(screen.getByText('Third step')).toBeInTheDocument();
  });

  it('should render headers', () => {
    const text = `# Main Header
## Sub Header
### Small Header`;

    render(<EnhancedMarkdown text={text} />);

    // Check that headers are rendered
    expect(screen.getByText('Main Header')).toBeInTheDocument();
    expect(screen.getByText('Sub Header')).toBeInTheDocument();
    expect(screen.getByText('Small Header')).toBeInTheDocument();
  });

  it('should render text content', () => {
    const text = 'Morning (9:00 AM - 10:30 AM) Study Mathematics';

    render(<EnhancedMarkdown text={text} />);

    expect(screen.getByText(/Morning/)).toBeInTheDocument();
    expect(screen.getByText(/9:00 AM - 10:30 AM/)).toBeInTheDocument();
    expect(screen.getByText(/Study Mathematics/)).toBeInTheDocument();
  });

  it('should render colon-separated content', () => {
    const text = 'Monday: Complete algebra homework';

    render(<EnhancedMarkdown text={text} />);

    expect(screen.getByText(/Monday/)).toBeInTheDocument();
    expect(screen.getByText(/Complete algebra homework/)).toBeInTheDocument();
  });

  it('should render section headers with colons', () => {
    const text = 'Additional Tips: Remember to practice daily';

    render(<EnhancedMarkdown text={text} />);

    expect(screen.getByText(/Additional Tips/)).toBeInTheDocument();
    expect(screen.getByText(/Remember to practice daily/)).toBeInTheDocument();
  });

  it('should handle mixed LaTeX and markdown content', () => {
    const text = 'The equation $E=mc^2$ is **very important** in physics';

    render(<EnhancedMarkdown text={text} />);

    // Check LaTeX rendering
    expect(screen.getByTestId('latex-formula')).toBeInTheDocument();
    expect(screen.getByTestId('latex-formula').textContent).toBe('E=mc^2');

    // Check bold text is rendered
    expect(screen.getByText('very important')).toBeInTheDocument();
  });

  it('should handle complex structured content', () => {
    const text = `# Study Plan

## Morning Session
Morning (9:00 AM - 10:30 AM) Mathematics

### Topics to cover:
- Algebra: $ax^2 + bx + c = 0$
- **Geometry**: Area and perimeter
- *Statistics*: Mean and median

Additional Tips: Practice \`daily\` for best results`;

    render(<EnhancedMarkdown text={text} />);

    // Check various elements are rendered
    expect(screen.getByText('Study Plan')).toBeInTheDocument();
    expect(screen.getByText('Morning Session')).toBeInTheDocument();
    expect(screen.getByText(/Morning/)).toBeInTheDocument();
    expect(screen.getByText('Topics to cover:')).toBeInTheDocument();
    expect(screen.getByText('Geometry')).toBeInTheDocument();
    expect(screen.getByText('Statistics')).toBeInTheDocument();
    expect(screen.getByText('daily')).toBeInTheDocument();
    expect(screen.getByTestId('latex-formula')).toBeInTheDocument();
  });

  it('should handle regular content', () => {
    const text = 'Solve the following quadratic equation by factoring';

    render(<EnhancedMarkdown text={text} />);

    expect(screen.getByText('Solve the following quadratic equation by factoring')).toBeInTheDocument();
  });

  it('should handle mathematical equations', () => {
    const text = 'x^2 + 5x + 6 = 0';

    render(<EnhancedMarkdown text={text} />);

    // Check that the equation text is rendered
    expect(screen.getByText(/x\^2 \+ 5x \+ 6 = 0/)).toBeInTheDocument();
  });

  it('should handle educational content with Question and Hint sections', () => {
    const text = `Question:

Solve the following quadratic equation by factoring
x^2 + 5x + 6 = 0

Hint: Think about two numbers that multiply to 6 and add up to 5.`;

    render(<EnhancedMarkdown text={text} />);

    // Check that sections are properly formatted
    expect(screen.getByText('Question:')).toBeInTheDocument();
    expect(screen.getByText('Hint:')).toBeInTheDocument();
    expect(screen.getByText('Solve the following quadratic equation by factoring')).toBeInTheDocument();

    // Check that hint content is properly formatted
    expect(screen.getByText(/Think about two numbers that multiply to/)).toBeInTheDocument();
  });
});