import { parseQuestionBreakdown } from '../QuestionBreakdown';
import { validateEvaluationData } from '../../utils/xmlEvaluationParser';

// Test data based on the actual evaluation output structure
const testEvaluationData = [
    `<evaluation>
    <total_marks_awarded>100</total_marks_awarded>
    <maximum_possible_marks>250</maximum_possible_marks>

    <question number="1">
        <marks_awarded>5</marks_awarded>
        <marks_possible>10</marks_possible>
        <detailed_feedback>
            <overall_comment>The answer attempts to address the question by highlighting the significance of cave paintings, but it lacks depth and specific examples from ancient Indian cave paintings beyond a superficial mention of prehistoric sites. The connection to aesthetic sensitivity and creativeness is not well-developed.</overall_comment>
            <structural_analysis>
                <introduction>The introduction is generic and states the importance of paintings as historical sources but does not directly engage with the "aesthetic sensitivity and creativeness" aspect of the question.</introduction>
                <body_structure_and_flow>The body lists some cave sites but fails to elaborate on the artistic elements or the creative expression evident in them. The flow is disjointed, moving from prehistoric to IVC and then to ancient India without a clear thematic progression related to the question's core demand.</body_structure_and_flow>
                <conclusion>The conclusion is a brief summary of the points made but does not effectively synthesize the argument about aesthetic sensitivity and creativeness.</conclusion>
            </structural_analysis>
            <content_analysis>
                <relevance_and_accuracy>The mention of Sohagihat and Lakhudiyar is relevant, but the description is minimal.</relevance_and_accuracy>
                <analytical_depth_and_rigor>The answer is largely descriptive and lacks analytical depth.</analytical_depth_and_rigor>
                <keyword_and_citation_usage>Keywords like "aesthetic sensitivity" and "creativeness" are mentioned but not adequately explored.</keyword_and_citation_usage>
            </content_analysis>
            <presentation_analysis>
                <clarity_and_language>The language is generally understandable, but some phrases are awkward.</clarity_and_language>
                <visuals>The flowchart is present but its content is very basic.</visuals>
            </presentation_analysis>
        </detailed_feedback>
        <marks_breakdown>
            <criterion name="Understanding of the question's core demand (aesthetic sensitivity/creativeness)">1/3</criterion>
            <criterion name="Relevance and accuracy of examples (Indian cave paintings)">2/3</criterion>
            <criterion name="Depth of analysis and explanation">1/3</criterion>
            <criterion name="Structure and presentation">1/1</criterion>
        </marks_breakdown>
    </question>

    <question number="14">
        <marks_awarded>9</marks_awarded>
        <possible_marks>15</possible_marks>
        <detailed_feedback>
            <overall_comment>The answer effectively discusses the dual impact of globalization on the Indian craft industry, providing relevant positive and negative examples. It demonstrates a good understanding of the topic and its nuances.</overall_comment>
        </detailed_feedback>
        <marks_breakdown>
            <criterion name="Discussion of positive impacts with examples">4/6</criterion>
            <criterion name="Discussion of negative impacts with examples">4/6</criterion>
            <criterion name="Analysis of government/mitigation efforts">0.5/1</criterion>
            <criterion name="Structure and presentation">0.5/2</criterion>
        </marks_breakdown>
    </question>
</evaluation>`
];

// Test the actual escaped JSON format from your file
const actualEvaluationOutput = "\"<evaluation>\\n    <total_marks_awarded>100</total_marks_awarded>\\n    <maximum_possible_marks>250</maximum_possible_marks>\\n\\n    <question number=\\\"1\\\">\\n        <marks_awarded>5</marks_awarded>\\n        <marks_possible>10</marks_possible>\\n        <detailed_feedback>\\n            <overall_comment>The answer attempts to address the question by highlighting the significance of cave paintings.</overall_comment>\\n        </detailed_feedback>\\n        <marks_breakdown>\\n            <criterion name=\\\"Understanding of the question's core demand (aesthetic sensitivity/creativeness)\\\">1/3</criterion>\\n            <criterion name=\\\"Relevance and accuracy of examples (Indian cave paintings)\\\">2/3</criterion>\\n        </marks_breakdown>\\n    </question>\\n</evaluation>\"";

// Test edge cases
const edgeCaseTests = [
    // Empty array
    [],
    // Null data
    null,
    // Undefined data
    undefined,
    // Invalid string
    ["invalid xml content"],
    // Malformed XML
    ["<evaluation><question><marks_awarded>5</marks_awarded></evaluation>"],
    // Missing required elements
    ["<evaluation><question number=\"1\"></question></evaluation>"],
    // Escaped JSON string (like in the actual output)
    "\"<evaluation>\\n    <total_marks_awarded>100</total_marks_awarded>\\n    <maximum_possible_marks>250</maximum_possible_marks>\\n\\n    <question number=\\\"1\\\">\\n        <marks_awarded>5</marks_awarded>\\n        <marks_possible>10</marks_possible>\\n    </question>\\n</evaluation>\"",
];

describe('QuestionBreakdown Robustness Tests', () => {
    test('should parse valid evaluation data correctly', () => {
        const result = parseQuestionBreakdown(testEvaluationData);
        
        expect(result).not.toBeNull();
        expect(result?.totalMarks).toBe(100);
        expect(result?.maxMarks).toBe(250);
        expect(result?.questions).toHaveLength(2);
        expect(result?.questions[0].questionNumber).toBe("1");
        expect(result?.questions[0].marksAwarded).toBe(5);
        expect(result?.questions[0].marksPossible).toBe(10);
        expect(result?.questions[0].percentage).toBe(50);
    });

    test('should handle edge cases gracefully', () => {
        edgeCaseTests.forEach((testCase, index) => {
            const result = parseQuestionBreakdown(testCase);
            // Should not throw errors and should return null for invalid data
            expect(() => parseQuestionBreakdown(testCase)).not.toThrow();
            console.log(`Edge case ${index + 1}: ${result ? 'Parsed successfully' : 'Returned null as expected'}`);
        });
    });

    test('should validate evaluation data correctly', () => {
        const validation = validateEvaluationData(testEvaluationData);
        expect(validation.isValid).toBe(true);
        expect(validation.errors).toHaveLength(0);
    });

    test('should detect invalid data in validation', () => {
        const validation = validateEvaluationData(null);
        expect(validation.isValid).toBe(false);
        expect(validation.errors.length).toBeGreaterThan(0);
    });

    test('should handle criteria breakdown correctly with new format', () => {
        const result = parseQuestionBreakdown(testEvaluationData);

        expect(result?.questions[0].criteriaBreakdown).toHaveLength(4);
        expect(result?.questions[0].criteriaBreakdown[0].criterion).toContain("Understanding");
        expect(result?.questions[0].criteriaBreakdown[0].score).toBe("1");
        expect(result?.questions[0].criteriaBreakdown[0].maxScore).toBe("3");
    });

    test('should extract feedback from complex nested structures', () => {
        const result = parseQuestionBreakdown(testEvaluationData);

        expect(result?.questions[0].feedback).toContain("Overall:");
        expect(result?.questions[0].feedback).toContain("Structure:");
        expect(result?.questions[0].feedback).toContain("Content:");
        expect(result?.questions[0].feedback).toContain("Presentation:");
    });

    test('should handle different marks_possible tag names', () => {
        const result = parseQuestionBreakdown(testEvaluationData);

        // Question 1 uses marks_possible, Question 14 uses possible_marks
        expect(result?.questions[0].marksPossible).toBe(10);
        expect(result?.questions[1].marksPossible).toBe(15);
    });

    test('should handle escaped JSON format', () => {
        const result = parseQuestionBreakdown([actualEvaluationOutput]);

        expect(result).not.toBeNull();
        expect(result?.totalMarks).toBe(100);
        expect(result?.maxMarks).toBe(250);
        expect(result?.questions).toHaveLength(1);
    });

    test('should calculate percentages correctly', () => {
        const result = parseQuestionBreakdown(testEvaluationData);

        expect(result?.overallPercentage).toBe(40); // 100/250 = 40%
        expect(result?.questions[0].percentage).toBe(50); // 5/10 = 50%
        expect(result?.questions[1].percentage).toBe(60); // 9/15 = 60%
    });

    test('should handle criteria with missing maxScore correctly', () => {
        const dataWithMissingMaxScore = [
            `<evaluation>
                <total_marks_awarded>80</total_marks_awarded>
                <maximum_possible_marks>100</maximum_possible_marks>
                <question number="1">
                    <marks_awarded>80</marks_awarded>
                    <marks_possible>100</marks_possible>
                    <feedback>Good work with some areas for improvement.</feedback>
                    <marks_breakdown>
                        <criterion name="Content">15/20</criterion>
                        <criterion name="Structure">5</criterion>
                        <criterion name="Grammar">10</criterion>
                        <criterion name="Presentation">0</criterion>
                    </marks_breakdown>
                </question>
            </evaluation>`
        ];

        const result = parseQuestionBreakdown(dataWithMissingMaxScore);

        expect(result).not.toBeNull();
        expect(result?.questions).toHaveLength(1);

        const question = result?.questions[0];
        expect(question?.criteriaBreakdown).toHaveLength(4);

        // Check that criteria with maxScore are parsed correctly
        const contentCriterion = question?.criteriaBreakdown.find(c => c.criterion === 'Content');
        expect(contentCriterion?.score).toBe('15');
        expect(contentCriterion?.maxScore).toBe('20');

        // Check that criteria without maxScore have undefined maxScore
        const structureCriterion = question?.criteriaBreakdown.find(c => c.criterion === 'Structure');
        expect(structureCriterion?.score).toBe('5');
        expect(structureCriterion?.maxScore).toBeUndefined();

        const grammarCriterion = question?.criteriaBreakdown.find(c => c.criterion === 'Grammar');
        expect(grammarCriterion?.score).toBe('10');
        expect(grammarCriterion?.maxScore).toBeUndefined();

        // Check that criteria with score 0 and no maxScore are still included
        const presentationCriterion = question?.criteriaBreakdown.find(c => c.criterion === 'Presentation');
        expect(presentationCriterion).toBeDefined();
        expect(presentationCriterion?.criterion).toBe('Presentation');
        expect(presentationCriterion?.score).toBe('0');
        expect(presentationCriterion?.maxScore).toBeUndefined();
    });
});
