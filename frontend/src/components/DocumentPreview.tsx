import React, { useState, useEffect, useRef } from 'react';
import * as pdfjs from 'pdfjs-dist';
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
    'pdfjs-dist/build/pdf.worker.min.mjs',
    import.meta.url
).toString();

interface DocumentPreviewProps {
    pdfBlob: Blob;
}

const DocumentPreview: React.FC<DocumentPreviewProps> = ({ pdfBlob }) => {
    const [pdfDoc, setPdfDoc] = useState<pdfjs.PDFDocumentProxy | null>(null);
    const [numPages, setNumPages] = useState<number>(0);
    const [pageNumber, setPageNumber] = useState<number>(1);
    const canvasRef = useRef<HTMLCanvasElement>(null);

    useEffect(() => {
        if (!pdfBlob) return;

        const url = URL.createObjectURL(pdfBlob);

        const loadingTask = pdfjs.getDocument(url);

        loadingTask.promise.then((pdf) => {
            setPdfDoc(pdf);
            setNumPages(pdf.numPages);
            setPageNumber(1);
        }).catch((error) => {
            console.error('Error loading PDF:', error);
        });

        return () => {
            URL.revokeObjectURL(url);
            setPdfDoc(null);
            setNumPages(0);
        };
    }, [pdfBlob]);

    useEffect(() => {
        if (!pdfDoc || !canvasRef.current) return;
        const canvas = canvasRef.current;

        pdfDoc.getPage(pageNumber).then((page) => {
            const containerWidth = canvas.parentElement?.clientWidth || 800;
            const viewport = page.getViewport({ scale: 1 });
            const scale = containerWidth / viewport.width;
            const scaledViewport = page.getViewport({ scale });

            const context = canvas.getContext('2d');
            if (!context) return;

            const outputScale = window.devicePixelRatio || 1;

            canvas.width = Math.floor(scaledViewport.width * outputScale);
            canvas.height = Math.floor(scaledViewport.height * outputScale);
            canvas.style.width = `${Math.floor(scaledViewport.width)}px`;
            canvas.style.height = `${Math.floor(scaledViewport.height)}px`;

            // Calculate transform as before
            const transform = outputScale !== 1
                ? [outputScale, 0, 0, outputScale, 0, 0]
                : null;

            // Create renderContext conditionally
            const renderContext = {
                canvasContext: context,
                viewport: scaledViewport,
                ...(transform ? { transform } : {}),  // Only include if transform is not null
            };

            // Then call render
            page.render(renderContext);

        }).catch((error) => {
            console.error('Error rendering page:', error);
        });
    }, [pdfDoc, pageNumber]);

    const goToPrevPage = () => {
        setPageNumber((prev) => Math.max(prev - 1, 1));
    };

    const goToNextPage = () => {
        setPageNumber((prev) => Math.min(prev + 1, numPages));
    };

    // Handle window resize for responsiveness
    useEffect(() => {
        const handleResize = () => {
            if (pdfDoc) {
                setPageNumber((current) => current); // Trigger re-render on resize
            }
        };

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [pdfDoc]);

    return (
        <div style={{ width: '100%', height: '100%', overflow: 'auto', maxHeight: '100vh' }}>
            <canvas
                ref={canvasRef}
                style={{
                    border: '1px solid black',
                    display: 'block',
                    margin: '0 auto',
                    width: '100%',   // Ensures canvas stretches horizontally
                    maxHeight: '82.5vh', // Prevents overflow on mobile
                    objectFit: 'contain',
                    background: '#fff' // Reduces edge contrast
                }}
            />
            <div style={{ marginTop: '8px', textAlign: 'center' }}>
                <button onClick={goToPrevPage} disabled={pageNumber <= 1}>Previous</button>
                <span style={{ margin: '0 12px' }}>Page {pageNumber} of {numPages}</span>
                <button onClick={goToNextPage} disabled={pageNumber >= numPages}>Next</button>
            </div>
        </div>
    );
};

export default DocumentPreview;
