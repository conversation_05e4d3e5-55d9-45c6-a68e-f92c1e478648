import React from 'react';
import { useTracking } from '../hooks/useTracking';
import { useAnalytics } from '../contexts/analyticsContext';

// Simple test component to verify analytics is working
const AnalyticsTest: React.FC = () => {
    const { trackClick, trackFeatureUsage } = useTracking();
    const { isEnabled, setAnalyticsEnabled, getDebugInfo } = useAnalytics();

    const debugInfo = getDebugInfo();

    const handleTestClick = () => {
        console.log('Test button clicked - tracking...');
        trackClick('test-button', 'Test Button', 'analytics_test');
        trackFeatureUsage('analytics_test', 'test_button_clicked', {
            timestamp: new Date().toISOString(),
            testData: 'This is a test'
        });
    };

    const handleToggleAnalytics = () => {
        setAnalyticsEnabled(!isEnabled);
    };

    return (
        <div className="p-6 bg-card rounded-lg border border-border max-w-md mx-auto">
            <h2 className="text-xl font-bold text-primary mb-4">Analytics Test</h2>
            
            <div className="space-y-4">
                <div>
                    <p className="text-sm text-muted-foreground mb-2">
                        Analytics Status: <span className={isEnabled ? 'text-green-600' : 'text-red-600'}>
                            {isEnabled ? 'Enabled' : 'Disabled'}
                        </span>
                    </p>
                    <button
                        onClick={handleToggleAnalytics}
                        className="px-4 py-2 bg-accent text-white rounded-md hover:bg-accent/90 transition-colors"
                    >
                        {isEnabled ? 'Disable' : 'Enable'} Analytics
                    </button>
                </div>

                <div>
                    <button
                        onClick={handleTestClick}
                        className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
                    >
                        Test Analytics Tracking
                    </button>
                    <p className="text-xs text-muted-foreground mt-2">
                        Click this button and check the browser console for analytics logs.
                        Also check the Network tab for API calls to /api/analytics/interaction
                    </p>
                </div>

                <div className="text-xs text-muted-foreground">
                    <p><strong>Debug Info:</strong></p>
                    <div className="bg-gray-100 dark:bg-gray-800 p-2 rounded mt-2 font-mono">
                        <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
                    </div>
                </div>

                <div className="text-xs text-muted-foreground">
                    <p><strong>Debug Steps:</strong></p>
                    <ol className="list-decimal list-inside space-y-1">
                        <li>Open browser console (F12)</li>
                        <li>Click the test button above</li>
                        <li>Look for "Analytics:" logs in console</li>
                        <li>Check Network tab for API requests</li>
                        <li>Verify user is logged in</li>
                    </ol>
                </div>
            </div>
        </div>
    );
};

export default AnalyticsTest;
