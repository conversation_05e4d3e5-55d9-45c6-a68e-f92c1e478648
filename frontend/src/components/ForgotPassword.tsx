import React, { useState } from 'react';
import { EnvelopeIcon, ArrowLeftIcon, PaperAirplaneIcon } from '@heroicons/react/24/outline';
import Background from '../components/Background';
import { ToastContainer, toast } from 'react-toastify';
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';

interface ForgotPasswordProps {
    onBackToLogin: () => void;
    usertype: string;
}

const ForgotPassword: React.FC<ForgotPasswordProps> = ({ onBackToLogin, usertype }) => {
    const [email, setEmail] = useState('');
    const [isSent, setIsSent] = useState(false);

    const axiosPrivate = useAxiosPrivate();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        const resetToast = toast.loading("Sending reset link...", {
            position: "top-right",
            toastId: 'reset',
        });

        try {
            await axiosPrivate.post( '/api/password/forgot-password' , { usertype, email });
            
            toast.update(resetToast, {
                render: "Reset link sent successfully",
                type: "success",
                isLoading: false,
                autoClose: 1500,
            });
            
            setIsSent(true);
        } catch (err) {
            toast.update(resetToast, {
                render: "Failed to send reset link",
                type: "error",
                isLoading: false,
                autoClose: 1500,
            });
        }
    };

    return (
        <div className="min-h-screen flex flex-col relative overflow-hidden bg-background">
            <ToastContainer
                position="top-center"
                autoClose={1500}
                hideProgressBar={true}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
            />
            <Background />
    
            {/* Main Content */}
            <div className="grow flex items-center justify-center p-4">
                <div className="bg-card p-8 rounded-2xl border border-border w-full max-w-md shadow-lg">
                    <button 
                        onClick={onBackToLogin}
                        className="flex items-center text-accent mb-6 hover:text-accent-dark transition-colors cursor-pointer"
                    >
                        <ArrowLeftIcon className="mr-2" width={20} height={20} />
                        Back to Login
                    </button>
    
                    <AegisScholarLogoWithoutText className="w-16 h-16 mx-auto text-accent" style={{ fill: 'var(--color-accent)' }} />
                    <div className="text-center mb-8 mt-4">
                        <h1 className="text-3xl font-['Space_Grotesk'] font-bold text-primary mb-2">
                            Reset Your Password
                        </h1>
                        <p className="text-muted-foreground">
                            {isSent 
                                ? "Check your email for reset instructions" 
                                : "Enter your email to receive a password reset link"}
                        </p>
                    </div>
    
                    {!isSent ? (
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div>
                                <label className="block text-sm font-medium text-primary mb-2">
                                    Email Address
                                </label>
                                <div className="relative">
                                    <input
                                        type="email"
                                        required
                                        className="w-full px-4 py-2 pl-10 bg-secondary-100 border border-border rounded-lg 
                                                focus:outline-none focus:ring-2 focus:ring-accent-light transition-all text-primary"
                                        placeholder="Enter your email address"
                                        value={email}
                                        onChange={(e) => setEmail(e.target.value)}
                                    />
                                    <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" width={20} height={20} />
                                </div>
                            </div>
    
                            <button
                                type="submit"
                                className="w-full py-3 bg-accent text-white rounded-full hover:bg-accent-dark 
                                        transition-all duration-300 flex items-center justify-center group cursor-pointer"
                            >
                                Send Reset Link
                                <PaperAirplaneIcon className="ml-2 group-hover:translate-x-1 transition-transform duration-300" width={20} height={20} />
                            </button>
                        </form>
                    ) : (
                        <div className="text-center space-y-6">
                            <div className="p-4 bg-success-light text-success rounded-lg">
                                We've sent a password reset link to <strong>{email}</strong>. 
                                Please check your inbox and follow the instructions to reset your password.
                            </div>
                            
                            <button
                                onClick={onBackToLogin}
                                className="w-full py-3 bg-accent text-white rounded-full hover:bg-accent-dark 
                                        transition-all duration-300 flex items-center justify-center cursor-pointer" 
                            >
                                Return to Login
                            </button>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;