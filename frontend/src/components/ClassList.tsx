import React, { useEffect, useState } from "react";
import { BookOpen, Plus, Users, LogOut, UserPlus, CheckCheck, Copy, UserRoundPen, AlertCircle, X } from "lucide-react";
import { useNavigate, useLocation } from "react-router-dom";
import { useUser } from "../contexts/userContext";
import { useAxiosPrivate } from "../hooks/useAxiosPrivate";
import { AcademicCapIcon, BookOpenIcon, UsersIcon } from "@heroicons/react/24/outline";
import { fetchWithCache } from "../utils/cacheUtil";
import { CheckCircleIcon } from "@heroicons/react/24/outline";
import { createPortal } from "react-dom";

interface Class {
  _id?: string;
  classStd?: string; // Made optional to match all usages
  classCode: string;
  schoolId: string;
  teacherId: string[];
  subject: string;
  className: string;
  students: string[];
}

const Modal = ({ isOpen, onClose, children, className = "" }: { isOpen: boolean; onClose: () => void; children: React.ReactNode; className?: string }) => {
    if (!isOpen) return null;
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === "Escape") onClose();
    };
    return createPortal(
      <div
        className="fixed inset-0 z-50 flex items-center justify-center"
        role="dialog"
        aria-modal="true"
        onKeyDown={handleKeyDown}
      >
        <div className="fixed inset-0 backdrop-blur-md bg-opacity-50"></div>
        <div
          className={`bg-background rounded-lg z-50 p-6 w-full relative border border-border shadow-lg m-4 ${className}`}
          onClick={e => e.stopPropagation()}
        >
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-muted-foreground hover:text-foreground"
          >
            <X className="h-5 w-5" />
          </button>
          {children}
        </div>
      </div>,
      document.body
    );
  };

const ClassList = () => {
  const navigate = useNavigate();
  const { user, setUser } = useUser();
  const [classes, setClasses] = useState<Class[]>(user?.classes || []);
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [showLeaveModal, setShowLeaveModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [classToLeave, setClassToLeave] = useState<Class | null>(null);
  const [availableClasses, setAvailableClasses] = useState<Class[]>([]);
  const [selectedClassToJoin, setSelectedClassToJoin] = useState<Class | null>(null);
  const [isFetchingClasses, setIsFetchingClasses] = useState(false);

  const axiosPrivate = useAxiosPrivate();
  const handleClassClick = (classItem: Class) => {
    user?.role === 'Teacher' ? navigate("/ClassDetails/" + classItem._id) : navigate("/subjectDetails/" + classItem._id);
  };

  const handleOpenJoinModal = async () => {
    setShowJoinModal(true);
    setIsFetchingClasses(true);
    setError("");
    try {
      const response = await fetchWithCache(axiosPrivate, "/api/classes/getAllClasses");
      if (response) {
        // Convert the object of classes to an array
        const classesArray: Class[] = Object.values(response).map((cls: any) => ({
          _id: cls._id,
          classStd: cls.classStd,
          classCode: cls.classCode,
          subject: cls.subject,
          className: cls.className,
          schoolId: cls.schoolId,
          teacherId: cls.teacherId,
          students: cls.students,
        }));
        console.log("Available classes:", classesArray);
        setAvailableClasses(classesArray);
      }
    } catch (error) {
      setError("Failed to load classes. Please try again.");
    } finally {
      setIsFetchingClasses(false);
    }
  };

  const handleJoinSelectedClass = async () => {
    if (!selectedClassToJoin || !user?.id) {
      setError("Please select a class to join");
      return;
    }
    setIsLoading(true);
    setError("");
    try {
      const response = await axiosPrivate.put("/api/classes/joinClass", {
        classCode: selectedClassToJoin.classCode,
        userid: user?.id,
        role: user?.role,
      });
      // Normalize response to Class object
      const joinedClass = response.data || response;
      const updatedClasses = Array.isArray(user?.classes) ? [...user.classes, joinedClass] : [joinedClass];
      setClasses(updatedClasses);
      if (user) {
        setUser({ ...user, classes: updatedClasses });
      }
      setShowJoinModal(false);
      setSelectedClassToJoin(null);
      setAvailableClasses([]);
    } catch (error) {
      setError("Failed to join class. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleLeaveClass = async () => {
    if (!classToLeave || !user || !classToLeave.classCode) return;
    setIsLoading(true);
    setError("");
    try {
      const response = await axiosPrivate.delete("/api/classes/leaveClass", {
        data: {
          classCode: classToLeave.classCode,
          userid: user?.id,
          role: user?.role,
        }
      });

      console.log("Leave class response:", response);
      const updatedClasses = (user.classes || []).filter(c => c.classCode !== classToLeave.classCode);
      setUser({ ...user, classes: updatedClasses });
      setShowLeaveModal(false);
      setClassToLeave(null);
    } catch (error) {
      console.error("Error leaving class:", error);
      setError("An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (user?.classes) {
      setClasses(user.classes);
    }
  }, [user?.classes]);

  const getSubjectIcon = (subject: string) => {
    const subjectLower = subject.toLowerCase();
    if (subjectLower.includes('mathematics')) return "/subjectIcons/math.png";
    if (subjectLower.includes('prelims')) return "/subjectIcons/NationalEmblem.png";
    if (subjectLower.includes('computer')) return "/subjectIcons/computer.png";
    if (subjectLower.includes('hindi')) return "/subjectIcons/hindi.png";
    if (subjectLower.includes('english')) return "/subjectIcons/english.png";
    if (subjectLower.includes('science') && !subjectLower.includes('social')) return "/subjectIcons/science.png";
    if (subjectLower.includes('social') || subjectLower.includes('history') || subjectLower.includes('civics'))
      return "/subjectIcons/socialScience.png";
    return "/subjectIcons/math.png"; // Default icon
  };

  const getSubjectColor = (subject: string) => {
    const subjectLower = subject.toLowerCase();
    if (subjectLower.includes('math'))
      return "bg-[hsl(var(--subject-math)/0.1)] hover:bg-[hsl(var(--subject-math)/0.2)] text-[hsl(var(--subject-math))]";
    if (subjectLower.includes('prelims') || subjectLower.includes('civil service'))
      return "bg-[hsl(var(--subject-upsc)/0.1)] hover:bg-[hsl(var(--subject-upsc)/0.2)] text-[hsl(var(--subject-upsc))]";
    if (subjectLower.includes('science') && !subjectLower.includes('social'))
      return "bg-[hsl(var(--subject-science)/0.1)] hover:bg-[hsl(var(--subject-science)/0.2)] text-[hsl(var(--subject-science))]";
    if (subjectLower.includes('english'))
      return "bg-[hsl(var(--subject-english)/0.1)] hover:bg-[hsl(var(--subject-english)/0.2)] text-[hsl(var(--subject-english))]";
    if (subjectLower.includes('hindi'))
      return "bg-[hsl(var(--subject-history)/0.1)] hover:bg-[hsl(var(--subject-history)/0.2)] text-[hsl(var(--subject-history))]";
    if (subjectLower.includes('computer'))
      return "bg-[hsl(var(--subject-computer)/0.1)] hover:bg-[hsl(var(--subject-computer)/0.2)] text-[hsl(var(--subject-computer))]";
    if (subjectLower.includes('social') || subjectLower.includes('history'))
      return "bg-[hsl(var(--subject-history)/0.1)] hover:bg-[hsl(var(--subject-history)/0.2)] text-[hsl(var(--subject-history))]";
    // Default color
    return "bg-accent/10 hover:bg-accent/20 text-accent";
  };

  // Group classes by classStd for modal display
  const groupClassesByStd = (classes: Class[]) => {
    const grouped: Record<string, Class[]> = {};
    classes.forEach(cls => {
      const std = cls.classStd || cls.className || "Other";
      if (!grouped[std]) grouped[std] = [];
      grouped[std].push(cls);
    });
    return grouped;
  };

  return (
    <div className="flex flex-col h-full">
      <div className="p-2 border-b border-border flex justify-between items-center">
        <div className="flex items-center">
          <AcademicCapIcon className="h-4 w-4 mr-2 text-accent" />
          <h2 className="text-lg font-['Space_Grotesk'] font-bold text-primary">My Classes</h2>
        </div>
        {classes.length > 0 && !user?.schoolCode && (
          <div className="flex gap-2">
            <button
              onClick={handleOpenJoinModal}
              className="flex items-center gap-2 px-3 py-1 hover:bg-accent/10 rounded-full transition-colors text-accent"
            >
              <UserPlus className="h-4 w-4" />
              Join Class
            </button>
          </div>
        )}
      </div>

      <div className="flex-1 overflow-auto p-2">
        {classes.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-5">
            {classes.map((classItem) => (
              <div
                key={classItem._id}
                className={`relative ${getSubjectColor(classItem.subject)} rounded-lg p-5 border border-border h-auto sm:h-52 shadow-sm hover:shadow transition-all duration-200 cursor-pointer group overflow-hidden`}
                onClick={() => handleClassClick(classItem)}
              >
                {/* Class title */}
                <h3 className="text-xl font-['Space_Grotesk'] font-bold lg:absolute lg:bottom-4 lg:left-4 text-primary mb-4 truncate pr-16">
                  {classItem.className}
                </h3>

                {/* Subject name */}
                <p className="text-sm font-medium text-foreground/70 lg:absolute lg:bottom-4 lg:left-4 lg:mt-2">
                  {classItem.subject[0].toUpperCase() + classItem.subject.slice(1)}
                </p>

                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setClassToLeave(classItem);
                    setShowLeaveModal(true);
                  }}
                  className="p-2 bg-destructive/10 hover:bg-destructive/20 rounded-full text-destructive absolute bottom-4 right-4 shadow-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-destructive/50 z-10 lg:opacity-0 group-hover:lg:opacity-100"
                  aria-label="Leave class"
                >
                  <LogOut className="h-4 w-4" />
                </button>

                {/* Subject icon */}
                <div className="absolute top-5 right-5 lg:w-36 lg:h-36 h-24 w-24 opacity-40">
                  <img
                    src={getSubjectIcon(classItem.subject)}
                    alt={`${classItem.subject} icon`}
                    className="w-full h-full object-contain"
                  />
                </div>

                {/* Teacher/Student info */}
                <div className="space-y-2 text-sm">
                  {user?.role === 'Teacher' && (
                    <>
                      <div className="flex items-center text-foreground/70">
                        <UsersIcon className="h-4 w-4 mr-2 flex-shrink-0" />
                        <span className="truncate">{classItem.students.length} Students</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : (
          // No classes message - updated style
          <div className="flex flex-col items-center justify-center h-full py-12 px-4 text-center">
            <div className="bg-accent/10 rounded-full p-4 mb-4">
              <AlertCircle className="h-6 w-6 text-accent" />
            </div>
            <h3 className="text-lg font-['Space_Grotesk'] font-medium text-primary mb-2">No Classes Found</h3>
            <p className="text-sm text-foreground/70 max-w-xs mb-4">
              The classes you are enrolled in will appear here.
            </p>
            <button
              onClick={handleOpenJoinModal}
              className="flex items-center gap-2 px-3 py-1 hover:bg-accent/10 rounded-full transition-colors text-accent"
              hidden={!user?.schoolCode}
            >
              <UserPlus className="h-4 w-4" />
              Join Class
            </button>
          </div>
        )}
      </div>
      {showJoinModal && (
        <Modal isOpen={showJoinModal} onClose={() => { setShowJoinModal(false); setError(""); setSelectedClassToJoin(null); }} className="max-w-2xl">
          <h3 className="text-lg font-bold mb-4">Join Class</h3>
          {error && <p className="text-destructive mb-4 text-sm">{error}</p>}
          {isFetchingClasses ? (
            <div className="text-center py-8">
              <svg className="animate-spin h-8 w-8 mx-auto text-accent" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
              </svg>
            </div>
          ) : (
            <>
              <div className="max-h-96 overflow-y-auto mb-4"> {/* increased height for more space */}
                {availableClasses.length > 0 ? (
                  Object.entries(groupClassesByStd(availableClasses)).map(([std, classList]) => (
                    <div key={std} className="mb-3">
                      <div className="font-bold text-primary mb-2 text-base">
                        {Number(std) > 12 ? classList[0].className : `Class ${std}`}
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2"> {/* more columns for bigger modal */}
                        {classList.map((classItem) => (
                          <button
                            key={classItem._id}
                            onClick={() => setSelectedClassToJoin(classItem)}
                            className={`flex items-center justify-between p-3 rounded border-2 transition-all w-full text-left ${selectedClassToJoin?._id === classItem._id ? "border-accent bg-accent/10" : "border-border hover:border-accent/50 hover:bg-accent/5"}`}
                          >
                            <span className="font-semibold">{classItem.subject}</span>
                            {selectedClassToJoin?._id === classItem._id && <CheckCircleIcon className="w-5 h-5 text-accent" />}
                          </button>
                        ))}
                      </div>
                    </div>
                  ))
                ) : <div className="text-center text-muted-foreground">No classes available.</div>}
              </div>
              <button
                onClick={handleJoinSelectedClass}
                disabled={!selectedClassToJoin || isLoading}
                className={`w-full bg-accent text-accent-foreground rounded-md py-2 hover:bg-accent/90 transition-colors ${!selectedClassToJoin ? "opacity-50 cursor-not-allowed" : ""}`}
              >
                {isLoading ? "Joining..." : "Join Selected Class"}
              </button>
              <button
                onClick={() => { setShowJoinModal(false); setError(""); setSelectedClassToJoin(null); }}
                className="w-full mt-2 bg-muted text-foreground rounded-md py-2 hover:bg-muted/80 transition-colors"
              >
                Cancel
              </button>
            </>
          )}
        </Modal>
      )}

      {showLeaveModal && (
        <Modal isOpen={showLeaveModal} onClose={() => { setShowLeaveModal(false); setClassToLeave(null); }} className="max-w-md">
          <h3 className="text-lg font-semibold mb-4">Leave Class</h3>
          <p className="mb-4">
            Are you sure you want to leave {classToLeave?.className}? This action cannot be undone.
          </p>
          <div className="flex gap-2">
            <button
              onClick={handleLeaveClass}
              disabled={isLoading}
              className="flex-1 bg-destructive text-destructive-foreground rounded py-1 px-3 text-sm hover:bg-destructive/90 disabled:bg-destructive/50 transition-colors"
            >
              {isLoading ? "Leaving..." : "Leave Class"}
            </button>
            <button
              onClick={() => {
                setShowLeaveModal(false);
                setClassToLeave(null);
              }}
              className="flex-1 bg-muted text-foreground rounded py-1 px-3 text-sm hover:bg-muted/80 transition-colors"
            >
              Cancel
            </button>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default ClassList;