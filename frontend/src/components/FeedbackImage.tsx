import { useState, useRef, useEffect } from 'react';
import { Upload, X, FileImage, Camera } from 'lucide-react';

interface ImageUploadProps {
  onImagesChange: (images: File[]) => void;
  maxImages?: number;
  maxSizeKB?: number;
}


const FeedbackImageComp = ({ onImagesChange, maxImages = 3, maxSizeKB = 5000 }: ImageUploadProps) => {
  const [images, setImages] = useState<File[]>([]);
  const [previews, setPreviews] = useState<string[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [error, setError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

    
  const handleFiles = async (files: FileList | null) => {
    if (!files) return;
    
    const newFiles = Array.from(files);
    const validFiles: File[] = [];
    const newPreviews: string[] = [];

    // Validate files
    for (const file of newFiles) {
      // Check file type
      if (!file.type.startsWith('image/')) {
        setError('Please select only image files');
        continue;
      }
      
      // Check file size
      if (file.size > maxSizeKB * 1024) {
        setError(`Image size should be less than ${maxSizeKB}KB`);
        continue;
      }
      
      // Check total count
      if (images.length + validFiles.length >= maxImages) {
        setError(`Maximum ${maxImages} images allowed`);
        break;
      }
      
      validFiles.push(file);
    }

    // Create previews for all valid files
    const previewPromises = validFiles.map(file => {
      return new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.readAsDataURL(file);
      });
    });
    
    await Promise.all(previewPromises).then(newPreviews => {
      setPreviews(prev => [...prev, ...newPreviews]);
    });
    
    if (validFiles.length > 0) {
      const updatedImages = [...images, ...validFiles];
      console.log(`updatedImages len: ${updatedImages.length}, value: ${JSON.stringify(updatedImages)}`)
      setImages(updatedImages);
      onImagesChange(updatedImages);
      setError('');
    }
  };

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    const newPreviews = previews.filter((_, i) => i !== index);
    
    setImages(newImages);
    setPreviews(newPreviews);
      console.log(`updatedImages len: ${newImages.length}, value: ${JSON.stringify(newImages)}`)
    onImagesChange(newImages);
    setError('');
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files);
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className="space-y-3">
      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-3 sm:p-4 text-center transition-all duration-200 cursor-pointer ${
          dragActive
            ? 'border-primary bg-primary/10'
            : images.length >= maxImages
            ? 'border-muted bg-muted cursor-not-allowed'
            : 'border-border hover:border-border/60 hover:bg-muted/50'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={images.length < maxImages ? openFileDialog : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleInputChange}
          className="hidden"
          disabled={images.length >= maxImages}
        />
        
        <div className="flex flex-col items-center gap-2">
          {images.length >= maxImages ? (
            <>
              <FileImage className="text-muted-foreground" size={20} />
              <p className="text-xs sm:text-sm text-muted-foreground">
                Maximum {maxImages} images reached
              </p>
            </>
          ) : (
            <>
              <div className="flex items-center gap-2 text-muted-foreground">
                <Upload size={18} />
                <Camera size={18} />
              </div>
              <div className="text-xs sm:text-sm text-foreground">
                <p className="font-medium">Drop images here or click to browse</p>
                <p className="text-xs text-muted-foreground mt-1">
                  Max {maxImages} images, up to {maxSizeKB/1000}MB each
                </p>
              </div>
            </>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="text-danger text-xs sm:text-sm bg-danger/10 border border-danger/20 rounded-lg px-3 py-2">
          {error}
        </div>
      )}

      {/* Image Previews */}
      {previews.length > 0 && (
        <div className="grid grid-cols-3 gap-2">
          {previews.map((preview, index) => (
            <div key={index} className="relative group">
              <img
                src={preview}
                alt={`Preview ${index}`}
                className="w-full h-16 sm:h-20 object-cover rounded-lg border border-border"
              />
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  removeImage(index);
                }}
                className="absolute -top-1 -right-1 bg-danger text-danger-foreground rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-danger/90"
              >
                <X size={10} />
              </button>
            </div>
          ))}
        </div>
      )}

      {/* Image Counter */}
      {images.length > 0 && (
        <div className="text-xs text-muted-foreground text-center">
          {images.length} of {maxImages} images selected
        </div>
      )}
    </div>
  );
};

export default FeedbackImageComp;
