import React, { useState, useEffect } from 'react';
import { Bell, Plus, CheckCircle2, Circle, Trash2, Calendar, AlertCircle } from 'lucide-react';
// import { createTask, updateTask, deleteTask } from '../api';
import { useUser } from '../contexts/userContext';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { useNavigate, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';

interface Task {
  title: string;
  due: Date;
  completed: boolean;
  priority: 'low' | 'medium' | 'high';
  _id?: string;
}

const TaskList = () => {
  const { user, setUser } = useUser();
  const [tasks, setTasks] = useState<Task[]>(user?.tasks || []);
  const [newTask, setNewTask] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [selectedDue, setSelectedDue] = useState('');
  const [selectedPriority, setSelectedPriority] = useState<Task['priority']>('medium');

  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (user?.tasks) {
      setTasks(user.tasks);
    }
  }, [user?.tasks]);

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-600';
      case 'medium': return 'bg-yellow-100 text-yellow-600';
      case 'low': return 'bg-green-100 text-green-600';
    }
  };

  const axiosPrivate = useAxiosPrivate();

  const handleAddTask = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newTask.trim()) {
      const taskData: Task = {
        title: newTask,
        due: new Date(selectedDue),
        completed: false,
        priority: selectedPriority
      };

      try {
        const response = await axiosPrivate.post(`/api/tasks/create${user?.role}Task`, { userid: user?.id, task: taskData });
        const data = response?.data;
        // console.error('Task created:', response);
        setTasks([...tasks, data?.task]); // Assuming the API returns the created task with _id
        setNewTask('');
        setShowModal(false);
        if (user) {
          setUser({ ...user, tasks: [...user?.tasks ?? [], data?.task] });
        }
        toast.success('Task created successfully');
      } catch (error) {
        // navigate('/login', { state: { from: location }, replace: true });
        console.error('Error creating task:', error);
        toast.error('Error creating task');
        // Handle error appropriately
      }
    }
  };

  const handleToggleTask = async (task: Task) => {
    if (!task._id) return;

    try {
      const updatedTask = { ...task, completed: !task.completed };
      await axiosPrivate.put(`/api/tasks/update${user?.role}Task`, { userid: user?.id, taskid: task._id, task: updatedTask });
      setTasks(tasks.map(t => t._id === task._id ? updatedTask : t));
      if (user) {
        setUser({ ...user, tasks: tasks?.map(t => t._id === task._id ? updatedTask : t) || [] });
      }
      toast.success('Task updated successfully', { autoClose: 2000 });
    } catch (error) {
      // navigate('/login', { state: { from: location }, replace: true });
      console.error('Error updating task:', error);
      toast.error('Error updating task', { autoClose: 2000 });
    }
  };

  const handleDeleteTask = async (task: Task) => {
    if (!task._id) return;

    try {
      await axiosPrivate.delete(`/api/tasks/delete${user?.role}Task`, {
        data: {
          userid: user?.id, taskid: task._id
        }
      });
      setTasks(tasks.filter(t => t._id !== task._id));
      if (user) {
        setUser({ ...user, tasks: tasks?.filter(t => t._id !== task._id) || [] });
      }
      toast.success('Task deleted successfully', { autoClose: 2000 });
    } catch (error) {
      // navigate('/login', { state: { from: location }, replace: true });
      console.error('Error deleting task:', error);
      toast.error('Error deleting task', { autoClose: 2000 });
      // Handle error appropriately
    }
  };

  // Helper function to get the current local datetime in the format required by datetime-local (YYYY-MM-DDTHH:MM)
  const getLocalDatetime = () => {
    const now = new Date();
    // Remove seconds and milliseconds for consistency
    now.setSeconds(0, 0);
    // Adjust for timezone offset so that the value is in local time (not UTC)
    const offset = now.getTimezoneOffset();
    const localDate = new Date(now.getTime() - offset * 60 * 1000);
    return localDate.toISOString().slice(0, 16);
  };

  return (
    <div className="flex flex-col lg:h-full ">
      <div className="p-2 border-b flex justify-between items-center bg-orange-50">
        <div className="flex items-center">
          <Bell className="h-4 w-4 mr-2 text-orange-600" />
          <h2 className="text-sm font-semibold text-orange-800">Tasks</h2>
        </div>
        <button
          onClick={() => setShowModal(true)}
          className="p-1 hover:bg-gray-100 rounded-full transition-colors"
        >
          <Plus className="h-4 w-4" />
        </button>
      </div>

      <div className="flex flex-col gap-2 p-2 overflow-auto max-h-[250px] md:max-h-[300px] lg:max-h-full scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
        { tasks.length > 0 ? (
        tasks.map((task) => {
          return (
            <div
              key={task._id}
              className="flex items-center justify-between p-2 hover:shadow-md transition duration-200 border border-gray-200 rounded-lg group"
            >
              <div className="flex items-center flex-1">
                <button
                  onClick={() => handleToggleTask(task)}
                  className="p-1 hover:bg-gray-100 rounded-full"
                >
                  {task.completed ? (
                    <CheckCircle2 className="h-4 w-4 text-green-500" />
                  ) : (
                    <Circle className="h-4 w-4 text-gray-400" />
                  )}
                </button>
                <div className="ml-2 flex-1">
                  <p className={`text-sm ${task.completed ? 'line-through text-gray-500' : ''}`}>
                    {task.title}
                  </p>
                  <div className="flex items-center gap-2 mt-1">
                    <span className={`text-xs px-2 py-0.5 rounded-full ${getPriorityColor(task.priority)}`}>
                      {task.priority}
                    </span>
                    <span className="text-xs text-gray-500 flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      {new Date(task.due).toDateString()}
                    </span>
                  </div>
                </div>
              </div>
              <button
                onClick={() => handleDeleteTask(task)}
                className="p-1 hover:bg-red-100 rounded-full opacity-50 group-hover:opacity-100 transition-opacity"
              >
                <Trash2 className="h-4 w-4 text-red-600" />
              </button>
            </div>
          );
        })
      ) : (
        <div className="flex flex-col items-center justify-center h-full py-8 px-4 text-center">
          <div className="bg-gray-50 rounded-full p-4 mb-3">
            <AlertCircle className="h-6 w-6 text-gray-300" />
          </div>
          <h3 className="text-md font-medium text-gray-700 mb-1">No Tasks Found</h3>
          <p className="text-xs text-gray-500 max-w-xs">
            Click the + button above to add a new task
          </p>
        </div>
      )}
      </div>

      {showModal && (
        <div className="fixed inset-0 z-50 backdrop-blur-sm bg-opacity-50 flex justify-center items-center">
          <div className="bg-white border shadow-lg border-gray-200 p-6 rounded-lg w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Add New Task</h3>
            <form onSubmit={handleAddTask}>
              <input
                type="text"
                value={newTask}
                onChange={(e) => setNewTask(e.target.value)}
                placeholder="Enter new task..."
                className="w-full p-2 mb-4 border rounded"
              />
              <div className="grid grid-cols-2 gap-4 mb-4">
                <input
                  type="datetime-local"
                  className="w-full rounded border border-gray-300 p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  value={selectedDue}
                  // The min attribute ensures only dates/times from now onward can be selected
                  min={getLocalDatetime()}
                  onChange={(e) => setSelectedDue(e.target.value)}
                  required
                />
                <select
                  value={selectedPriority}
                  onChange={(e) => setSelectedPriority(e.target.value as Task['priority'])}
                  className="p-2 border rounded text-sm"
                >
                  <option value="low">Low Priority</option>
                  <option value="medium">Medium Priority</option>
                  <option value="high">High Priority</option>
                </select>
              </div>
              <div className="flex gap-2">
                <button
                  type="submit"
                  className="flex-1 bg-blue-600 text-white rounded py-1 px-3 text-sm hover:bg-blue-700"
                >
                  Add Task
                </button>
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="flex-1 text-gray-700 rounded py-1 px-3 text-sm hover:bg-gray-200"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskList;
