import React, { useEffect, useState, useCallback, useMemo } from 'react';
import Joyride, { ACTIONS, STATUS, CallBackProps, Step, Styles } from 'react-joyride';

// Custom step interface extending the default Step interface
export interface TourStep extends Omit<Step, 'target'> {
  target: string;
  content: string;
  placement?: 'top' | 'bottom' | 'left' | 'right' | 'auto' | 'center';
  disableBeacon?: boolean;
  title?: string;
  hideCloseButton?: boolean;
  hideFooter?: boolean;
  showProgress?: boolean;
  styles?: Partial<Styles>;
}

// User interface for tour completion tracking
export interface TourUser {
  id: string;
  role: string;
  tourCompleted?: boolean;
  [key: string]: any; // Allow additional user properties
}

// Callback function types
export type TourCallback = (data: CallBackProps) => void;
export type TourCompletionCallback = (completed: boolean, skipped: boolean) => void;
export type TourStepCallback = (stepIndex: number, step: TourStep) => void;

// Main component props interface
export interface OnboardingTourProps {
  // Required props
  steps: TourStep[];
  
  // User-related props
  user?: TourUser | null;
  tourKey?: string; // Unique key for this tour (used for localStorage)
  
  // Tour behavior props
  autoStart?: boolean;
  startDelay?: number;
  continuous?: boolean;
  showSkipButton?: boolean;
  showProgress?: boolean;
  disableOverlay?: boolean;
  disableScrolling?: boolean;
  
  // Manual control props
  run?: boolean;
  stepIndex?: number;
  
  // Callback props
  onTourComplete?: TourCompletionCallback;
  onStepChange?: TourStepCallback;
  onCallback?: TourCallback;
  
  // Styling props
  styles?: Partial<Styles>;
  locale?: {
    back?: string;
    close?: string;
    last?: string;
    next?: string;
    open?: string;
    skip?: string;
  };
  
  // Advanced props
  debug?: boolean;
  getHelpers?: (helpers: any) => void;
  hideBackButton?: boolean;
  spotlightClicks?: boolean;
  spotlightPadding?: number;
}

const OnboardingTour: React.FC<OnboardingTourProps> = ({
  steps,
  user,
  tourKey = 'default-tour',
  autoStart = true,
  startDelay = 1000,
  continuous = true,
  showSkipButton = true,
  showProgress = false,
  disableOverlay = false,
  disableScrolling = false,
  run: externalRun,
  stepIndex: externalStepIndex,
  onTourComplete,
  onStepChange,
  onCallback,
  styles: customStyles,
  locale,
  debug = false,
  getHelpers,
  hideBackButton = false,
  spotlightClicks = false,
  spotlightPadding = 4,
}) => {
  // Internal state
  const [internalRun, setInternalRun] = useState(false);
  const [internalStepIndex, setInternalStepIndex] = useState(0);
  const [isReady, setIsReady] = useState(false);

  // Determine if tour should run (external control takes precedence)
  const shouldRun = externalRun !== undefined ? externalRun : internalRun;
  const currentStepIndex = externalStepIndex !== undefined ? externalStepIndex : internalStepIndex;

  // Generate storage key for tour completion
  const storageKey = useMemo(() => {
    const baseKey = `tour-completed-${tourKey}`;
    return user?.id ? `${baseKey}-${user.id}` : baseKey;
  }, [tourKey, user?.id]);

  // Check if tour has been completed
  const isTourCompleted = useMemo(() => {
    if (user?.tourCompleted !== undefined) {
      return user.tourCompleted;
    }
    return localStorage.getItem(storageKey) === 'true';
  }, [user?.tourCompleted, storageKey]);

  // Validate steps before starting tour
  const validateSteps = useCallback(() => {
    if (!steps || steps.length === 0) {
      if (debug) console.warn('OnboardingTour: No steps provided');
      return false;
    }

    const invalidSteps = steps.filter(step => !step.target || !step.content);
    if (invalidSteps.length > 0) {
      if (debug) console.warn('OnboardingTour: Invalid steps found', invalidSteps);
      return false;
    }

    return true;
  }, [steps, debug]);

  // Wait for DOM elements to be available
  const waitForTargets = useCallback(async () => {
    const maxWait = 10000; // 10 seconds max wait
    const interval = 100; // Check every 100ms
    let waited = 0;

    while (waited < maxWait) {
      const allTargetsExist = steps.every(step => {
        const element = document.querySelector(step.target);
        return element !== null;
      });

      if (allTargetsExist) {
        return true;
      }

      await new Promise(resolve => setTimeout(resolve, interval));
      waited += interval;
    }

    if (debug) {
      console.warn('OnboardingTour: Some targets not found after waiting', {
        missingTargets: steps
          .filter(step => !document.querySelector(step.target))
          .map(step => step.target)
      });
    }

    return false;
  }, [steps, debug]);

  // Initialize tour
  useEffect(() => {
    const initializeTour = async () => {
      if (!validateSteps()) return;

      // Wait a bit for the page to render
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Wait for target elements
      await waitForTargets();
      
      setIsReady(true);

      // Auto-start logic
      if (autoStart && !isTourCompleted && externalRun === undefined) {
        setTimeout(() => {
          setInternalRun(true);
        }, startDelay);
      }
    };

    initializeTour();
  }, [validateSteps, waitForTargets, autoStart, isTourCompleted, startDelay, externalRun]);

  // Handle tour completion
  const markTourAsCompleted = useCallback((completed: boolean, skipped: boolean) => {
    localStorage.setItem(storageKey, 'true');
    onTourComplete?.(completed, skipped);
  }, [storageKey, onTourComplete]);

  // Joyride callback handler
  const handleJoyrideCallback = useCallback((data: CallBackProps) => {
    const { action, index, status, type } = data;

    if (debug) {
      console.log('OnboardingTour callback:', { action, index, status, type });
    }

    // Handle step changes
    if (type === "step:after" || type === "error:target_not_found") {
      const newIndex = index + (action === ACTIONS.PREV ? -1 : 1);
      
      if (externalStepIndex === undefined) {
        setInternalStepIndex(newIndex);
      }
      
      onStepChange?.(newIndex, steps[newIndex]);
    }

    // Handle tour completion
    if (status === STATUS.FINISHED) {
      if (externalRun === undefined) {
        setInternalRun(false);
        setInternalStepIndex(0);
      }
      markTourAsCompleted(true, false);
    } else if (status === STATUS.SKIPPED) {
      if (externalRun === undefined) {
        setInternalRun(false);
        setInternalStepIndex(0);
      }
      markTourAsCompleted(false, true);
    }

    // Call external callback if provided
    onCallback?.(data);
  }, [debug, externalStepIndex, externalRun, onStepChange, steps, markTourAsCompleted, onCallback]);

  // Default styles
  const defaultStyles: Partial<Styles> = {
    options: {
      overlayColor: 'hsl(var(--background) / 0.5)',
      primaryColor: 'hsl(var(--accent))',
      textColor: 'hsl(var(--foreground))',
      width: 340,
      zIndex: 10000,
    },
    tooltip: {
      backgroundColor: 'hsl(var(--card))',
      border: '1px solid hsl(var(--border))',
      borderRadius: 'var(--radius-lg)',
      color: 'hsl(var(--foreground))',
      padding: '16px',
    },
    tooltipContainer: {
      textAlign: 'left',
    },
    tooltipTitle: {
      color: 'hsl(var(--primary))',
      fontSize: '18px',
      fontWeight: 'bold',
      margin: '0 0 8px',
    },
    tooltipContent: {
      color: 'hsl(var(--muted-foreground))',
      fontSize: '14px',
      padding: '0',
    },
    buttonNext: {
      backgroundColor: 'hsl(var(--accent))',
      color: 'hsl(var(--accent-foreground))',
      borderRadius: 'var(--radius-md)',
      fontSize: '14px',
      padding: '8px 16px',
    },
    buttonBack: {
      color: 'hsl(var(--muted-foreground))',
      fontSize: '14px',
      marginRight: '8px',
      padding: '8px 16px',
    },
    buttonSkip: {
      color: 'hsl(var(--muted-foreground))',
      fontSize: '12px',
      textDecoration: 'underline',
    },
    buttonClose: {
      color: 'hsl(var(--muted-foreground))',
      position: 'absolute',
      right: '10px',
      top: '10px',
    },
    spotlight: {
      borderRadius: 'var(--radius-lg)',
    },
  };

  // Merge custom styles with defaults
  const mergedStyles = useMemo(() => {
    if (!customStyles) return defaultStyles;
    
    return {
      ...defaultStyles,
      ...customStyles,
      options: {
        ...defaultStyles.options,
        ...customStyles.options,
      },
    };
  }, [customStyles, defaultStyles]);

  // Default locale
  const defaultLocale = {
    back: 'Back',
    close: 'Close',
    last: 'Finish',
    next: 'Next',
    open: 'Open the dialog',
    skip: 'Skip Tour',
  };

  // Don't render if not ready or no steps
  if (!isReady || !steps || steps.length === 0) {
    return null;
  }

  return (
    <Joyride
      steps={steps}
      run={shouldRun}
      stepIndex={currentStepIndex}
      continuous={continuous}
      showSkipButton={showSkipButton}
      showProgress={showProgress}
      disableOverlay={disableOverlay}
      disableScrolling={disableScrolling}
      hideBackButton={hideBackButton}
      spotlightClicks={spotlightClicks}
      spotlightPadding={spotlightPadding}
      callback={handleJoyrideCallback}
      styles={mergedStyles}
      locale={{ ...defaultLocale, ...locale }}
      debug={debug}
      getHelpers={getHelpers}
    />
  );
};

// Export utility functions for external control
// Utility function to reset tour completion for a specific tour
// Utility function to check if tour is completed
export default OnboardingTour;