import React from "react";

interface CourseCardProps {
  title: string;
  category: string;
  students: number;
  rating: number;
  color: string;
}

const CourseCard: React.FC<CourseCardProps> = ({ title, category, students, rating, color }) => (
  <div className={`${color} rounded-lg p-4`}>
    <div className="flex items-center justify-between mb-2">
      <span className="text-sm flex items-center">
        {category === 'IT & Software' && <span className="mr-2">💻</span>}
        {category === 'Business' && <span className="mr-2">💼</span>}
        {category === 'Media Training' && <span className="mr-2">🎥</span>}
        {category === 'Interior' && <span className="mr-2">🏠</span>}
        {category}
      </span>
      <div className="flex items-center">
        <span className="text-[hsl(var(--warning))] mr-1">★</span>
        <span className="text-sm">{rating.toFixed(1)}</span>
      </div>
    </div>
    <h3 className="text-lg font-semibold mb-2">{title}</h3>
    <div className="flex items-center justify-between">
      <span className="text-sm">{students.toLocaleString()} students</span>
      <div className="flex">
        <img src="/api/placeholder/24/24" alt="User 1" className="w-6 h-6 rounded-full -mr-2" />
        <img src="/api/placeholder/24/24" alt="User 2" className="w-6 h-6 rounded-full" />
      </div>
    </div>
  </div>
);

export default CourseCard;