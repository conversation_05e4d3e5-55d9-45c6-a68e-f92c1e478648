import React from 'react';
import { Sun, Moon } from 'lucide-react';
import { useTheme } from '../contexts/themeContext';
import { Button } from '../shadcn/components/ui/button';

interface ThemeToggleProps {
  size?: 'sm' | 'default' | 'lg';
  showLabel?: boolean;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({
  size = 'default',
  showLabel = false
}) => {
  const { theme, setTheme } = useTheme();

  const getCurrentIcon = () => {
    return theme === 'dark' ? <Moon className="h-4 w-4" /> : <Sun className="h-4 w-4" />;
  };

  const getThemeDisplayName = () => {
    return theme === 'dark' ? 'Dark' : 'Light';
  };

  // Simple toggle between light and dark
  const handleToggle = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  return (
    <Button
      variant="ghost"
      size={size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'icon'}
      onClick={handleToggle}
      className="relative"
      title={`Current theme: ${getThemeDisplayName()}. Click to toggle to ${theme === 'light' ? 'dark' : 'light'} mode.`}
    >
      {getCurrentIcon()}
      {showLabel && (
        <span className="ml-2">{getThemeDisplayName()}</span>
      )}
      <span className="sr-only">Toggle theme</span>
    </Button>
  );
};

export default ThemeToggle;
