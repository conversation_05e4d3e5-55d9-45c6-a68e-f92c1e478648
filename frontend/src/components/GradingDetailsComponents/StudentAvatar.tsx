// components/StudentCard/StudentAvatar.tsx
import React from 'react';
import { getScoreBgClass, getStudentInitials } from '../../utils/gradingUtils';

interface StudentAvatarProps {
    name?: string;
    percentage?: number;
}

export const StudentAvatar: React.FC<StudentAvatarProps> = ({ name, percentage }) => (
    <div className={`w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-accent/10 flex items-center justify-center font-medium text-xs sm:text-sm flex-shrink-0`}>
        {getStudentInitials(name || '')}
    </div>
);
