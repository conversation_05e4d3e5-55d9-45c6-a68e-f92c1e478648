import { formatScore, getScoreColorClass } from '../../utils/gradingUtils';

interface ScoreDisplayProps {
    scores: { totalMarks: number; maxMarks: number; percentage: number } | null;
    hasError: boolean;
    isCompleted: boolean;
}

export const ScoreDisplay: React.FC<ScoreDisplayProps> = ({ scores, hasError, isCompleted }) => {
    if (scores && isCompleted) {
        return (
            <div className="text-left">
                <p className={`font-semibold text-sm sm:text-base ${getScoreColorClass(scores.percentage)}`}>
                    {formatScore(scores.totalMarks)}/{formatScore(scores.maxMarks)}
                </p>
                {/* <p className={`text-xs sm:text-sm ${getScoreColorClass(scores.percentage)}`}>
                    {formatScore(scores.percentage)}%
                </p> */}
            </div>
        );
    }

    return (
        <div className="text-left">
            <p className="text-muted-foreground text-sm">
                {hasError ? 'Error in grading' : 'Not graded yet'}
            </p>
        </div>
    );
};