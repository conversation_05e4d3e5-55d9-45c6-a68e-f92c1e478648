// components/GradingDetails/StatsSidebar.tsx
import React from 'react';
import { ArrowTrendingUpIcon, ChartBarIcon } from '@heroicons/react/24/outline';
import { ClassStats } from '../../types/gradingTypes';

interface StatsSidebarProps {
    stats: ClassStats | null;
}

export const StatsSidebar: React.FC<StatsSidebarProps> = ({ stats }) => {
    if (!stats) {
        return (
            <div className="space-y-4 sm:space-y-6 order-2 lg:order-2">
                <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-3 sm:p-4">
                    <div className="flex items-center gap-2 mb-3">
                        <ChartBarIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0 text-muted-foreground" />
                        <h3 className="font-semibold text-foreground text-sm sm:text-base">
                            Class Stats
                        </h3>
                    </div>
                    <div className="text-center py-4">
                        <p className="text-xs sm:text-sm text-muted-foreground">
                            No graded submissions yet
                        </p>
                    </div>
                </div>
            </div>
        );
    }

    const getPerformanceColor = (percentage: number): string => {
        if (percentage >= 80) return 'text-success-600 dark:text-success-400';
        if (percentage >= 60) return 'text-primary-600 dark:text-primary-400';
        if (percentage >= 40) return 'text-warning-600 dark:text-warning-400';
        return 'text-danger-600 dark:text-danger-400';
    };

    const getPerformanceLabel = (percentage: number): string => {
        if (percentage >= 80) return 'Excellent';
        if (percentage >= 60) return 'Good';
        if (percentage >= 40) return 'Average';
        return 'Needs Improvement';
    };

    return (
        <div className="space-y-4 sm:space-y-6 order-2 lg:order-2">
            <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-3 sm:p-4">
                <h3 className="font-semibold mb-3 flex items-center gap-2 text-foreground text-sm sm:text-base">
                    <ArrowTrendingUpIcon className="h-4 w-4 sm:h-5 sm:w-5 flex-shrink-0" />
                    Class Statistics
                </h3>
                
                <div className="grid grid-cols-2 sm:grid-cols-1 gap-3">
                    <div className="p-2 rounded-md bg-muted/50">
                        <p className="text-xs sm:text-sm text-muted-foreground mb-1">Class Average</p>
                        <p className={`font-bold text-sm sm:text-base ${getPerformanceColor(stats.average)}`}>
                            {stats.average.toFixed(1)}%
                        </p>
                    </div>

                    <div className="p-2 rounded-md bg-success-50 dark:bg-success-900/10">
                        <p className="text-xs sm:text-sm text-muted-foreground mb-1">Highest Score</p>
                        <p className="font-bold text-success-600 dark:text-success-400 text-sm sm:text-base">
                            {stats.highest.toFixed(1)}%
                        </p>
                    </div>

                    <div className="p-2 rounded-md bg-danger-50 dark:bg-danger-900/10">
                        <p className="text-xs sm:text-sm text-muted-foreground mb-1">Lowest Score</p>
                        <p className="font-bold text-danger-600 dark:text-danger-400 text-sm sm:text-base">
                            {stats.lowest.toFixed(1)}%
                        </p>
                    </div>

                    <div className="p-2 rounded-md bg-primary-50 dark:bg-primary-900/10">
                        <p className="text-xs sm:text-sm text-muted-foreground mb-1">Graded</p>
                        <p className="font-bold text-primary-600 dark:text-primary-400 text-sm sm:text-base">
                            {stats.totalStudents}/{stats.totalSubmissions}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    );
};
