import React, { useState } from 'react';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { useUser } from '@/contexts/userContext';
import { toast } from 'react-toastify';

interface RefundButtonProps {
    submissionId: string;
    sheetId: string;
    onRefundSuccess?: () => void;
    className?: string;
}

export const RefundButton: React.FC<RefundButtonProps> = ({
    submissionId,
    sheetId,
    onRefundSuccess,
    className = ""
}) => {
    const axiosPrivate = useAxiosPrivate();
    const { user } = useUser();
    const [isRefunding, setIsRefunding] = useState(false);

    const handleRefundRequest = async () => {
        if (!submissionId || !user?.id) {
            toast.error('Unable to process refund request');
            return;
        }

        setIsRefunding(true);
        try {
            const response = await axiosPrivate.post(`/api/aegisGrader/refunds/${submissionId}`, {
                sheetId: sheetId,
                reason: 'Grading failed'
            });

            toast.success(response.data.message || 'Refund request submitted successfully');
            
            // Call success callback if provided
            if (onRefundSuccess) {
                onRefundSuccess();
            }
        } catch (error: any) {
            console.error('Error requesting refund:', error);
            const errorMessage = error.response?.data?.message || 'Failed to submit refund request';
            toast.error(errorMessage);
        } finally {
            setIsRefunding(false);
        }
    };

    return (
        <button
            onClick={handleRefundRequest}
            disabled={isRefunding}
            className={`px-3 py-2 bg-destructive text-destructive-foreground rounded-md hover:bg-destructive/90 transition-colors text-xs sm:text-sm whitespace-nowrap flex-shrink-0 min-h-[36px] sm:min-h-[40px] disabled:opacity-50 disabled:cursor-not-allowed ${className}`}
        >
            <span className="hidden sm:inline">
                {isRefunding ? 'Requesting...' : 'Request Refund'}
            </span>
            <span className="sm:hidden">
                {isRefunding ? 'Refunding...' : 'Refund'}
            </span>
        </button>
    );
};
