// components/StudentCard/ActionButton.tsx
interface ActionButtonProps {
    onClick: () => void;
    label: string;
    shortLabel: string;
}

export const ActionButton: React.FC<ActionButtonProps> = ({ onClick, label, shortLabel }) => (
    <button
        className="px-3 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors text-xs sm:text-sm whitespace-nowrap flex-shrink-0 min-h-[36px] sm:min-h-[40px]"
        onClick={onClick}
    >
        <span className="hidden sm:inline">{label}</span>
        <span className="sm:hidden">{shortLabel}</span>
    </button>
);