// components/GradingDetails/TestInfo.tsx
import React from 'react';

interface TestInfoProps {
    testDetails: {
        subject: string;
        className: string;
        date: string;
    };
}

export const TestInfo: React.FC<TestInfoProps> = ({ testDetails }) => {
    return (
        <div className="bg-card dark:bg-card rounded-lg shadow-sm border border-border p-4 sm:p-6">
            <h1 className="text-lg sm:text-xl lg:text-2xl font-bold font-['Space_Grotesk'] mb-2 text-foreground break-words">
                {testDetails.subject || 'Unnamed Test'}
            </h1>
            <p className="text-sm sm:text-base text-muted-foreground break-words">
                {testDetails.className} • {testDetails.date}
            </p>
        </div>
    );
};
