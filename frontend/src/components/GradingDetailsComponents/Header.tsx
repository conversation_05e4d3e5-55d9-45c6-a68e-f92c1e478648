// components/GradingDetails/Header.tsx
import React from 'react';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { Status } from '../../types/aegisGrader';
import { StatusBadge } from './StatusBadge';

interface HeaderProps {
    status: Status | string;
    onBack: () => void;
}

export const Header: React.FC<HeaderProps> = ({ status, onBack }) => {
    return (
        <div className="flex items-center justify-between gap-2 sm:gap-4">
            <button
                onClick={onBack}
                className="flex items-center gap-1 sm:gap-2 p-2 rounded-md text-muted-foreground hover:bg-muted transition-all duration-200 min-h-[40px]"
            >
                <ArrowLeftIcon className="w-4 h-4 flex-shrink-0" />
                <span className="text-sm font-medium">Back</span>
            </button>
            <StatusBadge status={status} />
        </div>
    );
};
