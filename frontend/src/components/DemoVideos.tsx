import React, { useState } from 'react';
import ReactPlayer from 'react-player/lazy';
import { motion } from 'framer-motion';
import { useInView } from 'react-intersection-observer';
import { PlayIcon } from '@heroicons/react/24/solid';

// Define the TypeScript interface for the video prop
interface DemoVideo {
  id: number;
  title: string;
  videoId: string;
  description: string;
}

const DemoVideoCard = ({ video }: { video: DemoVideo }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  
  // Intersection Observer to detect when the component is in view
  const { ref, inView } = useInView({
    triggerOnce: false,
    threshold: 0.5
  });
  
  const prefix = 'https://aegisscholar-platform-web.s3.ap-south-1.amazonaws.com/uploads/homepageDemos/';

  // Check if device is mobile when component mounts
  React.useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const handleMouseEnter = () => {
    if (!isMobile) setIsPlaying(true);
  };

  const handleMouseLeave = () => {
    if (!isMobile) setIsPlaying(false);
  };

  const handleCardClick = () => {
    if (isMobile) setIsPlaying(!isPlaying);
  };

  return (
    <motion.div
      ref={ref}
      animate={{ opacity: inView ? 1 : 0 }}
      transition={{ duration: 0.5 }}
      className="overflow-hidden transition-shadow duration-300 bg-white"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onClick={handleCardClick}
    >
      <div className="relative w-full" style={{ paddingTop: '56.25%' }}>
        <ReactPlayer
          url={prefix + video.videoId}
          loop={true}
          width="100%"
          height="100%"
          className="absolute top-0 left-0"
          playing={isPlaying && inView}
          muted={true}
        />
        
        {/* Play icon overlay */}
        {!isPlaying && (
          <div className="absolute inset-0 flex transition-all duration-300 items-center justify-center bg-black/30">
            <PlayIcon width={24} height={24} className="text-white" />
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default DemoVideoCard;
