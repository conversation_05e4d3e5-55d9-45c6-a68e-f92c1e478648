import { History, Clock, Users, ChevronRight, AlertCircle } from 'lucide-react';
import { useEffect, useState, MouseEvent } from 'react';
import { useUser } from '../contexts/userContext';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { useNavigate } from 'react-router-dom';
import { fetchWithCache } from '../utils/cacheUtil';
import { AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline';

interface CompletedTest {
  testId: string;
  testDate: Date;
  startTime: string;
  duration: number;
  _fullTestData?: any; // Optional field for pre-populated test data
}

interface TestDetails {
  id: string;
  subject: string;
  topic: string;
  testDate: Date;
  startTime: string;
  duration: number;
  numberOfQuestions: number;
  totalMarks: number;
  testType: string;
  className: string;
  classStd: string;
  studentsCount: number;
  submissionsCount: number;
}

interface GroupedTestItem extends TestDetails {
    studentCount: number;
    allTestIds: string[];
}

const groupPersonalizedTests = (tests: TestDetails[]): TestDetails[] => {
    const normalTests = tests.filter(test => test.testType !== 'personalized');
    const personalizedTests = tests.filter(test => test.testType === 'personalized');

    // Group personalized tests by date, time, and topic
    const groupedTests = personalizedTests.reduce((acc: { [key: string]: GroupedTestItem }, test) => {
        const key = `${test.testDate.getTime()}_${test.startTime}_${test.topic}_${test.duration}`;

        if (!acc[key]) {
            acc[key] = {
                ...test,
                studentCount: 1,
                allTestIds: [test.id] // Initialize with first test ID
            };
        } else {
            acc[key].studentCount += 1;
            acc[key].allTestIds.push(test.id); // Add this test ID to the group
        }

        return acc;
    }, {});
    console.log("Grouped Personalized Tests:", groupedTests);

    return [...normalTests, ...Object.values(groupedTests)];
};

const TeacherTestHistory = () => {
  const { user } = useUser();
  const axiosPrivate = useAxiosPrivate();
  const [completedTests, setCompletedTests] = useState<CompletedTest[]>([]);
  const [testDetailsMap, setTestDetailsMap] = useState<Record<string, TestDetails>>({});
  const [groupedTests, setGroupedTests] = useState<TestDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [sortOption, setSortOption] = useState<'date-latest' | 'date-oldest' | 'subject-az' | 'subject-za' | 'type' | 'students-desc' | 'students-asc'>('date-latest');
  const [showSortMenu, setShowSortMenu] = useState(false);
  const navigate = useNavigate();

  // Updated getTestDetails function using fetchWithCache
  const getTestDetails = async (testId: string) => {
    try {
      // Validate testId format (should be a valid ObjectId)
      if (!testId || typeof testId !== 'string' || testId.length !== 24) {
        console.warn('Invalid testId format:', testId);
        return null;
      }

      const data = await fetchWithCache(axiosPrivate, `/api/test/${testId}`);
      if (!data) {
        throw new Error('Failed to fetch test details');
      }
      return data;
    } catch (error) {
      console.error('Failed to fetch test details for testId:', testId, error);
      return null;
    }
  };

  // Helper function to format date and time in IST
  const formatDateTimeIST = (date: Date, startTime?: string): string => {
    const testDate = new Date(date);
    if (startTime) {
      const [hours, minutes] = startTime.split(':');
      testDate.setHours(parseInt(hours), parseInt(minutes));
    }
    const options: Intl.DateTimeFormatOptions = {
      timeZone: 'Asia/Kolkata',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
    };
    return new Intl.DateTimeFormat('en-US', options).format(testDate);
  };

  // Check if test is completed (past the test date + duration)
  const isTestCompleted = (testDate: Date, startTime: string, duration: number): boolean => {
    const now = new Date();
    const testDateTime = new Date(testDate);
    const [hours, minutes] = startTime.split(':');
    testDateTime.setHours(parseInt(hours), parseInt(minutes));
    testDateTime.setMinutes(testDateTime.getMinutes() + duration);
    return now > testDateTime;
  };

  // Build completed tests from user test history
  useEffect(() => {
    // console.log("User Test History:", user?.testHistory);
    if (user?.testHistory && user.testHistory.length > 0) {
      const tests = user.testHistory
        .map((test: any) => {
          // Validate test data
          if (!test._id) {
            console.warn('Test missing _id:', test);
            return null;
          }

          // Check if test is already populated (has subject, topics, etc.) or just an ObjectId
          const isPopulated = test.subject && test.testDate && test.startTime;

          if (isPopulated) {
            // Test is already populated, use it directly
            return {
              testId: test._id,
              testDate: new Date(test.testDate),
              startTime: test.startTime,
              duration: test.duration,
              // Store the full test data for immediate use
              _fullTestData: test
            };
          } else {
            // Test is just an ObjectId, we'll need to fetch it
            return {
              testId: test._id || test,
              testDate: new Date(), // Placeholder, will be updated when fetched
              startTime: '00:00',
              duration: 60,
            };
          }
        })
        .filter((test): test is CompletedTest => {
          if (!test) return false;

          // If we have full test data, check if it's completed
          if (test._fullTestData) {
            const isCompleted = isTestCompleted(test.testDate, test.startTime, test.duration);
            return isCompleted;
          }

          // If we don't have full data, we'll check completion after fetching
          return true;
        })
        .sort((a, b) => b.testDate.getTime() - a.testDate.getTime());

      setCompletedTests(tests); // Remove grouping here
      setLoading(false);
    } else {
      setCompletedTests([]);
      setLoading(false);
    }
  }, [user?.testHistory]);

  // Fetch test details for each completed test using the caching helper
  useEffect(() => {
    const fetchTestDetails = async () => {
      const detailsMap: Record<string, TestDetails> = {};

      // Process each test
      await Promise.all(
        completedTests.map(async (test: any) => {
          // Check if we already have full test data
          if (test._fullTestData) {
            const testData = test._fullTestData;

            detailsMap[test.testId] = {
              id: testData._id,
              subject: testData.subject,
              topic: Array.isArray(testData.topics)
                ? testData.topics.join(', ')
                : testData.topics,
              testDate: new Date(testData.testDate),
              startTime: testData.startTime,
              duration: testData.duration,
              numberOfQuestions: testData.numberOfQuestions,
              totalMarks: testData.totalMarks,
              testType: testData.testType,
              className: testData.class?.className || 'Unknown Class',
              classStd: testData.class?.classStd || '',
              studentsCount: testData.class?.students?.length || 0,
              submissionsCount: 0, // TODO: Calculate actual submissions
            };
          } else {
            // Need to fetch test data
            const details = await getTestDetails(test.testId);
            const testData = details && (details as any).test;

            if (testData && testData._id && testData.subject && testData.topics) {
              detailsMap[test.testId] = {
                id: testData._id,
                subject: testData.subject,
                topic: Array.isArray(testData.topics)
                  ? testData.topics.join(', ')
                  : testData.topics,
                testDate: new Date(testData.testDate),
                startTime: testData.startTime,
                duration: testData.duration,
                numberOfQuestions: testData.numberOfQuestions,
                totalMarks: testData.totalMarks,
                testType: testData.testType,
                className: testData.class?.className || 'Unknown Class',
                classStd: testData.class?.classStd || '',
                studentsCount: testData.class?.students?.length || 0,
                submissionsCount: 0, // TODO: Calculate actual submissions
              };
            }
          }
        })
      );

      setTestDetailsMap(detailsMap);
    };

    if (completedTests.length > 0) {
      fetchTestDetails();
    }
  }, [completedTests]);

  // Group personalized tests after all details are fetched
  useEffect(() => {
    if (Object.keys(testDetailsMap).length > 0) {
      const detailsArray = Object.values(testDetailsMap);
      const grouped = groupPersonalizedTests(detailsArray);
      setGroupedTests(grouped);
    } else {
      setGroupedTests([]);
    }
  }, [testDetailsMap]);

  const getStudentCount = (test: TestDetails) => {
    // GroupedTestItem has studentCount, TestDetails has studentsCount
    return (test as any).studentCount ?? test.studentsCount ?? 0;
  };

  // Sorting function
  const sortTests = (tests: TestDetails[]) => {
    switch (sortOption) {
      case 'date-oldest':
        return [...tests].sort((a, b) => a.testDate.getTime() - b.testDate.getTime());
      case 'subject-az':
        return [...tests].sort((a, b) => a.subject.localeCompare(b.subject));
      case 'subject-za':
        return [...tests].sort((a, b) => b.subject.localeCompare(a.subject));
      case 'type':
        return [...tests].sort((a, b) => a.testType.localeCompare(b.testType));
      case 'students-desc':
        return [...tests].sort((a, b) => getStudentCount(b) - getStudentCount(a));
      case 'students-asc':
        return [...tests].sort((a, b) => getStudentCount(a) - getStudentCount(b));
      case 'date-latest':
      default:
        return [...tests].sort((a, b) => b.testDate.getTime() - a.testDate.getTime());
    }
  };

  const handleViewSubmissions = (test: CompletedTest, details?: TestDetails) => {
    navigate('/test-submissions', {
      state: {
        testId: test.testId,
        testDetails: details || test
      }
    });
  };

  // Close sort menu on outside click
  useEffect(() => {
    if (!showSortMenu) return;
    const handleClick = (e: Event) => {
      const menu = document.getElementById('teacher-history-sort-menu');
      if (menu && !menu.contains(e.target as Node)) {
        setShowSortMenu(false);
      }
    };
    document.addEventListener('mousedown', handleClick);
    return () => document.removeEventListener('mousedown', handleClick);
  }, [showSortMenu]);

  if (loading) {
    return (
      <div className="flex flex-col h-full max-h-[350px]">
        <div className="p-2 border-b border-border mb-2 flex justify-between items-center shrink-0">
          <div className="flex items-center">
            <History className="h-4 w-4 mr-2 text-accent" />
            <h2 className="text-lg font-['Space_Grotesk'] font-bold text-primary">Test History</h2>
          </div>
        </div>
        <div className="flex-1 min-h-0 overflow-hidden">
          <div className="h-full overflow-y-auto px-2 scrollbar-thin scrollbar-thumb-muted scrollbar-track-muted/50">
            <div className="flex flex-col gap-3 pb-2">
              {Array.from({ length: 3 }).map((_, index) => (
                <div
                  key={`skeleton-${index}`}
                  className="bg-card rounded-lg border border-border animate-pulse shrink-0"
                >
                  <div className="p-3">
                    <div className="border-b border-border mb-2 pb-2">
                      <div className="flex justify-between items-start gap-2">
                        <div className="flex-1 min-w-0">
                          <div className="h-4 bg-muted-foreground/30 rounded w-20 mb-1"></div>
                          <div className="h-3 bg-muted-foreground/30 rounded w-24 mb-1"></div>
                          <div className="h-4 bg-muted-foreground/30 rounded w-16"></div>
                        </div>
                        <div className="h-4 bg-muted-foreground/30 rounded w-20"></div>
                      </div>
                    </div>
                    <div className="h-3 bg-muted-foreground/30 rounded w-16"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  const sortOptions = [
    { value: 'date-latest', label: 'Date (Latest First)' },
    { value: 'date-oldest', label: 'Date (Oldest First)' },
    { value: 'subject-az', label: 'Subject (A-Z)' },
    { value: 'subject-za', label: 'Subject (Z-A)' },
    { value: 'type', label: 'Test Type' },
    // { value: 'students-desc', label: 'Students (Most First)' },
    // { value: 'students-asc', label: 'Students (Least First)' },
  ];

  return (
    <div className="flex flex-col h-full max-h-[350px]">
      <div className="p-2 border-b border-border mb-2 flex justify-between items-center shrink-0">
        <div className="flex items-center">
          <History className="h-4 w-4 mr-2 text-accent" />
          <h2 className="text-lg font-['Space_Grotesk'] font-bold text-primary">Test History</h2>
        </div>
        <div className="relative">
          <button
            className="flex items-center border border-border rounded-md p-2 bg-background text-foreground focus:outline-none focus:ring-1 focus:ring-accent transition-all duration-150 hover:border-accent"
            onClick={() => setShowSortMenu((prev) => !prev)}
            title="Filter/Sort"
            type="button"
          >
            <AdjustmentsHorizontalIcon className='h-4 w-4' />
          </button>
          {showSortMenu && (
            <div id="recent-activity-sort-menu" className="absolute right-0 mt-2 w-48 bg-card border border-border rounded shadow-lg z-50">
              {sortOptions.map(option => (
                <button
                  key={option.value}
                  className={`w-full text-left px-4 py-2 text-xs hover:bg-accent/10 ${sortOption === option.value ? 'font-bold text-accent' : ''}`}
                  onClick={() => {
                    setSortOption(option.value as typeof sortOption);
                    setShowSortMenu(false);
                  }}
                >
                  {option.label}
                </button>
              ))}
            </div>
          )}
        </div>
      </div>

      <div className="flex-1 min-h-0 overflow-hidden">
        <div className="h-full overflow-y-auto px-2 scrollbar-thin scrollbar-thumb-muted scrollbar-track-muted/50">
          {groupedTests.length > 0 ? (
            <div className="flex flex-col gap-3 pb-2">
              {sortTests(groupedTests).map((details) => {
                return (
                  <div
                    key={details.id}
                    className="bg-card hover:bg-accent/5 rounded-lg border border-border hover:shadow-sm transition-all duration-200 group relative shrink-0"
                  >
                    <div className="p-3">
                      <div className="border-b border-border mb-2 pb-2">
                        <div className="flex justify-between items-start gap-2">
                          <div className="flex-1 min-w-0">
                            <h3 className="text-sm font-['Space_Grotesk'] font-semibold text-primary truncate">
                              {details.subject}
                            </h3>
                            <p className="text-xs text-foreground/70 truncate">
                              {details.topic}
                            </p>
                            <span className={`text-xs px-2 py-0.5 rounded-full mt-1 inline-block ${details.testType === 'personalized'
                                ? 'bg-purple-100 text-purple-600'
                                : details.testType === 'diagnostic'
                                  ? 'bg-blue-100 text-blue-600'
                                  : 'bg-green-100 text-green-600'
                                }`}>
                              {details.testType.charAt(0).toUpperCase() + details.testType.slice(1)}
                            </span>
                          </div>
                          <button
                            onClick={() => handleViewSubmissions({ testId: details.id, testDate: details.testDate, startTime: details.startTime, duration: details.duration }, details)}
                            className="flex items-center text-xs text-accent hover:text-accent/80 shrink-0"
                            disabled={!details}
                          >
                            View Submissions
                            <ChevronRight className="h-3 w-3 ml-1" />
                          </button>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-xs text-muted-foreground">
                        <span className="flex items-center truncate">
                          <Clock className="h-3 w-3 mr-1 shrink-0" />
                          <span className="truncate">
                            {formatDateTimeIST(details.testDate, details.startTime)}
                          </span>
                        </span>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center h-full py-8 px-4 text-center">
              <div className="bg-accent/10 rounded-full p-4 mb-4">
                <AlertCircle className="h-5 w-5 text-accent" />
              </div>
              <h3 className="text-lg font-['Space_Grotesk'] font-medium text-primary mb-2">No Completed Tests</h3>
              <p className="text-sm text-foreground/70 max-w-xs">
                Once your scheduled tests are completed, they will appear here for you to review submissions.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TeacherTestHistory;