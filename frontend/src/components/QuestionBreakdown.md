# QuestionBreakdown Component - Robust Error Handling

## Overview
The QuestionBreakdown component has been completely rewritten to handle edge cases and malformed data robustly, ensuring production-ready reliability.

## Key Improvements

### 1. Input Validation & Sanitization
- **Multiple Input Formats**: Handles arrays, strings, objects, and nested structures
- **Data Sanitization**: Removes control characters, fixes encoding issues
- **Type Safety**: Validates and converts data types with fallbacks

### 2. XML Parsing Robustness
- **Structure Repair**: Automatically fixes common XML malformation issues
- **Escaped Content**: Handles JSON-escaped XML strings (like `\"` and `\\n`)
- **Missing Tags**: Adds missing closing tags and wrapper elements
- **Parser Error Recovery**: Attempts multiple repair strategies before failing

### 3. Data Extraction Resilience
- **Multiple Selectors**: Uses fallback CSS selectors for each data point
- **Graceful Degradation**: Continues processing even if some data is missing
- **Calculated Values**: Derives missing percentages and totals from available data
- **Validation**: Ensures numeric values are within expected ranges

### 4. Error Boundaries & Recovery
- **React Error Boundary**: Catches and handles rendering errors gracefully
- **Detailed Logging**: Provides comprehensive error information for debugging
- **User-Friendly Messages**: Shows helpful error messages instead of crashes
- **Retry Mechanisms**: Allows users to retry failed operations

## Handled Edge Cases

### Input Format Variations
```typescript
// Array format (current)
["<evaluation>...</evaluation>"]

// Direct string
"<evaluation>...</evaluation>"

// Object format (future compatibility)
{ evaluation: "<evaluation>...</evaluation>" }

// Escaped JSON string
"\"<evaluation>\\n...\""
```

### XML Structure Issues
- Unclosed tags: `<question><marks_awarded>5</marks_awarded></evaluation>`
- Nested tags: `<question><question>...</question>`
- Missing wrapper: `<total_marks>100</total_marks>` (without `<evaluation>`)
- Malformed attributes: `<question number="1>` (missing quote)

### Data Quality Issues
- Missing required elements
- Invalid numeric values
- Empty or null content
- Extremely long feedback text
- Inconsistent criteria naming

### Performance Considerations
- Large XML documents (>1MB)
- Many questions (>100)
- Complex nested structures
- Memory-efficient parsing

## API Reference

### Main Functions

#### `parseQuestionBreakdown(evaluationData: any): EvaluationBreakdown | null`
Parses evaluation data with comprehensive error handling.

**Parameters:**
- `evaluationData`: Raw evaluation data in various formats

**Returns:**
- `EvaluationBreakdown` object or `null` if parsing fails

#### `validateEvaluationData(data: any): ValidationResult`
Validates evaluation data and provides detailed feedback.

**Returns:**
```typescript
{
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
```

### Utility Functions

#### `sanitizeNumber(value: any, fallback: number = 0): number`
Safely converts values to positive integers.

#### `sanitizeString(value: any, fallback: string = ''): string`
Safely converts values to trimmed strings.

#### `sanitizePercentage(awarded: number, possible: number): number`
Calculates percentage with bounds checking (0-100%).

## Usage Examples

### Basic Usage
```typescript
import QuestionBreakdownDisplay, { parseQuestionBreakdown } from './QuestionBreakdown';

// Parse data
const evaluationData = parseQuestionBreakdown(rawData);

// Display component
<QuestionBreakdownDisplay evaluationData={evaluationData} />
```

### With Validation
```typescript
import { validateEvaluationData } from './QuestionBreakdown';

const validation = validateEvaluationData(rawData);
if (!validation.isValid) {
  console.error('Validation errors:', validation.errors);
  console.warn('Warnings:', validation.warnings);
}
```

### Error Handling
```typescript
try {
  const result = parseQuestionBreakdown(data);
  if (!result) {
    // Handle parsing failure
    showErrorMessage('Failed to parse evaluation data');
  }
} catch (error) {
  // Handle unexpected errors
  console.error('Unexpected error:', error);
}
```

## Testing

The component includes comprehensive tests covering:
- Valid data parsing
- Edge case handling
- Error boundary functionality
- Performance with large datasets
- Memory leak prevention

Run tests with:
```bash
npm test QuestionBreakdown.test.ts
```

## Production Considerations

### Monitoring
- Log parsing failures for analysis
- Track performance metrics
- Monitor error rates

### Fallbacks
- Graceful degradation when data is incomplete
- Alternative display modes for different data quality levels
- User feedback for data issues

### Security
- Input sanitization prevents XSS
- No eval() or dangerous operations
- Safe XML parsing only

## Migration Guide

The new implementation is backward compatible but provides better error handling:

1. **No Breaking Changes**: Existing usage continues to work
2. **Enhanced Error Messages**: More detailed feedback for debugging
3. **Better Performance**: Optimized parsing for large datasets
4. **Future-Proof**: Supports multiple input formats

## Support

For issues or questions:
1. Check the test file for usage examples
2. Review error logs for specific issues
3. Use validation function to diagnose data problems
4. Contact development team for complex cases
