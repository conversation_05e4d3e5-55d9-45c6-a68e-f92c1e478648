import { Handle, Position, NodeProps } from '@xyflow/react';
import { SubtopicNode } from './types';

export function SubtopicNodeComponent({ data, isConnectable }: NodeProps<SubtopicNode>) {
  return (
    <div className="bg-[hsl(var(--color-node-subtopic-light))] border border-[hsl(var(--color-node-subtopic-border))] rounded-md p-3 w-52 shadow-xs hover: cursor-pointer">
      <div className="flex items-center justify-between">
        <span className="text-sm font-medium text-[hsl(var(--graph-subtopic))] truncate w-full pr-2">
          {data.name}
        </span>
        <span
          className={`text-xs px-2 py-0.5 rounded ${
            data.proficiency > 0
              ? 'bg-[hsl(var(--color-node-subtopic-light))] text-[hsl(var(--graph-subtopic))]'
              : 'bg-[hsl(var(--color-neutral-light))] text-[hsl(var(--color-neutral-foreground))]'
          }`}
        >
          {Number(data.proficiency.toFixed(2))}%
        </span>
      </div>
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-[hsl(var(--graph-subtopic))] rounded-full"
      />
      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-[hsl(var(--graph-subtopic))] rounded-full"
      />
    </div>
  );
}