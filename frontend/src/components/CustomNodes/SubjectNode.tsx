import { Handle, Position, NodeProps } from '@xyflow/react';
import { SubjectNode } from './types';

export function SubjectNodeComponent({ data, isConnectable }: NodeProps<SubjectNode>) {
  return (
    <div className="bg-[hsl(var(--color-node-subject-light))] border border-[hsl(var(--color-node-subject-border))] rounded-lg p-4 w-64 shadow-xs hover: cursor-pointer">
      <div className="flex justify-between items-start mb-2">
        <h3 className="text-lg font-semibold text-[hsl(var(--graph-subject))]">{data.name}</h3>
        <span className="text-sm text-[hsl(var(--graph-subject))] bg-[hsl(var(--color-node-subject-light))] border border-[hsl(var(--color-node-subject-border))] rounded px-2 py-1">
          {data.topics.length} topics
        </span>
      </div>
      {data.description && (
        <p className="text-sm text-[hsl(var(--graph-subject))] opacity-80">{data.description}</p>
      )}
      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-[hsl(var(--graph-subject))] rounded-full"
      />
      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-[hsl(var(--graph-subject))] rounded-full"
      />
    </div>
  );
}