import { Handle, Position, NodeProps } from '@xyflow/react';
import { ChevronDown, ChevronRight } from 'lucide-react';
import { TopicNode } from './types';

export function TopicNodeComponent({ data, isConnectable }: NodeProps<TopicNode>) {
  const ChevronIcon = data.isExpanded ? ChevronDown : ChevronRight;

  return (
    <div className="min-w-[260px] bg-[hsl(var(--color-node-chapter-light))] border border-[hsl(var(--color-node-chapter-border))] rounded-md shadow-xs group hover: cursor-pointer">
      <div className="p-3 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <ChevronIcon className="w-4 h-4 text-[hsl(var(--graph-chapter))] group-hover:text-[hsl(var(--graph-chapter))] transition-colors" />
          <span className="text-lg font-medium text-[hsl(var(--graph-chapter))]">
            {data.name}
          </span>
        </div>
      </div>

      <Handle
        type="target"
        position={Position.Top}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-[hsl(var(--graph-chapter))] rounded-full"
      />
      <Handle
        type="source"
        position={Position.Bottom}
        isConnectable={isConnectable}
        className="w-2 h-2 bg-[hsl(var(--graph-chapter))] rounded-full"
      />
    </div>
  );
}