import { LabeledGroupNode } from '../../shadcn/components/labeled-group-node';
import { SubjectNodeComponent } from './SubjectNode';
import { SubtopicNodeComponent } from './SubtopicNode';
import { TopicNodeComponent } from './TopicNode';
export * from './types';

export const nodeTypes = {
  subject: SubjectNodeComponent,
  chapter: TopicNodeComponent,
  subtopic: SubtopicNodeComponent,
  labeledGroupNode: LabeledGroupNode,
};