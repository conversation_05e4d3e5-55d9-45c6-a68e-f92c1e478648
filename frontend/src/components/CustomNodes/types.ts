import { Node } from '@xyflow/react';

export type TopicToSubtopicsMap = Record<string, string[]>;

export type SubjectNode = Node<{
  name: string; // Subject name (e.g., "Mathematics")
  description?: string; // Optional description of the subject
  topics: string[]; // Array of Topic IDs
}, 'subject'>;


export type TopicNode = Node<{
  name: string;
  description: string;
  order: number;
  children?: string[]; // Array of Subtopic IDs or Node IDs
  parents?: string[]; // Optional array of parent IDs if needed
  proficiency: number; // Student's proficiency in the topic
  isExpanded: boolean; // Whether the topic is expanded
  status: string; // Progress status (e.g., "Not Started", "In Progress", "Completed")
}, 'chapter'>;

export type SubtopicNode = Node<{
  name: string;
  description: string;
  children?: string[]; // Array of Subtopic IDs or Node IDs
  parents: string[]; // Array of Topic IDs or Node IDs
  proficiency: number; // Student's proficiency in the subtopic
  status: string; // Progress status
  isHidden: boolean; // Whether the subtopic is hidden
}, 'subtopic'>;


export type CustomNode = SubjectNode | TopicNode | SubtopicNode;

