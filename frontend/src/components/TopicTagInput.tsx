import { useState, KeyboardEvent, ChangeEvent, useEffect } from 'react';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { X } from 'lucide-react';


interface ScheduleTestForm {
  class: string;
  subject: string;
  topics: string[];
  date: string;
  startTime: string;
  duration: number;
  numberOfQuestions: number;
  totalMarks: number;
  passingMarks: number;
  instructions: string;
  testType: string;
}

interface TopicTagsInputProps {
  value: string[];
  onChange: (topics: string[]) => void;
  disabled: boolean;
  topicList: string[];
  // scheduleForm: ScheduleTestForm;
}

const TopicTagsInput = ({ value = [], onChange, disabled, topicList=[]}: TopicTagsInputProps) => {
  const [inputValue, setInputValue] = useState<string>('');
  // const axiosPrivate = useAxiosPrivate();
  // const [suggestions] = useState<string[]>([
  //   "Real Numbers",
  //   "Polynomials",
  //   "Pair of Linear Equations in Two Variables",
  //   "Quadratic Equations",
  //   "Arithmetic Progressions",
  //   "Triangles",
  //   "Coordinate Geometry",
  //   "Introduction to Trigonometry",
  //   "Some Applications of Trigonometry",
  //   "Circles",
  //   "Constructions",
  //   "Areas Related to Circles",
  //   "Surface Areas and Volumes",
  //   "Statistics",
  //   "Probability"
  // ]);
  //

  // const [suggestions] = useState<string[]>(topicList);
  // console.error(`[INFO] got suggestions list: ${suggestions}`);
  // const [loadedSugg, setLoadedSugg] = useState<boolean>(false);

  // useEffect(()=> {
  //   // fetch the suggestions the first chance you get
  //   console.error(`[INFO] fetching the topic list`);
  //   fetchSuggestions();
  // }, []);
  // useEffect(() => {
  //   console.error(`[INFO] suggestion list is: ${suggestions}`);
  //   // setLoadedSugg(true);
  // }, [suggestions])

  const [showSuggestions, setShowSuggestions] = useState<boolean>(false);

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value;
    setInputValue(input);
    // setShowSuggestions(input.length > 0 && loadedSugg);
    setShowSuggestions(input.length > 0);
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && inputValue.trim()) {
      e.preventDefault();
      if (!value.includes(inputValue.trim())) {
        onChange([...value, inputValue.trim()]);
      }
      setInputValue('');
      setShowSuggestions(false);
    } else if (e.key === 'Backspace' && !inputValue && value.length > 0) {
      onChange(value.slice(0, -1));
    }
  };

  const addTag = (tag: string) => {
    if (!value.includes(tag)) {
      onChange([...value, tag]);
    }
    setInputValue('');
    setShowSuggestions(false);
  };

  const removeTag = (tagToRemove: string) => {
    onChange(value.filter(tag => tag !== tagToRemove));
  };

  const filteredSuggestions = topicList.filter(
    suggestion => suggestion.toLowerCase().includes(inputValue.toLowerCase())
  );

  return (
    <div className="w-full">
      <div className="min-h-[42px] w-full rounded border border-input p-1 flex flex-wrap gap-2 focus-within:ring-1 focus-within:ring-primary focus-within:border-primary dark:bg-background">
        {value.map((tag, index) => (
          <span
            key={index}
            className="inline-flex items-center bg-primary/10 text-primary dark:bg-accent/20 dark:text-accent text-xs px-2 py-1 rounded"
          >
            {tag}
            <button
              type="button"
              onClick={() => removeTag(tag)}
              className="ml-1 hover:text-primary-foreground dark:hover:text-accent-foreground"
            >
              <X className="h-3 w-3" />
            </button>
          </span>
        ))}
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          className="flex-1 min-w-[120px] p-1 text-sm focus:outline-hidden bg-transparent text-primary dark:text-foreground placeholder:text-muted-foreground/70"
          disabled={disabled}
          placeholder={(value.length === 0 && !disabled)? "Type to add topics..." : (value.length === 0 && disabled)? "Topics are automatically selected for this test type": ""}
        />
      </div>

      {showSuggestions && filteredSuggestions.length > 0 && (
        <div className="absolute mt-1 w-80 max-h-48 overflow-auto bg-background dark:bg-popover border border-input dark:border-input rounded-md shadow-lg z-10">
          {filteredSuggestions.map((suggestion, index) => (
            <button
              key={index}
              className="w-full text-left px-4 py-2 text-sm text-primary hover:bg-primary/5 dark:text-foreground dark:hover:bg-accent/20 focus:bg-primary/5 dark:focus:bg-accent/20 focus:outline-hidden"
              onClick={() => addTag(suggestion)}
            >
              {suggestion}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default TopicTagsInput;
