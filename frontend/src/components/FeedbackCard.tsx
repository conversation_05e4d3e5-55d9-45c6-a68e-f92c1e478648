import React from 'react';
import { ThumbsUp, ThumbsDown, Bug, MessageCircle, User, Calendar} from 'lucide-react';


export const FeedbackCard = (feed: any) => {
  console.error(`[INFO] Rendering FeedbackCard for feed: ${JSON.stringify(feed)}`);
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getFeedbackIcon = (feedtype: string) => {
    switch (feedtype.toLowerCase()) {
      case 'like':
        return <ThumbsUp className="w-5 h-5 text-green-500" />;
      case 'dislike':
        return <ThumbsDown className="w-5 h-5 text-red-500" />;
      case 'bug':
        return <Bug className="w-5 h-5 text-orange-500" />;
      default:
        return <MessageCircle className="w-5 h-5 text-purple-500" />;
    }
  }

  const getFeedbackColor = (feedtype: string) => {
    switch (feedtype.toLowerCase()) {
      case 'like':
        return 'bg-green-50 border-green-200';
      case 'dislike':
        return 'bg-red-50 border-red-200';
      case 'bug':
        return 'bg-orange-50 border-orange-200';
      default:
        return 'bg-purple-50 border-purple-200';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6 hover:shadow-lg transition-shadow duration-200">
      {/*Header with feedback type and role of user*/}
      <div className="flex items-center justify-between mb-4">
        <div className={`flex items-center gap-2 px-3 py-1 rounded-full ${getFeedbackColor(feed.feedbackType)}`}>
          {getFeedbackIcon(feed.feedbackType)}
          <span className="text-sm font-medium capitalize">{feed.feedbackType}</span>
        </div>
        <div className="flex items-center gap-1 text-sm text-gray-600">
          <User className="w-4 h-4" />
          <span className="font-medium">{feed.role}</span>
        </div>
      </div>

      {/*Comment*/}
      <div className="mb-4">
        <p className="text-gray-800 leading-relaxed">
          {feed.comment || "No comment provided"}
        </p>
      </div>

      {/* Displaying images */}
      {feed.images && feed.images.length > 0 && (
        <div className="mt-4">
          <h3 className="text-md font-semibold text-gray-700 mb-2">Attached Images:</h3>
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {feed.images.map((img: any, imgIndex: any) => (
              <div key={imgIndex} className="relative w-full h-32 overflow-hidden rounded-md shadow-sm border border-gray-300">
                {/* The magic happens here: use img.imageData as the src */}
                <img
                  src={img.imageData} // This is the Base64 Data URL
                  alt={img.filename || `Feedback Image ${imgIndex + 1}`}
                  className="w-full h-full object-cover" // object-cover ensures image fills the box
                />
                {/* Optional: Overlay filename on hover */}
                <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center text-white text-xs opacity-0 hover:opacity-100 transition-opacity duration-200">
                  <span>{img.filename}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/*Footer with timestamp and user id*/}
      <div className="flex items-center justify-between text-sm text-gray-500 pt-4 border-t border-gray-100">
        <div className="flex items-center gap-1">
          <Calendar className="w-4 h-4" />
          <span> {formatDate(feed.createdAt)}</span>
        </div>
        <div className="text-xs font-mono bg-gray-100 px-2 py-1 rounded">
          ID: {feed.userId?.slice(-8) || 'Unknown'}
        </div>
      </div>
    </div>
  );

}

