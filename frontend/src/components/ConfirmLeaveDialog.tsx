import { AlertTriangle } from 'lucide-react';
import React from 'react';

interface ConfirmLeaveDialogProps {
  show: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmLeaveDialog: React.FC<ConfirmLeaveDialogProps> = ({ show, onConfirm, onCancel }) => {
  if (!show) {
    return null; // Don't render the dialog if 'show' is false
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center backdrop-blur-sm bg-opacity-50 z-50">
      <div className="bg-white border border-gray-200 p-6 rounded-lg shadow-2xl max-w-md w-full mx-4">
        <div className='flex items-center gap-3 mb-4'>
          <AlertTriangle className='h-6 w-6 text-yellow-500' />
          <h2 className="text-xl font-semibold text-gray-800">Exit Test</h2>
        </div>

        <p className="mb-6 text-gray-600">Your test will be submitted with your current responses if you exit now.</p>
        <div className="flex justify-end space-x-4">
          <button
            className="text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            onClick={onCancel}
          >
            Cancel
          </button>
          <button
            className="bg-accent text-white px-6 py-2 rounded-lg hover:bg-accent-dark transition-colors duration-200"
            onClick={onConfirm}
          >
            Submit & Exit
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmLeaveDialog;