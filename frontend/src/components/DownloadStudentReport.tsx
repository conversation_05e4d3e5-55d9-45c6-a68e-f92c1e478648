import React, { useState } from 'react';
import { PDFDocument, rgb } from 'pdf-lib';
import { ArrowDownTrayIcon } from '@heroicons/react/24/outline';

// Type definitions
interface CriterionBreakdown {
  criterion: string;
  score: string;
  maxScore?: string;
}

interface FeedbackSection {
  title: string;
  content: string;
  subsections?: { title: string; content: string }[];
}

interface QuestionBreakdown {
  questionNumber: string;
  marksAwarded: number;
  marksPossible: number;
  percentage: number;
  feedback: string;
  structuredFeedback?: FeedbackSection[];
  criteriaBreakdown: CriterionBreakdown[];
}

interface EvaluationBreakdown {
  totalMarks: number;
  maxMarks: number;
  overallPercentage: number;
  questions: QuestionBreakdown[];
}

interface SubmissionData {
  studentName: string;
  rollNumber: string;
  totalMarks: number;
  maxMarks: number;
  percentage: number;
  pdfUrl?: string;
  detailedBreakdown: EvaluationBreakdown;
  subjectName?: string;
}

interface DownloadStudentReportProps {
  submissionData: SubmissionData;
  breakdownContentRef: React.RefObject<HTMLDivElement>;
}

const DownloadStudentReport: React.FC<DownloadStudentReportProps> = ({ submissionData, breakdownContentRef }) => {
  const [isDownloading, setIsDownloading] = useState(false);

  const downloadCombinedReport = async () => {
    console.log('Download button clicked!');
    console.log('Submission data:', submissionData);
    console.log('Breakdown ref current:', breakdownContentRef.current);

    if (!breakdownContentRef.current) {
      console.error('Breakdown content ref is not available');
      return;
    }

    setIsDownloading(true);
    console.log('Starting download process...');

    try {
      // Always create feedback-only PDF for now
      console.log('Creating feedback report PDF...');
      await createFeedbackOnlyPDF();
      console.log('Download completed successfully');
    } catch (error) {
      console.error('Error generating report:', error);
      alert('Failed to generate PDF report. Please try again.');
    } finally {
      setIsDownloading(false);
      console.log('Download process finished');
    }
  };



  const createFeedbackOnlyPDF = async () => {
    try {
      console.log('Starting createFeedbackOnlyPDF...');
      console.log('Submission data:', submissionData);

      // Use the evaluation data directly (it's already parsed)
      console.log('Raw detailedBreakdown:', submissionData.detailedBreakdown);

      const evaluationData = submissionData.detailedBreakdown;
      console.log('Using evaluation data directly:', evaluationData);

      if (!evaluationData) {
        throw new Error('No evaluation data available');
      }

      if (!evaluationData.questions || evaluationData.questions.length === 0) {
        throw new Error('No questions found in evaluation data');
      }

      // Create new PDF document with portrait orientation
      const pdfDoc = await PDFDocument.create();
      let currentPage = pdfDoc.addPage([595.28, 841.89]); // A4 portrait (width, height)
      const { width: pageWidth, height: pageHeight } = currentPage.getSize();
      let currentY = pageHeight - 50;

      const margin = 50;
      const lineHeight = 14;
      const sectionSpacing = 20;

      // Helper function to add new page
      const addNewPage = () => {
        console.log('Adding new page, currentY was:', currentY);
        currentPage = pdfDoc.addPage([595.28, 841.89]); // A4 portrait
        currentY = pageHeight - 50;
        console.log('New page created, currentY now:', currentY);
      };

      // Helper function to draw a single line of text
      const drawSingleLine = (text: string, x: number, y: number, fontSize: number, color = rgb(0, 0, 0)) => {
        // Safety checks
        if (!text || typeof text !== 'string') {
          return y;
        }

        // Check if we need a new page
        if (y < margin + lineHeight) {
          addNewPage();
          y = currentY;
        }

        try {
          // More gentle text cleaning - preserve most characters
          const cleanText = text
            .replace(/[\x00-\x1F\x7F]/g, '') // Remove only control characters
            .replace(/[""]/g, '"') // Replace smart quotes
            .replace(/['']/g, "'") // Replace smart apostrophes
            .trim();

          if (cleanText) {
            currentPage.drawText(cleanText, { x, y, size: fontSize, color });
          }
        } catch (error) {
          console.error('Error drawing text:', text, error);
          try {
            // Fallback with basic character replacement
            const fallbackText = text
              .replace(/[^\x20-\x7E]/g, ' ') // Replace non-ASCII with spaces instead of ?
              .replace(/\s+/g, ' ') // Collapse multiple spaces
              .trim();
            if (fallbackText) {
              currentPage.drawText(fallbackText, { x, y, size: fontSize, color });
            }
          } catch (fallbackError) {
            console.error('Fallback text drawing also failed:', fallbackError);
          }
        }

        return y;
      };

      // Helper function to draw text with word wrapping
      const drawWrappedText = (text: string, x: number, maxWidth: number, fontSize: number, color = rgb(0, 0, 0)) => {
        // Safety check for input parameters
        if (!text || typeof text !== 'string') {
          currentY -= lineHeight;
          return;
        }

        // Clean the text first - more gentle approach
        const cleanText = text
          .replace(/[\x00-\x1F\x7F]/g, '') // Remove only control characters
          .replace(/[""]/g, '"') // Replace smart quotes
          .replace(/['']/g, "'") // Replace smart apostrophes
          .replace(/\s+/g, ' ') // Replace multiple spaces with single space
          .trim();

        if (!cleanText) {
          currentY -= lineHeight;
          return;
        }

        const words = cleanText.split(' ');
        let line = '';
        const charWidth = fontSize * 0.55; // More accurate character width estimation

        for (const word of words) {
          const testLine = line + (line ? ' ' : '') + word;
          const textWidth = testLine.length * charWidth;

          if (textWidth > maxWidth && line) {
            // Draw the current line
            currentY = drawSingleLine(line, x, currentY, fontSize, color);
            currentY -= lineHeight;
            line = word;
          } else {
            line = testLine;
          }
        }

        // Draw the last line if there's content
        if (line) {
          currentY = drawSingleLine(line, x, currentY, fontSize, color);
          currentY -= lineHeight;
        }
      };

      // Add header
      currentY = drawSingleLine('Detailed Feedback Report', margin, currentY, 18, rgb(0, 0, 0));
      currentY -= sectionSpacing;

      // Student information
      currentY = drawSingleLine(`Student: ${submissionData.studentName}`, margin, currentY, 14, rgb(0, 0, 0));
      currentY -= lineHeight;

      currentY = drawSingleLine(`Roll Number: ${submissionData.rollNumber}`, margin, currentY, 12, rgb(0.3, 0.3, 0.3));
      currentY -= lineHeight;

      currentY = drawSingleLine(`Subject: ${submissionData.subjectName || 'N/A'}`, margin, currentY, 12, rgb(0.3, 0.3, 0.3));
      currentY -= lineHeight;

      currentY = drawSingleLine(`Score: ${submissionData.totalMarks}/${submissionData.maxMarks} (${submissionData.percentage}%)`, margin, currentY, 12, rgb(0.3, 0.3, 0.3));
      currentY -= lineHeight;

      currentY = drawSingleLine(`Date: ${new Date().toLocaleDateString()}`, margin, currentY, 12, rgb(0.3, 0.3, 0.3));
      currentY -= sectionSpacing;

      // Add questions breakdown
      evaluationData.questions.forEach((question, questionIndex) => {
        try {
          console.log(`Processing question ${questionIndex + 1}:`, question.questionNumber);

          // Check if we need space for question header
          if (currentY < margin + lineHeight * 4) {
            addNewPage();
          }

          // Question header
          const questionTitle = question.questionNumber || `Question ${questionIndex + 1}`;
          currentY = drawSingleLine(questionTitle, margin, currentY, 14, rgb(0, 0, 0));

          // Score on the same line (right side)
          const scoreText = `${question.marksAwarded || 0}/${question.marksPossible || 0} marks (${question.percentage || 0}%)`;
          drawSingleLine(scoreText, pageWidth - 200, currentY + lineHeight, 12, rgb(0.3, 0.3, 0.3));
          currentY -= lineHeight + 5;

        // Marking criteria
        if (question.criteriaBreakdown.length > 0) {
          if (currentY < margin + lineHeight * 3) {
            addNewPage();
          }

          currentY = drawSingleLine('Marking Criteria:', margin + 15, currentY, 11, rgb(0, 0, 0));
          currentY -= lineHeight * 0.8;

          question.criteriaBreakdown.forEach((criterion, criterionIndex) => {
            try {
              if (currentY < margin + lineHeight * 2) {
                addNewPage();
              }

              // Calculate percentage for criterion
              const score = parseFloat(criterion.score) || 0;
              const maxScore = parseFloat(criterion.maxScore || '0') || 0;
              const percentage = maxScore > 0 ? Math.round((score / maxScore) * 100) : 0;

              // Check if maxScore is available and valid
              const hasValidMaxScore = criterion.maxScore && criterion.maxScore !== '0' && criterion.maxScore !== 'undefined' && criterion.maxScore !== '?';

              // Format criterion with score and percentage - only show maxScore if it's valid
              const scoreText = hasValidMaxScore
                ? `${criterion.score}/${criterion.maxScore}${maxScore > 0 ? ` (${percentage}%)` : ''}`
                : criterion.score;

              // Draw criterion name (simplified approach - always use wrapped text for consistency)
              const maxCriterionWidth = pageWidth - margin - 200; // Leave space for score

              // Ensure criterion name exists
              const criterionName = criterion.criterion || `Criterion ${criterionIndex + 1}`;

              // Check if criterion text is very long (simple character count check)
              if (criterionName.length > 70) {
                // Use wrapped text for long criteria names
                drawWrappedText(`• ${criterionName}`, margin + 30, maxCriterionWidth, 9, rgb(0.2, 0.2, 0.2));
                currentY -= lineHeight * 0.5;
                // Draw score on next line, indented
                currentY = drawSingleLine(`   ${scoreText}`, margin + 50, currentY, 9, rgb(0.4, 0.4, 0.4));
              } else {
                // Draw criterion name and score on same line
                currentY = drawSingleLine(`• ${criterionName}`, margin + 30, currentY, 9, rgb(0.2, 0.2, 0.2));
                drawSingleLine(scoreText, pageWidth - 120, currentY + lineHeight, 9, rgb(0.4, 0.4, 0.4));
              }
              currentY -= lineHeight;
            } catch (criterionError) {
              console.error(`Error drawing criterion ${criterionIndex}:`, criterionError);
              // Continue with next criterion
              currentY -= lineHeight; // Still advance to avoid infinite loop
            }
          });

          currentY -= lineHeight * 0.5; // Extra space after criteria
        }

        // Feedback - Handle both structured and plain feedback
        if (question.feedback || (question.structuredFeedback && question.structuredFeedback.length > 0)) {
          console.log(`Question ${question.questionNumber} feedback:`, question.feedback);
          console.log(`Question ${question.questionNumber} structured feedback:`, question.structuredFeedback);

          if (currentY < margin + lineHeight * 3) {
            addNewPage();
          }

          currentY = drawSingleLine('Detailed Feedback:', margin + 15, currentY, 11, rgb(0, 0, 0));
          currentY -= lineHeight * 0.8;

          // Use structured feedback if available, otherwise fall back to plain feedback
          try {
            if (question.structuredFeedback && question.structuredFeedback.length > 0) {
              console.log('Drawing structured feedback for question:', question.questionNumber);
              // Draw structured feedback with proper formatting
              question.structuredFeedback.forEach((section, sectionIndex) => {
                try {
                  // Check if we need a new page for the section
                  if (currentY < margin + lineHeight * 4) {
                    addNewPage();
                  }

                  // Section title
                  if (section.title) {
                    currentY = drawSingleLine(section.title, margin + 30, currentY, 10, rgb(0, 0, 0));
                    currentY -= lineHeight;
                  }

                  // Section content
                  if (section.content) {
                    drawWrappedText(section.content, margin + 50, pageWidth - margin - 80, 9, rgb(0.4, 0.4, 0.4));
                    currentY -= lineHeight * 0.5;
                  }

                  // Subsections
                  if (section.subsections && section.subsections.length > 0) {
                    section.subsections.forEach((subsection, subIndex) => {
                      try {
                        if (currentY < margin + lineHeight * 2) {
                          addNewPage();
                        }

                        // Subsection title
                        if (subsection.title) {
                          currentY = drawSingleLine(`• ${subsection.title}`, margin + 60, currentY, 9, rgb(0.2, 0.2, 0.2));
                          currentY -= lineHeight * 0.8;
                        }

                        // Subsection content
                        if (subsection.content) {
                          drawWrappedText(subsection.content, margin + 80, pageWidth - margin - 100, 8, rgb(0.5, 0.5, 0.5));
                          currentY -= lineHeight * 0.3; // Less spacing for subsections
                        }
                      } catch (subError) {
                        console.error(`Error drawing subsection ${subIndex}:`, subError);
                        // Continue with next subsection
                      }
                    });
                  }

                  currentY -= lineHeight * 0.8; // Space between sections
                } catch (sectionError) {
                  console.error(`Error drawing section ${sectionIndex}:`, sectionError);
                  // Continue with next section
                }
              });
            } else if (question.feedback) {
              console.log('Drawing plain feedback for question:', question.questionNumber);
              // Fall back to plain feedback
              drawWrappedText(question.feedback, margin + 30, pageWidth - margin - 60, 9, rgb(0.3, 0.3, 0.3));
            }
          } catch (feedbackError) {
            console.error('Error drawing feedback:', feedbackError);
            // Try to draw a simple error message
            try {
              currentY = drawSingleLine('Error displaying feedback', margin + 40, currentY, 10, rgb(0.5, 0, 0));
              currentY -= lineHeight;
            } catch (errorMsgError) {
              console.error('Error drawing error message:', errorMsgError);
            }
          }
        }

        // Add spacing between questions
        currentY -= sectionSpacing;

          // Ensure we don't start a new question too close to the bottom
          if (currentY < margin + 100) {
            addNewPage();
          }
        } catch (questionError) {
          console.error(`Error processing question ${questionIndex + 1}:`, questionError);
          // Add a simple error message and continue
          try {
            currentY = drawSingleLine(`Error processing question ${questionIndex + 1}`, margin, currentY, 12, rgb(0.5, 0, 0));
            currentY -= sectionSpacing;
          } catch (errorMsgError) {
            console.error('Error drawing error message:', errorMsgError);
          }
        }
      });

      // Save and download
      const pdfBytes = await pdfDoc.save();
      const blob = new Blob([pdfBytes], { type: 'application/pdf' });

      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${submissionData.studentName.replace(/\s+/g, '_')}_Feedback_Report_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      console.log('PDF created and downloaded successfully');

    } catch (error) {
      console.error('Error creating feedback PDF:', error);
      throw error;
    }
  };

  return (
    <div className="download-report-container">
      <button
            onClick={downloadCombinedReport}
            disabled={isDownloading}
            className="flex items-center gap-1 lg:gap-2 px-2 lg:px-3 py-2 text-xs lg:text-sm font-medium text-foreground bg-muted hover:bg-accent border border-border rounded-md transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Download Student Report"
        >
            <ArrowDownTrayIcon className="w-3 lg:w-4 h-3 lg:h-4" />
            <span className="inline">{isDownloading ? 'Generating...' : 'Download Report'}</span>
        </button>
      
      {/* {submissionData.pdfUrl && (
        <p className="report-info">
          This will combine the original answer sheet with detailed feedback.
        </p>
      )} */}
    </div>
  );
};

export default DownloadStudentReport;
