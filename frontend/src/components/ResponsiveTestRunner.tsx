import React, { useState, useRef } from 'react';
import { 
  runResponsiveTests, 
  generateResponsiveReport, 
  RESPONSIVE_BREAKPOINTS,
  ResponsiveBreakpoint 
} from '../utils/responsiveTestUtils';

interface ResponsiveTestRunnerProps {
  targetComponent?: string;
}

export const ResponsiveTestRunner: React.FC<ResponsiveTestRunnerProps> = ({ 
  targetComponent = 'body' 
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<string>('');
  const [selectedBreakpoint, setSelectedBreakpoint] = useState<ResponsiveBreakpoint | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const runTests = async () => {
    setIsRunning(true);
    setResults('');

    try {
      const container = targetComponent === 'body' 
        ? document.body 
        : document.querySelector(targetComponent) as HTMLElement;

      if (!container) {
        setResults('Error: Target container not found');
        return;
      }

      const testResults = await runResponsiveTests(container);
      const report = generateResponsiveReport(testResults);
      setResults(report);
    } catch (error) {
      setResults(`Error running tests: ${error}`);
    } finally {
      setIsRunning(false);
    }
  };

  const simulateBreakpoint = (breakpoint: ResponsiveBreakpoint) => {
    setSelectedBreakpoint(breakpoint);
    
    // Update CSS custom properties for testing
    document.documentElement.style.setProperty('--test-width', `${breakpoint.width}px`);
    document.documentElement.style.setProperty('--test-height', `${breakpoint.height}px`);
    
    // Simulate viewport change
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: breakpoint.width,
    });
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: breakpoint.height,
    });

    window.dispatchEvent(new Event('resize'));
  };

  const resetViewport = () => {
    setSelectedBreakpoint(null);
    document.documentElement.style.removeProperty('--test-width');
    document.documentElement.style.removeProperty('--test-height');
    
    // Reset to actual viewport
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: window.screen.width,
    });
    Object.defineProperty(window, 'innerHeight', {
      writable: true,
      configurable: true,
      value: window.screen.height,
    });

    window.dispatchEvent(new Event('resize'));
  };

  const downloadReport = () => {
    if (!results) return;

    const blob = new Blob([results], { type: 'text/markdown' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `responsive-test-report-${new Date().toISOString().split('T')[0]}.md`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div ref={containerRef} className="fixed bottom-4 right-4 z-50 bg-card border border-border rounded-lg shadow-lg p-4 max-w-sm">
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-foreground">Responsive Tester</h3>
          <button
            onClick={() => containerRef.current?.remove()}
            className="text-muted-foreground hover:text-foreground p-1"
          >
            ✕
          </button>
        </div>

        {/* Breakpoint Simulator */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-muted-foreground">Simulate Breakpoint:</label>
          <select
            value={selectedBreakpoint?.name || ''}
            onChange={(e) => {
              const breakpoint = RESPONSIVE_BREAKPOINTS.find(bp => bp.name === e.target.value);
              if (breakpoint) {
                simulateBreakpoint(breakpoint);
              } else {
                resetViewport();
              }
            }}
            className="w-full px-2 py-1 text-xs border border-border rounded bg-background"
          >
            <option value="">Current Viewport</option>
            {RESPONSIVE_BREAKPOINTS.map(bp => (
              <option key={bp.name} value={bp.name}>
                {bp.description} ({bp.width}×{bp.height})
              </option>
            ))}
          </select>
        </div>

        {/* Current Viewport Info */}
        <div className="text-xs text-muted-foreground">
          Current: {window.innerWidth}×{window.innerHeight}px
          {selectedBreakpoint && (
            <div className="text-primary">
              Simulating: {selectedBreakpoint.description}
            </div>
          )}
        </div>

        {/* Test Runner */}
        <div className="space-y-2">
          <button
            onClick={runTests}
            disabled={isRunning}
            className="w-full px-3 py-2 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90 disabled:opacity-50"
          >
            {isRunning ? 'Running Tests...' : 'Run Responsive Tests'}
          </button>

          {results && (
            <button
              onClick={downloadReport}
              className="w-full px-3 py-2 text-xs bg-secondary text-secondary-foreground rounded hover:bg-secondary/90"
            >
              Download Report
            </button>
          )}
        </div>

        {/* Quick Results */}
        {results && (
          <div className="max-h-32 overflow-y-auto text-xs">
            <div className="text-muted-foreground">
              {results.split('\n').slice(0, 10).join('\n')}
              {results.split('\n').length > 10 && '\n... (download for full report)'}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Development helper to add the test runner to any page
export const addResponsiveTestRunner = (targetComponent?: string) => {
  if (process.env.NODE_ENV !== 'development') return;

  const existingTester = document.getElementById('responsive-test-runner');
  if (existingTester) return;

  const container = document.createElement('div');
  container.id = 'responsive-test-runner';
  document.body.appendChild(container);

  import('react-dom/client').then(({ createRoot }) => {
    const root = createRoot(container);
    root.render(<ResponsiveTestRunner targetComponent={targetComponent} />);
  });
};

// CSS for testing viewport simulation
const testingStyles = `
  @media (max-width: 480px) {
    .responsive-test-mobile {
      display: block !important;
    }
    .responsive-test-desktop {
      display: none !important;
    }
  }
  
  @media (min-width: 481px) {
    .responsive-test-mobile {
      display: none !important;
    }
    .responsive-test-desktop {
      display: block !important;
    }
  }
`;

// Inject testing styles
if (process.env.NODE_ENV === 'development') {
  const style = document.createElement('style');
  style.textContent = testingStyles;
  document.head.appendChild(style);
}
