import React, { useState, useEffect } from 'react';
import {
    LineChart, Line, AreaChart, Area, BarChart, Bar, PieChart, Pie, Cell,
    XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer
} from 'recharts';
import {
    UsersIcon, EyeIcon, ClockIcon, DevicePhoneMobileIcon,
    ComputerDesktopIcon, ChartBarIcon, ArrowTrendingUpIcon,
    ArrowDownTrayIcon, CalendarIcon, FunnelIcon
} from '@heroicons/react/24/outline';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';

interface AnalyticsData {
    overview: {
        totalUsers: number;
        totalSessions: number;
        avgSessionDuration: number;
        dateRange: number;
    };
    userTypeBreakdown: Array<{ _id: string; count: number }>;
    topPages: Array<{ _id: string; totalViews: number }>;
    featureUsage: Array<{ _id: string; totalUsage: number }>;
    dailyActivity: Array<{ _id: string; sessions: number; uniqueUsers: number }>;
}

interface RealTimeData {
    activeSessions: number;
    activeUsers: number;
    recentPageViews: Array<{ page: string; views: number; uniqueUsers: number }>;
    timestamp: string;
}

const AnalyticsDashboard: React.FC = () => {
    const axiosPrivate = useAxiosPrivate();
    const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
    const [realTimeData, setRealTimeData] = useState<RealTimeData | null>(null);
    const [loading, setLoading] = useState(true);
    const [dateRange, setDateRange] = useState(30);
    const [selectedMetric, setSelectedMetric] = useState<'sessions' | 'users' | 'pageviews'>('sessions');

    // Fetch analytics data
    const fetchAnalyticsData = async (range: number) => {
        try {
            setLoading(true);
            const response = await axiosPrivate.get(`/api/analytics/dashboard?dateRange=${range}`);
            setAnalyticsData(response.data);
        } catch (error) {
            console.error('Error fetching analytics data:', error);
        } finally {
            setLoading(false);
        }
    };

    // Fetch real-time data
    const fetchRealTimeData = async () => {
        try {
            const response = await axiosPrivate.get('/api/analytics/realtime');
            setRealTimeData(response.data);
        } catch (error) {
            console.error('Error fetching real-time data:', error);
        }
    };

    // Export data
    const exportData = async (type: string, format: 'json' | 'csv' = 'csv') => {
        try {
            const response = await axiosPrivate.get(`/api/analytics/export?type=${type}&dateRange=${dateRange}&format=${format}`, {
                responseType: format === 'csv' ? 'blob' : 'json'
            });

            if (format === 'csv') {
                const blob = new Blob([response.data], { type: 'text/csv' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${type}_analytics_${dateRange}days.csv`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            } else {
                const blob = new Blob([JSON.stringify(response.data, null, 2)], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${type}_analytics_${dateRange}days.json`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }
        } catch (error) {
            console.error('Error exporting data:', error);
        }
    };

    useEffect(() => {
        fetchAnalyticsData(dateRange);
        fetchRealTimeData();

        // Set up real-time data refresh
        const interval = setInterval(fetchRealTimeData, 30000); // Every 30 seconds
        return () => clearInterval(interval);
    }, [dateRange]);

    const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'];

    if (loading) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent"></div>
            </div>
        );
    }

    return (
        <div className="space-y-6 p-6 bg-background min-h-screen">
            {/* Header */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                <div>
                    <h1 className="text-3xl font-bold text-primary">Analytics Dashboard</h1>
                    <p className="text-muted-foreground mt-1">Platform usage insights and user behavior analytics</p>
                </div>
                
                <div className="flex items-center gap-4 mt-4 sm:mt-0">
                    {/* Date Range Selector */}
                    <select
                        value={dateRange}
                        onChange={(e) => setDateRange(Number(e.target.value))}
                        className="px-3 py-2 border border-border rounded-md bg-background text-primary focus:outline-none focus:ring-2 focus:ring-accent"
                    >
                        <option value={7}>Last 7 days</option>
                        <option value={30}>Last 30 days</option>
                        <option value={90}>Last 90 days</option>
                        <option value={365}>Last year</option>
                    </select>

                    {/* Export Button */}
                    <div className="relative group">
                        <button className="flex items-center gap-2 px-4 py-2 bg-accent text-white rounded-md hover:bg-accent/90 transition-colors">
                            <ArrowDownTrayIcon className="w-4 h-4" />
                            Export
                        </button>
                        <div className="absolute right-0 mt-2 w-48 bg-card border border-border rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-10">
                            <button
                                onClick={() => exportData('sessions', 'csv')}
                                className="block w-full text-left px-4 py-2 text-sm text-primary hover:bg-accent/10 transition-colors"
                            >
                                Sessions (CSV)
                            </button>
                            <button
                                onClick={() => exportData('interactions', 'csv')}
                                className="block w-full text-left px-4 py-2 text-sm text-primary hover:bg-accent/10 transition-colors"
                            >
                                Interactions (CSV)
                            </button>
                            <button
                                onClick={() => exportData('pages', 'csv')}
                                className="block w-full text-left px-4 py-2 text-sm text-primary hover:bg-accent/10 transition-colors"
                            >
                                Page Analytics (CSV)
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            {/* Real-time Metrics */}
            {realTimeData && (
                <div className="bg-card rounded-lg border border-border p-6">
                    <h2 className="text-xl font-semibold text-primary mb-4 flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        Real-time Activity
                    </h2>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="text-center">
                            <div className="text-2xl font-bold text-accent">{realTimeData.activeSessions}</div>
                            <div className="text-sm text-muted-foreground">Active Sessions</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-accent">{realTimeData.activeUsers}</div>
                            <div className="text-sm text-muted-foreground">Active Users</div>
                        </div>
                        <div className="text-center">
                            <div className="text-2xl font-bold text-accent">{realTimeData.recentPageViews.length}</div>
                            <div className="text-sm text-muted-foreground">Pages Being Viewed</div>
                        </div>
                    </div>
                </div>
            )}

            {/* Overview Cards */}
            {analyticsData && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div className="bg-card rounded-lg border border-border p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-muted-foreground">Total Users</p>
                                <p className="text-2xl font-bold text-primary">{analyticsData.overview.totalUsers}</p>
                            </div>
                            <UsersIcon className="w-8 h-8 text-accent" />
                        </div>
                    </div>

                    <div className="bg-card rounded-lg border border-border p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-muted-foreground">Total Sessions</p>
                                <p className="text-2xl font-bold text-primary">{analyticsData.overview.totalSessions}</p>
                            </div>
                            <EyeIcon className="w-8 h-8 text-accent" />
                        </div>
                    </div>

                    <div className="bg-card rounded-lg border border-border p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-muted-foreground">Avg Session Duration</p>
                                <p className="text-2xl font-bold text-primary">
                                    {Math.round(analyticsData.overview.avgSessionDuration / 60)}m
                                </p>
                            </div>
                            <ClockIcon className="w-8 h-8 text-accent" />
                        </div>
                    </div>

                    <div className="bg-card rounded-lg border border-border p-6">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="text-sm text-muted-foreground">Date Range</p>
                                <p className="text-2xl font-bold text-primary">{analyticsData.overview.dateRange} days</p>
                            </div>
                            <CalendarIcon className="w-8 h-8 text-accent" />
                        </div>
                    </div>
                </div>
            )}

            {/* Charts Section */}
            {analyticsData && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Daily Activity Chart */}
                    <div className="bg-card rounded-lg border border-border p-6">
                        <h3 className="text-lg font-semibold text-primary mb-4">Daily Activity Trend</h3>
                        <ResponsiveContainer width="100%" height={300}>
                            <LineChart data={analyticsData.dailyActivity}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="_id" />
                                <YAxis />
                                <Tooltip />
                                <Legend />
                                <Line type="monotone" dataKey="sessions" stroke="#8884d8" name="Sessions" />
                                <Line type="monotone" dataKey="uniqueUsers" stroke="#82ca9d" name="Unique Users" />
                            </LineChart>
                        </ResponsiveContainer>
                    </div>

                    {/* User Type Breakdown */}
                    <div className="bg-card rounded-lg border border-border p-6">
                        <h3 className="text-lg font-semibold text-primary mb-4">User Type Distribution</h3>
                        <ResponsiveContainer width="100%" height={300}>
                            <PieChart>
                                <Pie
                                    data={analyticsData.userTypeBreakdown}
                                    cx="50%"
                                    cy="50%"
                                    labelLine={false}
                                    label={({ _id, percent }) => `${_id} ${(percent * 100).toFixed(0)}%`}
                                    outerRadius={80}
                                    fill="#8884d8"
                                    dataKey="count"
                                >
                                    {analyticsData.userTypeBreakdown.map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                    ))}
                                </Pie>
                                <Tooltip />
                            </PieChart>
                        </ResponsiveContainer>
                    </div>

                    {/* Top Pages */}
                    <div className="bg-card rounded-lg border border-border p-6">
                        <h3 className="text-lg font-semibold text-primary mb-4">Most Visited Pages</h3>
                        <ResponsiveContainer width="100%" height={300}>
                            <BarChart data={analyticsData.topPages.slice(0, 8)}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="_id" angle={-45} textAnchor="end" height={100} />
                                <YAxis />
                                <Tooltip />
                                <Bar dataKey="totalViews" fill="#8884d8" />
                            </BarChart>
                        </ResponsiveContainer>
                    </div>

                    {/* Feature Usage */}
                    <div className="bg-card rounded-lg border border-border p-6">
                        <h3 className="text-lg font-semibold text-primary mb-4">Feature Usage</h3>
                        <ResponsiveContainer width="100%" height={300}>
                            <BarChart data={analyticsData.featureUsage}>
                                <CartesianGrid strokeDasharray="3 3" />
                                <XAxis dataKey="_id" />
                                <YAxis />
                                <Tooltip />
                                <Bar dataKey="totalUsage" fill="#82ca9d" />
                            </BarChart>
                        </ResponsiveContainer>
                    </div>
                </div>
            )}
        </div>
    );
};

export default AnalyticsDashboard;
