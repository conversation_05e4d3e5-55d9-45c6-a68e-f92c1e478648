import { ArrowRight } from 'lucide-react';
import { motion } from 'framer-motion';
import { BaseComponentProps, ModalHandlers, StepItem } from './types';

interface HowItWorksProps extends BaseComponentProps, ModalHandlers {}

const HowItWorks: React.FC<HowItWorksProps> = ({
  setRegisterModalOpen,
  className = ''
}) => {
  const steps: StepItem[] = [
    {
      number: '01',
      title: 'Sign Up',
      description: 'Create your account and set up your profile in under 2 minutes',
      animationPattern: 'dots'
    },
    {
      number: '02',
      title: 'Create Content',
      description: 'Let AI generate personalized tests and learning materials instantly',
      animationPattern: 'network'
    },
    {
      number: '03',
      title: 'Get Insights',
      description: 'Receive detailed analytics and AI-powered recommendations',
      animationPattern: 'radial'
    }
  ];

  const renderAnimationPattern = (pattern: string, index: number) => {
    switch (pattern) {
      case 'dots':
        return (
          <div className="grid grid-cols-8 gap-2 opacity-60">
            {Array.from({ length: 32 }).map((_, i) => (
              <motion.div
                key={i}
                className="w-2 h-6 rounded-full"
                style={{
                  backgroundColor: i % 4 === 0 ? '#3b82f6' : i % 4 === 1 ? '#10b981' : i % 4 === 2 ? '#f59e0b' : '#8b5cf6'
                }}
                animate={{
                  scaleY: [1, 1.5, 1],
                  opacity: [0.3, 1, 0.3]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.1
                }}
              />
            ))}
          </div>
        );
      case 'network':
        return (
          <div className="relative w-32 h-32">
            {Array.from({ length: 12 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-3 h-3 rounded-full"
                style={{
                  backgroundColor: i % 3 === 0 ? '#3b82f6' : i % 3 === 1 ? '#10b981' : '#f59e0b',
                  left: `${50 + 40 * Math.cos((i * 2 * Math.PI) / 12)}%`,
                  top: `${50 + 40 * Math.sin((i * 2 * Math.PI) / 12)}%`,
                  transform: 'translate(-50%, -50%)'
                }}
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.2
                }}
              />
            ))}
          </div>
        );
      case 'radial':
        return (
          <div className="relative w-32 h-32">
            {Array.from({ length: 16 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-8 rounded-full origin-bottom"
                style={{
                  backgroundColor: i % 4 === 0 ? '#3b82f6' : i % 4 === 1 ? '#10b981' : i % 4 === 2 ? '#f59e0b' : '#8b5cf6',
                  left: '50%',
                  bottom: '50%',
                  transform: `translateX(-50%) rotate(${i * 22.5}deg)`,
                  transformOrigin: 'bottom center'
                }}
                animate={{
                  scaleY: [0.5, 1, 0.5],
                  opacity: [0.3, 1, 0.3]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.1
                }}
              />
            ))}
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <section id="how-it-works" className={`py-24 bg-gradient-to-b from-background to-muted/30 overflow-hidden ${className}`}>
      <div className="container mx-auto px-6">
        <div className="text-center mb-20">
          <motion.span
            initial={{ opacity: 0}}
            whileInView={{ opacity: 1}}
            viewport={{ once: true }}
            className="inline-block mb-6 bg-accent/10 text-accent px-6 py-3 rounded-full font-medium text-sm tracking-wider uppercase"
          >
            HOW IT WORKS
          </motion.span>
          <motion.h2
            initial={{ opacity: 0}}
            whileInView={{ opacity: 1}}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-4xl sm:text-5xl lg:text-6xl font-bold font-['Space_Grotesk'] mb-6 leading-tight"
          >
            Get Started in Minutes
          </motion.h2>
          <motion.p
            initial={{ opacity: 0}}
            whileInView={{ opacity: 1}}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-xl text-foreground/70 max-w-3xl mx-auto"
          >
            Simple setup, powerful results
          </motion.p>
        </div>

        {/* Steps Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0}}
              whileInView={{ opacity: 1}}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              className="bg-card/50 backdrop-blur-sm border border-border/50 rounded-xl p-12 text-left relative overflow-hidden group hover:bg-card/70 transition-all duration-300"
            >
              {/* Step Number */}
              <div className="absolute top-6 right-6 w-12 h-12 bg-muted/30 rounded-xl flex items-center justify-center">
                <span className="text-sm font-medium text-foreground/60">{step.number}</span>
              </div>

              {/* Visual Element - Animated Pattern */}
              <div className="w-full h-48 mb-8 bg-gradient-to-br from-accent/10 to-primary/10 rounded-xl flex items-center justify-center relative overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  {renderAnimationPattern(step.animationPattern, index)}
                </div>
              </div>

              <h3 className="text-2xl font-bold mb-4">{step.title}</h3>
              <p className="text-foreground/70 leading-relaxed">
                {step.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* CTA Button */}
        <motion.div
          initial={{ opacity: 0}}
          whileInView={{ opacity: 1}}
          viewport={{ once: true }}
          transition={{ delay: 0.6 }}
          className="text-center mt-16"
        >
          <button
            onClick={() => setRegisterModalOpen(true)}
            className="bg-accent text-accent-foreground px-10 py-4 rounded-xl font-semibold text-lg transition-all hover:scale-105 inline-flex items-center gap-3"
          >
            Start Free Today
            <ArrowRight className="w-5 h-5" />
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default HowItWorks;
