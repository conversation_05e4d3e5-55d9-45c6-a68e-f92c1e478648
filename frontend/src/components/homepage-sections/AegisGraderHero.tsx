import { Sparkles } from 'lucide-react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import Orb from '../HeroSectionBackground/Orb';
import { BaseComponentProps, ModalHandlers } from './types';

// ShinyText component
const ShinyText = ({ text, disabled = false, speed = 5, className = '' }: {
  text: string;
  disabled?: boolean;
  speed?: number;
  className?: string;
}) => {
  const animationDuration = `${speed}s`;

  return (
    <div
      className={`text-accent bg-clip-text inline-block ${disabled ? '' : 'animate-shine'} ${className}`}
      style={{
        backgroundImage: 'linear-gradient(120deg, rgba(255, 255, 255, 0) 40%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0) 60%)',
        backgroundSize: '200% 100%',
        WebkitBackgroundClip: 'text',
        '--animation-duration': animationDuration,
      } as React.CSSProperties}
    >
      {text}
    </div>
  );
};

interface AegisGraderHeroProps extends BaseComponentProps, ModalHandlers { }

const AegisGraderHero: React.FC<AegisGraderHeroProps> = ({
  setRegisterModalOpen,
  className = ''
}) => {
  const navigate = useNavigate();

  return (
    <section className={`relative lg:min-h-screen min-h-[90dvh] flex flex-col items-center justify-center overflow-hidden lg:py-4 py-12 mb-16 lg:mb-0 ${className}`}>
      {/* Orb as background */}
      <div className="absolute inset-0 z-0 pointer-events-none">
        <Orb
          hoverIntensity={0.5}
          rotateOnHover={true}
          hue={0}
          forceHoverState={false}
        />
      </div>

      {/* Hero Content - Centered */}
      <div className="relative z-20 text-center max-w-7xl mx-auto px-6 w-full">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="inline-flex items-center gap-2 mb-8 bg-accent/15 backdrop-blur-md px-4 py-2 rounded-full font-medium text-sm sm:text-base border border-accent/30 mx-auto"
        >
          <Sparkles className="animate-pulse w-4 h-4 text-accent" />
          <ShinyText text="AI-Powered Grading" speed={5} />
        </motion.div>

        <motion.h1
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 font-['Space_Grotesk'] tracking-tight text-foreground leading-tight"
        >
          Meet{' '}
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-accent via-primary to-accent bg-size-200 animate-gradient">
            AegisGrader
          </span>
        </motion.h1>

        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="text-lg sm:text-xl lg:text-2xl mb-12 text-foreground/80 leading-relaxed max-w-4xl mx-auto"
        >
          Transform your grading workflow with AI that understands context, provides detailed feedback, and saves you hours every week
        </motion.p>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
        >
          <button
            className="lg:w-auto w-fit bg-accent text-accent-foreground lg:px-8 px-6 lg:py-4 py-3 rounded-xl font-semibold text-sm sm:text-base lg:text-lg transition-all duration-300 hover:scale-105 flex items-center justify-center gap-2 cursor-pointer group backdrop-blur-sm border border-accent/20"
            onClick={() => navigate('/register?type=institution')}
          >
            <span>Start Grading Smarter</span>
            {/* <Sparkles className="w-5 h-5 group-hover:rotate-12 transition-transform" /> */}
          </button>
        </motion.div>


      </div>
    </section>
  );
};

export default AegisGraderHero;
