import AegisScholarLogoWithoutText from '../../assets/AegisScholarLogoIcon';
import { useNavigate } from 'react-router-dom';
import { BaseComponentProps, ModalHandlers } from './types';

interface FooterProps extends BaseComponentProps, ModalHandlers {}

const Footer: React.FC<FooterProps> = ({
  setLoginModalOpen,
  className = ''
}) => {
  const navigate = useNavigate();
  return (
    <footer className={`bg-primary text-primary-foreground py-12 ${className}`}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
          {/* Company Info */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <AegisScholarLogoWithoutText
                className="w-8 h-8"
                style={{ fill: 'var(--color-primary-foreground)' }}
              />
              <span className="text-xl font-bold">AegisScholar</span>
            </div>
            <p className="text-primary-foreground/80 mb-4">
              Revolutionizing grading with AI-powered assessment technology.
            </p>
          </div>

          <div></div>
          
          {/* Product */}
          <div>
            <h4 className="text-lg font-bold mb-4">AegisGrader</h4>
            <ul className="space-y-2">
              <li><a href="#aegis-grader-features" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">AI Grading</a></li>
              <li><a href="#aegis-grader-workflow" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">How It Works</a></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h4 className="text-lg font-bold mb-4">Support</h4>
            <ul className="space-y-2">
              <li><button onClick={() => navigate('/login?type=institution')} className="text-primary-foreground/80 hover:text-primary-foreground transition-colors cursor-pointer">Login</button></li>
              <li><a href="/privacy-policy" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">Privacy Policy</a></li>
              <li><a href="#" className="text-primary-foreground/80 hover:text-primary-foreground transition-colors">Terms of Service</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-primary-foreground/20 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-primary-foreground/60 text-sm mb-4 md:mb-0">
              © 2025 AegisScholar. All rights reserved.
            </div>
            <div className="flex items-center space-x-6 text-sm text-primary-foreground/60">
              <span>SOC 2 Compliant</span>
              <span>GDPR Ready</span>
              <span>99.9% Uptime</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
