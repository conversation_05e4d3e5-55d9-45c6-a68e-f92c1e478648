import { X, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { BaseComponentProps } from './types';

interface ProblemSectionProps extends BaseComponentProps {}

const ProblemSection: React.FC<ProblemSectionProps> = ({
  className = ''
}) => {
  const traditionalProblems = [
    'Manual test creation takes hours',
    'Generic assessments for all students',
    'Limited feedback and insights',
    'No personalized learning paths',
    'Time-consuming grading process',
    'Outdated user interfaces'
  ];

  const aegisScholarSolutions = [
    'AI-generated tests in minutes',
    'Personalized assessments for each student',
    'Detailed analytics and insights',
    'Adaptive learning recommendations',
    'Automated grading with feedback',
    'Modern, intuitive interface'
  ];

  return (
    <section className={`py-24 bg-muted/20 pt-40 overflow-hidden ${className}`}>
      <div className="container mx-auto px-6">
        <div className="text-center mb-20">
          <motion.span
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="inline-block mb-6 bg-destructive/10 text-destructive px-6 py-3 rounded-full font-medium text-sm"
          >
            THE PROBLEM
          </motion.span>
          <motion.h2
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-4xl sm:text-5xl font-bold font-['Space_Grotesk'] mb-6"
          >
            Traditional education tools are designed for administration, not learning.
          </motion.h2>
        </div>

        {/* Comparison Table */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto bg-card rounded-xl p-12 border border-border"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
            {/* Traditional Tools */}
            <div className="space-y-6">
              <h3 className="text-xl font-bold text-destructive mb-6 text-center">Traditional Tools</h3>
              <div className="space-y-4">
                {traditionalProblems.map((item, index) => (
                  <div key={index} className="flex items-center">
                    <X className="w-5 h-5 text-destructive mr-3 flex-shrink-0" />
                    <span className="text-foreground/80">{item}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* AegisScholar */}
            <div className="space-y-6">
              <h3 className="text-xl font-bold text-accent mb-6 text-center">AegisScholar</h3>
              <div className="space-y-4">
                {aegisScholarSolutions.map((item, index) => (
                  <div key={index} className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-accent mr-3 flex-shrink-0" />
                    <span className="text-foreground/80">{item}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ProblemSection;
