// Shared types and interfaces for homepage components

export interface FAQItem {
  id: number;
  question: string;
  answer: string;
}

export interface TestimonialItem {
  id: number;
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  avatar: string;
  title: string;
}

export interface FeatureItem {
  title: string;
  description: string;
  features: string[];
  image: string;
}

export interface StatisticItem {
  value: string;
  label: string;
  description: string;
}

export interface SecurityFeature {
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
}

export interface PricingPlan {
  name: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  isPopular?: boolean;
  buttonText: string;
}

export interface StepItem {
  number: string;
  title: string;
  description: string;
  animationPattern: 'dots' | 'network' | 'radial';
}

// Common props interfaces
export interface BaseComponentProps {
  className?: string;
}

export interface ModalHandlers {
  setRegisterModalOpen: (open: boolean) => void;
  setLoginModalOpen: (open: boolean) => void;
}

export interface ScrollState {
  scrollY: number;
  isScrolled: boolean;
}

export interface MenuState {
  menuOpen: boolean;
  setMenuOpen: (open: boolean) => void;
}

// Animation variants for framer-motion
export interface AnimationVariants {
  initial: object;
  animate: object;
  transition?: object;
}
