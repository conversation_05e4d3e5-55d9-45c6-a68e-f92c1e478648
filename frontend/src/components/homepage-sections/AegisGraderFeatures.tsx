import { Zap, CheckCircle, FileText } from 'lucide-react';
import { motion } from 'framer-motion';
import { BaseComponentProps } from './types';

interface AegisGraderFeaturesProps extends BaseComponentProps { }

const AegisGraderFeatures: React.FC<AegisGraderFeaturesProps> = ({
  className = ''
}) => {
  const features = [
    {
      icon: <Zap className="w-6 h-6" />,
      title: "90% Faster Grading",
      description: "Reduce grading time from hours to minutes with AI-powered assessment"
    },
    {
      icon: <CheckCircle className="w-6 h-6" />,
      title: "Consistent Accuracy",
      description: "Eliminate grading bias with standardized AI evaluation criteria"
    },
    {
      icon: <FileText className="w-6 h-6" />,
      title: "Detailed Feedback",
      description: "Provide comprehensive feedback that helps students improve"
    }
  ];

  return (
    <section id="aegis-grader-features" className={`py-16 lg:py-24 bg-gradient-to-b from-background to-muted/20 ${className}`}>
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0}}
          whileInView={{ opacity: 1}}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6 font-['Space_Grotesk'] text-foreground">
            Why Choose AegisGrader?
          </h2>
          <p className="text-lg sm:text-xl text-foreground/70 max-w-3xl mx-auto">
            Transform your grading workflow with AI that understands context and provides meaningful feedback
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0}}
          whileInView={{ opacity: 1}}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto"
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0}}
              whileInView={{ opacity: 1}}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              className="bg-card/50 backdrop-blur-md border border-border/50 rounded-xl p-8 text-center hover:bg-card/70 transition-all duration-300"
            >
              <div className="inline-flex items-center justify-center w-16 h-16 bg-accent/10 text-accent rounded-xl mb-6 transition-colors">
                {feature.icon}
              </div>
              <h3 className="text-xl font-semibold mb-4 text-foreground">{feature.title}</h3>
              <p className="text-foreground/70 leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default AegisGraderFeatures;
