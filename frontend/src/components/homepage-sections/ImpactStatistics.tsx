import { motion } from 'framer-motion';
import { BaseComponentProps, StatisticItem } from './types';

interface ImpactStatisticsProps extends BaseComponentProps {}

const ImpactStatistics: React.FC<ImpactStatisticsProps> = ({
  className = ''
}) => {
  const statistics: StatisticItem[] = [
    {
      value: '85%',
      label: 'Time Saved',
      description: 'Average time reduction in test creation and grading'
    },
    {
      value: '3.2x',
      label: 'Better Results',
      description: 'Improvement in learning outcomes and test scores'
    },
    {
      value: '99%',
      label: 'Satisfaction Rate',
      description: 'Teachers who would recommend AegisScholar'
    }
  ];

  return (
    <section className={`py-24 bg-background ${className}`}>
      <div className="container mx-auto px-6">
        <div className="text-center mb-20">
          <motion.h2
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="text-4xl sm:text-5xl font-bold font-['Space_Grotesk'] mb-6"
          >
            Unlock the educational growth you've been needing
          </motion.h2>
          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-foreground/70 max-w-3xl mx-auto"
          >
            Join thousands of educators and students who have transformed their learning experience
          </motion.p>
        </div>

        {/* Key Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto mb-20">
          {statistics.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="text-center bg-card rounded-xl p-12 border border-border"
            >
              <h3 className="text-5xl font-bold text-accent mb-4">{stat.value}</h3>
              <p className="text-xl font-semibold mb-2">{stat.label}</p>
              <p className="text-foreground/70">{stat.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ImpactStatistics;
