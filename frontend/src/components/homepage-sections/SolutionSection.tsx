import { Z<PERSON>, Brain, MessageSquare } from 'lucide-react';
import { motion } from 'framer-motion';
import { BaseComponentProps } from './types';

interface SolutionSectionProps extends BaseComponentProps {}

const SolutionSection: React.FC<SolutionSectionProps> = ({
  className = ''
}) => {
  const solutionFeatures = [
    {
      icon: Zap,
      title: 'Instant AI Generation',
      description: 'Create personalized tests, quizzes, and learning materials in seconds, not hours.'
    },
    {
      icon: Brain,
      title: 'Smart Analytics',
      description: 'Get deep insights into student performance and learning patterns with AI analysis.'
    },
    {
      icon: MessageSquare,
      title: '24/7 AI Tutor',
      description: 'Provide students with round-the-clock personalized learning support and guidance.'
    }
  ];

  return (
    <section className={`py-24 bg-background ${className}`}>
      <div className="container mx-auto px-6">
        <div className="text-center mb-20">
          <motion.span
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="inline-block mb-6 bg-accent/10 text-accent px-6 py-3 rounded-full font-medium text-sm"
          >
            THE SOLUTION
          </motion.span>
          <motion.h2
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-4xl sm:text-5xl font-bold font-['Space_Grotesk'] mb-6"
          >
            AegisScholar is a complete AI-powered teaching and learning platform.
          </motion.h2>
        </div>

        {/* Solution Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {solutionFeatures.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="text-center p-8"
            >
              <div className="w-16 h-16 bg-accent/10 rounded-xl flex items-center justify-center mx-auto mb-6">
                <feature.icon className="w-8 h-8 text-accent" />
              </div>
              <h3 className="text-xl font-bold mb-4">{feature.title}</h3>
              <p className="text-foreground/70 leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SolutionSection;
