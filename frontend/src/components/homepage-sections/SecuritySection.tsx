import { Shield, Lock, Search, Database, Eye, Key } from 'lucide-react';
import { motion } from 'framer-motion';
import { BaseComponentProps, SecurityFeature } from './types';

interface SecuritySectionProps extends BaseComponentProps {}

const SecuritySection: React.FC<SecuritySectionProps> = ({
  className = ''
}) => {
  const securityFeatures: SecurityFeature[] = [
    {
      title: 'SOC 2 Compliance',
      description: 'SOC 2 compliance provided through continuous testing and monitoring of our security controls.',
      icon: Shield
    },
    {
      title: 'Two-Factor Authentication',
      description: 'Enhanced account security through an authenticator app of your choice for multi-layer protection.',
      icon: Lock
    },
    {
      title: 'Penetration Testing',
      description: 'Regular testing to identify any weak spots in our system\'s defenses which attackers could exploit.',
      icon: Search
    },
    {
      title: 'Automated Backups',
      description: 'Every transaction and data point is saved and recoverable at any point in time for complete data protection.',
      icon: Database
    },
    {
      title: 'Activity Monitoring',
      description: 'Real-time monitoring for irregular activity, enhancing the security of your data and providing peace of mind.',
      icon: Eye
    },
    {
      title: 'End-to-End Encryption',
      description: 'All sensitive information is protected with industry-standard encryption protocols during transmission and storage.',
      icon: Key
    }
  ];

  return (
    <section className={`py-24 bg-background overflow-hidden ${className}`}>
      <div className="container mx-auto px-6">
        <div className="text-center mb-20">
          <motion.span
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="inline-block mb-6 bg-accent/10 text-accent px-6 py-3 rounded-full font-medium text-sm"
          >
            SECURITY
          </motion.span>
          <motion.h2
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-4xl sm:text-5xl font-bold font-['Space_Grotesk'] mb-6"
          >
            Enterprise-Grade Security
          </motion.h2>
          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-xl text-foreground/70 max-w-3xl mx-auto"
          >
            Built on education industry-standard security and powered by trusted partners in technology, we ensure your educational data is protected, so you can focus on teaching.
          </motion.p>
        </div>

        {/* Security Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
          {securityFeatures.map((feature, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-card rounded-xl p-12 border border-border text-center"
            >
              <div className="w-16 h-16 bg-accent/10 rounded-xl flex items-center justify-center mx-auto mb-6">
                <feature.icon className="w-8 h-8 text-accent" />
              </div>
              <h3 className="text-xl font-bold mb-4">{feature.title}</h3>
              <p className="text-foreground/70 leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SecuritySection;
