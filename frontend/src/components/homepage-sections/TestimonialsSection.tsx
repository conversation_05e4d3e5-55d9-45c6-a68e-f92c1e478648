import { Star } from 'lucide-react';
import { motion } from 'framer-motion';
import { BaseComponentProps, TestimonialItem, StatisticItem } from './types';

interface TestimonialsSectionProps extends BaseComponentProps {}

const TestimonialsSection: React.FC<TestimonialsSectionProps> = ({
  className = ''
}) => {
  const testimonials: TestimonialItem[] = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      role: 'Principal',
      company: 'Lincoln High School',
      content: 'We experimented with other tools, but at the end of the day, AegisScholar was a clear winner on functionality and ease of use. If they continue doing what they\'re doing, they\'ll have a place with us forever.',
      rating: 5,
      avatar: '/api/placeholder/48/48',
      title: 'Long-Term Partnership'
    },
    {
      id: 2,
      name: '<PERSON>',
      role: 'Math Teacher',
      company: 'Roosevelt Academy',
      content: 'Anytime I reach out to AegisScholar\'s team with a question, I\'ll get an email back within 15 minutes max, letting me know they\'re on it. That kind of personal service is rare in any industry.',
      rating: 5,
      avatar: '/api/placeholder/48/48',
      title: 'Dedicated Service & Support'
    },
    {
      id: 3,
      name: '<PERSON>',
      role: 'Science Teacher',
      company: 'Westfield Middle School',
      content: 'The time savings have made a huge difference. Before, I was spending hours creating tests and grading. Now, I can focus on what matters most - actually teaching and connecting with my students.',
      rating: 5,
      avatar: '/api/placeholder/48/48',
      title: 'Time Savings Put Back Into Teaching'
    }
  ];

  const statistics: StatisticItem[] = [
    {
      value: '175%',
      label: 'Higher Engagement',
      description: 'Average increase in student participation and interaction'
    },
    {
      value: '5x',
      label: 'Faster Grading',
      description: 'Speed improvement in assessment and feedback delivery'
    },
    {
      value: '96%',
      label: 'Would Recommend',
      description: 'Teachers who would recommend AegisScholar to colleagues'
    }
  ];

  return (
    <section id="testimonials" className={`py-24 bg-muted/20 overflow-hidden ${className}`}>
      <div className="container mx-auto px-6">
        <div className="text-center mb-20">
          <motion.h2
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="text-4xl sm:text-5xl font-bold font-['Space_Grotesk'] mb-6"
          >
            An educational platform teachers can finally rely on.
          </motion.h2>
          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-foreground/70 max-w-3xl mx-auto"
          >
            Join 10,000+ forward-thinking educators maximizing their teaching impact. It's time your tools worked as hard as you do.
          </motion.p>
        </div>

        {/* Key Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto mb-20">
          {statistics.map((stat, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="text-center"
            >
              <h3 className="text-5xl font-bold text-accent mb-2">{stat.value}</h3>
              <p className="text-lg font-semibold mb-2">{stat.label}</p>
              <p className="text-foreground/70">{stat.description}</p>
            </motion.div>
          ))}
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <motion.div
              key={testimonial.id}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-card rounded-xl p-12 border border-border"
            >
              <div className="mb-6">
                <h4 className="text-lg font-bold text-accent mb-2">{testimonial.title}</h4>
                <div className="flex mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-foreground/80 leading-relaxed italic">
                  "{testimonial.content}"
                </p>
              </div>
              <div className="flex items-center">
                <img
                  src={testimonial.avatar}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-xl object-cover border-2 border-accent/20 mr-4"
                />
                <div>
                  <h5 className="font-bold text-primary">{testimonial.name}</h5>
                  <p className="text-sm text-foreground/60">{testimonial.role}, {testimonial.company}</p>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
