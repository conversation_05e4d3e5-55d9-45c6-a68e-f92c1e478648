import { motion } from 'framer-motion';
import { BaseComponentProps, ScrollState } from './types';
import { useEffect, useState } from 'react';
import { useTheme } from '@/contexts/themeContext';

interface DashboardPreviewProps extends BaseComponentP<PERSON>, ScrollState { }

const DashboardPreview: React.FC<DashboardPreviewProps> = ({
  scrollY,
  isScrolled,
  className = ''
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const maxScale = isMobile ? 1.05 : 1.2;
  const scrollMultiplier = isMobile ? 0.0001 : 0.0003;
  const { theme } = useTheme();

  return (
    <div className={`relative z-30 -mt-32 ${className}`}>
      <motion.div
        className="w-full px-6 overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 1, delay: 0.8 }}
        style={{
          transform: `scale(${Math.min(maxScale, 1 + scrollY * scrollMultiplier)})`,
          transition: 'transform 0.1s ease-out'
        }}
      >
        <div className="max-w-5xl mx-auto">
          <img
            src={theme === 'dark' ? `Dashboard_hero_image.png` : `Dashboard_hero_image_light.png`}
            alt="AegisScholar Dashboard Preview"
            loading="lazy"
            width="1024"
            height="640"
            className="w-full h-auto rounded-xl border border-border/20 shadow-2xl aspect-[1024/640] max-w-full"
          />
        </div>
      </motion.div>
    </div>
  );
};

export default DashboardPreview;
