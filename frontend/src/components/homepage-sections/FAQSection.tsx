import { useState } from 'react';
import { Plus } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { BaseComponentProps, FAQItem } from './types';

interface FAQSectionProps extends BaseComponentProps {}

const FAQSection: React.FC<FAQSectionProps> = ({
  className = ''
}) => {
  const [openFaqIndex, setOpenFaqIndex] = useState<number | null>(null);

  const faqData: FAQItem[] = [
    {
      id: 1,
      question: 'How does the AI create personalized tests?',
      answer: 'Our AI analyzes curriculum standards, student performance data, and learning objectives to generate questions that match each student\'s skill level and learning needs. It continuously adapts based on student responses.'
    },
    {
      id: 2,
      question: 'Is my student data secure and private?',
      answer: 'Absolutely. We use enterprise-grade encryption and follow strict data privacy regulations including GDPR and FERPA. Your data is never shared with third parties without explicit consent.'
    },
    {
      id: 3,
      question: 'What subjects and grade levels are supported?',
      answer: 'AegisScholar supports all major subjects from elementary through university level, including STEM, humanities, languages, and professional courses. Our AI adapts to any curriculum standard.'
    },
    {
      id: 4,
      question: 'How much time can teachers save using AegisScholar?',
      answer: 'Teachers typically save 80-85% of their time on administrative tasks like test creation, grading, and progress tracking. This translates to 10-15 hours per week for most educators.'
    }
  ];

  const toggleFaq = (index: number) => {
    setOpenFaqIndex(openFaqIndex === index ? null : index);
  };

  return (
    <section id="faq" className={`py-24 bg-muted/20 overflow-hidden ${className}`}>
      <div className="container mx-auto px-6">
        <div className="text-center mb-20">
          <motion.h2
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="text-4xl sm:text-5xl font-bold font-['Space_Grotesk'] mb-6"
          >
            Frequently Asked Questions
          </motion.h2>
        </div>

        <div className="max-w-4xl mx-auto">
          {faqData.map((faq, index) => (
            <motion.div
              key={faq.id}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              className="mb-6"
            >
              <div className="bg-card rounded-xl border border-border overflow-hidden hover:border-accent/50 transition-all duration-300">
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-muted/30 transition-colors"
                >
                  <span className="font-semibold text-primary pr-4 text-lg">{faq.question}</span>
                  <div className="flex-shrink-0">
                    {openFaqIndex === index ? (
                      <Plus className="w-6 h-6 text-accent rotate-45 transition transition-all duration-300" />
                    ) : (
                      <Plus className="w-6 h-6 text-accent transition transition-all duration-300" />
                    )}
                  </div>
                </button>

                <AnimatePresence>
                  {openFaqIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: 'auto', opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3 }}
                      className="overflow-hidden"
                    >
                      <div className="px-8 pb-6 text-foreground/80 leading-relaxed text-lg">
                        {faq.answer}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FAQSection;
