import { <PERSON>, Spark<PERSON>, Clock } from 'lucide-react';
import { motion } from 'framer-motion';
import { BaseComponentProps } from './types';

interface AegisGraderWorkflowProps extends BaseComponentProps { }

const AegisGraderWorkflow: React.FC<AegisGraderWorkflowProps> = ({
  className = ''
}) => {
  const workflow = [
    {
      icon: <Camera className="w-8 h-8" />,
      title: "Scan or Upload",
      description: "Capture test papers with your camera or upload PDF files",
    },
    {
      icon: <Sparkles className="w-8 h-8" />,
      title: "AI Analysis",
      description: "Advanced AI analyzes answers and applies grading rubrics",
    },
    {
      icon: <Clock className="w-8 h-8" />,
      title: "Instant Results",
      description: "Get detailed grades and feedback in seconds, not hours",
    }
  ];

  return (
    <section id="aegis-grader-workflow" className={`py-16 lg:py-24 bg-gradient-to-b from-muted/20 to-background ${className}`}>
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0}}
          whileInView={{ opacity: 1}}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold mb-6 font-['Space_Grotesk'] text-foreground">
            How It Works
          </h2>
          <p className="text-lg sm:text-xl text-foreground/70 max-w-3xl mx-auto">
            Get started with AegisGrader in three simple steps and revolutionize your grading process
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0}}
          whileInView={{ opacity: 1}}
          viewport={{ once: true }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="max-w-6xl mx-auto"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
            {workflow.map((step, index) => (
              <motion.div
                key={step.title}
                initial={{ opacity: 0}}
                whileInView={{ opacity: 1}}
                viewport={{ once: true }}
                transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
                className="text-center relative"
              >
                
                {/* Icon */}
                <div className="inline-flex items-center justify-center w-20 h-20 bg-primary text-primary-foreground rounded-full mb-6 transition-transform duration-300">
                  {step.icon}
                </div>
                
                {/* Content */}
                <h3 className="text-xl lg:text-2xl font-semibold mb-4 text-foreground">{step.title}</h3>
                <p className="text-foreground/70 leading-relaxed">{step.description}</p>
                
                {/* Connector Line */}
                {index < workflow.length - 1 && (
                  <div className="hidden md:block absolute top-10 left-full w-full h-0.5 bg-gradient-to-r from-primary/50 to-transparent transform -translate-x-10 z-0 transition-transform duration-300" />
                )}
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default AegisGraderWorkflow;
