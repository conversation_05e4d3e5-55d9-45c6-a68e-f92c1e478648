import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { BaseComponentProps, ModalHandlers } from './types';

interface FinalCTAProps extends BaseComponentProps, ModalHandlers {}

const FinalCTA: React.FC<FinalCTAProps> = ({
  setRegisterModalOpen,
  className = ''
}) => {
  const navigate = useNavigate();
  return (
    <section className={`py-24 overflow-hidden ${className}`}>
      <div className="container mx-auto px-6 flex justify-center">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.8 }}
          className="relative w-full max-w-4xl overflow-hidden rounded-[40px] bg-gradient-to-br from-primary via-primary to-primary/90 p-6 sm:p-10 md:p-20"
        >

          {/* Content */}
          <div className="relative z-10">
            <h2 className="mb-3 text-3xl font-bold text-primary-foreground sm:text-4xl md:mb-4 md:text-5xl font-['Space_Grotesk']">
              Ready to revolutionize your grading?
            </h2>
            <p className="mb-6 max-w-md text-base text-primary-foreground/90 sm:text-lg md:mb-8">
              Join educators who have reduced their grading time by 90% with AegisGrader's AI-powered assessment.
            </p>

            <div className="flex flex-col gap-4 sm:flex-row sm:gap-6">
              <button
                onClick={() => navigate('/register?type=institution')}
                className="flex w-full items-center justify-center rounded-full bg-background px-5 py-3 text-foreground sm:w-[240px] hover:bg-background/90 transition-colors"
              >
                <span className="font-medium">Get Started</span>
                {/* <span className="h-5 w-5 flex-shrink-0 rounded-full bg-accent"></span> */}
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default FinalCTA;
