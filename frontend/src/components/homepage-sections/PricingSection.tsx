import { <PERSON><PERSON>ir<PERSON>, Shield } from 'lucide-react';
import { motion } from 'framer-motion';
import { BaseComponentProps, ModalHandlers, PricingPlan } from './types';

interface PricingSectionProps extends BaseComponentProps, ModalHandlers {}

const PricingSection: React.FC<PricingSectionProps> = ({
  setRegisterModalOpen,
  className = ''
}) => {
  const pricingPlans: PricingPlan[] = [
    {
      name: 'Free',
      price: '$0',
      period: '/month',
      description: 'Perfect for getting started',
      features: [
        'Up to 50 students',
        '10 AI tests per month',
        'Basic analytics',
        'Email support'
      ],
      buttonText: 'Start Free',
      isPopular: false
    },
    {
      name: 'Pro',
      price: '$29',
      period: '/month',
      description: 'For serious educators',
      features: [
        'Unlimited students',
        'Unlimited AI tests',
        'Advanced analytics',
        'Priority support',
        'Custom integrations'
      ],
      buttonText: 'Start Pro Trial',
      isPopular: true
    }
  ];

  return (
    <section id="pricing" className={`py-24 bg-background overflow-hidden ${className}`}>
      <div className="container mx-auto px-6">
        <div className="text-center mb-20">
          <motion.h2
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            className="text-4xl sm:text-5xl font-bold font-['Space_Grotesk'] mb-6"
          >
            Simple, Transparent Pricing
          </motion.h2>
          <motion.p
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-foreground/70 max-w-3xl mx-auto"
          >
            Start free, upgrade when you're ready
          </motion.p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
          {pricingPlans.map((plan, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className={`bg-card rounded-xl p-12 transition-all duration-300 relative ${
                plan.isPopular 
                  ? 'border-2 border-accent' 
                  : 'border border-border hover:border-accent/50'
              }`}
            >
              {plan.isPopular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-accent text-accent-foreground px-6 py-2 rounded-xl text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-3xl font-bold mb-4">{plan.name}</h3>
                <div className="mb-6">
                  <span className="text-5xl font-bold text-primary">{plan.price}</span>
                  <span className="text-foreground/60 ml-2">{plan.period}</span>
                </div>
                <p className="text-foreground/70 text-lg">{plan.description}</p>
              </div>

              <ul className="space-y-4 mb-10">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <CheckCircle className="w-5 h-5 text-accent mr-3 flex-shrink-0" />
                    <span className="text-foreground/80">{feature}</span>
                  </li>
                ))}
              </ul>

              <button
                onClick={() => setRegisterModalOpen(true)}
                className={`w-full py-4 rounded-xl font-semibold text-lg transition-all hover:scale-105 ${
                  plan.isPopular
                    ? 'bg-accent text-accent-foreground'
                    : 'bg-muted text-foreground hover:bg-muted/80'
                }`}
              >
                {plan.buttonText}
              </button>
            </motion.div>
          ))}
        </div>

        {/* Guarantee */}
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ delay: 0.3 }}
          className="text-center mt-16"
        >
          <div className="inline-flex items-center gap-3 bg-card px-8 py-4 rounded-xl border border-border">
            <Shield className="w-6 h-6 text-accent" />
            <span className="text-foreground/80 font-medium">30-day money-back guarantee</span>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default PricingSection;
