import { EnvelopeIcon, PhoneIcon } from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';
import { BaseComponentProps } from './types';

interface ContactSectionProps extends BaseComponentProps {}

const ContactSection: React.FC<ContactSectionProps> = ({
  className = ''
}) => {
  return (
    <section id="contact" className={`py-20 bg-gradient-to-b from-background to-muted/30 overflow-hidden ${className}`}>
      <div className="container mx-auto px-6">
        <div className="flex flex-col md:flex-row gap-12">
          {/* Contact Info */}
          <motion.div
            initial={{ x: -50, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="w-full md:w-1/2 space-y-6"
          >
            <span className="inline-block mb-3 bg-accent/10 text-accent px-4 py-2 rounded-full font-medium text-sm">
              GET IN TOUCH
            </span>
            <h2 className="text-3xl sm:text-4xl font-bold font-['Space_Grotesk']">
              Let's Build the Future of Education Together
            </h2>
            <p className="text-foreground/80 text-lg">
              Have a question, a suggestion, or just want to connect? We're eager to hear from you and ready to help.
            </p>
            <div className="space-y-4 pt-4">
              <div className="flex items-center space-x-4 text-foreground">
                <div className="bg-accent/10 p-3 rounded-xl">
                  <EnvelopeIcon className="w-5 h-5 text-accent" />
                </div>
                <a
                  href="mailto:<EMAIL>"
                  className="hover:text-accent transition-colors"
                >
                  <EMAIL>
                </a>
              </div>
              <div className="flex items-center space-x-4 text-foreground">
                <div className="bg-accent/10 p-3 rounded-xl">
                  <PhoneIcon className="w-5 h-5 text-accent" />
                </div>
                <a
                  href="tel:+918860063830"
                  className="hover:text-accent transition-colors"
                >
                  +91 - 8860063830
                </a>
              </div>
            </div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            whileInView={{ x: 0, opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
            className="w-full md:w-1/2 bg-card p-12 rounded-xl border border-border"
          >
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium mb-2">Name</label>
                  <input
                    id="name"
                    type="text"
                    className="p-3 border border-input rounded-xl bg-muted text-foreground w-full focus:outline-none focus:ring-2 focus:ring-accent/50"
                    placeholder="Your name"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium mb-2">Email</label>
                  <input
                    id="email"
                    type="email"
                    className="p-3 border border-input rounded-xl bg-muted text-foreground w-full focus:outline-none focus:ring-2 focus:ring-accent/50"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="message" className="block text-sm font-medium mb-2">Message</label>
                <textarea
                  id="message"
                  rows={4}
                  className="p-3 border border-input rounded-xl bg-muted text-foreground w-full focus:outline-none focus:ring-2 focus:ring-accent/50"
                  placeholder="How can we help you?"
                ></textarea>
              </div>
              <button
                type="submit"
                className="bg-accent text-white px-6 py-3 rounded-xl font-medium transition-all w-full cursor-pointer"
              >
                Send Message
              </button>
            </form>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
