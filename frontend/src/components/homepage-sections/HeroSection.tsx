import { Zap } from 'lucide-react';
import { motion } from 'framer-motion';
import Orb from '../HeroSectionBackground/Orb';
import { BaseComponentProps, ModalHandlers } from './types';

interface HeroSectionProps extends BaseComponentProps, ModalHandlers { }

const HeroSection: React.FC<HeroSectionProps> = ({
  setRegisterModalOpen,
  className = ''
}) => {
  return (
    <section className={`relative lg:min-h-screen min-h-[80vh] flex flex-col items-center justify-center overflow-hidden py-12 mb-16 lg:mb-0 ${className}`}>
      {/* Orb as background */}
      <div className="absolute inset-0 z-0 pointer-events-none">
        <Orb
          hoverIntensity={0.5}
          rotateOnHover={true}
          hue={0}
          forceHoverState={false}
        />
      </div>

      {/* Additional Background Elements */}
      <div className="absolute inset-0 z-5 pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-32 h-32 sm:w-48 sm:h-48 lg:w-72 lg:h-72 bg-accent/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-24 h-24 sm:w-36 sm:h-36 lg:w-56 lg:h-56 bg-primary/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Hero Content - Centered */}
      <div className="relative z-20 text-center max-w-7xl mx-auto px-6 w-full">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="inline-flex items-center gap-2 mb-8 bg-accent/15 backdrop-blur-md text-accent px-4 py-2 rounded-full font-medium text-sm sm:text-base border border-accent/30 mx-auto"
        >
          <Zap className="w-4 h-4" />
          <span className="inline">Alfred to your Batman</span>
        </motion.div>

        <motion.h1
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.3 }}
          className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 font-['Space_Grotesk'] tracking-tight text-foreground leading-tight"
        >
          A Truly Intelligent{' '}
          <br className="hidden sm:block" />
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-accent via-primary to-accent bg-size-200 animate-gradient">
            Teaching Ally
          </span>
        </motion.h1>

        <motion.p
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="sm:text-base lg:text-lg mb-8 text-foreground/80 leading-relaxed max-w-3xl mx-auto"
        >
          AI that remembers your entire learning journey and guides you accordingly
        </motion.p>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <button
            className="lg:w-auto w-fit bg-accent text-accent-foreground lg:px-8 px-6 lg:py-3 py-2 rounded-xl font-semibold text-sm sm:text-base lg:text-lg transition-all duration-300 hover:scale-105 flex items-center justify-center gap-2 cursor-pointer group backdrop-blur-sm border border-accent/20"
            onClick={() => setRegisterModalOpen(true)}
          >
            <span>Get Started</span>
          </button>
        </motion.div>
      </div>
    </section>
  );
};

export default HeroSection;
