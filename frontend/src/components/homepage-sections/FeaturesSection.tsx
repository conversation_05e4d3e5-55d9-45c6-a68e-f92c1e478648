import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, CheckCircle } from 'lucide-react';
import { motion } from 'framer-motion';
import { BaseComponentProps, FeatureItem } from './types';

interface FeaturesSectionProps extends BaseComponentProps {}

const FeaturesSection: React.FC<FeaturesSectionProps> = ({
  className = ''
}) => {
  const features: FeatureItem[] = [
    {
      title: 'AI Test Creation',
      description: 'Generate curriculum-aligned assessments instantly with our advanced AI engine that understands your teaching objectives.',
      features: [
        'Curriculum-aligned content generation',
        'Multiple question types and formats',
        'Instant test creation in minutes',
        'Customizable difficulty levels'
      ],
      image: 'Dashboard_hero_image.png'
    },
    {
      title: 'Smart Analytics',
      description: 'Get comprehensive insights into student performance with AI-powered analytics that help you make data-driven decisions.',
      features: [
        'Real-time performance tracking',
        'Personalized learning recommendations',
        'Detailed progress reports',
        'Predictive learning analytics'
      ],
      image: 'Dashboard_hero_image.png'
    },
    {
      title: '24/7 AI Tutor',
      description: 'Provide students with round-the-clock personalized learning support that adapts to their individual needs and learning style.',
      features: [
        'Always available learning support',
        'Personalized explanations and hints',
        'Interactive problem-solving assistance',
        'Adaptive learning pathways'
      ],
      image: 'Dashboard_hero_image.png'
    }
  ];

  return (
    <section id="features" className={`py-24 bg-muted/20 ${className}`}>
      <div className="container mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-20">
          <motion.span
            initial={{ opacity: 0}}
            whileInView={{ opacity: 1}}
            viewport={{ once: true }}
            className="inline-block mb-6 bg-accent/10 text-accent px-6 py-3 rounded-full font-medium text-sm"
          >
            WHAT WE OFFER
          </motion.span>
          <motion.h2
            initial={{ opacity: 0}}
            whileInView={{ opacity: 1}}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-4xl sm:text-5xl font-bold font-['Space_Grotesk'] mb-6"
          >
            We make it easy to transform education
          </motion.h2>
        </div>

        {/* Feature Cards */}
        {features.map((feature, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0}}
            whileInView={{ opacity: 1}}
            viewport={{ once: true }}
            transition={{ duration: 0.6, delay: index * 0.2 }}
            className={`bg-card rounded-xl p-12 border border-border ${index < features.length - 1 ? 'mb-12' : ''}`}
          >
            <div className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${index === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
              <div className={index === 1 ? 'lg:order-2' : ''}>
                <h3 className="text-3xl font-bold mb-6">{feature.title}</h3>
                <p className="text-xl text-foreground/70 leading-relaxed mb-8">
                  {feature.description}
                </p>
                <div className="space-y-4">
                  {feature.features.map((featureItem, featureIndex) => (
                    <div key={featureIndex} className="flex items-center">
                      <CheckCircle className="w-5 h-5 text-accent mr-3 flex-shrink-0" />
                      <span className="text-foreground/80">{featureItem}</span>
                    </div>
                  ))}
                </div>
              </div>
              <div className={`relative ${index === 1 ? 'lg:order-1' : ''}`}>
                <img
                  src={feature.image}
                  alt={`${feature.title} Interface`}
                  loading="lazy"
                  width="600"
                  height="400"
                  className="w-full h-auto rounded-xl border border-border/20 aspect-[600/400]"
                />
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </section>
  );
};

export default FeaturesSection;
