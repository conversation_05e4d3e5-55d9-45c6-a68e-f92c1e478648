import React, { useState, useEffect } from 'react';
import { useUser } from '../contexts/userContext';
import AegisAiChatbot from './AegisAiChatbot';

interface TeacherContext {
  classId: string;
  subject: string;
  className: string;
  studentCount: number;
}

const TeacherAegisAi: React.FC = () => {
  const { user } = useUser();
  const [teacherContext, setTeacherContext] = useState<TeacherContext | null>(null);

  // Get all unique subjects taught by the teacher
  const getTeacherSubjects = () => {
    if (!user?.classes) return [];
    const subjects = user.classes.map(cls => ({ subjectName: cls.subject }));
    // Remove duplicates based on subjectName
    const uniqueSubjects = subjects.filter((subject, index, self) =>
      index === self.findIndex(s => s.subjectName === subject.subjectName)
    );
    return uniqueSubjects;
  };

  // Get default class for a subject (first class found for that subject)
  const getDefaultClassForSubject = (subject: string) => {
    return user?.classes?.find(cls => cls.subject === subject);
  };

  // Handle subject change for teachers
  const handleTeacherSubjectChange = (newSubject: string) => {
    const defaultClass = getDefaultClassForSubject(newSubject);
    if (defaultClass) {
      // Update the context to the new subject
      setTeacherContext({
        classId: defaultClass._id,
        subject: defaultClass.subject,
        className: defaultClass.className,
        studentCount: defaultClass.students?.length || 0
      });
    }
  };

  // Auto-initialize with first available class
  useEffect(() => {
    if (user?.classes?.length && !teacherContext) {
      const firstClass = user.classes[0];
      setTeacherContext({
        classId: firstClass._id,
        subject: firstClass.subject,
        className: firstClass.className,
        studentCount: firstClass.students?.length || 0
      });
    }
  }, [user, teacherContext]);



  // Show loading or error state if no teacher context yet
  if (!teacherContext) {
    if (!user?.classes || user.classes.length === 0) {
      return (
        <div className="h-screen bg-background dark:bg-background overflow-hidden flex items-center justify-center">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-primary dark:text-primary mb-2">No Classes Found</h3>
            <p className="text-muted-foreground dark:text-muted-foreground">
              You don't have any classes assigned yet. Please contact your administrator.
            </p>
          </div>
        </div>
      );
    }

    return (
      <div className="h-screen bg-background dark:bg-background overflow-hidden flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary dark:border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground dark:text-muted-foreground">Loading your classes...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-background dark:bg-background overflow-hidden">
      <AegisAiChatbot
        subject={teacherContext.subject}
        studentId={`teacher_${user?.id}_class_${teacherContext.classId}`}
        availableSubjects={getTeacherSubjects()}
        onSubjectChange={handleTeacherSubjectChange}
      />
    </div>
  );
};

export default TeacherAegisAi;
