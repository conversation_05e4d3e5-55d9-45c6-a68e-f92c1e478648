import React, { useState } from 'react';
import { Group } from '@visx/group';
import { HeatmapRect } from '@visx/heatmap';
import { scaleLinear } from '@visx/scale';
import { Maximize2, Minimize2 } from 'lucide-react';

// Types
interface ActivityDataPoint {
  date: string;
  count: number;
}

const ActivityHeatmap: React.FC = () => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [activityData, setActivityData] = useState<ActivityDataPoint[]>(() => {
    const today = new Date();
    const data: ActivityDataPoint[] = [];

    for (let i = 0; i < 365; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      
      // Simulate varying activity levels
      const count = Math.floor(Math.random() * 5);
      data.push({
        date: date.toISOString().split('T')[0],
        count
      });
    }

    return data;
  });

  // Prepare data for heatmap
  const width = 800;
  const height = 400;
  const margin = { top: 20, right: 20, bottom: 20, left: 20 };

  // Color scales
  const colorScale = scaleLinear<string>({
    domain: [0, 4],
    range: ['hsl(var(--muted))', 'hsl(var(--info))']
  });

  // Group data into weeks and columns
  const weeksData = Array.from({ length: 53 }, (_, weekIndex) => ({
    bin: weekIndex,
    bins: Array.from({ length: 7 }, (_, dayIndex) => {
      const dataIndex = weekIndex * 7 + dayIndex;
      const dataPoint = activityData[dataIndex] || { count: 0 };
      return {
        bin: dataPoint,
        count: dataPoint.count,
        date: dataPoint.date
      };
    })
  }));

  return (
    <div className={`transition-all duration-300 ${isFullscreen 
      ? "fixed inset-0 z-50 m-0 rounded-none" 
      : ""}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-semibold">Learning Activity</h3>
        <button
          onClick={() => setIsFullscreen(!isFullscreen)}
          className="p-2 hover:bg-gray-100 rounded-full transition-colors"
          aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
        >
          {isFullscreen ? <Minimize2 size={20} /> : <Maximize2 size={20} />}
        </button>
      </div>
      <div
        className={`relative w-full transition-all duration-300 ${isFullscreen 
          ? "h-[calc(100vh-120px)]" 
          : "h-[400px]"} overflow-hidden rounded-lg bg-white`}
      >
        <svg width="100%" height="100%" viewBox={`0 0 ${width} ${height}`}>
          <Group top={margin.top} left={margin.left}>
            <HeatmapRect
              data={weeksData}
              xScale={(d) => d * 15}
              yScale={(d) => d * 15}
              colorScale={colorScale}
              binWidth={13}
              binHeight={13}
              gap={2}
            >
              {(heatmap) =>
                heatmap.map((heatmapBins) =>
                  heatmapBins.map((bin) => (
                    <rect
                      key={`heatmap-rect-${bin.row}-${bin.column}`}
                      width={bin.width}
                      height={bin.height}
                      x={bin.x}
                      y={bin.y}
                      fill={bin.color}
                      fillOpacity={bin.opacity}
                      className="transition-all duration-200 hover:opacity-75"
                    />
                  ))
                )
              }
            </HeatmapRect>
          </Group>
        </svg>
        <div className="absolute bottom-2 left-2 right-2 flex items-center justify-between text-xs text-gray-500">
          <span>Less</span>
          <div className="flex items-center gap-1">
            {[0, 1, 2, 3, 4].map((level) => (
              <div
                key={level}
                className="w-3 h-3 rounded"
                style={{ backgroundColor: colorScale(level) }}
              />
            ))}
            <span className="ml-2">More</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivityHeatmap;