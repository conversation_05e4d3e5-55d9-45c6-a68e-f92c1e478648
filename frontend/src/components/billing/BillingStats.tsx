import React, { useState, useEffect } from 'react';
import {
    CreditCardIcon,
    BanknotesIcon,
    ChartBarIcon,
    ArrowTrendingUpIcon,
    CalendarIcon,
    ArrowPathIcon
} from '@heroicons/react/24/outline';
import { useAxiosPrivate } from '@/hooks/useAxiosPrivate';
import { BillingStatsProps, BillingDashboardData } from '@/types/billing';
import CreditBalance from '@/components/credits/CreditBalance';
import BillingSkeleton from './BillingSkeleton';

const BillingStats: React.FC<BillingStatsProps> = ({ 
    className = "",
    showDetailedStats = true
}) => {
    const [dashboardData, setDashboardData] = useState<BillingDashboardData | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const axiosPrivate = useAxiosPrivate();

    const fetchDashboardData = async () => {
        try {
            setLoading(true);
            setError(null);
            const response = await axiosPrivate.get('/api/credits/dashboard');
            setDashboardData(response.data.data);
        } catch (err: any) {
            console.error('Error fetching dashboard data:', err);
            setError(err.response?.data?.message || 'Failed to fetch billing data');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchDashboardData();
    }, []);

    const formatAmount = (amount: number) => {
        return `₹${(amount / 100).toFixed(2)}`;
    };

    const formatDate = (dateString?: string) => {
        if (!dateString) return 'Never';
        return new Date(dateString).toLocaleDateString('en-IN', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    if (loading) {
        return (
            <div className={`bg-card rounded-lg border border-border p-6 ${className}`}>
                <div className="flex items-center justify-center py-8">
                    <ArrowPathIcon className="w-6 h-6 animate-spin text-primary" />
                    <span className="ml-2 text-muted-foreground">Loading billing stats...</span>
                </div>
            </div>
        );
    }

    if (error || !dashboardData) {
        return (
            <div className={`bg-card rounded-lg border border-destructive/50 p-6 ${className}`}>
                <div className="text-center py-8">
                    <p className="text-destructive mb-3">{error || 'Failed to load billing data'}</p>
                    <button 
                        onClick={fetchDashboardData}
                        className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors flex items-center gap-2 mx-auto"
                    >
                        <ArrowPathIcon className="w-4 h-4" />
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    const stats = [
        {
            title: 'Total Spent',
            value: dashboardData?.billingInfo?.totalAmountSpent ? formatAmount(dashboardData.billingInfo.totalAmountSpent) : '₹0.00',
            icon: BanknotesIcon,
            color: 'text-primary',
            bgColor: 'bg-primary/10'
        },
        {
            title: 'Credits Purchased',
            value: dashboardData?.paymentAnalytics?.totalCreditsPurchased?.toString() || '0',
            icon: CreditCardIcon,
            color: 'text-success',
            bgColor: 'bg-success/10'
        },
        {
            title: 'Credits Used',
            value: dashboardData?.usageAnalytics?.totalCreditsUsed?.toString() || '0',
            icon: ChartBarIcon,
            color: 'text-warning',
            bgColor: 'bg-warning/10'
        },
        {
            title: 'Avg. Credits/Day',
            value: dashboardData?.usageAnalytics?.averageCreditsPerDay?.toString() || '0',
            icon: ArrowTrendingUpIcon,
            color: 'text-purple-600 dark:text-purple-400',
            bgColor: 'bg-purple-50 dark:bg-purple-900/20'
        }
    ];

    return (
        <div className={`space-y-6 ${className}`}>
            {/* Current Credit Balance */}
            <CreditBalance
                className="mb-6"
            />

            {/* Stats Grid */}
            {loading ? (
                <BillingSkeleton type="stats" />
            ) : (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {stats.map((stat, index) => (
                        <div key={index} className="bg-card rounded-lg border border-border p-4">
                            <div className="flex items-center gap-3">
                                <div className={`p-2 rounded-full ${stat.bgColor}`}>
                                    <stat.icon className={`w-5 h-5 ${stat.color}`} />
                                </div>
                                <div>
                                    <p className="text-sm text-muted-foreground">{stat.title}</p>
                                    <p className="text-lg font-semibold text-foreground">{stat.value}</p>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>
            )}

            {/* Detailed Stats */}
            {showDetailedStats && !loading && dashboardData && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Usage Summary */}
                    <div className="bg-card rounded-lg border border-border p-6">
                        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                            <ChartBarIcon className="w-5 h-5" />
                            Usage Summary
                        </h3>
                        <div className="space-y-4">
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Total Evaluations:</span>
                                <span className="font-medium">{dashboardData?.usageAnalytics.totalEvaluations || 0}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Most Used Feature:</span>
                                <span className="font-medium">{dashboardData?.usageAnalytics.mostUsedFeature || 'None'}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Credits Remaining:</span>
                                <span className="font-medium text-primary">
                                    {dashboardData?.creditBalance.currentBalance || 0} credits
                                </span>
                            </div>
                        </div>
                    </div>

                    {/* Payment Summary */}
                    <div className="bg-card rounded-lg border border-border p-6">
                        <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                            <BanknotesIcon className="w-5 h-5" />
                            Payment Summary
                        </h3>
                        <div className="space-y-4">
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Average Order Value:</span>
                                <span className="font-medium">
                                    {dashboardData ? formatAmount(dashboardData.paymentAnalytics.averageOrderValue) : '₹0.00'}
                                </span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Favorite Package:</span>
                                <span className="font-medium">{dashboardData?.paymentAnalytics.mostPopularPackage || 'None'}</span>
                            </div>
                            <div className="flex justify-between">
                                <span className="text-muted-foreground">Last Purchase:</span>
                                <span className="font-medium">
                                    {formatDate(dashboardData?.billingInfo.lastPurchaseDate)}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Quick Actions */}
            <div className="bg-secondary/20 rounded-lg p-4">
                <div className="flex items-center justify-between">
                    <div>
                        <h4 className="font-medium text-foreground">Need more credits?</h4>
                        <p className="text-sm text-muted-foreground">
                            Purchase credits to continue using AegisGrader
                        </p>
                    </div>
                    <button className="px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors">
                        Buy Credits
                    </button>
                </div>
            </div>
        </div>
    );
};

export default BillingStats;
