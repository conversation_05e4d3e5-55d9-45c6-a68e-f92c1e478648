import React from 'react';

interface BillingSkeletonProps {
    type?: 'dashboard' | 'history' | 'analytics' | 'stats';
    className?: string;
}

const BillingSkeleton: React.FC<BillingSkeletonProps> = ({ 
    type = 'dashboard', 
    className = "" 
}) => {
    const SkeletonBox = ({ className: boxClassName = "" }: { className?: string }) => (
        <div className={`bg-secondary/50 rounded animate-pulse ${boxClassName}`} />
    );

    const SkeletonLine = ({ className: lineClassName = "" }: { className?: string }) => (
        <div className={`bg-secondary/50 rounded h-4 animate-pulse ${lineClassName}`} />
    );

    if (type === 'stats') {
        return (
            <div className={`space-y-6 ${className}`}>
                {/* Credit Balance Skeleton */}
                <div className="bg-card rounded-lg border border-border p-4">
                    <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                            <SkeletonBox className="w-10 h-10 rounded-full" />
                            <div className="space-y-2">
                                <SkeletonLine className="w-24 h-5" />
                                <SkeletonLine className="w-32 h-3" />
                            </div>
                        </div>
                        <SkeletonBox className="w-20 h-8 rounded-lg" />
                    </div>
                </div>

                {/* Stats Grid Skeleton */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {[...Array(4)].map((_, i) => (
                        <div key={i} className="bg-card rounded-lg border border-border p-4">
                            <div className="flex items-center gap-3">
                                <SkeletonBox className="w-10 h-10 rounded-full" />
                                <div className="space-y-2">
                                    <SkeletonLine className="w-16 h-3" />
                                    <SkeletonLine className="w-12 h-6" />
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Detailed Stats Skeleton */}
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {[...Array(2)].map((_, i) => (
                        <div key={i} className="bg-card rounded-lg border border-border p-6">
                            <div className="flex items-center gap-2 mb-4">
                                <SkeletonBox className="w-5 h-5" />
                                <SkeletonLine className="w-24 h-5" />
                            </div>
                            <div className="space-y-4">
                                {[...Array(3)].map((_, j) => (
                                    <div key={j} className="flex justify-between">
                                        <SkeletonLine className="w-20 h-4" />
                                        <SkeletonLine className="w-16 h-4" />
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        );
    }

    if (type === 'history') {
        return (
            <div className={`space-y-4 ${className}`}>
                {/* Filters Skeleton */}
                <div className="bg-card rounded-lg border border-border p-4">
                    <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-2">
                            <SkeletonBox className="w-5 h-5" />
                            <SkeletonLine className="w-16 h-5" />
                        </div>
                        <SkeletonLine className="w-20 h-4" />
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        {[...Array(4)].map((_, i) => (
                            <div key={i} className="space-y-2">
                                <SkeletonLine className="w-12 h-4" />
                                <SkeletonBox className="w-full h-10 rounded-lg" />
                            </div>
                        ))}
                    </div>
                </div>

                {/* Transaction List Skeleton */}
                <div className="bg-card rounded-lg border border-border">
                    <div className="flex items-center justify-between p-4 border-b border-border">
                        <div className="flex items-center gap-3">
                            <SkeletonBox className="w-5 h-5" />
                            <SkeletonLine className="w-32 h-5" />
                            <SkeletonBox className="w-20 h-6 rounded-full" />
                        </div>
                    </div>
                    <div className="divide-y divide-border">
                        {[...Array(5)].map((_, i) => (
                            <div key={i} className="p-4">
                                <div className="flex items-center justify-between">
                                    <div className="flex items-center gap-3">
                                        <SkeletonBox className="w-10 h-10 rounded-full" />
                                        <div className="space-y-2">
                                            <SkeletonLine className="w-32 h-4" />
                                            <SkeletonLine className="w-48 h-3" />
                                            <SkeletonLine className="w-24 h-3" />
                                        </div>
                                    </div>
                                    <div className="text-right space-y-2">
                                        <SkeletonLine className="w-20 h-4" />
                                        <SkeletonLine className="w-16 h-3" />
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    if (type === 'analytics') {
        return (
            <div className={`space-y-6 ${className}`}>
                {/* Header Skeleton */}
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                        <SkeletonBox className="w-6 h-6" />
                        <SkeletonLine className="w-32 h-6" />
                    </div>
                    <SkeletonBox className="w-32 h-10 rounded-lg" />
                </div>

                {/* Key Metrics Skeleton */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {[...Array(3)].map((_, i) => (
                        <div key={i} className="bg-card rounded-lg border border-border p-4">
                            <div className="flex items-center gap-3">
                                <SkeletonBox className="w-10 h-10 rounded-full" />
                                <div className="space-y-2">
                                    <SkeletonLine className="w-20 h-3" />
                                    <SkeletonLine className="w-12 h-8" />
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Usage by Feature Skeleton */}
                <div className="bg-card rounded-lg border border-border p-6">
                    <SkeletonLine className="w-32 h-5 mb-4" />
                    <div className="space-y-4">
                        {[...Array(3)].map((_, i) => (
                            <div key={i} className="space-y-2">
                                <div className="flex justify-between items-center">
                                    <SkeletonLine className="w-24 h-4" />
                                    <SkeletonLine className="w-20 h-4" />
                                </div>
                                <SkeletonBox className="w-full h-2 rounded-full" />
                                <SkeletonLine className="w-32 h-3" />
                            </div>
                        ))}
                    </div>
                </div>

                {/* Recent Usage Trend Skeleton */}
                <div className="bg-card rounded-lg border border-border p-6">
                    <SkeletonLine className="w-40 h-5 mb-4" />
                    <div className="space-y-3">
                        {[...Array(7)].map((_, i) => (
                            <div key={i} className="flex items-center justify-between py-2">
                                <div className="flex items-center gap-3">
                                    <SkeletonBox className="w-4 h-4" />
                                    <SkeletonLine className="w-16 h-4" />
                                </div>
                                <SkeletonLine className="w-24 h-4" />
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        );
    }

    // Default dashboard skeleton
    return (
        <div className={`space-y-6 ${className}`}>
            {/* Header Skeleton */}
            <div className="space-y-4">
                <div className="flex items-center gap-4">
                    <SkeletonBox className="w-10 h-10" />
                    <div className="space-y-2">
                        <SkeletonLine className="w-48 h-8" />
                        <SkeletonLine className="w-64 h-4" />
                    </div>
                </div>
                
                {/* Tab Navigation Skeleton */}
                <div className="flex flex-col sm:flex-row gap-2">
                    {[...Array(3)].map((_, i) => (
                        <SkeletonBox key={i} className="h-16 flex-1 rounded-lg" />
                    ))}
                </div>
            </div>

            {/* Content Skeleton */}
            <div className="space-y-6">
                {/* Stats Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {[...Array(4)].map((_, i) => (
                        <SkeletonBox key={i} className="h-24 rounded-lg" />
                    ))}
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[...Array(2)].map((_, i) => (
                        <SkeletonBox key={i} className="h-32 rounded-lg" />
                    ))}
                </div>

                {/* Recent Transactions */}
                <SkeletonBox className="h-64 rounded-lg" />
            </div>
        </div>
    );
};

export default BillingSkeleton;
