import React, { useState } from 'react';

// Enhanced date/time picker components
const DateTimePicker: React.FC<{
  type: 'date' | 'time';
  value: string;
  onChange: (value: string) => void;
  min?: string;
  required?: boolean;
  label: string;
  icon: React.ComponentType<any>;
}> = ({ type, value, onChange, min, required, label, icon: Icon }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="relative">
      <label className="block text-sm font-medium text-muted-foreground mb-1.5">
        {label}{required && "*"}
      </label>
      <div className="relative">
        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <Icon className="h-4 w-4 text-muted-foreground" />
        </div>
        <input
          type={type}
          className="w-full rounded-lg border border-border pl-10 pr-3 py-2.5 text-sm focus:ring-2 focus:ring-accent focus:border-accent transition-colors bg-white hover:bg-gray-50 focus:bg-white"
          value={value}
          min={min}
          onChange={(e) => onChange(e.target.value)}
          required={required}
          onFocus={() => setIsOpen(true)}
          onBlur={() => setTimeout(() => setIsOpen(false), 100)}
        />
      </div>
    </div>
  );
};

export default DateTimePicker;
