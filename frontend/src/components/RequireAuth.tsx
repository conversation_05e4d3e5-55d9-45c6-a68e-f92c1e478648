import React, { useEffect, useState, useRef } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useUser } from '../contexts/userContext';
import { axiosDefault } from '../axios';

interface RequireAuthProps {
  children: React.ReactNode;
}

const RequireAuth: React.FC<RequireAuthProps> = ({ children }) => {
  const { user, setUser } = useUser();
  const location = useLocation();
  const [authState, setAuthState] = useState<'loading' | 'authenticated' | 'unauthenticated'>('loading');
  const hasInitialized = useRef(false);

  useEffect(() => {
    const checkAuthentication = async () => {
      // Prevent multiple initialization attempts
      if (hasInitialized.current) return;
      hasInitialized.current = true;

      try {
        const email = sessionStorage.getItem('email');
        const role = sessionStorage.getItem('role');

        console.log('RequireAuth: Checking authentication', {
          email,
          role,
          hasAccessToken: !!user?.accessToken,
          userEmail: user?.email
        });

        // If no email/role in sessionStorage, user is not logged in
        if (!email || !role) {
          console.log('RequireAuth: No email/role in sessionStorage');
          setAuthState('unauthenticated');
          return;
        }

        // Try to refresh the token first
        console.log('RequireAuth: Attempting token refresh...');
        try {
          const refreshResponse = await axiosDefault.post('/api/refresh/', { role }, {
            withCredentials: true,
          });

          console.log('RequireAuth: Refresh response:', refreshResponse.data);

          if (refreshResponse.data?.accessToken) {
            console.log('RequireAuth: Token refreshed successfully');

            // Fetch user details with the new token
            const detailsResponse = await axiosDefault.post(`/api/details/getDetail${role}`,
              { email },
              {
                headers: {
                  'Authorization': `Bearer ${refreshResponse.data.accessToken}`,
                  'Content-Type': 'application/json'
                },
                withCredentials: true
              }
            );

            console.log('RequireAuth: User details response:', detailsResponse.data);
            const userData = detailsResponse?.data;
            setUser({ ...userData, accessToken: refreshResponse.data.accessToken });
            setAuthState('authenticated');
          } else {
            throw new Error('No access token received from refresh');
          }
        } catch (refreshError: any) {
          console.error('RequireAuth: Token refresh failed:', refreshError);
          console.error('RequireAuth: Refresh error response:', refreshError.response?.data);
          console.error('RequireAuth: Refresh error status:', refreshError.response?.status);
          // Clear stale session data
          sessionStorage.removeItem('email');
          sessionStorage.removeItem('role');
          setUser(null);
          setAuthState('unauthenticated');
        }

      } catch (error) {
        console.error('RequireAuth: Error during authentication check:', error);
        setAuthState('unauthenticated');
      }
    };

    checkAuthentication();
  }, [setUser]); // Include setUser in dependencies

  // Show loading spinner while checking authentication
  if (authState === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
          <p className="text-foreground/70">Checking authentication...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (authState === 'unauthenticated') {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Render children if authenticated
  return <>{children}</>;
};

export default RequireAuth;
