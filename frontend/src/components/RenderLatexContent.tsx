import React, { ComponentType, ReactNode } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import ReactKatex from "@pkasila/react-katex";
import 'katex/dist/katex.min.css'

interface EnhancedMarkdownProps {
    text: string
    isUserMessage?: boolean
}

interface CodeProps {
    inline?: boolean
    className?: string
    children: ReactNode
}

interface StyleProps {
    className?: string
    children: ReactNode
}

const MarkdownComponents: Record<string, ComponentType<any>> = {
    // === HEADERS ===
    h1: ({ children }: { children: ReactNode }) => (
        <h3 className="
      text-xl font-bold 
      border-b-2 
      text-[color:var(--primary-foreground)] 
      bg-[color:var(--primary)] 
      border-[color:var(--primary)]/30 
      pb-2 mb-3
    ">
            {children}
        </h3>
    ),
    h2: ({ children }: { children: ReactNode }) => (
        <h4 className="
      text-lg font-semibold 
      border-b 
      text-[color:var(--accent-foreground)] 
      bg-[color:var(--accent)] 
      border-[color:var(--accent)]/30 
      pb-1 mb-2
    ">
            {children}
        </h4>
    ),
    h3: ({ children }: { children: ReactNode }) => (
        <h5 className="
      text-base font-medium 
      text-[color:var(--secondary-foreground)] 
      bg-[color:var(--secondary)] 
      mb-1
    ">
            {children}
        </h5>
    ),

    // === PARAGRAPHS ===
    p: ({ children }: { children: ReactNode }) => (
        <p className="
      text-sm leading-relaxed my-1 
      text-[color:var(--foreground)]
    ">
            {children}
        </p>
    ),

    // === LISTS ===
    ul: ({ children }: { children: ReactNode }) => (
        <ul className="list-disc pl-5 space-y-1 my-2">
            {children}
        </ul>
    ),
    ol: ({ children }: { children: ReactNode }) => (
        <ol className="list-decimal pl-5 space-y-1 my-2">
            {children}
        </ol>
    ),
    li: ({ children }: { children: ReactNode }) => (
        <li className="
      text-sm leading-relaxed pl-1 
      text-[color:var(--foreground)]
    ">
            {children}
        </li>
    ),

    // === CODE BLOCKS & INLINE CODE ===
    code: ({ inline, className, children }: CodeProps) => {
        if (inline) {
            return (
                <code className="
          px-1.5 py-0.5 rounded 
          bg-[color:var(--info-light)] 
          text-[color:var(--info-foreground)] 
          font-mono text-sm 
          border border-[color:var(--info)]
        ">
                    {children}
                </code>
            )
        }
        return (
            <pre className="
        p-3 rounded-lg overflow-auto text-sm 
        bg-[color:var(--muted)] 
        text-[color:var(--foreground)] 
        border border-[color:var(--border)]
      ">
                <code className={className}>{children}</code>
            </pre>
        )
    },

    // === STRONG / EMPHASIS ===
    strong: ({ children }: { children: ReactNode }) => (
        <strong className="
      font-bold rounded-md px-1.5 py-0.5 
      bg-[color:var(--primary)] 
      text-[color:var(--primary-foreground)]
    ">
            {children}
        </strong>
    ),
    em: ({ children }: { children: ReactNode }) => (
        <em className="
      italic font-medium rounded px-1 py-0.5 
      bg-[color:var(--secondary)] 
      text-[color:var(--secondary-foreground)]
    ">
            {children}
        </em>
    ),

    // === TABLES ===
    table: ({ children }: { children: ReactNode }) => (
        <table className="
      table-auto border-collapse w-full mb-4 
      text-[color:var(--foreground)]
    ">
            {children}
        </table>
    ),
    th: ({ children }: { children: ReactNode }) => (
        <th className="
      border px-2 py-1 
      bg-[color:var(--muted)] 
      text-[color:var(--muted-foreground)] 
      text-left text-sm
    ">
            {children}
        </th>
    ),
    td: ({ children }: { children: ReactNode }) => (
        <td className="border px-2 py-1 text-sm">
            {children}
        </td>
    ),

    // === BLOCKQUOTES ===
    blockquote: ({ children }: { children: ReactNode }) => (
        <blockquote className="
      pl-4 my-4 border-l-4 
      border-[color:var(--primary)] 
      bg-[color:var(--primary-light)]/10 
      text-[color:var(--primary-foreground)]/90
      rounded-r-lg py-2 pr-2
    ">
            {children}
        </blockquote>
    ),

    // === HORIZONTAL RULE ===
    hr: () => (
        <hr className="
      my-8 border-none h-px 
      bg-gradient-to-r 
      from-[color:var(--muted)]/20 
      via-[color:var(--muted)] 
      to-[color:var(--muted)]/20
    " />
    ),

    // === LINKS ===
    a: ({ href, children }: { href?: string; children: ReactNode }) => (
        <a
            href={href}
            target="_blank"
            rel="noopener noreferrer"
            className="
        text-[color:var(--info)] 
        hover:text-[color:var(--info-foreground)]
        underline decoration-[color:var(--info)]/30
        hover:decoration-[color:var(--info)]
        transition-colors duration-200
        rounded px-1 py-0.5
        hover:bg-[color:var(--info-light)]/10
      "
        >
            {children}
        </a>
    ),

    // === IMAGES ===
    img: ({ src, alt, title }: { src?: string; alt?: string; title?: string }) => (
        <div className="relative my-4">
            <img
                src={src}
                alt={alt || ''}
                title={title}
                loading="lazy"
                className="
          max-w-full h-auto rounded-lg 
          border border-[color:var(--border)]
          bg-[color:var(--muted)]
          transition-opacity duration-200
          hover:opacity-95
        "
                onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.onerror = null;
                    target.className = `
            max-w-full h-32 rounded-lg 
            border border-[color:var(--destructive)] 
            bg-[color:var(--destructive-light)]/10
            flex items-center justify-center
            text-[color:var(--destructive)]
            text-sm font-medium
          `;
                    target.alt = 'Failed to load image';
                }}
            />
        </div>
    ),

    // === TASK LISTS ===
    input: ({ type, checked }: { type?: string; checked?: boolean }) => {
        if (type === 'checkbox') {
            return (
                <input
                    type="checkbox"
                    checked={checked}
                    readOnly
                    className="
            mr-2 rounded 
            border-[color:var(--border)]
            checked:bg-[color:var(--primary)]
            checked:border-[color:var(--primary)]
            focus:ring-[color:var(--ring)]
          "
                />
            )
        }
        return null
    },

    // === STRIKETHROUGH ===
    del: ({ children }: { children: ReactNode }) => (
        <del className="
      line-through decoration-2 
      decoration-[color:var(--destructive)]/70
      text-[color:var(--muted-foreground)]
    ">
            {children}
        </del>
    ),

    // === DEFINITION LISTS ===
    dl: ({ children }: { children: ReactNode }) => (
        <dl className="
      space-y-4 my-4
      text-[color:var(--foreground)]
    ">
            {children}
        </dl>
    ),
    dt: ({ children }: { children: ReactNode }) => (
        <dt className="
      font-semibold text-base
      text-[color:var(--primary-foreground)]
      bg-[color:var(--primary)]/10
      px-2 py-1 rounded-t-lg
    ">
            {children}
        </dt>
    ),
    dd: ({ children }: { children: ReactNode }) => (
        <dd className="
      pl-4 text-sm
      border-l-2 border-[color:var(--primary)]/30
      ml-2
    ">
            {children}
        </dd>
    ),

    // === KaTeX MATH OVERRIDES ===
    div: ({ className, children }: StyleProps) => {
        // display math container
        if (className?.includes('math')) {
            return (
                <div className="
          my-6 p-4 rounded-lg text-center 
          bg-[color:var(--accent-light)] 
          text-[color:var(--accent-foreground)] 
          border border-[color:var(--accent)]
        ">
                    {children}
                </div>
            )
        }
        return <div>{children}</div>
    },
    span: ({ className, children }: StyleProps) => {
        // inline math
        if (className?.includes('math')) {
            return (
                <span className="
          inline-block px-1 rounded-md 
          bg-[color:var(--accent-light)] 
          text-[color:var(--accent-foreground)]
        ">
                    {children}
                </span>
            )
        }
        return <span>{children}</span>
    },

    // === SUBSCRIPT & SUPERSCRIPT ===
    sub: ({ children }: { children: ReactNode }) => (
        <sub className="
      text-xs relative -bottom-1
      text-[color:var(--foreground)]/80
      bg-[color:var(--muted)]/10
      px-0.5 rounded
    ">
            {children}
        </sub>
    ),
    sup: ({ children }: { children: ReactNode }) => (
        <sup className="
      text-xs relative top-[-0.5em]
      text-[color:var(--foreground)]/80
      bg-[color:var(--muted)]/10
      px-0.5 rounded
    ">
            {children}
        </sup>
    ),
}

export const EnhancedMarkdown: React.FC<EnhancedMarkdownProps> = ({
    text,
    isUserMessage = false,
}) => {
    if (!text) return null

    const containerBg = isUserMessage
        ? 'bg-[color:var(--accent)]'
        : 'bg-[color:var(--popover)]'

    return (
        <div
            className={`
        enhanced-content rounded-lg space-y-4 
        ${containerBg}
        text-[color:var(--foreground)]
      `}
        >
            <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkMath]}
                rehypePlugins={[rehypeKatex]}
                components={MarkdownComponents}
                skipHtml={false}
            >
                {text}
            </ReactMarkdown>
        </div>
    )
}

export const renderLatexContent = (text: string) => {
    return text?.split(/(\$\$?[^$]+\$\$?)/).map((part, index) => {
        const isEquation = part.startsWith('$') && part.endsWith('$');

        return isEquation ? (
            <ReactKatex
                key={index}
            >
                {part.replace(/^\$|\$$/g, '')}
            </ReactKatex>
        ) : (
            <React.Fragment key={index}>{part}</React.Fragment>
        );
    });
};
