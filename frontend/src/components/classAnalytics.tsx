import React, { useState } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON>, Legend } from 'recharts';
import { ArrowTrendingUpIcon, ArrowUpCircleIcon, ExclamationTriangleIcon, ChevronRightIcon, ChevronDownIcon } from '@heroicons/react/24/outline';

const ClassAnalytics = ({ classData }: { classData: any }) => {
    const analytics = classData.analytics;
    const [expandedSections, setExpandedSections] = useState({
        topics: true,  // Start with topics expanded by default
        trend: true    // Start with trend expanded by default
        // Add more sections as needed
    });

    const getStudentGroupCount = (group: any) => {
        return analytics?.studentGroups?.[group]?.length || 0;
    };

    const toggleSection = (section: keyof typeof expandedSections) => {
        setExpandedSections(prev => ({
          ...prev,
          [section]: !prev[section]
        }));
      };

    // Custom formatter for tooltips
    const percentFormatter = (value: any) => `${(value * 100).toFixed(1)}%`;

    // Custom formatter for XAxis tick labels
    const topicFormatter = (topicId: string) => {
        if (topicId.length > 10) {
            return `${topicId.substring(0, 10)}...`;
        }
        return topicId;
    };

    return (
        <div className="space-y-4">
            {/* Stats Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <div className="bg-card p-3 rounded-lg border shadow-sm">
                    <div className="flex items-center gap-2 mb-1">
                        <ArrowTrendingUpIcon className="w-4 h-4 text-blue-500" />
                        <span className="text-xs text-gray-500">Class Proficiency</span>
                    </div>
                    <span className="text-xl font-semibold">
                        {(analytics?.classPerformance?.averageProficiency * 100 || 0).toFixed(1)}%
                    </span>
                </div>

                <div className="bg-card p-3 rounded-lg border shadow-sm">
                    <div className="flex items-center gap-2 mb-1">
                        <ArrowUpCircleIcon className="w-4 h-4 text-green-500" />
                        <span className="text-xs text-gray-500">On Track</span>
                    </div>
                    <span className="text-xl font-semibold">
                        {getStudentGroupCount('onTrack')}
                    </span>
                </div>

                <div className="bg-card p-3 rounded-lg border shadow-sm">
                    <div className="flex items-center gap-2 mb-1">
                        <ExclamationTriangleIcon className="w-4 h-4 text-yellow-500" />
                        <span className="text-xs text-gray-500">Needs Help</span>
                    </div>
                    <span className="text-xl font-semibold">
                        {getStudentGroupCount('needsIntervention')}
                    </span>
                </div>

                <div className="bg-card p-3 rounded-lg border shadow-sm">
                    <div className="flex items-center gap-2 mb-1">
                        <ArrowTrendingUpIcon className="w-4 h-4 text-purple-500" />
                        <span className="text-xs text-gray-500">Accelerated</span>
                    </div>
                    <span className="text-xl font-semibold">
                        {getStudentGroupCount('accelerated')}
                    </span>
                </div>
            </div>

            {/* Topic Performance (Collapsible) */}
            <div className="bg-card rounded-lg border shadow-sm">
                <button
                    className="w-full p-3 flex justify-between items-center text-left"
                    onClick={() => toggleSection('topics')}
                >
                    <h3 className="text-base font-semibold">Topic Performance</h3>
                    {expandedSections.topics ? <ChevronDownIcon className="w-4 h-4" /> : <ChevronRightIcon className="w-4 h-4" />}
                </button>

                {expandedSections.topics && (
                    <div className="p-3 pt-0">
                        <div className="h-64 p-4">
                            <ResponsiveContainer width="100%" height="100%">
                                <BarChart
                                    data={analytics?.topicInsights || []}
                                    margin={{ top: 5, right: 10, left: 0, bottom: 20 }}
                                    barSize={20}
                                >
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis
                                        dataKey="topicId"
                                        angle={-45}
                                        textAnchor="end"
                                        height={60}
                                        tick={{ fontSize: 10 }}
                                        tickFormatter={topicFormatter}
                                    />
                                    <YAxis
                                        tickFormatter={percentFormatter}
                                        domain={[0, 1]}
                                        tick={{ fontSize: 10 }}
                                    />
                                    <Tooltip formatter={percentFormatter} />
                                    <Legend layout='horizontal' verticalAlign='top' align='right' wrapperStyle={{ fontSize: 10}} />
                                    <Bar dataKey="averageProficiency" fill="hsl(var(--chart-primary))" name="Proficiency" />
                                    <Bar dataKey="masteryRate" fill="hsl(var(--chart-secondary))" name="Mastery" />
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                    </div>
                )}
            </div>

            {/* Proficiency Trend (Collapsible) */}
            <div className="bg-card rounded-lg border shadow-sm">
                <button
                    className="w-full p-3 flex justify-between items-center text-left"
                    onClick={() => toggleSection('trend')}
                >
                    <h3 className="text-base font-semibold">Proficiency Trend</h3>
                    {expandedSections.trend ? <ChevronDownIcon className="w-4 h-4" /> : <ChevronRightIcon className="w-4 h-4" />}
                </button>

                {expandedSections.trend && (
                    <div className="p-3 pt-0">
                        <div className="h-64">
                            <ResponsiveContainer width="100%" height="100%">
                                <LineChart
                                    data={analytics?.classPerformance?.proficiencyTrend || []}
                                    margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
                                >
                                    <CartesianGrid strokeDasharray="3 3" />
                                    <XAxis
                                        dataKey="date"
                                        tick={{ fontSize: 10 }}
                                    />
                                    <YAxis
                                        tickFormatter={percentFormatter}
                                        domain={[0, 1]}
                                        tick={{ fontSize: 10 }}
                                    />
                                    <Tooltip formatter={percentFormatter} />
                                    <Legend wrapperStyle={{ fontSize: 10 }} />
                                    <Line
                                        type="monotone"
                                        dataKey="averageProficiency"
                                        stroke="hsl(var(--chart-primary))"
                                        name="Proficiency"
                                        activeDot={{ r: 6 }}
                                        strokeWidth={2}
                                    />
                                </LineChart>
                            </ResponsiveContainer>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default ClassAnalytics;