import { ThumbsUp, ThumbsDown, Bug, Send, Check, MessageCircle } from 'lucide-react';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { useState, useEffect } from 'react';
import FeedbackImageComp from "./FeedbackImage";


interface Feedback {
  role: string | undefined,
  type: string,
  comment: string,
  id: string | undefined,
  images: File[]
}

const FeedBackComp = (roleid: any) => {
  const [sendingRequest, setSendingRequest] = useState(false);
  const [feedback, setFeedback] = useState<Feedback>({
    role: roleid.role,
    type: '',
    comment: '',
    id: roleid.id,
    images: []
  });

  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const axiosPrivate = useAxiosPrivate();

  // Handle responsive behavior
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 640);
    };

    handleResize(); // Initial check
    window.addEventListener('resize', handleResize);

    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const handleTypeSelect = (type: string) => {
    setFeedback(prev => ({ ...prev, type }));
  }

  const handleCommentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setFeedback(prev => ({ ...prev, comment: e.target.value }));
  }

  const handleImageChange = ( images: File[]) => {
    console.log(`got images: ${images}`)
    setFeedback(prev => ({ ...prev, images: images}))
  }

  const handleSubmit = async () => {
    // Here we would typically send the feedback to your server
    try {
      const formData = new FormData();
      formData.append('comment', feedback.comment);
      formData.append('type', feedback.type);
      formData.append('id', roleid.id);
      formData.append('role', roleid.role);

      feedback.images.forEach((image) => {
        formData.append('images', image)
      })
      setSendingRequest(true);

      await axiosPrivate.post('/api/feedback/feed/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      });
    } catch (error) {
      console.error('Error submitting feedback: ', error);
    }
    setIsSubmitted(true);
    setSendingRequest(false);

    setTimeout(() => {
      setIsSubmitted(false);
      setIsExpanded(false);
      setFeedback(prev => ({ ...prev, type: '', comment: '' }));
    }, 2000);
  }

  const feedbackTypes = [
    { type: 'like', icon: ThumbsUp, label: 'Like', color: 'text-success hover:bg-success/10' },
    { type: 'dislike', icon: ThumbsDown, label: 'Dislike', color: 'text-danger hover:bg-danger/10' },
    { type: 'bug', icon: Bug, label: 'Bug Report', color: 'text-warning hover:bg-warning/10' }
  ];

  if (isSubmitted) {
    return (
      <div className={`fixed z-50 ${isMobile ? 'bottom-20 right-4 left-4' : 'bottom-6 right-6'}`}>
        <div className="bg-success text-success-foreground px-4 sm:px-6 py-3 rounded-lg shadow-lg flex items-center gap-2 animate-pulse border border-border">
          <Check size={isMobile ? 18 : 20} />
          <span className="font-medium text-sm sm:text-base">Feedback submitted!</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`fixed z-50 ${isMobile ? 'bottom-20 right-4 left-4' : 'bottom-6 right-6'}`}>
      <div className={`bg-card rounded-xl shadow-xl border border-border transition-all duration-300 ease-in-out ${
        isExpanded
          ? isMobile ? 'w-full' : 'w-96'
          : isMobile ? 'w-14' : 'w-16'
        }`}>

        {/* Initial Feedback Button */}
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className={`flex items-center gap-2 px-3 sm:px-4 py-2 sm:py-3 bg-primary text-primary-foreground rounded-xl hover:bg-primary/90 transition-all duration-200 ${
            isExpanded ? 'rounded-b-none w-full' : 'shadow-lg hover:shadow-xl'
          }`}
        >
          <MessageCircle size={isMobile ? 18 : 20} />
          {isExpanded && !isMobile &&
            <span className="font-medium text-sm sm:text-base">Feedback</span>
          }
          {isExpanded && isMobile &&
            <span className="font-medium text-sm">Feedback</span>
          }
          <div className={`transition-transform duration-300 ${isExpanded ? 'rotate-180 absolute right-3 sm:right-4' : ''}`}>
            <svg width={isMobile ? "14" : "16"} height={isMobile ? "14" : "16"} viewBox="0 0 16 16" fill="currentColor">
              <path d="M4 6l4 4 4-4" stroke="currentColor" strokeWidth="2" fill="none" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </div>
        </button>

        {/* Expanded Content */}
        <div className={`overflow-auto transition-all duration-300 ease-in-out ${
          isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
        }`}>
          <div className={`p-4 sm:p-6 pt-3 sm:pt-4 space-y-3 sm:space-y-4`}>

            {/* Feedback Type Buttons */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground block">How was your experience?</label>
              <div className={`grid ${isMobile ? 'grid-cols-1 gap-2' : 'grid-cols-3 gap-2'}`}>
                {feedbackTypes.map(({ type, icon: Icon, label, color }) => (
                  <button
                    key={type}
                    onClick={() => handleTypeSelect(type)}
                    className={`flex ${isMobile ? 'flex-row' : 'flex-col'} items-center gap-2 sm:gap-1 p-2 sm:p-3 rounded-lg border-2 transition-all duration-200 ${
                      feedback.type === type
                        ? 'border-primary bg-primary/10 text-primary'
                        : `border-border ${color} hover:border-border/60`
                    }`}
                  >
                    <Icon size={isMobile ? 18 : 20} />
                    <span className="text-xs sm:text-sm font-medium">{label}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Comment Box */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground block">
                Tell us more (optional)
              </label>
              <textarea
                value={feedback.comment}
                onChange={handleCommentChange}
                placeholder="Share your thoughts..."
                className="w-full px-3 py-2 border border-input bg-background text-foreground rounded-lg resize-none focus:ring-2 focus:ring-ring focus:border-transparent transition-all duration-200 placeholder:text-muted-foreground"
                rows={isMobile ? 2 : 3}
              />
            </div>

            {/*Upload Image Section*/}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground block">
                Add screenshots
              </label>
              <FeedbackImageComp
                onImagesChange={handleImageChange}
                maxImages={3}
                maxSizeKB={5000}
              />
            </div>

            {/* Submit Button */}
            <button
              onClick={handleSubmit}
              disabled={!feedback.type || sendingRequest}
              className={`w-full flex items-center justify-center gap-2 px-4 py-2 sm:py-3 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base ${
                feedback.type
                  ? 'bg-primary text-primary-foreground hover:bg-primary/90 hover:shadow-md'
                  : 'bg-muted text-muted-foreground cursor-not-allowed'
              }`}
            >
              <Send size={isMobile ? 14 : 16} />
              {sendingRequest ? 'Submitting...' : 'Submit Feedback'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default FeedBackComp;