import React from 'react';
import { Brain, Target, Book, Clock, Star, TrendingUp, Award } from 'lucide-react';

const StudentStats = () => {
    const stats = [
        {
            icon: <Star className="h-5 w-5 text-[hsl(var(--info))]" />,
            label: "Daily Streak",
            value: "7 days",
            subtitle: "Keep it up! 🔥",
            badge: "Today",
            bgColor: "bg-[hsl(var(--info)/0.2)]",
            badgeBg: "bg-[hsl(var(--info)/0.1)]",
            badgeText: "text-[hsl(var(--info))]"
        },
        {
            icon: <Target className="h-5 w-5 text-[hsl(var(--success))]" />,
            label: "Weekly Goals",
            value: "4/5",
            subtitle: "Almost there!",
            badge: "Goals",
            bgColor: "bg-[hsl(var(--success)/0.2)]",
            badgeBg: "bg-[hsl(var(--success)/0.1)]",
            badgeText: "text-[hsl(var(--success))]"
        },
        {
            icon: <Award className="h-5 w-5 text-[hsl(var(--subject-english))]" />,
            label: "Experience Points",
            value: "1,240",
            subtitle: "Level 5 - Advanced",
            badge: "XP",
            bgColor: "bg-[hsl(var(--subject-english)/0.2)]",
            badgeBg: "bg-[hsl(var(--subject-english)/0.1)]",
            badgeText: "text-[hsl(var(--subject-english))]"
        },
        {
            icon: <Clock className="h-5 w-5 text-orange-600" />,
            label: "Study Time",
            value: "2.5h",
            subtitle: "Today's focus time",
            badge: "Time",
            bgColor: "bg-orange-100",
            badgeBg: "bg-orange-50",
            badgeText: "text-orange-600"
        }
    ];

    return (
        <div className="space-y-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {stats.map((stat, index) => (
                    <div key={index} className="bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-all duration-200">
                        {/* Mobile View */}
                        <div className="md:hidden flex items-center gap-3">
                            <div className={`${stat.bgColor} p-2 rounded-lg`}>
                                {stat.icon}
                            </div>
                            <span className="text-lg font-bold text-gray-900">{stat.value}</span>
                        </div>

                        {/* Desktop View */}
                        <div className="hidden md:block">
                            <div className="flex items-center justify-between">
                                <div className={`${stat.bgColor} p-2 rounded-lg`}>
                                    {stat.icon}
                                </div>
                                <span className={`text-xs font-medium px-2 py-1 ${stat.badgeBg} ${stat.badgeText} rounded-full`}>
                                    {stat.badge}
                                </span>
                            </div>
                            <div className="mt-3">
                                <h3 className="text-sm font-medium text-gray-600">{stat.label}</h3>
                                <div className="flex items-end gap-2">
                                    <span className="text-2xl font-bold text-gray-900">{stat.value}</span>
                                </div>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">{stat.subtitle}</p>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default StudentStats;