import { ListCheck } from 'lucide-react';
import React from 'react';

interface ConfirmSubmitDialogProps {
  show: boolean;
  onConfirm: () => void;
  onCancel: () => void;
  attemptedQuestions: number;
  totalQuestions: number;
  isSubmitting?: boolean;
}

const ConfirmSubmitDialog: React.FC<ConfirmSubmitDialogProps> = ({
  show,
  onConfirm,
  onCancel,
  attemptedQuestions,
  totalQuestions,
  isSubmitting = false,
}) => {
  if (!show) return null;

  return (
    <div className="fixed inset-0 backdrop-blur-sm bg-black/50 flex items-center justify-center z-50">
      <div className="bg-card border border-border p-6 rounded-xl shadow-2xl max-w-md w-full mx-4">

        <div className='flex items-center gap-3 mb-4'>
          <ListCheck className='h-6 w-6 text-card-foreground' />
          <h2 className="text-xl font-semibold text-card-foreground">
            {isSubmitting ? 'Submitting Test...' : 'Confirm Test Submission'}
          </h2>
        </div>

        <div className="mb-6">
          <p className="text-card-foreground mb-3">
            You have attempted{' '}
            <span className="font-semibold text-primary">{attemptedQuestions}</span> out of{' '}
            <span className="font-semibold text-primary">{totalQuestions}</span> questions.
          </p>
          {attemptedQuestions < totalQuestions && !isSubmitting && (
            <p className="text-destructive font-medium bg-destructive/10 px-3 py-2 rounded-lg border border-destructive/20">
              There are still {totalQuestions - attemptedQuestions} unanswered questions.
            </p>
          )}
          {isSubmitting && (
            <p className="text-primary font-medium bg-primary/10 px-3 py-2 rounded-lg border border-primary/20">
              Please wait while your test is being submitted...
            </p>
          )}
        </div>

        {!isSubmitting && (
          <p className="text-card-foreground mb-8">Are you sure you want to submit the test?</p>
        )}
        
        <div className="flex justify-end space-x-4">
          <button
            onClick={onCancel}
            disabled={isSubmitting}
            className="px-6 py-2 text-card-foreground rounded-lg hover:bg-muted transition-colors duration-200 disabled:bg-muted disabled:cursor-not-allowed disabled:text-muted-foreground border border-border"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            disabled={isSubmitting}
            className="px-6 py-2 bg-destructive text-destructive-foreground rounded-lg hover:bg-destructive/90 transition-colors duration-200 disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Test'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConfirmSubmitDialog;