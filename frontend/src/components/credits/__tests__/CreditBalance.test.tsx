import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../../utils/test-utils';
import CreditBalance from '../CreditBalance';

// Mock the axios hook
const mockAxiosPrivate = {
  get: vi.fn()
};

vi.mock('../../../hooks/useAxiosPrivate', () => ({
  useAxiosPrivate: () => mockAxiosPrivate
}));

describe('CreditBalance', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockCreditStats = {
    currentBalance: 25,
    totalEarned: 50,
    totalSpent: 25,
    totalPurchased: 30,
    totalAmountSpent: 5000,
    lastUpdated: '2024-01-15T10:30:00Z'
  };

  it('renders loading state initially', () => {
    mockAxiosPrivate.get.mockImplementation(() => new Promise(() => {})); // Never resolves
    
    render(<CreditBalance />);
    
    expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
  });

  it('renders error state when API call fails', async () => {
    mockAxiosPrivate.get.mockRejectedValue({
      response: { data: { message: 'Network error' } }
    });
    
    render(<CreditBalance />);
    
    await waitFor(() => {
      expect(screen.getByText('Error')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Error')).toHaveClass('text-danger');
  });

  it('renders credit balance with success styling when credits are sufficient', async () => {
    mockAxiosPrivate.get.mockResolvedValue({
      data: { data: mockCreditStats }
    });
    
    render(<CreditBalance />);
    
    await waitFor(() => {
      expect(screen.getByText('25')).toBeInTheDocument();
    });
    
    expect(screen.getByText('Credits')).toBeInTheDocument();
    const container = screen.getByText('25').closest('div');
    expect(container).toHaveClass('bg-success/10', 'text-success', 'border-success/20');
  });

  it('renders warning styling when credits are low (≤5)', async () => {
    const lowCreditStats = { ...mockCreditStats, currentBalance: 4 };
    mockAxiosPrivate.get.mockResolvedValue({
      data: { data: lowCreditStats }
    });
    
    render(<CreditBalance />);
    
    await waitFor(() => {
      expect(screen.getByText('4')).toBeInTheDocument();
    });
    
    const container = screen.getByText('4').closest('div');
    expect(container).toHaveClass('bg-warning/10', 'text-warning', 'border-warning/20');
    expect(screen.getByTestId('exclamation-triangle-icon')).toHaveClass('text-warning');
  });

  it('renders critical styling when credits are critical (≤2)', async () => {
    const criticalCreditStats = { ...mockCreditStats, currentBalance: 1 };
    mockAxiosPrivate.get.mockResolvedValue({
      data: { data: criticalCreditStats }
    });
    
    render(<CreditBalance />);
    
    await waitFor(() => {
      expect(screen.getByText('1')).toBeInTheDocument();
    });
    
    const container = screen.getByText('1').closest('div');
    expect(container).toHaveClass('bg-danger/10', 'text-danger', 'border-danger/20');
    expect(screen.getByTestId('exclamation-triangle-icon')).toHaveClass('text-danger', 'animate-pulse');
  });

  it('shows tooltip on hover with appropriate content', async () => {
    mockAxiosPrivate.get.mockResolvedValue({
      data: { data: mockCreditStats }
    });
    
    render(<CreditBalance />);
    
    await waitFor(() => {
      expect(screen.getByText('25')).toBeInTheDocument();
    });
    
    const creditBadge = screen.getByText('25').closest('div');
    fireEvent.mouseEnter(creditBadge!);
    
    await waitFor(() => {
      expect(screen.getByText('✅ Credits available for AegisGrader')).toBeInTheDocument();
      expect(screen.getByText('Each test evaluation uses 1 credit')).toBeInTheDocument();
    });
  });

  it('shows purchase button in tooltip when credits are low and onPurchaseClick is provided', async () => {
    const lowCreditStats = { ...mockCreditStats, currentBalance: 3 };
    mockAxiosPrivate.get.mockResolvedValue({
      data: { data: lowCreditStats }
    });
    
    const mockOnPurchaseClick = vi.fn();
    render(<CreditBalance onPurchaseClick={mockOnPurchaseClick} />);
    
    await waitFor(() => {
      expect(screen.getByText('3')).toBeInTheDocument();
    });
    
    const creditBadge = screen.getByText('3').closest('div');
    fireEvent.mouseEnter(creditBadge!);
    
    await waitFor(() => {
      expect(screen.getByText('Purchase More Credits')).toBeInTheDocument();
    });
    
    fireEvent.click(screen.getByText('Purchase More Credits'));
    expect(mockOnPurchaseClick).toHaveBeenCalled();
  });

  it('retries API call when retry button is clicked in error state', async () => {
    mockAxiosPrivate.get
      .mockRejectedValueOnce({ response: { data: { message: 'Network error' } } })
      .mockResolvedValueOnce({ data: { data: mockCreditStats } });
    
    render(<CreditBalance />);
    
    await waitFor(() => {
      expect(screen.getByText('Error')).toBeInTheDocument();
    });
    
    const retryButton = screen.getByRole('button');
    fireEvent.click(retryButton);
    
    await waitFor(() => {
      expect(screen.getByText('25')).toBeInTheDocument();
    });
    
    expect(mockAxiosPrivate.get).toHaveBeenCalledTimes(2);
  });

  it('applies custom className prop', async () => {
    mockAxiosPrivate.get.mockResolvedValue({
      data: { data: mockCreditStats }
    });
    
    render(<CreditBalance className="custom-class" />);
    
    await waitFor(() => {
      expect(screen.getByText('25')).toBeInTheDocument();
    });
    
    const container = screen.getByText('25').closest('.custom-class');
    expect(container).toBeInTheDocument();
  });
});
