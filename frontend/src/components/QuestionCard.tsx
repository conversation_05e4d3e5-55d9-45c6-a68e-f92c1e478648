import { BookmarkIcon, XMarkIcon } from '@heroicons/react/24/outline';
import React, { useState } from 'react';
import { renderLatexContent } from './RenderLatexContent';

interface Question {
    _id: string;
    answer: string;
    assignedDiff: string;
    difficulty: number;
    discrimination_parameter: number;
    images?: string[];
    options: string[];
    question: string;
    solution?: string;
    subtopic: string[];
    topic: string[];
}

interface CardProps {
    question: {
        _id: string;
        answer: string;
        assignedDiff: string;
        difficulty: number;
        discrimination_parameter: number;
        images?: string[];
        options?: string[];
        question: string;
        solution?: string;
        subtopic: string[];
        topic: string[];
    };
    onKeep: () => void;
    onDiscard: () => void;
    style?: React.CSSProperties;
    isTopCard: boolean;
    images: (path: string) => string;
    className?: string;
}

const cleanQuestionText = (questionText: string): string => {
    if (!questionText) return '';
    return questionText.replace(/\boptions:\s*[\s\S]*$/i, '').trim();
};

const cleanOptionText = (optionText: string): string => {
    if (!optionText) return '';
    return optionText.replace(/^[A-Za-z][\)\.\:]\s*/, '');
}

const QuestionCard: React.FC<CardProps> = ({
    question,
    onKeep,
    onDiscard,
    style,
    isTopCard,
    className
}) => {
    const [isLeaving, setIsLeaving] = useState(false);
    const [direction, setDirection] = useState<'left' | 'right' | null>(null);
    const [showSolution, setShowSolution] = useState(false);
    const prefix = 'https://aegisscholar-platform-web.s3.ap-south-1.amazonaws.com/uploads/images/questions/';


    const handleAction = (action: 'keep' | 'discard') => {
        setIsLeaving(true);
        setDirection(action === 'keep' ? 'right' : 'left');
        setTimeout(() => {
            action === 'keep' ? onKeep() : onDiscard();
        }, 300);
    };

    const rotation = isTopCard ? 0 : Math.random() * 6 - 3;

    // const images = require.context(prefix, false, /\.(png|jpg|jpeg|svg)$/);


    const renderQuestionImages = () => {
        if (!question.images?.length) return null;

        return question.images
            .filter(image => !image.includes('_a'))
            .map((image, index) => {
                const imageName = image.split("/").pop();
                const imagePath = `${prefix}${imageName}`;
                // console.error(imagePath);
                return (
                    <img
                        key={index}
                        src={imagePath}
                        alt={`Question Image ${index + 1}`}
                        className="w-full h-56 object-contain rounded-lg mb-4"
                    />
                );
            });
    };

    const renderAnswerImages = () => {
        if (!question.images?.length) return null;

        return question.images
            .filter(image => image.includes('_a'))
            .map((image, index) => {
                const imageName = image.split("/").pop();
                const imagePath = `${prefix}${imageName}`;
                return (
                    <img
                        key={index}
                        src={imagePath}
                        alt={`Answer Image ${index + 1}`}
                        className="w-full h-56 object-contain rounded-lg mb-4"
                    />
                );
            });
    };
    const [showSubtopics, setShowSubtopics] = useState(false);


    return (
        <div
            className={`absolute w-full max-w-2xl mx-auto bg-card border border-border rounded-lg shadow-lg 
        transition-all duration-300 transform ${className}
        ${isLeaving && direction === 'left' ? '-translate-x-full opacity-0' : ''}
        ${isLeaving && direction === 'right' ? 'translate-x-full opacity-0' : ''}
        hover:shadow-xl h-[400px] sm:h-[500px]`}
            style={{
                ...style,
                transform: `rotate(${rotation}deg) ${isLeaving ? (direction === 'left' ? 'translateX(-100%)' : 'translateX(100%)') : ''}`,
            }}
        >
            <div className="h-full flex flex-col">
                {/* Scrollable content area */}
                <div className="flex-1 overflow-y-auto p-3 sm:p-4">
                    {/* Updated header section */}
                    <div className="mb-2 sm:mb-3">
                        <div className="text-xs sm:text-sm font-['Space_Grotesk] font-semibold text-primary break-words mb-1">
                            {String(question.topic).split(',').join(', ')}
                        </div>
                        <div className="flex items-center justify-between">
                            <span
                                onClick={() => setShowSubtopics(!showSubtopics)}
                                className="text-xs text-accent cursor-pointer hover:underline"
                            >
                                Subtopics
                            </span>
                            <span className={`
            px-2 py-0.5 rounded-full text-xs font-medium whitespace-nowrap
            ${question.difficulty >= 0.7
                                    ? 'bg-danger-light text-danger'
                                    : question.difficulty >= 0.3
                                        ? 'bg-warning-light text-warning'
                                        : 'bg-success-light text-success'}
        `}>
                                Difficulty: {question.difficulty * 10}
                            </span>
                        </div>

                        {showSubtopics && (
                            <div className="flex flex-wrap gap-1 mt-1">
                                {String(question.subtopic).split(',').map((subtopic, idx) => (
                                    <span
                                        key={idx}
                                        className="inline-block bg-muted text-muted-foreground px-1.5 py-0.5 rounded text-xs"
                                    >
                                        {subtopic.trim()}
                                    </span>
                                ))}
                            </div>
                        )}
                    </div>

                    <div className="space-y-2 sm:space-y-3">
                        {renderQuestionImages()}

                        <h3 className="text-sm sm:text-base text-card-foreground">
                            {renderLatexContent(cleanQuestionText(question.question))}
                        </h3>

                        <div className="space-y-1.5 sm:space-y-2">
                            {question.options?.map((option, idx) => (
                                <div
                                    key={idx}
                                    className={`p-2 sm:p-3 rounded-lg transition-colors flex items-start gap-2
                                        ${question.answer === String.fromCharCode(65 + idx)
                                            ? 'bg-success-light border border-success'
                                            : 'bg-muted'}
                                        `}
                                >
                                    <span className="shrink-0 flex items-center justify-center w-5 h-5 sm:w-6 sm:h-6 rounded-full bg-accent-light text-accent font-medium text-xs sm:text-sm">
                                        {String.fromCharCode(65 + idx)}
                                    </span>
                                    <div className="flex-1 text-xs sm:text-sm pt-0.5 text-card-foreground">
                                        {renderLatexContent(cleanOptionText(option))}
                                    </div>
                                </div>
                            ))}
                        </div>

                        {(question.solution || question.images?.some(img => img.includes('_a'))) && (
                            <div className="mt-2 sm:mt-3">
                                <button
                                    onClick={() => setShowSolution(!showSolution)}
                                    className="w-full py-1.5 sm:py-2 text-accent hover:bg-accent-light rounded-lg transition-colors text-xs sm:text-sm"
                                >
                                    {showSolution ? 'Hide Solution' : 'Show Solution'}
                                </button>

                                {showSolution && (
                                    <div className="mt-2 p-2 sm:p-3 bg-accent-light rounded-lg">
                                        {renderAnswerImages()}
                                        {question.solution && (
                                            <p className="text-card-foreground text-xs sm:text-sm">
                                                {renderLatexContent(question.solution)}
                                            </p>
                                        )}
                                    </div>
                                )}
                            </div>
                        )}
                    </div>
                </div>

                {/* Fixed action buttons at bottom */}
                <div className="flex gap-2 p-3 sm:p-4 border-t border-border rounded-lg bg-card">
                    <button
                        onClick={() => handleAction('discard')}
                        className="flex-1 py-2 px-3 bg-card border hover:cursor-pointer border-danger text-danger rounded-lg
                            hover:bg-danger-light transition-colors flex items-center justify-center gap-1.5 text-xs sm:text-sm"
                    >
                        <XMarkIcon width={16} height={16} className="sm:w-4 sm:h-4" />
                        Discard
                    </button>
                    <button
                        onClick={() => handleAction('keep')}
                        className="flex-1 py-2 px-3 bg-accent hover:cursor-pointer text-white rounded-lg
                            transition-colors flex items-center justify-center gap-1.5 text-xs sm:text-sm"
                    >
                        <BookmarkIcon width={16} height={16} className="sm:w-4 sm:h-4" />
                        Keep
                    </button>
                </div>
            </div>
        </div>
    );
};

export default QuestionCard;