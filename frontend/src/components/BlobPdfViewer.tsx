import React, { useState, useEffect, useRef } from 'react';
import { useS3Utils } from '@/hooks/useS3Utils';
import * as pdfjs from 'pdfjs-dist';
import PulsatingDots from './PulsatingDotsLoader';

interface BlobPdfViewerProps {
    s3Key: string;
    title: string;
    className?: string;
    style?: React.CSSProperties;
}

const BlobPdfViewer: React.FC<BlobPdfViewerProps> = ({ s3Key, title, className, style }) => {
    const [blobUrl, setBlobUrl] = useState<string>('');
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string>('');
    const [progress, setProgress] = useState<number>(0);
    const { fetchPdfAsBlob } = useS3Utils();
    const abortControllerRef = useRef<AbortController | null>(null);
    const containerRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const loadPdfAsBlob = async () => {
            if (!s3Key) {
                setLoading(false);
                return;
            }

            if (blobUrl) {
                URL.revokeObjectURL(blobUrl);
                setBlobUrl('');
            }

            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }

            abortControllerRef.current = new AbortController();

            try {
                setLoading(true);
                setError('');
                setProgress(0);

                console.log('Fetching PDF as blob for s3Key:', s3Key);

                const blob = await fetchPdfAsBlob(s3Key);

                console.log('PDF blob received, size:', blob.size);

                const objectUrl = URL.createObjectURL(blob);
                setBlobUrl(objectUrl);
                setProgress(100);
                console.log('PDF blob created successfully');
            } catch (err: any) {
                if (err.name === 'AbortError') {
                    console.log('PDF fetch aborted');
                    return;
                }
                console.error('Error loading PDF as blob:', err);
                setError('Failed to load PDF');
            } finally {
                setLoading(false);
                setProgress(0);
            }
        };

        loadPdfAsBlob();

        return () => {
            if (abortControllerRef.current) {
                abortControllerRef.current.abort();
            }
            if (blobUrl) {
                URL.revokeObjectURL(blobUrl);
            }
        };
    }, [s3Key, fetchPdfAsBlob]);

    useEffect(() => {
        if (blobUrl && containerRef.current) {
            // Configure worker
            pdfjs.GlobalWorkerOptions.workerSrc = new URL(
                'pdfjs-dist/build/pdf.worker.min.mjs',
                import.meta.url
            ).toString();

            const renderPdf = async () => {
                try {
                    const pdf = await pdfjs.getDocument(blobUrl).promise;
                    const numPages = pdf.numPages;

                    const container = containerRef.current;
                    if (!container) {
                        throw new Error('Container element not found');
                    }

                    // Clear existing content
                    container.innerHTML = '';

                    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
                        const page = await pdf.getPage(pageNum);
                        const scale = 1.5; // Fixed scale (zoom removed)
                        const viewport = page.getViewport({ scale });

                        const canvas = document.createElement('canvas');
                        const context = canvas.getContext('2d');
                        if (!context) {
                            throw new Error('Failed to get canvas context');
                        }

                        canvas.height = viewport.height;
                        canvas.width = viewport.width;
                        canvas.style.width = '100%'; // Responsive width
                        canvas.style.marginBottom = '10px'; // Spacing between pages

                        const renderContext = {
                            canvasContext: context,
                            viewport: viewport,
                        };
                        await page.render(renderContext).promise;

                        container.appendChild(canvas);
                    }
                } catch (err) {
                    console.error('Error rendering PDF:', err);
                    setError('Failed to render PDF');
                }
            };

            renderPdf();
        }
    }, [blobUrl]);

    // Loading, error, and no PDF states remain unchanged
    if (loading) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <div className="text-center">
                    <PulsatingDots />
                    {/* <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div> */}
                    <p className="text-sm text-muted-foreground p-4">Loading PDF...</p>
                    {progress > 0 && (
                        <div className="mt-2 w-32 mx-auto">
                            <div className="bg-secondary rounded-full h-2">
                                <div
                                    className="bg-primary h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${progress}%` }}
                                />
                            </div>
                            <p className="text-xs text-muted-foreground mt-1">{progress}%</p>
                        </div>
                    )}
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <div className="text-center">
                    <p className="text-sm text-destructive mb-2">{error}</p>
                    <button
                        onClick={() => window.location.reload()}
                        className="text-xs text-primary hover:underline"
                    >
                        Retry
                    </button>
                </div>
            </div>
        );
    }

    if (!blobUrl) {
        return (
            <div className={`flex items-center justify-center bg-muted/30 ${className}`} style={style}>
                <p className="text-sm text-muted-foreground">No PDF available</p>
            </div>
        );
    }

    return (
        <div
            className={`relative flex flex-col ${className}`}
            style={{ ...style, overflow: 'auto', height: '100%' }}  // Ensure full height for scrolling
        >
            {/* Sticky toolbar at top-right */}
            <div className="sticky top-0 self-end z-10 p-2 rounded-md">
                <a
                    href={blobUrl}
                    download={`${title}.pdf`}
                    className="px-2 py-1 bg-accent/50 text-primary-foreground hover:bg-accent rounded"
                >
                    Download
                </a>
            </div>
            <div ref={containerRef} className="pdf-container flex-grow">
                {/* PDF pages will be appended here */}
            </div>
        </div>
    );


};

export default BlobPdfViewer;
