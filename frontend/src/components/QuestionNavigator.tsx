import React, { useState, useEffect } from 'react';
import { CheckCircle, Circle, PanelLeftClose, PanelLeftOpen, Menu, X, LayoutList } from 'lucide-react';

interface Question {
    id: string;
    answered: boolean;
    preview: React.ReactNode;
}

interface QuestionNavigatorProps {
    questions: Array<Question> | undefined;
    currentQuestionIndex: number;
    onQuestionSelect: (index: number) => void;
    onExpandChange?: (expanded: boolean) => void;
}

const QuestionNavigator: React.FC<QuestionNavigatorProps> = ({
    questions,
    currentQuestionIndex,
    onQuestionSelect,
    onExpandChange,
}) => {
    const [isExpanded, setIsExpanded] = useState(true);
    const [isMobileOpen, setIsMobileOpen] = useState(false);
    const [isMobile, setIsMobile] = useState(false);

    // Check for mobile screen on mount and resize
    useEffect(() => {
        const checkMobile = () => {
            setIsMobile(window.innerWidth < 768);
            if (window.innerWidth < 768) {
                setIsExpanded(false);
                setIsMobileOpen(false);
            }
        };

        checkMobile();
        window.addEventListener('resize', checkMobile);
        return () => window.removeEventListener('resize', checkMobile);
    }, []);

    const handleExpandClick = () => {
        if (isMobile) {
            const newValue = !isMobileOpen;
            setIsMobileOpen(newValue);
        } else {
            const newValue = !isExpanded;
            setIsExpanded(newValue);
            onExpandChange?.(newValue);
        }
    };

    const handleQuestionClick = (index: number) => {
        onQuestionSelect(index);
        if (isMobile) {
            setIsMobileOpen(false);
        }
    };

    if (!questions?.length) return null;

    return (
        <>
            {/* Mobile Question Navigator Overlay - ONLY FOR MOBILE */}
            {isMobile && (
                <div className="flex z-30">
                    <button
                        onClick={handleExpandClick}
                        className="flex items-center justify-center bg-accent text-white w-8 h-8 rounded-lg shadow-lg"
                    >
                        {isMobileOpen ? <X className="h-4 w-4" /> : <LayoutList className="h-4 w-4" />}
                    </button>
                </div>
            )}

            {/* Mobile Overlay - ONLY FOR MOBILE */}
            {isMobile && isMobileOpen && (
                <div
                    className="fixed inset-0 bg-black/50 z-20"
                    onClick={() => setIsMobileOpen(false)}
                ></div>
            )}

            {/* Navigator Panel - DIFFERENT FOR DESKTOP AND MOBILE */}
            {isMobile ? (
                /* MOBILE VERSION */
                <div className={`fixed top-0 left-0 h-screen z-30 bg-card w-3/4 max-w-xs 
                              transition-all duration-300 ease-in-out ${isMobileOpen ? 'translate-x-0' : '-translate-x-full'}`}>
                    <div className="flex flex-col h-full">
                        <div className="flex items-center justify-between p-4 bg-secondary-100 border-b">
                            <span className="font-medium font-['Space_Grotesk'] text-primary">All Questions</span>
                            <span className="px-2 py-0.5 text-sm bg-secondary-200 text-muted-foreground rounded-full">
                                {questions.filter(q => q.answered).length}/{questions.length}
                            </span>
                            <button onClick={() => setIsMobileOpen(false)} className="text-gray-700">
                                <X className="h-5 w-5" />
                            </button>
                        </div>

                        <div className="flex-1 overflow-y-auto p-2 space-y-2">
                            {questions.map((question, index) => (
                                <button
                                    key={question.id}
                                    onClick={() => handleQuestionClick(index)}
                                    className={`w-full flex items-center p-3 rounded-lg text-left text-sm transition-all duration-200 ${index === currentQuestionIndex
                                            ? 'bg-accent-light border-2 border-accent'
                                            : 'border border-border hover:border-accent-light'
                                        }`}
                                >
                                    <div className={`flex items-center justify-center w-6 h-6 rounded-full ${question.answered ? 'bg-accent-light' : 'bg-gray-100'
                                        } mr-2`}>
                                        <span className={`text-xs ${question.answered ? 'text-accent font-bold' : 'text-gray-600'
                                            }`}>
                                            {index + 1}
                                        </span>
                                    </div>
                                    <div className="truncate text-primary">
                                        {question.preview}
                                    </div>
                                </button>
                            ))}
                        </div>
                    </div>
                </div>
            ) : (
                /* DESKTOP VERSION - EXACTLY AS BEFORE */
                <div className={`h-[85vh] rounded-lg shadow-lg border border-border transition-all duration-300 ease-in-out ${isExpanded ? 'w-80' : 'w-12'} bg-card`}>
                    <div className="flex flex-col w-full h-full relative">
                        <div className={`flex items-center p-4 bg-secondary-100 border-b ${isExpanded ? 'justify-between' : 'justify-center'}`}>
                            {isExpanded && (
                                <>
                                    <span className="font-semibold font-['Space_Grotesk'] text-primary">All Questions</span>
                                    <span className="ml-2 px-2 py-0.5 text-sm bg-secondary-200 text-muted-foreground rounded-full">
                                        {questions.filter(q => q.answered).length}/{questions.length}
                                    </span>
                                </>
                            )}
                            <button
                                className="z-10 flex items-center justify-center w-6 h-6 duration-200"
                                onClick={handleExpandClick}
                            >
                                {isExpanded ? (
                                    <PanelLeftClose className="h-6 w-6 text-gray-500" />
                                ) : (
                                    <PanelLeftOpen className="h-6 w-6 text-gray-500" />
                                )}
                            </button>
                        </div>

                        {/* Question List for Desktop */}
                        {isExpanded ? (
                            <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 p-4 space-y-2">
                                {questions.map((question, index) => (
                                    <button
                                        key={question.id}
                                        onClick={() => handleQuestionClick(index)}
                                        className={`group w-full flex items-center p-3 rounded-lg text-left text-sm font-medium transition-all duration-200 ${index === currentQuestionIndex
                                                ? 'bg-accent-light border-2 border-accent'
                                                : 'border border-border hover:border-accent-light'
                                            }`}
                                    >
                                        <div className={`flex items-center justify-center w-8 h-8 rounded-full mr-3 ${question.answered ? 'text-accent' : 'text-gray-400'
                                            }`}>
                                            {question.answered ? (
                                                <CheckCircle className="h-5 w-5" />
                                            ) : (
                                                <Circle className="h-5 w-5" />
                                            )}
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <div className="flex items-center">
                                                <span className="text-sm font-medium text-primary mr-2">
                                                    Question {index + 1}
                                                </span>
                                            </div>
                                            <span className="text-muted-foreground truncate">{question.preview}</span>
                                        </div>
                                    </button>
                                ))}
                            </div>
                        ) : (
                            <div className="flex-1 overflow-y-auto scrollbar-none scrollbar-thumb-gray-300 scrollbar-track-gray-100 p-2 space-y-2">
                                {questions.map((question, index) => (
                                    <button
                                        key={question.id}
                                        onClick={() => handleQuestionClick(index)}
                                        className={`w-full flex items-center justify-center p-1 rounded-lg transition-all duration-200 ${index === currentQuestionIndex
                                                ? 'bg-accent-light border-2 border-accent'
                                                : 'border border-border hover:border-accent-light'
                                            }`}
                                    >
                                        <div className={`flex items-center justify-center rounded-full ${question.answered ? 'text-accent' : 'text-gray-400'
                                            }`}>
                                            {question.answered ? (
                                                <CheckCircle className="h-5 w-5" />
                                            ) : (
                                                <span className="text-xs">{index + 1}</span>
                                            )}
                                        </div>
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>
                </div>
            )}
        </>
    );
};

export default QuestionNavigator;