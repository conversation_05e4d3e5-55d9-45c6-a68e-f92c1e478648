import React from 'react';

const Background: React.FC = () => {
  return (
    <div
      className="-z-30 absolute top-0 left-0 w-full h-full overflow-hidden"
    >
      <svg aria-hidden="true" className="pointer-events-none h-full w-full fill-[hsl(var(--muted-foreground)/0.8)] absolute inset-0 z-0 [mask-image:radial-gradient(75vw_circle_at_center,hsl(var(--background)),transparent)]"><defs><pattern id=":R6bqlb:" width="16" height="16" patternUnits="userSpaceOnUse" patternContentUnits="userSpaceOnUse" x="0" y="0"><circle id="pattern-circle" cx="1" cy="1" r="1"></circle></pattern></defs><rect width="100%" height="100%" strokeWidth="0" fill="url(#:R6bqlb:)"></rect></svg>
    </div>
  );
};

export default Background;
