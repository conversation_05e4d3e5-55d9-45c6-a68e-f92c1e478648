import AegisScholarLogo from './logos/AegisScholar_transparent.svg?react';
import { useNavigate } from 'react-router-dom';

interface AegisScholarProps {
  className?: string;
  style?: React.CSSProperties;
}

const AegisScholarLogoWithText: React.FC<AegisScholarProps> = ({ 
  className = '', 
  style 
}) => {
  const navigate = useNavigate();

  return (
    <AegisScholarLogo 
      className={`cursor-pointer object-contain ${className}`}
      style={style}
      // onClick={() => navigate('/')}
    />
  );
};

export default AegisScholarLogoWithText;