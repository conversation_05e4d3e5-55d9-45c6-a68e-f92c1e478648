import AegisScholarLogoIcon from './logos/logo.svg?react';
import { useNavigate } from 'react-router-dom';

interface AegisScholarProps {
  className?: string;
  style?: React.CSSProperties;
}

const AegisScholarLogoWithoutText: React.FC<AegisScholarProps> = ({ className = '', style }) => {
  const navigate = useNavigate();

  return (
    <AegisScholarLogoIcon 
      className={`cursor-pointer object-contain ${className}`}
      style={style}
      // onClick={() => navigate('/')}
    />
  );
};

export default AegisScholarLogoWithoutText;