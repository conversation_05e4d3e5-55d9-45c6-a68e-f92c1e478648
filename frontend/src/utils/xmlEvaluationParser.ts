import { XMLParser, XMLValidator } from 'fast-xml-parser';

// --- Type Definitions ---
export interface CriterionBreakdown {
    criterion: string;
    score: string;
    maxScore?: string;
}

export interface FeedbackSection {
    title: string;
    content: string;
    subsections?: { title: string; content: string }[];
}

export interface QuestionBreakdown {
    questionNumber: string;
    marksAwarded: number;
    marksPossible: number;
    percentage: number;
    feedback: string;
    structuredFeedback?: FeedbackSection[];
    criteriaBreakdown: CriterionBreakdown[];
}

export interface EvaluationBreakdown {
    totalMarks: number;
    maxMarks: number;
    overallPercentage: number;
    questions: QuestionBreakdown[];
}

// For GradingDetails compatibility
export interface ParsedEvaluation {
    total_marks: number;
    maximum_possible_marks: number;
    percentage_score: number;
    section: Array<{
        name: string;
        section_marks: number;
        section_possible_marks: number;
        question: Array<{
            question_number: number;
            marks_awarded: number;
            marks_possible: number;
            feedback: string;
        }>;
    }>;
}

// --- Utility Functions ---
const sanitizeNumber = (value: any, fallback: number = 0): number => {
    if (typeof value === 'number' && !isNaN(value) && isFinite(value)) {
        return Math.max(0, Math.round(value));
    }
    if (typeof value === 'string') {
        const parsed = parseFloat(value.replace(/[^\d.-]/g, ''));
        if (!isNaN(parsed) && isFinite(parsed)) {
            return Math.max(0, Math.round(parsed));
        }
    }
    return fallback;
};

const sanitizeString = (value: any, fallback: string = ''): string => {
    if (typeof value === 'string') {
        return value.trim();
    }
    if (value != null) {
        return String(value).trim();
    }
    return fallback;
};

const sanitizePercentage = (awarded: number, possible: number): number => {
    if (possible <= 0) return 0;
    const percentage = (awarded / possible) * 100;
    return Math.max(0, Math.min(100, Math.round(percentage)));
};

// --- XML Processing Functions ---
const extractXMLFromRawData = (rawData: any): string | null => {
    try {
        let xmlString = '';

        // Handle different input formats
        if (Array.isArray(rawData)) {
            if (rawData.length === 0) {
                console.error('Empty array provided for evaluation data');
                return null;
            }
            xmlString = sanitizeString(rawData[0]);
        } else if (typeof rawData === 'string') {
            xmlString = sanitizeString(rawData);
        } else if (rawData && typeof rawData === 'object') {
            if (rawData.evaluation) {
                xmlString = sanitizeString(rawData.evaluation);
            } else if (rawData.content) {
                xmlString = sanitizeString(rawData.content);
            } else {
                console.error('Object format not recognized:', Object.keys(rawData));
                return null;
            }
        } else {
            console.error('Invalid evaluation data format:', typeof rawData, rawData);
            return null;
        }

        if (!xmlString) {
            console.error('Empty XML string after extraction');
            return null;
        }

        // Handle various edge cases from Gemini responses
        xmlString = handleGeminiResponseEdgeCases(xmlString);

        return xmlString;
    } catch (error) {
        console.error('Error extracting XML from raw data:', error);
        return null;
    }
};

const handleGeminiResponseEdgeCases = (rawString: string): string => {
    let processedString = rawString;

    // 1. Remove markdown code blocks (```xml, ```markdown, ``` etc.)
    processedString = processedString.replace(/```(?:xml|markdown|text)?\s*\n?/gi, '');
    processedString = processedString.replace(/```\s*$/g, '');

    // 2. Remove common markdown formatting
    processedString = processedString.replace(/^\s*#.*$/gm, ''); // Remove headers
    processedString = processedString.replace(/\*\*(.*?)\*\*/g, '$1'); // Remove bold
    processedString = processedString.replace(/\*(.*?)\*/g, '$1'); // Remove italic

    // 3. Handle HTML entities that might be present
    processedString = processedString
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'")
        .replace(/&amp;/g, '&'); // Do this last to avoid double-decoding

    // 4. Remove common prefixes/suffixes from AI responses
    const commonPrefixes = [
        'Here is the evaluation:',
        'Here\'s the evaluation:',
        'Evaluation:',
        'Result:',
        'Output:',
        'XML:',
        'The evaluation is:',
        'Based on the analysis:'
    ];

    commonPrefixes.forEach(prefix => {
        const regex = new RegExp(`^\\s*${prefix.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\s*`, 'i');
        processedString = processedString.replace(regex, '');
    });

    // 5. Remove trailing explanations after XML
    const xmlEndMatch = processedString.match(/<\/evaluation>\s*$/);
    if (xmlEndMatch) {
        const xmlEndIndex = processedString.lastIndexOf('</evaluation>') + '</evaluation>'.length;
        processedString = processedString.substring(0, xmlEndIndex);
    }

    // 6. Handle multiple XML blocks - take the first complete one
    const multipleXmlMatch = processedString.match(/<evaluation>[\s\S]*?<\/evaluation>/);
    if (multipleXmlMatch) {
        processedString = multipleXmlMatch[0];
    }

    // 7. Clean up whitespace and newlines
    processedString = processedString.trim();

    return processedString;
};

const cleanAndRepairXML = (xmlString: string): string => {
    let cleanedXml = xmlString;

    // Handle escaped quotes and newlines from JSON strings
    cleanedXml = cleanedXml
        .replace(/\\"/g, '"')
        .replace(/\\n/g, '\n')
        .replace(/\\\\/g, '\\');

    // Remove control characters and normalize whitespace
    cleanedXml = cleanedXml
        .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
        .replace(/&(?![a-zA-Z0-9#]{1,6};)/g, '&amp;')
        .trim();

    // Handle malformed XML from AI responses
    cleanedXml = repairMalformedXML(cleanedXml);

    // Extract XML content from markdown or raw string
    const xmlMatch = cleanedXml.match(/<evaluation>([\s\S]*?)<\/evaluation>/);
    if (xmlMatch) {
        cleanedXml = `<evaluation>${xmlMatch[1]}</evaluation>`;
    } else {
        // Try to find evaluation content without proper tags
        const contentMatch = cleanedXml.match(/total_marks_awarded|maximum_possible_marks|question/);
        if (contentMatch) {
            // Wrap content in evaluation tags
            cleanedXml = `<evaluation>${cleanedXml}</evaluation>`;
        } else {
            console.error('No evaluation XML found in data');
            return cleanedXml;
        }
    }

    // Fix common structural issues
    // 1. Ensure proper evaluation wrapper
    if (!cleanedXml.startsWith('<evaluation>')) {
        cleanedXml = '<evaluation>' + cleanedXml;
    }
    if (!cleanedXml.endsWith('</evaluation>')) {
        cleanedXml = cleanedXml + '</evaluation>';
    }

    // 2. Fix unclosed criterion tags
    cleanedXml = cleanedXml.replace(/<criterion([^>]*?)>([^<]+?)(?=<(?!\/criterion))/g,
        (_, attrs, content) => `<criterion${attrs}>${content}</criterion>`);

    // 3. Fix unclosed question tags
    cleanedXml = cleanedXml.replace(/<question([^>]*)>([^<]*(?:<(?!\/question>|question)[^<]*)*)<\/evaluation>/g,
        (_, attrs, content) => `<question${attrs}>${content}</question></evaluation>`);

    // 4. Fix nested question tags
    cleanedXml = cleanedXml.replace(/<question([^>]*)>([^<]*(?:<(?!question|\/question)[^<]*)*)<question/g,
        (_, attrs, content) => `<question${attrs}>${content}</question><question`);

    return cleanedXml;
};

const repairMalformedXML = (xmlString: string): string => {
    let repairedXml = xmlString;

    // 1. Fix common typos in tag names first (before other repairs)
    const tagCorrections = {
        'evalutation': 'evaluation',
        'questoin': 'question',
        'feeback': 'feedback',
        'criteron': 'criterion',
        'marks_awared': 'marks_awarded',
        'marks_posible': 'marks_possible'
    };

    Object.entries(tagCorrections).forEach(([wrong, correct]) => {
        const openRegex = new RegExp(`<${wrong}(\\s|>)`, 'gi');
        const closeRegex = new RegExp(`</${wrong}>`, 'gi');
        repairedXml = repairedXml.replace(openRegex, `<${correct}$1`);
        repairedXml = repairedXml.replace(closeRegex, `</${correct}>`);
    });

    // 2. Fix missing closing tags for common elements
    const tagsToClose = ['marks_awarded', 'marks_possible', 'feedback', 'total_marks_awarded', 'maximum_possible_marks'];
    tagsToClose.forEach(tag => {
        // Look for opening tag without closing tag
        const regex = new RegExp(`<${tag}([^>]*)>([^<]*?)(?=\\s*<(?!/${tag})(?![^>]*>))`, 'g');
        repairedXml = repairedXml.replace(regex, `<${tag}$1>$2</${tag}>`);
    });

    // 3. Fix malformed attributes (missing quotes) - be more careful
    repairedXml = repairedXml.replace(/(\w+)=([^"\s>]+)(?=[\s>])/g, (match, attr, value) => {
        // Don't quote if it's already quoted or if it looks like a number
        if (value.startsWith('"') || /^\d+$/.test(value)) {
            return match;
        }
        return `${attr}="${value}"`;
    });

    // 4. Remove duplicate consecutive tags
    repairedXml = repairedXml.replace(/(<(\w+)[^>]*>)\s*\1/g, '$1');

    // 5. Fix malformed CDATA sections
    repairedXml = repairedXml.replace(/\[CDATA\[(.*?)\]\]/g, '$1');
    repairedXml = repairedXml.replace(/<!\[CDATA\[(.*?)\]\]>/g, '$1');

    // 6. Clean up extra whitespace
    repairedXml = repairedXml.replace(/>\s+</g, '><');

    return repairedXml;
};

// --- Fast XML Parser Configuration ---
const xmlParserOptions = {
    ignoreAttributes: false,
    attributeNamePrefix: '@_',
    textNodeName: '#text',
    parseAttributeValue: true,
    trimValues: true,
    parseTrueNumberOnly: false,
    arrayMode: false,
    alwaysCreateTextNode: false,
    isArray: (name: string, _jpath: string, _isLeafNode: boolean, _isAttribute: boolean) => {
        // Always treat these as arrays to handle multiple instances
        return ['question', 'criterion', 'section'].includes(name);
    }
};

// --- Main Parsing Functions ---
export const parseEvaluationData = (rawData: any): EvaluationBreakdown | null => {
    try {
        // Step 1: Extract XML string from raw data
        const xmlString = extractXMLFromRawData(rawData);
        if (!xmlString) {
            return null;
        }

        // Step 2: Clean and repair XML
        const cleanedXml = cleanAndRepairXML(xmlString);

        // Step 3: Validate XML structure
        const isValid = XMLValidator.validate(cleanedXml);
        if (isValid !== true) {
            console.warn('XML validation issues:', isValid);

            // If validation fails completely, try fallback parsing
            if (typeof isValid === 'object' && isValid.err) {
                console.warn('Attempting fallback parsing due to validation errors');
                return attemptFallbackParsing(cleanedXml);
            }
        }

        // Step 4: Parse XML using fast-xml-parser
        const parser = new XMLParser(xmlParserOptions);
        const parsedData = parser.parse(cleanedXml);

        if (!parsedData?.evaluation) {
            console.error('No evaluation element found in parsed XML');
            return attemptFallbackParsing(cleanedXml);
        }

        // Step 5: Convert to standardized format
        const evaluationBreakdown = convertToEvaluationBreakdown(parsedData.evaluation);

        // Step 6: Validate and correct totals based on question marks
        return evaluationBreakdown ? validateAndCorrectEvaluationTotals(evaluationBreakdown) : null;

    } catch (error) {
        console.error('Error parsing evaluation data:', error);
        // Try fallback parsing as last resort
        const xmlString = extractXMLFromRawData(rawData);
        if (xmlString) {
            return attemptFallbackParsing(cleanAndRepairXML(xmlString));
        }
        return null;
    }
};

const attemptFallbackParsing = (xmlString: string): EvaluationBreakdown | null => {
    try {
        console.log('Attempting fallback DOM parsing...');

        // Use DOM parser as fallback
        const parser = new DOMParser();
        const xmlDoc = parser.parseFromString(xmlString, 'text/xml');

        const parserError = xmlDoc.querySelector('parsererror');
        if (parserError) {
            console.error('DOM parser also failed:', parserError.textContent);
            return null;
        }

        const evaluation = xmlDoc.querySelector('evaluation');
        if (!evaluation) {
            console.error('No evaluation element found in DOM parsing');
            return null;
        }

        // Extract basic data using DOM methods
        const totalMarks = sanitizeNumber(
            evaluation.querySelector('total_marks_awarded, total_marks')?.textContent
        );
        const maxMarks = sanitizeNumber(
            evaluation.querySelector('maximum_possible_marks, max_marks, possible_marks')?.textContent
        );
        const overallPercentage = sanitizePercentage(totalMarks, maxMarks);

        const questions: QuestionBreakdown[] = [];
        const questionElements = evaluation.querySelectorAll('question');

        Array.from(questionElements).forEach((question, index) => {
            const questionNumber = sanitizeString(
                question.getAttribute('number') || question.querySelector('number')?.textContent || `Question ${index + 1}`
            );
            const marksAwarded = sanitizeNumber(
                question.querySelector('marks_awarded, awarded, score')?.textContent
            );
            const marksPossible = sanitizeNumber(
                question.querySelector('marks_possible, possible_marks, possible, max_marks, total')?.textContent
            );
            const feedback = sanitizeString(
                question.querySelector('feedback, detailed_feedback, comment, overall_comment')?.textContent
            );

            if (questionNumber) {
                questions.push({
                    questionNumber,
                    marksAwarded,
                    marksPossible,
                    percentage: sanitizePercentage(marksAwarded, marksPossible),
                    feedback,
                    criteriaBreakdown: []
                });
            }
        });

        return {
            totalMarks,
            maxMarks,
            overallPercentage,
            questions
        };

    } catch (error) {
        console.error('Fallback parsing also failed:', error);
        return null;
    }
};

export const parseEvaluationForGradingDetails = (rawData: any): ParsedEvaluation | null => {
    try {
        const evaluationBreakdown = parseEvaluationData(rawData);
        if (!evaluationBreakdown) {
            return null;
        }

        // Convert to GradingDetails format
        return convertToGradingDetailsFormat(evaluationBreakdown);

    } catch (error) {
        console.error('Error parsing evaluation for grading details:', error);
        return null;
    }
};

// --- Conversion Functions ---
const convertToEvaluationBreakdown = (evaluation: any): EvaluationBreakdown | null => {
    try {
        // Extract overall evaluation data with robust parsing
        const totalMarks = sanitizeNumber(
            evaluation.total_marks_awarded || evaluation.total_marks || 0
        );
        const maxMarks = sanitizeNumber(
            evaluation.maximum_possible_marks || evaluation.max_marks || evaluation.possible_marks || 0
        );
        const overallPercentage = sanitizeNumber(
            evaluation.percentage_score || evaluation.overall_percentage || 0
        ) || sanitizePercentage(totalMarks, maxMarks);

        // Extract questions
        const questions: QuestionBreakdown[] = [];
        const questionElements = Array.isArray(evaluation.question) 
            ? evaluation.question 
            : evaluation.question ? [evaluation.question] : [];

        questionElements.forEach((question: any, index: number) => {
            try {
                const questionBreakdown = parseQuestionFromXML(question, index);
                if (questionBreakdown) {
                    questions.push(questionBreakdown);
                }
            } catch (error) {
                console.warn(`Error parsing question ${index + 1}:`, error);
            }
        });

        return {
            totalMarks,
            maxMarks,
            overallPercentage,
            questions
        };

    } catch (error) {
        console.error('Error converting to evaluation breakdown:', error);
        return null;
    }
};

const parseQuestionFromXML = (question: any, index: number): QuestionBreakdown | null => {
    try {
        // Extract question number
        let questionNumber = sanitizeString(
            question['@_number'] || question.number || `Question ${index + 1}`
        );

        // Extract marks
        const marksAwarded = sanitizeNumber(
            question.marks_awarded || question.awarded || question.score || 0
        );
        const marksPossible = sanitizeNumber(
            question.marks_possible || question.possible_marks || question.possible || question.max_marks || question.total || 0
        );

        // Calculate percentage
        const percentage = sanitizePercentage(marksAwarded, marksPossible);

        // Extract feedback
        let feedback = '';
        let structuredFeedback: FeedbackSection[] = [];

        if (question.detailed_feedback) {
            const result = parseDetailedFeedback(question.detailed_feedback);
            feedback = result.feedback;
            structuredFeedback = result.structuredFeedback;
        } else if (question.feedback) {
            feedback = sanitizeString(question.feedback);
        } else if (question.comment || question.overall_comment) {
            feedback = sanitizeString(question.comment || question.overall_comment);
        }

        // Extract criteria breakdown
        const criteriaBreakdown = parseCriteriaFromXML(question);

        return {
            questionNumber,
            marksAwarded,
            marksPossible,
            percentage,
            feedback,
            structuredFeedback: structuredFeedback.length > 0 ? structuredFeedback : undefined,
            criteriaBreakdown
        };

    } catch (error) {
        console.warn(`Error parsing question ${index + 1}:`, error);
        return null;
    }
};

const parseDetailedFeedback = (detailedFeedback: any): { feedback: string; structuredFeedback: FeedbackSection[] } => {
    const feedbackParts: string[] = [];
    const structuredFeedback: FeedbackSection[] = [];

    try {
        // Extract overall comment
        let overallContent = '';
        if (detailedFeedback.overall_comment) {
            if (typeof detailedFeedback.overall_comment === 'string') {
                overallContent = sanitizeString(detailedFeedback.overall_comment);
            } else if (detailedFeedback.overall_comment['#text']) {
                overallContent = sanitizeString(detailedFeedback.overall_comment['#text']);
            }
        }

        if (overallContent) {
            feedbackParts.push(`Overall: ${overallContent}`);
            structuredFeedback.push({
                title: 'Overall Assessment',
                content: overallContent
            });
        }

        // Extract structural analysis
        if (detailedFeedback.structural_analysis) {
            const result = parseAnalysisSection(detailedFeedback.structural_analysis, 'Structural Analysis');
            if (result.content) {
                feedbackParts.push(`Structure: ${result.content}`);
                structuredFeedback.push(result.section);
            }
        }

        // Extract content analysis
        if (detailedFeedback.content_analysis) {
            const result = parseAnalysisSection(detailedFeedback.content_analysis, 'Content Analysis');
            if (result.content) {
                feedbackParts.push(`Content: ${result.content}`);
                structuredFeedback.push(result.section);
            }
        }

        // Extract presentation analysis
        if (detailedFeedback.presentation_analysis) {
            const result = parseAnalysisSection(detailedFeedback.presentation_analysis, 'Presentation Analysis');
            if (result.content) {
                feedbackParts.push(`Presentation: ${result.content}`);
                structuredFeedback.push(result.section);
            }
        }

        return {
            feedback: feedbackParts.join('\n\n'),
            structuredFeedback
        };

    } catch (error) {
        console.warn('Error parsing detailed feedback:', error);
        return {
            feedback: sanitizeString(detailedFeedback),
            structuredFeedback: []
        };
    }
};

const parseAnalysisSection = (analysis: any, title: string): { content: string; section: FeedbackSection } => {
    const parts: string[] = [];
    const subsections: { title: string; content: string }[] = [];

    // Common analysis fields
    const fields = [
        'introduction', 'body_structure_and_flow', 'conclusion',
        'relevance_and_accuracy', 'analytical_depth_and_rigor', 'keyword_and_citation_usage',
        'clarity_and_language', 'visuals'
    ];

    fields.forEach(field => {
        // Handle both direct properties and nested text content
        let text = '';
        if (analysis[field]) {
            if (typeof analysis[field] === 'string') {
                text = sanitizeString(analysis[field]);
            } else if (analysis[field]['#text']) {
                text = sanitizeString(analysis[field]['#text']);
            } else if (typeof analysis[field] === 'object') {
                text = sanitizeString(JSON.stringify(analysis[field]));
            }
        }

        if (text) {
            const fieldTitle = field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
            parts.push(`${fieldTitle}: ${text}`);
            subsections.push({ title: fieldTitle, content: text });
        }
    });

    return {
        content: parts.join('; '),
        section: {
            title,
            content: `Analysis of ${title.toLowerCase()}`,
            subsections: subsections.length > 0 ? subsections : undefined
        }
    };
};

const parseCriteriaFromXML = (question: any): CriterionBreakdown[] => {
    const criteriaBreakdown: CriterionBreakdown[] = [];

    try {
        // Look for marks breakdown in multiple possible locations
        const marksBreakdown = question.marks_breakdown || question.criteria || question.breakdown;
        if (!marksBreakdown) {
            return criteriaBreakdown;
        }

        // Extract all criterion elements
        const criteria = Array.isArray(marksBreakdown.criterion)
            ? marksBreakdown.criterion
            : marksBreakdown.criterion ? [marksBreakdown.criterion] : [];

        const criteriaMap = new Map<string, { score: string; maxScore?: string }>();

        // Process criteria
        criteria.forEach((criterion: any) => {
            const name = sanitizeString(criterion['@_name'] || criterion.name);

            // Extract score text more robustly - handle different XML parser outputs
            let scoreText = '';
            if (typeof criterion === 'string') {
                scoreText = criterion;
            } else if (criterion['#text'] !== undefined) {
                scoreText = sanitizeString(criterion['#text']);
            } else if (typeof criterion === 'number') {
                scoreText = String(criterion);
            } else if (typeof criterion === 'object' && criterion !== null) {
                // For simple text content, the parser might put it directly as the value
                // Check if this is a simple value object
                const keys = Object.keys(criterion);
                if (keys.length === 1 && keys[0].startsWith('@_')) {
                    // This is likely just an attribute, skip
                    return;
                } else if (keys.length === 0) {
                    // Empty object, skip
                    return;
                } else {
                    // Try to find text content in various forms
                    scoreText = sanitizeString(criterion['#text'] || criterion._ || criterion.value || '');
                }
            }

            scoreText = sanitizeString(scoreText);

            if (!name || scoreText === '') return;

            // Handle "1/3" format (new format from evaluation output)
            if (scoreText.includes('/')) {
                const [score, maxScore] = scoreText.split('/').map(s => s.trim());
                criteriaMap.set(name, { score, maxScore });
            }
            // Handle "Total Possible for X" pattern (old format)
            else if (name.startsWith('Total Possible for ')) {
                const baseName = name.replace('Total Possible for ', '').trim();
                if (criteriaMap.has(baseName)) {
                    criteriaMap.get(baseName)!.maxScore = scoreText;
                } else {
                    criteriaMap.set(baseName, { score: '0', maxScore: scoreText });
                }
            } else {
                // Regular criterion (old format)
                if (criteriaMap.has(name)) {
                    criteriaMap.get(name)!.score = scoreText;
                } else {
                    criteriaMap.set(name, { score: scoreText, maxScore: undefined });
                }
            }
        });

        // Convert map to array, filtering out empty entries
        criteriaMap.forEach((value, key) => {
            if (key && value.score !== undefined && value.score !== '') {
                criteriaBreakdown.push({
                    criterion: key,
                    score: value.score,
                    maxScore: value.maxScore // Don't set fallback to '0', leave undefined if not available
                });
            }
        });

    } catch (error) {
        console.warn('Error parsing criteria breakdown:', error);
    }

    return criteriaBreakdown;
};

const convertToGradingDetailsFormat = (evaluationBreakdown: EvaluationBreakdown): ParsedEvaluation => {
    // Create sections from questions
    const sections: ParsedEvaluation['section'] = [];

    if (evaluationBreakdown.questions.length > 0) {
        // Group questions into sections (for now, create one main section)
        const questions = evaluationBreakdown.questions.map(q => ({
            question_number: parseInt(q.questionNumber.replace(/\D/g, '')) || 1,
            marks_awarded: q.marksAwarded,
            marks_possible: q.marksPossible,
            feedback: q.feedback
        }));

        sections.push({
            name: 'Main Section',
            section_marks: evaluationBreakdown.totalMarks,
            section_possible_marks: evaluationBreakdown.maxMarks,
            question: questions
        });
    }

    return {
        total_marks: evaluationBreakdown.totalMarks,
        maximum_possible_marks: evaluationBreakdown.maxMarks,
        percentage_score: evaluationBreakdown.overallPercentage,
        section: sections
    };
};

// --- Validation Functions ---
export const validateEvaluationData = (data: any): { isValid: boolean; errors: string[]; warnings: string[] } => {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
        if (!data) {
            errors.push('No evaluation data provided');
            return { isValid: false, errors, warnings };
        }

        const parsed = parseEvaluationData(data);
        if (!parsed) {
            errors.push('Failed to parse evaluation data');
            return { isValid: false, errors, warnings };
        }

        if (parsed.questions.length === 0) {
            warnings.push('No questions found in evaluation data');
        }

        if (parsed.totalMarks === 0 && parsed.maxMarks === 0) {
            warnings.push('No marks information available');
        }

        parsed.questions.forEach((question, index) => {
            if (!question.questionNumber) {
                warnings.push(`Question ${index + 1} has no number`);
            }
            if (question.marksPossible === 0) {
                warnings.push(`Question ${question.questionNumber || index + 1} has no possible marks`);
            }
            if (!question.feedback) {
                warnings.push(`Question ${question.questionNumber || index + 1} has no feedback`);
            }
        });

        return { isValid: true, errors, warnings };
    } catch (error) {
        errors.push(`Validation error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        return { isValid: false, errors, warnings };
    }
};

// --- Legacy Support Functions ---
export const parseQuestionBreakdown = parseEvaluationData;

// --- Calculation Helper Functions ---
/**
 * Calculates the total marks by summing all marks awarded across all questions
 * This ensures accuracy and prevents calculation mistakes
 *
 * @example
 * ```typescript
 * const questions = [
 *   { questionNumber: '1', marksAwarded: 20, marksPossible: 25, ... },
 *   { questionNumber: '2', marksAwarded: 15, marksPossible: 20, ... }
 * ];
 * const totalMarks = calculateTotalMarksFromQuestions(questions); // Returns 35
 * ```
 *
 * @param questions Array of QuestionBreakdown objects
 * @returns Total marks awarded across all questions
 */
export const calculateTotalMarksFromQuestions = (questions: QuestionBreakdown[]): number => {
    if (!questions || questions.length === 0) {
        return 0;
    }

    return questions.reduce((total, question) => {
        const questionMarks = sanitizeNumber(question.marksAwarded, 0);
        return total + questionMarks;
    }, 0);
};

/**
 * Calculates the total possible marks by summing all possible marks across all questions
 * @param questions Array of QuestionBreakdown objects
 * @returns Total possible marks across all questions
 */
export const calculateTotalPossibleMarksFromQuestions = (questions: QuestionBreakdown[]): number => {
    if (!questions || questions.length === 0) {
        return 0;
    }

    return questions.reduce((total, question) => {
        const questionPossibleMarks = sanitizeNumber(question.marksPossible, 0);
        return total + questionPossibleMarks;
    }, 0);
};

/**
 * Recalculates and validates the evaluation breakdown totals based on question data
 * This function ensures that the total marks match the sum of individual question marks
 *
 * @example
 * ```typescript
 * const evaluationData = parseEvaluationData(xmlData);
 * const correctedData = validateAndCorrectEvaluationTotals(evaluationData);
 * // correctedData.totalMarks will be the accurate sum of all question marks
 * ```
 *
 * @param evaluationData EvaluationBreakdown object to validate and correct
 * @returns Updated EvaluationBreakdown with corrected totals
 */
export const validateAndCorrectEvaluationTotals = (evaluationData: EvaluationBreakdown): EvaluationBreakdown => {
    if (!evaluationData || !evaluationData.questions) {
        return evaluationData;
    }

    const calculatedTotalMarks = calculateTotalMarksFromQuestions(evaluationData.questions);
    const calculatedMaxMarks = calculateTotalPossibleMarksFromQuestions(evaluationData.questions);
    const calculatedPercentage = sanitizePercentage(calculatedTotalMarks, calculatedMaxMarks);

    // Check if there's a discrepancy and log it
    if (evaluationData.totalMarks !== calculatedTotalMarks) {
        console.warn(`Total marks mismatch detected:`, {
            originalTotal: evaluationData.totalMarks,
            calculatedTotal: calculatedTotalMarks,
            difference: Math.abs(evaluationData.totalMarks - calculatedTotalMarks)
        });
    }

    if (evaluationData.maxMarks !== calculatedMaxMarks) {
        console.warn(`Max marks mismatch detected:`, {
            originalMax: evaluationData.maxMarks,
            calculatedMax: calculatedMaxMarks,
            difference: Math.abs(evaluationData.maxMarks - calculatedMaxMarks)
        });
    }

    // Return corrected evaluation data
    return {
        ...evaluationData,
        totalMarks: calculatedTotalMarks,
        maxMarks: calculatedMaxMarks,
        overallPercentage: calculatedPercentage
    };
};

// Export utility functions for external use
export {
    sanitizeNumber,
    sanitizeString,
    sanitizePercentage,
    extractXMLFromRawData,
    cleanAndRepairXML
};
