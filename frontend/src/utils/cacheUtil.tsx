interface CacheItem {
    data: any;
    expiry: number;
  }
  
  const cache: Map<string, CacheItem> = new Map();
  
  /**
   * Sets data in the cache with an optional TTL (default is 5 minutes).
   * @param key Unique key for the cache item (e.g., the URL).
   * @param data The response data to cache.
   * @param ttl Time-to-live in milliseconds.
   */
  export const setCache = (key: string, data: any, ttl: number = 300000): void => {
    const expiry = Date.now() + ttl;
    cache.set(key, { data, expiry });
    console.error("cache map: ", cache);
  };
  
  /**
   * Retrieves data from the cache if available and not expired.
   * @param key The cache key.
   * @returns The cached data or null if not found or expired.
   */
  export const getCache = (key: string): any | null => {
    const cachedItem = cache.get(key);
    if (!cachedItem) return null;
    if (Date.now() > cachedItem.expiry) {
      cache.delete(key);
      return null;
    }
    return cachedItem.data;
  };
  
  /**
   * Clears the cache for a specific key or all entries.
   * @param key Optional. If provided, clears only that key. Otherwise, clears the entire cache.
   */
  export const clearCache = (key?: string): void => {
    if (key) {
      cache.delete(key);
    } else {
      cache.clear();
    }
  };
  
  /**
   * Makes an API call using the provided axios instance and caches the result.
   * If the data is already in the cache (and not expired), it returns the cached data.
   * @param axiosInstance Your axios instance.
   * @param url The API endpoint URL.
   * @param options (Optional) Axios options.
   * @param ttl (Optional) Time-to-live for the cache in milliseconds.
   * @returns The API response data.
   */
  export const fetchWithCache = async (
    axiosInstance: any,
    url: string,
    options = {},
    ttl: number = 300000 // default TTL is 5 minutes
  ): Promise<any> => {
    const cacheKey = url;
    const cachedData = getCache(cacheKey);
    if (cachedData) {
      return cachedData;
    }
    const response = await axiosInstance.get(url, options);
    if (response.data) {
      setCache(cacheKey, response.data, ttl);
    }
    return response.data;
  };
  