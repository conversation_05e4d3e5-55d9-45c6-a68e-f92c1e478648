/**
 * Utility functions for testing responsive design improvements
 */

export interface ResponsiveBreakpoint {
  name: string;
  width: number;
  height: number;
  description: string;
}

export const RESPONSIVE_BREAKPOINTS: ResponsiveBreakpoint[] = [
  {
    name: 'mobile-portrait',
    width: 375,
    height: 667,
    description: 'iPhone SE / Small Mobile Portrait'
  },
  {
    name: 'mobile-landscape',
    width: 667,
    height: 375,
    description: 'iPhone SE / Small Mobile Landscape'
  },
  {
    name: 'mobile-large',
    width: 414,
    height: 896,
    description: 'iPhone 11 Pro / Large Mobile'
  },
  {
    name: 'tablet-portrait',
    width: 768,
    height: 1024,
    description: 'iPad Portrait'
  },
  {
    name: 'tablet-landscape',
    width: 1024,
    height: 768,
    description: 'iPad Landscape'
  },
  {
    name: 'desktop-small',
    width: 1280,
    height: 720,
    description: 'Small Desktop / Laptop'
  },
  {
    name: 'desktop-large',
    width: 1920,
    height: 1080,
    description: 'Large Desktop'
  }
];

export interface ResponsiveTestCase {
  component: string;
  element: string;
  expectedBehavior: string;
  testFunction: (element: HTMLElement, breakpoint: ResponsiveBreakpoint) => boolean;
}

export const RESPONSIVE_TEST_CASES: ResponsiveTestCase[] = [
  {
    component: 'AegisGrader',
    element: '[data-testid="header"]',
    expectedBehavior: 'Header should stack vertically on mobile and horizontally on desktop',
    testFunction: (element, breakpoint) => {
      const isStacked = element.classList.contains('flex-col') || 
                       getComputedStyle(element).flexDirection === 'column';
      return breakpoint.width < 640 ? isStacked : !isStacked;
    }
  },
  {
    component: 'AegisGrader',
    element: 'button',
    expectedBehavior: 'All buttons should have minimum 44px touch target on mobile',
    testFunction: (element, breakpoint) => {
      if (breakpoint.width >= 640) return true; // Desktop doesn't need this check
      const rect = element.getBoundingClientRect();
      return rect.height >= 44 && rect.width >= 44;
    }
  },
  {
    component: 'GradingSubmissions',
    element: '.submission-card',
    expectedBehavior: 'Submission cards should be full width on mobile',
    testFunction: (element, breakpoint) => {
      if (breakpoint.width >= 640) return true; // Desktop can have different widths
      const rect = element.getBoundingClientRect();
      const containerRect = element.parentElement?.getBoundingClientRect();
      if (!containerRect) return false;
      return rect.width >= containerRect.width * 0.9; // Allow for padding
    }
  },
  {
    component: 'GradingDetails',
    element: '.search-input',
    expectedBehavior: 'Search input should be full width on mobile',
    testFunction: (element, breakpoint) => {
      if (breakpoint.width >= 640) return true;
      const rect = element.getBoundingClientRect();
      const containerRect = element.parentElement?.getBoundingClientRect();
      if (!containerRect) return false;
      return rect.width >= containerRect.width * 0.9;
    }
  }
];

/**
 * Simulates different viewport sizes for testing
 */
export const simulateViewport = (width: number, height: number): void => {
  // Update viewport meta tag
  let viewport = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;
  if (!viewport) {
    viewport = document.createElement('meta');
    viewport.name = 'viewport';
    document.head.appendChild(viewport);
  }
  viewport.content = `width=${width}, height=${height}, initial-scale=1.0`;

  // Simulate window resize
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });

  // Trigger resize event
  window.dispatchEvent(new Event('resize'));
};

/**
 * Tests touch target accessibility
 */
export const testTouchTargets = (container: HTMLElement): {
  passed: number;
  failed: number;
  details: Array<{ element: string; size: { width: number; height: number } }>;
} => {
  const interactiveElements = container.querySelectorAll(
    'button, a, input, select, textarea, [role="button"], [tabindex]'
  );

  let passed = 0;
  let failed = 0;
  const details: Array<{ element: string; size: { width: number; height: number } }> = [];

  interactiveElements.forEach((element) => {
    const rect = element.getBoundingClientRect();
    const isAccessible = rect.width >= 44 && rect.height >= 44;
    
    if (isAccessible) {
      passed++;
    } else {
      failed++;
      details.push({
        element: element.tagName.toLowerCase() + (element.className ? `.${element.className.split(' ')[0]}` : ''),
        size: { width: rect.width, height: rect.height }
      });
    }
  });

  return { passed, failed, details };
};

/**
 * Tests text readability at different screen sizes
 */
export const testTextReadability = (container: HTMLElement): {
  tooSmall: number;
  acceptable: number;
  details: Array<{ element: string; fontSize: number }>;
} => {
  const textElements = container.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, label');
  
  let tooSmall = 0;
  let acceptable = 0;
  const details: Array<{ element: string; fontSize: number }> = [];

  textElements.forEach((element) => {
    const computedStyle = getComputedStyle(element);
    const fontSize = parseFloat(computedStyle.fontSize);
    
    // Minimum 16px for body text on mobile
    if (fontSize < 14) {
      tooSmall++;
      details.push({
        element: element.tagName.toLowerCase() + (element.className ? `.${element.className.split(' ')[0]}` : ''),
        fontSize
      });
    } else {
      acceptable++;
    }
  });

  return { tooSmall, acceptable, details };
};

/**
 * Tests horizontal scrolling issues
 */
export const testHorizontalScroll = (container: HTMLElement = document.body): boolean => {
  return container.scrollWidth <= container.clientWidth;
};

/**
 * Comprehensive responsive test runner
 */
export const runResponsiveTests = async (
  container: HTMLElement,
  breakpoints: ResponsiveBreakpoint[] = RESPONSIVE_BREAKPOINTS
): Promise<{
  breakpoint: ResponsiveBreakpoint;
  touchTargets: ReturnType<typeof testTouchTargets>;
  textReadability: ReturnType<typeof testTextReadability>;
  horizontalScroll: boolean;
  customTests: Array<{ test: ResponsiveTestCase; passed: boolean }>;
}[]> => {
  const results = [];

  for (const breakpoint of breakpoints) {
    // Simulate viewport
    simulateViewport(breakpoint.width, breakpoint.height);
    
    // Wait for layout to settle
    await new Promise(resolve => setTimeout(resolve, 100));

    // Run tests
    const touchTargets = testTouchTargets(container);
    const textReadability = testTextReadability(container);
    const horizontalScroll = testHorizontalScroll(container);
    
    // Run custom tests
    const customTests = RESPONSIVE_TEST_CASES.map(test => {
      const element = container.querySelector(test.element) as HTMLElement;
      const passed = element ? test.testFunction(element, breakpoint) : false;
      return { test, passed };
    });

    results.push({
      breakpoint,
      touchTargets,
      textReadability,
      horizontalScroll,
      customTests
    });
  }

  return results;
};

/**
 * Generates a responsive test report
 */
export const generateResponsiveReport = (
  results: Awaited<ReturnType<typeof runResponsiveTests>>
): string => {
  let report = '# Responsive Design Test Report\n\n';

  results.forEach(result => {
    report += `## ${result.breakpoint.name} (${result.breakpoint.width}x${result.breakpoint.height})\n`;
    report += `${result.breakpoint.description}\n\n`;

    // Touch targets
    report += `### Touch Targets\n`;
    report += `- ✅ Passed: ${result.touchTargets.passed}\n`;
    report += `- ❌ Failed: ${result.touchTargets.failed}\n`;
    if (result.touchTargets.details.length > 0) {
      report += `- Issues:\n`;
      result.touchTargets.details.forEach(detail => {
        report += `  - ${detail.element}: ${detail.size.width}x${detail.size.height}px\n`;
      });
    }
    report += '\n';

    // Text readability
    report += `### Text Readability\n`;
    report += `- ✅ Acceptable: ${result.textReadability.acceptable}\n`;
    report += `- ❌ Too Small: ${result.textReadability.tooSmall}\n`;
    if (result.textReadability.details.length > 0) {
      report += `- Issues:\n`;
      result.textReadability.details.forEach(detail => {
        report += `  - ${detail.element}: ${detail.fontSize}px\n`;
      });
    }
    report += '\n';

    // Horizontal scroll
    report += `### Horizontal Scroll\n`;
    report += `- ${result.horizontalScroll ? '✅' : '❌'} ${result.horizontalScroll ? 'No issues' : 'Horizontal scroll detected'}\n\n`;

    // Custom tests
    report += `### Custom Tests\n`;
    result.customTests.forEach(({ test, passed }) => {
      report += `- ${passed ? '✅' : '❌'} ${test.expectedBehavior}\n`;
    });
    report += '\n---\n\n';
  });

  return report;
};
