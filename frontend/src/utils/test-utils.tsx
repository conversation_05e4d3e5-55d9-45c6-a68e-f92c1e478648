import React, { ReactElement } from 'react';
import { render, RenderOptions, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import userEvent from '@testing-library/user-event';

// Types for wrapping providers
interface AllTheProvidersProps {
  children: React.ReactNode;
}

// Add any providers your app uses here
const AllTheProviders = ({ children }: AllTheProvidersProps) => {
  return (
    <BrowserRouter>
      {children}
    </BrowserRouter>
  );
};

// Custom render function that includes providers
const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>,
) => render(ui, { wrapper: AllTheProviders, ...options });

// Setup user-event
const setupUserEvent = () => userEvent.setup();

// Helper to wait for elements to appear
const waitForElement = async (callback: () => HTMLElement | null) => {
  return await waitFor(() => {
    const element = callback();
    if (!element) console.error('Element not found');
    return element;
  });
};

// Helper to create a mock response 
const createMockApiResponse = <T,>(data: T, status = 200, statusText = 'OK') => {
  return {
    data,
    status,
    statusText,
    headers: {},
    config: {},
  };
};

// Helper to test async functionality in hooks
const renderHookWithProviders = async <T,>(
  hook: () => T,
) => {
  let result: T;
  function TestComponent() {
    result = hook();
    return null;
  }
  
  render(<TestComponent />, { wrapper: AllTheProviders });
  // @ts-ignore - result will be assigned in TestComponent
  return { result };
};

// re-export everything from testing-library
export * from '@testing-library/react';

// override render method and export custom helpers
export { 
  customRender as render,
  setupUserEvent,
  waitForElement,
  createMockApiResponse,
  renderHookWithProviders,
}; 