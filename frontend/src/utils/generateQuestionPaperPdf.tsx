import jsPDF from 'jspdf';
import 'katex/dist/katex.min.css'; // Ensure you have the CSS for KaTeX
import html2canvas from 'html2canvas';
import ReactKatex from '@pkasila/react-katex';
import React from 'react';
import server from 'react-dom/server';

interface Question {
    id: string;
    question: string;
    options: string[];
    images?: string[];
}

interface TestDetails {
    username: string;
    topics: string;
    subject: string;
    class: string;
    date: string;
    duration: number;
    instructions: string;
    totalMarks: number;
}

const cleanQuestionText = (questionText: string): string => {
    if (!questionText) return '';
    return questionText.replace(/\boptions:\s*[\s\S]*$/i, '').trim();
};

const cleanOptionText = (optionText: string): string => {
    if (!optionText) return '';
    return optionText.replace(/^[A-Za-z][\)\.\:]\s*/, '');
}

export const generateQuestionPaperPDF = async (
    testDetails: TestDetails,
    questions: Question[]
): Promise<void> => {
    try {
        const companyName = 'AegisScholar';
        const logoUrl = 'logo_transparent.png';

        const pdf = new jsPDF('p', 'pt', 'a4');

        const pageHeight = pdf.internal.pageSize.height;
        const pageWidth = pdf.internal.pageSize.width;
        const margin = 40;
        const contentWidth = pageWidth - (margin * 2);
        let y = margin;

        const addHeader = () => {
            const headerY = margin - 10;
            const logoWidth = 25;
            const logoHeight = 25;
            const spaceBetween = 3;

            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(12);

            const companyNameWidth = pdf.getTextWidth(companyName);
            const totalHeaderWidth = logoWidth + spaceBetween + companyNameWidth;

            const logoX = pageWidth - margin - totalHeaderWidth;

            if (logoUrl) {
                try {
                    const img = new Image();
                    img.src = logoUrl;
                    // Note: In a real app, ensure the image is loaded before calling addImage
                    pdf.addImage(img, 'PNG', logoX, headerY - (logoHeight / 2), logoWidth, logoHeight);
                } catch (e) {
                    console.error("Could not load the logo image for the PDF.", e);
                }
            }

            const companyNameX = logoX + logoWidth + spaceBetween;
            pdf.text(companyName, companyNameX, headerY, { baseline: 'middle' });

            pdf.setDrawColor(180, 180, 180);
            pdf.line(margin, headerY + 15, pageWidth - margin, headerY + 15);
        };

        // Add footer function
        const addFooter = (pageNumber: number) => {
            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(10);
            pdf.text(
                `Page ${pageNumber}`,
                pageWidth / 2,
                pageHeight - 20,
                { align: 'center' }
            );
        };

        const checkNewPage = (requiredSpace: number) => {
            if (y + requiredSpace > pageHeight - margin) {
                // Store current font settings
                const currentFontSize = pdf.getFontSize();
                const currentFont = pdf.getFont();

                const currentPage = pdf.getCurrentPageInfo().pageNumber;
                addFooter(currentPage);
                pdf.addPage();
                y = margin;
                addHeader();
                y += 40;

                // Restore font settings after new page
                pdf.setFontSize(currentFontSize);
                pdf.setFont(currentFont.fontName, currentFont.fontStyle);
            }
        };

        // Initial page header
        addHeader();
        y += 40; // Increase space after header for better visual separation

        // Main Header - Subject
        pdf.setFontSize(20);
        pdf.setFont('helvetica', 'bold');
        pdf.text(testDetails.subject, pageWidth / 2, y, { align: 'center' });
        y += 30;

        // Test Details Section
        pdf.setFontSize(11);
        pdf.setFont('helvetica', 'normal');

        // Left-aligned single column layout
        const detailsContent = {
            'Name': testDetails.username,
            'Class': testDetails.class,
            'Topics': testDetails.topics,
            'Date': testDetails.date,
            'Duration': `${testDetails.duration} minutes`
        };

        // Render details in a single column
        Object.entries(detailsContent).forEach(([label, value]) => {
            pdf.setFont('helvetica', 'bold');
            pdf.text(`${label}:`, margin, y);
            pdf.setFont('helvetica', 'normal');
            pdf.text(value, margin + 70, y);
            y += 20;
        });

        y += 15; // Additional spacing before instructions

        // Instructions
        if (testDetails.instructions.length > 0) {
            pdf.setFontSize(12);
            pdf.setFont('helvetica', 'bold');
            pdf.text('Instructions:', margin, y);
            y += 18;

            pdf.setFont('helvetica', 'normal');
            pdf.setFontSize(10);
            const instructionsLines = pdf.splitTextToSize(testDetails.instructions, contentWidth);
            instructionsLines.forEach((line: string) => {
                checkNewPage(12);
                pdf.text(line, margin, y);
                y += 12;
            });
            y += 25;
        }
        // Questions Section (Logic remains the same)
        pdf.setFontSize(11);
        questions.forEach((question, index) => {
            checkNewPage(40);

            // Question
            pdf.setFont('helvetica', 'bold');
            const questionNumber = `Q${index + 1}. `; 
            const questionText = cleanQuestionText(question.question);

            // Calculate proper width for wrapped text
            const maxWidth = contentWidth - pdf.getTextWidth(questionNumber) - 10;
            const questionLines = pdf.splitTextToSize(questionText, maxWidth);

            // Draw question number
            pdf.text(questionNumber, margin, y);

            // Draw question text with proper spacing
            pdf.setFont('helvetica', 'normal');
            questionLines.forEach((line: string) => {
                checkNewPage(14);
                pdf.text(line, margin + pdf.getTextWidth(questionNumber), y);
                y += 14;
            });
            y += 8;

            // Options with improved spacing
            question.options.forEach((option, optIndex) => {
                checkNewPage(14);
                const optionLetter = `${String.fromCharCode(65 + optIndex)}) `;  // Add space after bracket
                const optionText = cleanOptionText(option);
                const optionMaxWidth = contentWidth - 45;  // Account for option letter width
                const optionLines = pdf.splitTextToSize(optionText, optionMaxWidth);

                optionLines.forEach((line: string, lineIndex: number) => {
                    checkNewPage(14);
                    if (lineIndex === 0) {
                        pdf.text(optionLetter, margin + 20, y);
                        pdf.text(line, margin + 20 + pdf.getTextWidth(optionLetter), y);
                    } else {
                        pdf.text(line, margin + 20 + pdf.getTextWidth(optionLetter), y);
                    }
                    y += 14;
                });
            });
            y += 18;
        });

        // Add footer to last page
        const lastPage = pdf.getCurrentPageInfo().pageNumber;
        addFooter(lastPage);

        // Format filename first
        const formattedDate = testDetails.date.replace(/[\/\s]/g, '-');
        const filename = `${testDetails.class}_${testDetails.subject}_${formattedDate}.pdf`;

        // Generate PDF with filename
        const pdfData = pdf.output('arraybuffer');
        const pdfBlob = new Blob([pdfData], {
            type: 'application/pdf'
        });

        // Create object URL with suggested filename
        const pdfFile = new File([pdfBlob], filename, { type: 'application/pdf' });
        const pdfUrl = URL.createObjectURL(pdfFile);

        try {
            // Open preview in new tab
            const previewWindow = window.open('', '_blank');
            if (previewWindow) {
                previewWindow.location.href = pdfUrl;
            } else {
                // Fallback direct download
                const link = document.createElement('a');
                link.href = pdfUrl;
                link.download = filename;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            // Cleanup URL after delay
            setTimeout(() => URL.revokeObjectURL(pdfUrl), 60000);
        } catch (downloadError) {
            console.error('Error handling PDF:', downloadError);
            alert('Failed to preview/download PDF. Please try again.');
        }

    } catch (error) {
        console.error('Error generating PDF:', error);
        alert('Failed to generate PDF. Please try again.');
    }
};