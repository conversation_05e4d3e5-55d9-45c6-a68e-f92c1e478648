import {
    parseEvaluationData,
    parseEvaluationForGradingDetails,
    validateEvaluationData,
    calculateTotalMarksFromQuestions,
    calculateTotalPossibleMarksFromQuestions,
    validateAndCorrectEvaluationTotals
} from '../xmlEvaluationParser';

describe('XML Evaluation Parser', () => {
    const sampleXMLData = [
        `<evaluation>
            <total_marks_awarded>85</total_marks_awarded>
            <maximum_possible_marks>100</maximum_possible_marks>
            <percentage_score>85</percentage_score>
            <question number="1">
                <marks_awarded>20</marks_awarded>
                <marks_possible>25</marks_possible>
                <feedback>Good analysis but could be more detailed.</feedback>
                <marks_breakdown>
                    <criterion name="Content">15/20</criterion>
                    <criterion name="Structure">5/5</criterion>
                </marks_breakdown>
            </question>
            <question number="2">
                <marks_awarded>65</marks_awarded>
                <marks_possible>75</marks_possible>
                <feedback>Excellent work with minor improvements needed.</feedback>
                <detailed_feedback>
                    <overall_comment>Strong understanding demonstrated</overall_comment>
                    <structural_analysis>
                        <introduction>Clear and engaging</introduction>
                        <body_structure_and_flow>Well organized</body_structure_and_flow>
                        <conclusion>Effective summary</conclusion>
                    </structural_analysis>
                </detailed_feedback>
            </question>
        </evaluation>`
    ];

    const escapedJSONData = [
        `"<evaluation>\\n<total_marks_awarded>75</total_marks_awarded>\\n<maximum_possible_marks>100</maximum_possible_marks>\\n<question number=\\"1\\">\\n<marks_awarded>75</marks_awarded>\\n<marks_possible>100</marks_possible>\\n<feedback>Good work</feedback>\\n</question>\\n</evaluation>"`
    ];

    const markdownWrappedData = [
        `Here is the evaluation:

\`\`\`xml
<evaluation>
    <total_marks_awarded>80</total_marks_awarded>
    <maximum_possible_marks>100</maximum_possible_marks>
    <question number="1">
        <marks_awarded>80</marks_awarded>
        <marks_possible>100</marks_possible>
        <feedback>**Good work** with *minor improvements* needed.</feedback>
    </question>
</evaluation>
\`\`\`

This completes the evaluation.`
    ];

    const malformedXMLData = [
        `<evaluation>
            <total_marks_awarded>90</total_marks_awarded>
            <maximum_possible_marks>100</maximum_possible_marks>
            <question number="1">
                <marks_awarded>90</marks_awarded>
                <marks_possible>100</marks_possible>
                <feedback>Excellent work</feedback>
            </question>
        </evaluation>`
    ];

    const htmlEntitiesData = [
        `&lt;evaluation&gt;
            &lt;total_marks_awarded&gt;85&lt;/total_marks_awarded&gt;
            &lt;maximum_possible_marks&gt;100&lt;/maximum_possible_marks&gt;
            &lt;question number=&quot;1&quot;&gt;
                &lt;marks_awarded&gt;85&lt;/marks_awarded&gt;
                &lt;marks_possible&gt;100&lt;/marks_possible&gt;
                &lt;feedback&gt;Good analysis&lt;/feedback&gt;
            &lt;/question&gt;
        &lt;/evaluation&gt;`
    ];

    describe('parseEvaluationData', () => {
        it('should parse valid XML evaluation data correctly', () => {
            const result = parseEvaluationData(sampleXMLData);
            
            expect(result).not.toBeNull();
            expect(result?.totalMarks).toBe(85);
            expect(result?.maxMarks).toBe(100);
            expect(result?.overallPercentage).toBe(85);
            expect(result?.questions).toHaveLength(2);
            
            const firstQuestion = result?.questions[0];
            expect(firstQuestion?.questionNumber).toBe('1');
            expect(firstQuestion?.marksAwarded).toBe(20);
            expect(firstQuestion?.marksPossible).toBe(25);
            expect(firstQuestion?.feedback).toContain('Good analysis');
            expect(firstQuestion?.criteriaBreakdown).toHaveLength(2);
        });

        it('should handle escaped JSON format', () => {
            const result = parseEvaluationData(escapedJSONData);
            
            expect(result).not.toBeNull();
            expect(result?.totalMarks).toBe(75);
            expect(result?.maxMarks).toBe(100);
            expect(result?.questions).toHaveLength(1);
        });

        it('should return null for invalid data', () => {
            expect(parseEvaluationData(null)).toBeNull();
            expect(parseEvaluationData([])).toBeNull();
            expect(parseEvaluationData(['invalid xml'])).toBeNull();
        });

        it('should handle structured feedback correctly', () => {
            const result = parseEvaluationData(sampleXMLData);
            const secondQuestion = result?.questions[1];

            expect(secondQuestion?.structuredFeedback).toBeDefined();
            expect(secondQuestion?.structuredFeedback).toHaveLength(2);
            expect(secondQuestion?.structuredFeedback?.[0].title).toBe('Overall Assessment');
            expect(secondQuestion?.structuredFeedback?.[1].title).toBe('Structural Analysis');
        });

        it('should handle markdown-wrapped XML', () => {
            const result = parseEvaluationData(markdownWrappedData);

            expect(result).not.toBeNull();
            expect(result?.totalMarks).toBe(80);
            expect(result?.maxMarks).toBe(100);
            expect(result?.questions).toHaveLength(1);

            const question = result?.questions[0];
            expect(question?.feedback).toBe('Good work with minor improvements needed.');
        });

        it('should repair malformed XML', () => {
            const result = parseEvaluationData(malformedXMLData);

            expect(result).not.toBeNull();
            expect(result?.totalMarks).toBe(90);
            expect(result?.maxMarks).toBe(100);
            expect(result?.questions).toHaveLength(1);

            const question = result?.questions[0];
            expect(question?.marksAwarded).toBe(90);
            expect(question?.marksPossible).toBe(100);
            expect(question?.feedback).toBe('Excellent work');
        });

        it('should handle HTML entities', () => {
            const result = parseEvaluationData(htmlEntitiesData);

            expect(result).not.toBeNull();
            expect(result?.totalMarks).toBe(85);
            expect(result?.maxMarks).toBe(100);
            expect(result?.questions).toHaveLength(1);

            const question = result?.questions[0];
            expect(question?.feedback).toBe('Good analysis');
        });

        it('should handle mixed edge cases', () => {
            const mixedData = [
                `Here's the evaluation result:

\`\`\`
&lt;evaluation&gt;
    &lt;total_marks_awarded&gt;95&lt;/total_marks_awarded&gt;
    &lt;maximum_possible_marks&gt;100&lt;/maximum_possible_marks&gt;
    &lt;question number=&quot;1&quot;&gt;
        &lt;marks_awarded&gt;95&lt;/marks_awarded&gt;
        &lt;marks_possible&gt;100&lt;/marks_possible&gt;
        &lt;feedback&gt;**Excellent** analysis&lt;/feedback&gt;
    &lt;/question&gt;
&lt;/evaluation&gt;
\`\`\`

That's the complete evaluation.`
            ];

            const result = parseEvaluationData(mixedData);

            expect(result).not.toBeNull();
            expect(result?.totalMarks).toBe(95);
            expect(result?.maxMarks).toBe(100);
            expect(result?.questions).toHaveLength(1);

            const question = result?.questions[0];
            expect(question?.marksAwarded).toBe(95);
            expect(question?.marksPossible).toBe(100);
            expect(question?.feedback).toBe('Excellent analysis');
        });

        it('should handle criteria with missing maxScore correctly', () => {
            const dataWithMissingMaxScore = [
                `<evaluation>
                    <total_marks_awarded>80</total_marks_awarded>
                    <maximum_possible_marks>100</maximum_possible_marks>
                    <question number="1">
                        <marks_awarded>80</marks_awarded>
                        <marks_possible>100</marks_possible>
                        <feedback>Good work with some areas for improvement.</feedback>
                        <marks_breakdown>
                            <criterion name="Content">15/20</criterion>
                            <criterion name="Structure">5</criterion>
                            <criterion name="Grammar">10</criterion>
                        </marks_breakdown>
                    </question>
                </evaluation>`
            ];

            const result = parseEvaluationData(dataWithMissingMaxScore);

            expect(result).not.toBeNull();
            expect(result?.questions).toHaveLength(1);

            const question = result?.questions[0];
            expect(question?.criteriaBreakdown).toHaveLength(3);

            // Check that criteria with maxScore are parsed correctly
            const contentCriterion = question?.criteriaBreakdown.find(c => c.criterion === 'Content');
            expect(contentCriterion?.score).toBe('15');
            expect(contentCriterion?.maxScore).toBe('20');

            // Check that criteria without maxScore have undefined maxScore
            const structureCriterion = question?.criteriaBreakdown.find(c => c.criterion === 'Structure');
            expect(structureCriterion?.score).toBe('5');
            expect(structureCriterion?.maxScore).toBeUndefined();

            const grammarCriterion = question?.criteriaBreakdown.find(c => c.criterion === 'Grammar');
            expect(grammarCriterion?.score).toBe('10');
            expect(grammarCriterion?.maxScore).toBeUndefined();
        });
    });

    describe('parseEvaluationForGradingDetails', () => {
        it('should convert to GradingDetails format correctly', () => {
            const result = parseEvaluationForGradingDetails(sampleXMLData);
            
            expect(result).not.toBeNull();
            expect(result?.total_marks).toBe(85);
            expect(result?.maximum_possible_marks).toBe(100);
            expect(result?.percentage_score).toBe(85);
            expect(result?.section).toHaveLength(1);
            expect(result?.section[0].question).toHaveLength(2);
        });
    });

    describe('validateEvaluationData', () => {
        it('should validate correct data', () => {
            const validation = validateEvaluationData(sampleXMLData);
            
            expect(validation.isValid).toBe(true);
            expect(validation.errors).toHaveLength(0);
        });

        it('should detect invalid data', () => {
            const validation = validateEvaluationData(null);
            
            expect(validation.isValid).toBe(false);
            expect(validation.errors).toContain('No evaluation data provided');
        });

        it('should provide warnings for incomplete data', () => {
            const incompleteData = [
                `<evaluation>
                    <total_marks_awarded>0</total_marks_awarded>
                    <maximum_possible_marks>0</maximum_possible_marks>
                </evaluation>`
            ];
            
            const validation = validateEvaluationData(incompleteData);
            
            expect(validation.isValid).toBe(true);
            expect(validation.warnings).toContain('No questions found in evaluation data');
            expect(validation.warnings).toContain('No marks information available');
        });
    });

    describe('Calculation Helper Functions', () => {
        const mockQuestions = [
            {
                questionNumber: '1',
                marksAwarded: 20,
                marksPossible: 25,
                percentage: 80,
                feedback: 'Good work',
                criteriaBreakdown: []
            },
            {
                questionNumber: '2',
                marksAwarded: 65,
                marksPossible: 75,
                percentage: 87,
                feedback: 'Excellent',
                criteriaBreakdown: []
            }
        ];

        it('should calculate total marks correctly', () => {
            const totalMarks = calculateTotalMarksFromQuestions(mockQuestions);
            expect(totalMarks).toBe(85); // 20 + 65
        });

        it('should calculate total possible marks correctly', () => {
            const totalPossible = calculateTotalPossibleMarksFromQuestions(mockQuestions);
            expect(totalPossible).toBe(100); // 25 + 75
        });

        it('should handle empty questions array', () => {
            expect(calculateTotalMarksFromQuestions([])).toBe(0);
            expect(calculateTotalPossibleMarksFromQuestions([])).toBe(0);
        });

        it('should validate and correct evaluation totals', () => {
            const mockEvaluation = {
                totalMarks: 80, // Incorrect total (should be 85)
                maxMarks: 95,   // Incorrect max (should be 100)
                overallPercentage: 84, // Will be recalculated
                questions: mockQuestions
            };

            const correctedEvaluation = validateAndCorrectEvaluationTotals(mockEvaluation);

            expect(correctedEvaluation.totalMarks).toBe(85);
            expect(correctedEvaluation.maxMarks).toBe(100);
            expect(correctedEvaluation.overallPercentage).toBe(85); // 85/100 = 85%
        });

        it('should handle evaluation with correct totals', () => {
            const mockEvaluation = {
                totalMarks: 85,
                maxMarks: 100,
                overallPercentage: 85,
                questions: mockQuestions
            };

            const correctedEvaluation = validateAndCorrectEvaluationTotals(mockEvaluation);

            // Should remain the same since totals are already correct
            expect(correctedEvaluation.totalMarks).toBe(85);
            expect(correctedEvaluation.maxMarks).toBe(100);
            expect(correctedEvaluation.overallPercentage).toBe(85);
        });
    });
});
