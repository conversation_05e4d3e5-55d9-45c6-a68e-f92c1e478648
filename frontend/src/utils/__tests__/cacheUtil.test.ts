import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { setCache, getCache, clearCache, fetchWithCache } from '../cacheUtil';

describe('Cache Utilities', () => {
  beforeEach(() => {
    // Clear the cache before each test
    clearCache();
    
    // Reset timers and mocks
    vi.restoreAllMocks();
  });

  describe('setCache and getCache', () => {
    it('should store and retrieve data from cache', () => {
      const testKey = 'test-key';
      const testData = { name: 'Test Data' };
      
      // Set data in cache
      setCache(testKey, testData);
      
      // Get data from cache
      const result = getCache(testKey);
      
      // Verify
      expect(result).toEqual(testData);
    });

    it('should return null for non-existent cache keys', () => {
      const result = getCache('non-existent-key');
      expect(result).toBeNull();
    });

    it('should respect the TTL setting', () => {
      // Mock Date.now to control time
      const originalNow = Date.now;
      const mockNow = vi.fn();
      Date.now = mockNow;
      
      // Initial time
      mockNow.mockReturnValue(1000);
      
      // Set cache with 30ms TTL
      setCache('short-lived', 'data', 30);
      
      // Still within TTL
      mockNow.mockReturnValue(1025);
      expect(getCache('short-lived')).toEqual('data');
      
      // After TTL expiry
      mockNow.mockReturnValue(1035);
      expect(getCache('short-lived')).toBeNull();
      
      // Restore Date.now
      Date.now = originalNow;
    });
  });

  describe('clearCache', () => {
    it('should clear a specific cache entry', () => {
      // Setup multiple cache entries
      setCache('key1', 'value1');
      setCache('key2', 'value2');
      
      // Clear only one key
      clearCache('key1');
      
      // Verify
      expect(getCache('key1')).toBeNull();
      expect(getCache('key2')).toEqual('value2');
    });

    it('should clear all cache entries when no key is provided', () => {
      // Setup multiple cache entries
      setCache('key1', 'value1');
      setCache('key2', 'value2');
      
      // Clear all cache
      clearCache();
      
      // Verify
      expect(getCache('key1')).toBeNull();
      expect(getCache('key2')).toBeNull();
    });
  });

  describe('fetchWithCache', () => {
    it('should call API and cache the result if not cached', async () => {
      // Mock axios instance
      const mockAxios = {
        get: vi.fn().mockResolvedValue({ data: { results: [1, 2, 3] } })
      };
      
      // First call should hit the API
      const result1 = await fetchWithCache(mockAxios, '/test/api', {});
      
      // Verify
      expect(mockAxios.get).toHaveBeenCalledWith('/test/api', {});
      expect(result1).toEqual({ results: [1, 2, 3] });
      
      // Second call should use cache
      mockAxios.get.mockClear();
      const result2 = await fetchWithCache(mockAxios, '/test/api', {});
      
      // Verify axios not called again
      expect(mockAxios.get).not.toHaveBeenCalled();
      expect(result2).toEqual({ results: [1, 2, 3] });
    });

    it('should respect the TTL settings for API calls', async () => {
      // Mock Date.now to control time
      const originalNow = Date.now;
      const mockNow = vi.fn();
      Date.now = mockNow;
      mockNow.mockReturnValue(1000);
      
      // Mock axios instance
      const mockAxios = {
        get: vi.fn()
          .mockResolvedValueOnce({ data: { value: 'first call' } })
          .mockResolvedValueOnce({ data: { value: 'second call' } })
      };
      
      // First call with short TTL
      const result1 = await fetchWithCache(mockAxios, '/api/short-ttl', {}, 50);
      expect(result1).toEqual({ value: 'first call' });
      expect(mockAxios.get).toHaveBeenCalledTimes(1);
      
      // Time passes but within TTL
      mockNow.mockReturnValue(1040);
      
      // Second call should use cache
      const result2 = await fetchWithCache(mockAxios, '/api/short-ttl', {}, 50);
      expect(result2).toEqual({ value: 'first call' });
      expect(mockAxios.get).toHaveBeenCalledTimes(1);
      
      // Time passes beyond TTL
      mockNow.mockReturnValue(1060);
      
      // Third call should hit API again
      const result3 = await fetchWithCache(mockAxios, '/api/short-ttl', {}, 50);
      expect(result3).toEqual({ value: 'second call' });
      expect(mockAxios.get).toHaveBeenCalledTimes(2);
      
      // Restore Date.now
      Date.now = originalNow;
    });
  });
}); 