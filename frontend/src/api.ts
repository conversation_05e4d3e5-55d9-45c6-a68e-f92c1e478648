import { axiosDefault } from './axios';

const AUTH_URL = import.meta.env.API_AUTH_URL || '/api/auth';
// const KNOWLEDGEGRAPH_URL = '/api/knowledgegraph/student'

const apiRequest = async (method: string, url: string, data: any = null) => {
  try {
    // console.error(`Making ${method.toUpperCase()} request to ${url} with data:`, data); // Log request details
    const response = await axiosDefault({
      method, url, data,
    });
    return response.data;
  } catch (error: any) {
    if (error.response) {
      // Log detailed response data if available
      console.error(`Error response from ${method.toUpperCase()} request to ${url}:`, error.response.data);
    } else {
      // Log general error if response data is not available
      console.error(`Error during ${method.toUpperCase()} request to ${url}:`, error.message);
    }
     // Rethrow to handle upstream
  }
};

// Register a new user (POST request), dynamically handles students or teachers
export const register = async (userType: string, username: string, email: string, password: string, additionalData: object = {}) => {
  const url = `${AUTH_URL}/register${userType}`;
  return apiRequest('post', url, {userType, username, email, password, ...additionalData });
};

// User login (POST request), dynamically handles students or teachers
export const login = async (userType: string, email: string, password: string) => {
  const url = `${AUTH_URL}/login${userType}`;
  return apiRequest('post', url, { email, password });
};

// Register or login with Google (POST request), handles students or teachers
export const googleRegister = async (idToken: string, userType: string) => {
  const url = `${AUTH_URL}/google-auth`;
  try {
    const result = await apiRequest('post', url, { idToken, userType });
    console.log('Google Sign-In success:', result);
    return result;
  } catch (error) {
    console.error('Google Sign-In error:', error);
    throw error;
  }
};

// export const fetchKnowledgeGraph = async (studentId: string) => {
//   const url = `${KNOWLEDGEGRAPH_URL}/${studentId}`;
//   return apiRequest('get', url);
// };


