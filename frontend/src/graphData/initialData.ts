import { Edge, Node } from '@xyflow/react';
import { CustomNode, TopicToSubtopicsMap } from '../components/CustomNodes/types';


export const initialNodes: CustomNode[] = [];

export const initialEdges: Edge[] = [];


export const createTopicToSubtopicsMap = (data: any): TopicToSubtopicsMap => {
  const map: TopicToSubtopicsMap = {};

  data.topics.forEach((topic: any) => {
    map[topic._id] = topic.children.map((subtopic: any) => subtopic._id);
  });

  return map;
};