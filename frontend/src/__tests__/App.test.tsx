import { describe, it, expect, vi } from 'vitest';
import { render, screen } from '@testing-library/react';
import App from '../App';

// Don't use the test-utils with BrowserRouter since App might already include a router
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    // Mock the router components to avoid conflicts
    createBrowserRouter: vi.fn().mockReturnValue({}),
    RouterProvider: ({ router }: any) => <div data-testid="router-provider">RouterProvider Mock</div>,
    Route: ({ children }: any) => <div data-testid="route">Route Mock {children}</div>,
    Outlet: () => <div data-testid="outlet">Outlet Mock</div>,
    createRoutesFromElements: (children: any) => children,
  };
});

describe('App', () => {
  it('renders without crashing', () => {
    render(<App />);
    expect(screen.getByTestId('router-provider')).toBeInTheDocument();
  });
}); 