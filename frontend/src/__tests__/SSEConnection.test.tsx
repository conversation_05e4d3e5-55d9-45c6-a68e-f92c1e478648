import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { fetchEventSource } from '@microsoft/fetch-event-source';

// Mock the fetchEventSource function
vi.mock('@microsoft/fetch-event-source', () => ({
  fetchEventSource: vi.fn()
}));

describe('SSE Connection Management', () => {
  let mockAbortController: AbortController;
  let mockFetchEventSource: any;

  beforeEach(() => {
    mockAbortController = new AbortController();
    mockFetchEventSource = vi.mocked(fetchEventSource);
    
    // Reset all mocks
    vi.clearAllMocks();
    
    // Mock AbortController
    global.AbortController = vi.fn(() => mockAbortController) as any;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should properly configure SSE connection with correct headers', async () => {
    const mockUrl = 'http://localhost:8080/api/aegisGrader/getCurrentProgress/test-job-id';
    const mockToken = 'test-token';

    // Mock the fetchEventSource to resolve immediately
    mockFetchEventSource.mockResolvedValue(undefined);

    // Simulate the SSE connection setup from our component
    await fetchEventSource(mockUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${mockToken}`,
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      signal: mockAbortController.signal,
      onopen: vi.fn(),
      onmessage: vi.fn(),
      onclose: vi.fn(),
      onerror: vi.fn(),
      openWhenHidden: false
    });

    // Verify fetchEventSource was called with correct parameters
    expect(mockFetchEventSource).toHaveBeenCalledWith(mockUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${mockToken}`,
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      },
      signal: mockAbortController.signal,
      onopen: expect.any(Function),
      onmessage: expect.any(Function),
      onclose: expect.any(Function),
      onerror: expect.any(Function),
      openWhenHidden: false
    });
  });

  it('should handle connection abort properly', () => {
    const abortSpy = vi.spyOn(mockAbortController, 'abort');

    // Simulate cleanup
    mockAbortController.abort();

    expect(abortSpy).toHaveBeenCalled();
    expect(mockAbortController.signal.aborted).toBe(true);
  });

  it('should handle message parsing correctly', () => {
    const mockEvent = {
      data: JSON.stringify({
        progress: 50,
        status: 'processing',
        timestamp: new Date().toISOString()
      })
    };

    // Test the message parsing logic
    const parsedData = JSON.parse(mockEvent.data);
    
    expect(parsedData).toEqual({
      progress: 50,
      status: 'processing',
      timestamp: expect.any(String)
    });
    expect(parsedData.progress).toBe(50);
    expect(parsedData.status).toBe('processing');
  });

  it('should handle completion status correctly', () => {
    const mockCompletedEvent = {
      data: JSON.stringify({
        progress: 100,
        status: 'completed',
        timestamp: new Date().toISOString()
      })
    };

    const parsedData = JSON.parse(mockCompletedEvent.data);
    
    expect(parsedData.status).toBe('completed');
    expect(parsedData.progress).toBe(100);
  });

  it('should handle error status correctly', () => {
    const mockErrorEvent = {
      data: JSON.stringify({
        progress: 0,
        status: 'error',
        error: 'Processing failed',
        timestamp: new Date().toISOString()
      })
    };

    const parsedData = JSON.parse(mockErrorEvent.data);
    
    expect(parsedData.status).toBe('error');
    expect(parsedData.error).toBe('Processing failed');
  });

  it('should handle malformed JSON gracefully', () => {
    const mockMalformedEvent = {
      data: 'invalid json'
    };

    expect(() => {
      JSON.parse(mockMalformedEvent.data);
    }).toThrow();

    // In real implementation, this should be caught and handled gracefully
    try {
      JSON.parse(mockMalformedEvent.data);
    } catch (error) {
      expect(error).toBeInstanceOf(SyntaxError);
    }
  });
});

describe('Error Classes', () => {
  it('should create RetriableError correctly', () => {
    class RetriableError extends Error { }
    
    const error = new RetriableError('Network timeout');
    expect(error).toBeInstanceOf(Error);
    expect(error).toBeInstanceOf(RetriableError);
    expect(error.message).toBe('Network timeout');
  });

  it('should create FatalError correctly', () => {
    class FatalError extends Error { }
    
    const error = new FatalError('Authentication failed');
    expect(error).toBeInstanceOf(Error);
    expect(error).toBeInstanceOf(FatalError);
    expect(error.message).toBe('Authentication failed');
  });
});
