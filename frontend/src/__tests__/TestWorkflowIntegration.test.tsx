import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';

// Import components for integration testing
import ScheduleTestForm from '../pages/ScheduleTestPage';
import ReviewTestPage from '../pages/ReviewTestPage';
import TestInstructionsPage from '../pages/TestInstructionsPage';
import StudentTestPage from '../pages/StudentTestPage';
import ViewUpcomingTest from '../components/ViewUpcomingTest';

// Mock all external dependencies
vi.mock('../hooks/useAxiosPrivate', () => ({
  useAxiosPrivate: () => ({
    get: vi.fn(),
    post: vi.fn(),
  }),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({ state: null }),
  };
});

vi.mock('react-toastify', () => ({
  toast: {
    loading: vi.fn(),
    update: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
  },
  ToastContainer: () => <div data-testid="toast-container" />,
}));

vi.mock('@/utils/cacheUtil', () => ({
  fetchWithCache: vi.fn(),
}));

vi.mock('../components/QuestionCard', () => ({
  default: ({ question, onKeep, onDiscard }: any) => (
    <div data-testid="question-card">
      <h3>{question.question}</h3>
      <button onClick={() => onKeep(question)} data-testid="keep-button">
        Keep
      </button>
      <button onClick={() => onDiscard(question)} data-testid="discard-button">
        Discard
      </button>
    </div>
  ),
}));

// Mock UserProvider to accept value prop for testing
const MockUserProvider = ({ children, value }: { children: React.ReactNode; value?: any }) => {
  return <div data-testid="mock-user-provider">{children}</div>;
};

vi.mock('../contexts/userContext', () => ({
  useUser: () => ({
    user: null,
    setUser: vi.fn(),
  }),
  UserProvider: MockUserProvider,
}));

const mockTeacher = {
  id: 'teacher123',
  role: 'Teacher',
  name: 'John Teacher',
  classes: [
    {
      _id: 'class1',
      className: 'Class 10A',
      subjects: ['Mathematics', 'Physics'],
    },
  ],
};

const mockStudent = {
  id: 'student123',
  role: 'Student',
  name: 'Jane Student',
  classes: [
    {
      _id: 'class1',
      className: 'Class 10A',
      subjects: ['Mathematics', 'Physics'],
    },
  ],
  subjects: [
    {
      subjectName: 'Mathematics',
      testHistory: [
        {
          _id: 'test123',
          subject: 'Mathematics',
          testDate: new Date('2024-12-25'),
          startTime: '10:00',
          duration: 60,
          numberOfQuestions: 10,
          totalMarks: 100,
          active: true,
        },
      ],
      attemptedTests: [],
    },
  ],
};

const mockQuestions = [
  {
    _id: 'q1',
    question: 'What is 2 + 2?',
    options: ['3', '4', '5', '6'],
    answer: '4',
    topic: ['Algebra'],
    subtopic: ['Basic Operations'],
    difficulty: 0.5,
  },
  {
    _id: 'q2',
    question: 'What is 3 × 3?',
    options: ['6', '9', '12', '15'],
    answer: '9',
    topic: ['Algebra'],
    subtopic: ['Multiplication'],
    difficulty: 0.6,
  },
];

const renderWithProviders = (component: React.ReactElement, user = mockTeacher) => {
  // Mock the useUser hook to return the provided user
  vi.mocked(require('../contexts/userContext').useUser).mockReturnValue({
    user,
    setUser: vi.fn(),
  });

  return render(
    <BrowserRouter>
      <MockUserProvider value={{ user, setUser: vi.fn() }}>
        {component}
      </MockUserProvider>
    </BrowserRouter>
  );
};

describe('Test Workflow Integration Tests', () => {
  let mockAxios: any;
  let mockFetchWithCache: any;
  let mockNavigate: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockAxios = { get: vi.fn(), post: vi.fn() };
    mockFetchWithCache = vi.fn();
    mockNavigate = vi.fn();

    vi.mocked(require('../hooks/useAxiosPrivate').useAxiosPrivate).mockReturnValue(mockAxios);
    vi.mocked(require('@/utils/cacheUtil').fetchWithCache).mockImplementation(mockFetchWithCache);
    vi.mocked(require('react-router-dom').useNavigate).mockReturnValue(mockNavigate);
  });

  describe('Complete Teacher Test Creation Workflow', () => {
    describe('Personalized Test Creation', () => {
      it('creates personalized test end-to-end', async () => {
        // Mock API responses
        mockAxios.post
          .mockResolvedValueOnce({
            data: { recommendations: JSON.stringify(mockQuestions) },
          }) // recommend API
          .mockResolvedValueOnce({
            status: 201,
            data: { test: { _id: 'test123' } },
          }); // createPersonalizedTest API

        renderWithProviders(<ScheduleTestForm />);

        // Fill form for personalized test
        fireEvent.change(screen.getByLabelText(/class/i), {
          target: { value: 'class1' },
        });
        fireEvent.change(screen.getByLabelText(/subject/i), {
          target: { value: 'Mathematics' },
        });
        fireEvent.change(screen.getByLabelText(/test type/i), {
          target: { value: 'personalized' },
        });

        // Add topics
        const topicsInput = screen.getByLabelText(/topics/i);
        fireEvent.change(topicsInput, { target: { value: 'Algebra' } });
        fireEvent.keyDown(topicsInput, { key: 'Enter' });

        // Set future date
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        fireEvent.change(screen.getByLabelText(/date/i), {
          target: { value: tomorrow.toISOString().split('T')[0] },
        });

        // Submit form
        const submitButton = screen.getByRole('button', { name: /schedule test/i });
        fireEvent.click(submitButton);

        await waitFor(() => {
          expect(mockAxios.post).toHaveBeenCalledWith('/api/recommend', expect.any(Object));
          expect(mockAxios.post).toHaveBeenCalledWith('/api/test/createPersonalizedTest', expect.any(Object));
        });
      });
    });

    describe('Generic Test Creation', () => {
      it('creates generic test through review process', async () => {
        // Mock location state for ReviewTestPage
        vi.mocked(require('react-router-dom').useLocation).mockReturnValue({
          state: {
            class: 'class1',
            subject: 'Mathematics',
            testType: 'generic',
            numberOfQuestions: 10,
            topics: ['Algebra'],
            date: '2024-12-25',
            startTime: '10:00',
            duration: 60,
            totalMarks: 100,
            instructions: 'Test instructions',
          },
        });

        // Mock API responses for ReviewTestPage
        mockAxios.post
          .mockResolvedValueOnce({ data: mockTeacher }) // getDetailTeacher
          .mockResolvedValueOnce({
            data: { recommendations: JSON.stringify(mockQuestions) },
          }) // recommend
          .mockResolvedValueOnce({
            data: { test: { _id: 'test123' } },
          }); // create test

        renderWithProviders(<ReviewTestPage />);

        await waitFor(() => {
          expect(screen.getAllByTestId('question-card')).toHaveLength(2);
        });

        // Keep some questions
        const keepButtons = screen.getAllByTestId('keep-button');
        fireEvent.click(keepButtons[0]);
        fireEvent.click(keepButtons[1]);

        await waitFor(() => {
          expect(screen.getByText(/Schedule Test \(2\)/)).toBeInTheDocument();
        });

        // Schedule test
        const scheduleButton = screen.getByText(/Schedule Test \(2\)/);
        fireEvent.click(scheduleButton);

        // Confirm scheduling
        const confirmButton = screen.getByText(/confirm/i);
        fireEvent.click(confirmButton);

        await waitFor(() => {
          expect(mockAxios.post).toHaveBeenCalledWith('/api/test/create', expect.any(Object));
        });
      });
    });

    describe('Diagnostic Test Creation', () => {
      it('creates diagnostic test directly', async () => {
        mockAxios.post
          .mockResolvedValueOnce({
            data: { recommendations: JSON.stringify(mockQuestions) },
          })
          .mockResolvedValueOnce({
            status: 201,
            data: { test: { _id: 'test123' } },
          });

        renderWithProviders(<ScheduleTestForm />);

        // Fill form for diagnostic test
        fireEvent.change(screen.getByLabelText(/class/i), {
          target: { value: 'class1' },
        });
        fireEvent.change(screen.getByLabelText(/subject/i), {
          target: { value: 'Mathematics' },
        });
        fireEvent.change(screen.getByLabelText(/test type/i), {
          target: { value: 'diagnostic' },
        });

        // Set future date
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        fireEvent.change(screen.getByLabelText(/date/i), {
          target: { value: tomorrow.toISOString().split('T')[0] },
        });

        // Submit form
        const submitButton = screen.getByRole('button', { name: /schedule test/i });
        fireEvent.click(submitButton);

        await waitFor(() => {
          expect(mockAxios.post).toHaveBeenCalledWith('/api/recommend', expect.any(Object));
          expect(mockAxios.post).toHaveBeenCalledWith('/api/test/create', expect.any(Object));
        });
      });
    });
  });

  describe('Complete Student Test Taking Workflow', () => {
    it('completes full student test journey', async () => {
      // Step 1: Student sees test in dashboard
      renderWithProviders(<ViewUpcomingTest />, mockStudent);

      await waitFor(() => {
        expect(screen.getByText('Mathematics')).toBeInTheDocument();
      });

      // Step 2: Navigate to test instructions
      vi.mocked(require('react-router-dom').useLocation).mockReturnValue({
        state: {
          testId: 'test123',
          testMode: 'scheduled',
        },
      });

      const mockTestData = {
        _id: 'test123',
        class: [{ className: 'Class 10A' }],
        subject: 'Mathematics',
        topics: ['Algebra'],
        testDate: new Date('2024-12-25'),
        numberOfQuestions: 2,
        duration: 60,
        totalMarks: 100,
        testInstructions: 'Read carefully',
        startTime: '10:00',
        active: true,
        questions: mockQuestions,
      };

      mockFetchWithCache.mockResolvedValue({ test: mockTestData });

      renderWithProviders(<TestInstructionsPage />, mockStudent);

      await waitFor(() => {
        expect(screen.getByText('Test Instructions')).toBeInTheDocument();
        expect(screen.getByText('Mathematics')).toBeInTheDocument();
      });

      // Step 3: Agree to instructions and start test
      const checkbox = screen.getByRole('checkbox');
      fireEvent.click(checkbox);

      const startButton = screen.getByRole('button', { name: /start test/i });
      fireEvent.click(startButton);

      expect(mockNavigate).toHaveBeenCalledWith('/test', {
        state: {
          testId: 'test123',
          testMode: 'scheduled',
        },
      });

      // Step 4: Take the test
      renderWithProviders(<StudentTestPage />, mockStudent);

      await waitFor(() => {
        expect(screen.getByText('What is 2 + 2?')).toBeInTheDocument();
      });

      // Answer questions
      fireEvent.click(screen.getByLabelText('4'));

      // Navigate to next question
      const nextButton = screen.getByRole('button', { name: /next/i });
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText('What is 3 × 3?')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByLabelText('9'));

      // Submit test
      mockAxios.post.mockResolvedValue({ data: { success: true } });

      const submitButton = screen.getByRole('button', { name: /submit test/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockAxios.post).toHaveBeenCalledWith('/api/test/submit', {
          userId: 'student123',
          testId: 'test123',
          responses: expect.arrayContaining([
            expect.objectContaining({
              questionId: 'q1',
              selectedAnswer: '4',
            }),
            expect.objectContaining({
              questionId: 'q2',
              selectedAnswer: '9',
            }),
          ]),
          startTime: expect.any(String),
          endTime: expect.any(String),
          subject: 'Mathematics',
        });
      });
    });
  });

  describe('Error Handling Across Workflow', () => {
    it('handles API failures gracefully throughout workflow', async () => {
      // Test creation failure
      mockAxios.post.mockRejectedValue(new Error('API Error'));

      renderWithProviders(<ScheduleTestForm />);

      fireEvent.change(screen.getByLabelText(/class/i), {
        target: { value: 'class1' },
      });
      fireEvent.change(screen.getByLabelText(/subject/i), {
        target: { value: 'Mathematics' },
      });
      fireEvent.change(screen.getByLabelText(/test type/i), {
        target: { value: 'diagnostic' },
      });

      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      fireEvent.change(screen.getByLabelText(/date/i), {
        target: { value: tomorrow.toISOString().split('T')[0] },
      });

      const submitButton = screen.getByRole('button', { name: /schedule test/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(require('react-toastify').toast.error).toHaveBeenCalledWith(
          expect.stringContaining('Error scheduling test')
        );
      });
    });

    it('handles test loading failures', async () => {
      vi.mocked(require('react-router-dom').useLocation).mockReturnValue({
        state: {
          testId: 'test123',
          testMode: 'scheduled',
        },
      });

      mockFetchWithCache.mockRejectedValue(new Error('Failed to load test'));

      renderWithProviders(<TestInstructionsPage />, mockStudent);

      await waitFor(() => {
        expect(require('react-toastify').toast.error).toHaveBeenCalledWith(
          'Failed to load test details. Please try again.'
        );
        expect(mockNavigate).toHaveBeenCalledWith('/student-dashboard');
      });
    });

    it('handles test submission failures', async () => {
      const mockTestData = {
        _id: 'test123',
        questions: mockQuestions,
        duration: 60,
        subject: 'Mathematics',
        active: true,
      };

      mockFetchWithCache.mockResolvedValue({ test: mockTestData });
      mockAxios.post.mockRejectedValue(new Error('Submission failed'));

      renderWithProviders(<StudentTestPage />, mockStudent);

      await waitFor(() => {
        expect(screen.getByText('What is 2 + 2?')).toBeInTheDocument();
      });

      // Answer and try to submit
      fireEvent.click(screen.getByLabelText('4'));
      const nextButton = screen.getByRole('button', { name: /next/i });
      fireEvent.click(nextButton);

      await waitFor(() => {
        const submitButton = screen.getByRole('button', { name: /submit test/i });
        fireEvent.click(submitButton);
      });

      await waitFor(() => {
        expect(require('react-toastify').toast.error).toHaveBeenCalledWith(
          expect.stringContaining('Failed to submit test')
        );
      });
    });
  });
});
