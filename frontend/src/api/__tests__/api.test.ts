import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { register, login } from '../../api';
import { createMockApiResponse } from '../../utils/test-utils';

// Mock axios module
vi.mock('../../axios', () => ({
  axiosDefault: vi.fn()
}));

import { axiosDefault } from '../../axios';

describe('API Functions', () => {
  const mockAxiosDefault = axiosDefault as unknown as ReturnType<typeof vi.fn>;
  
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('register', () => {
    it('should make a POST request to the correct endpoint for student registration', async () => {
      // Mock successful response
      mockAxiosDefault.mockResolvedValueOnce(createMockApiResponse({ success: true }));
      
      // Call the register function with student data
      const result = await register('Student', 'testuser', '<EMAIL>', 'password', { grade: 10 });
      
      // Verify axios was called with correct parameters
      expect(mockAxiosDefault).toHaveBeenCalledWith({
        method: 'post',
        url: '/api/auth/registerStudent',
        data: {
          userType: 'Student',
          username: 'testuser',
          email: '<EMAIL>',
          password: 'password',
          grade: 10
        }
      });
      
      // Verify the result
      expect(result).toEqual({ success: true });
    });

    it('should make a POST request to the correct endpoint for teacher registration', async () => {
      // Mock successful response
      mockAxiosDefault.mockResolvedValueOnce(createMockApiResponse({ success: true }));
      
      // Call the register function with teacher data
      const result = await register('Teacher', 'teacheruser', '<EMAIL>', 'password', { subject: 'Math' });
      
      // Verify axios was called with correct parameters
      expect(mockAxiosDefault).toHaveBeenCalledWith({
        method: 'post',
        url: '/api/auth/registerTeacher',
        data: {
          userType: 'Teacher',
          username: 'teacheruser',
          email: '<EMAIL>',
          password: 'password',
          subject: 'Math'
        }
      });
      
      // Verify the result
      expect(result).toEqual({ success: true });
    });

    it('should handle registration errors', async () => {
      // Mock an error response
      const errorResponse = {
        response: {
          data: { message: 'Email already exists' },
          status: 409
        }
      };
      mockAxiosDefault.mockRejectedValueOnce(errorResponse);
      
      // Verify that the error is propagated
      await expect(
        register('Student', 'testuser', '<EMAIL>', 'password')
      ).rejects.toEqual(errorResponse);
    });
  });

  describe('login', () => {
    it('should make a POST request to the correct endpoint for student login', async () => {
      // Mock successful response
      const mockResponse = createMockApiResponse({ token: 'jwt-token', user: { id: '123', username: 'testuser' } });
      mockAxiosDefault.mockResolvedValueOnce(mockResponse);
      
      // Call the login function
      const result = await login('Student', '<EMAIL>', 'password');
      
      // Verify axios was called with correct parameters
      expect(mockAxiosDefault).toHaveBeenCalledWith({
        method: 'post',
        url: '/api/auth/loginStudent',
        data: {
          email: '<EMAIL>',
          password: 'password'
        }
      });
      
      // Verify the result
      expect(result).toEqual(mockResponse.data);
    });

    it('should make a POST request to the correct endpoint for teacher login', async () => {
      // Mock successful response
      const mockResponse = createMockApiResponse({ token: 'teacher-jwt-token', user: { id: '456', username: 'teacher' } });
      mockAxiosDefault.mockResolvedValueOnce(mockResponse);
      
      // Call the login function
      const result = await login('Teacher', '<EMAIL>', 'password');
      
      // Verify axios was called with correct parameters
      expect(mockAxiosDefault).toHaveBeenCalledWith({
        method: 'post',
        url: '/api/auth/loginTeacher',
        data: {
          email: '<EMAIL>',
          password: 'password'
        }
      });
      
      // Verify the result
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle login errors', async () => {
      // Mock an error response
      const errorResponse = {
        response: {
          data: { message: 'Invalid credentials' },
          status: 401
        }
      };
      mockAxiosDefault.mockRejectedValueOnce(errorResponse);
      
      // Verify that the error is propagated
      await expect(
        login('Student', '<EMAIL>', 'wrongpassword')
      ).rejects.toEqual(errorResponse);
    });
  });
}); 