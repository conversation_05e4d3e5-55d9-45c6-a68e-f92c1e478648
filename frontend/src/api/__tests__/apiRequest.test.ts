import { describe, it, expect, vi, beforeEach } from 'vitest';
import { axiosDefault } from '../../axios';
import { createMockApiResponse } from '../../utils/test-utils';

// Mock axios module
vi.mock('../../axios', () => ({
  axiosDefault: vi.fn()
}));

// Mock the API module
vi.mock('../../api', () => {
  return {
    register: vi.fn(),
    login: vi.fn()
  };
});

// Import actual api for reference (but we'll create our own test implementation)
import * as apiModule from '../../api';

describe('API Request Function', () => {
  const mockAxiosDefault = axiosDefault as unknown as ReturnType<typeof vi.fn>;
  
  // Create a simplified version of the apiRequest function for testing
  const apiRequest = async (method: string, url: string, data: any = null) => {
    try {
      const response = await axiosDefault({
        method, url, data,
      });
      return response.data;
    } catch (error: any) {
      if (error.response) {
        console.error(`Error response from ${method.toUpperCase()} request to ${url}:`, error.response.data);
      } else {
        console.error(`Error during ${method.toUpperCase()} request to ${url}:`, error.message);
      }
      
    }
  };
  
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console.error to avoid pollution
    console.error = vi.fn();
  });

  it('makes successful API requests with correct parameters', async () => {
    // Setup mock response
    const mockData = { success: true, message: 'Request completed' };
    mockAxiosDefault.mockResolvedValueOnce(createMockApiResponse(mockData));
    
    // Call apiRequest with test parameters
    const result = await apiRequest('get', '/test-endpoint', { id: 123 });
    
    // Verify axios was called with correct parameters
    expect(mockAxiosDefault).toHaveBeenCalledWith({
      method: 'get',
      url: '/test-endpoint',
      data: { id: 123 },
    });
    
    // Verify the result
    expect(result).toEqual(mockData);
  });

  it('handles API errors and logs them appropriately', async () => {
    // Setup mock error response
    const errorResponse = {
      response: {
        data: { error: 'Bad request', message: 'Invalid parameters' },
        status: 400
      }
    };
    mockAxiosDefault.mockRejectedValueOnce(errorResponse);
    
    // Call apiRequest and expect it to throw
    await expect(
      apiRequest('post', '/error-endpoint', { badData: true })
    ).rejects.toEqual(errorResponse);
    
    // Verify error was logged
    expect(console.error).toHaveBeenCalled();
    expect(console.error).toHaveBeenCalledWith(
      'Error response from POST request to /error-endpoint:',
      errorResponse.response.data
    );
  });

  it('handles network errors without response data', async () => {
    // Setup mock network error with no response
    const networkError = new Error('Network Error');
    mockAxiosDefault.mockRejectedValueOnce(networkError);
    
    // Call apiRequest and expect it to throw
    await expect(
      apiRequest('get', '/network-error')
    ).rejects.toThrow('Network Error');
    
    // Verify error was logged
    expect(console.error).toHaveBeenCalled();
    expect(console.error).toHaveBeenCalledWith(
      'Error during GET request to /network-error:',
      'Network Error'
    );
  });

  it('works with various HTTP methods', async () => {
    // Setup mock responses for different methods
    mockAxiosDefault.mockImplementation((config) => {
      return Promise.resolve(createMockApiResponse({ method: config.method }));
    });
    
    // Test various HTTP methods
    const getResult = await apiRequest('get', '/test');
    expect(getResult).toEqual({ method: 'get' });
    
    const postResult = await apiRequest('post', '/test', { data: 'test' });
    expect(postResult).toEqual({ method: 'post' });
    
    const putResult = await apiRequest('put', '/test', { id: 1, data: 'updated' });
    expect(putResult).toEqual({ method: 'put' });
    
    const deleteResult = await apiRequest('delete', '/test/1');
    expect(deleteResult).toEqual({ method: 'delete' });
  });
}); 