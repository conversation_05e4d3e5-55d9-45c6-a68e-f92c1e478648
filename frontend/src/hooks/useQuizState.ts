import { useState, useEffect } from 'react';

interface QuestionState {
  selectedAnswer: number | null;
  intuition: string;
}

interface QuizState {
  [questionId: string]: QuestionState;
}

export const useQuizState = (totalQuestions: number) => {
  const [quizState, setQuizState] = useState<QuizState>({}); // Initialize as empty object

  useEffect(() => {
    const savedState = sessionStorage.getItem('quizState');
    if (savedState) {
      const parsedState = JSON.parse(savedState);
      setQuizState(parsedState);
    }
  }, []);

  const updateQuestionState = (questionId: string, state: Partial<QuestionState>) => {
    // console.error('updateQuestionState', questionId, state);
    setQuizState(prevState => {
      const newState = {
        ...prevState,
        [questionId]: { 
          ...(prevState[questionId] || { selectedAnswer: null, intuition: '' }), 
          ...state 
        }
      };
      sessionStorage.setItem('quizState', JSON.stringify(newState));
      return newState;
    });
  };

  const getQuestionState = (questionId: string): QuestionState => {
    return quizState[questionId] || { selectedAnswer: null, intuition: '' };
  };


  const resetQuizState = () => {
    const resetState: QuizState = {};
    setQuizState(resetState);
    sessionStorage.removeItem('quizState');
  };

  return {
    updateQuestionState,
    getQuestionState,
    resetQuizState,
  };
};