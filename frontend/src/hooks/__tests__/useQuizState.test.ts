import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useQuizState } from '../useQuizState';

describe('useQuizState', () => {
  // Mock sessionStorage
  const mockSessionStorage = (() => {
    let store: Record<string, string> = {};
    return {
      getItem: vi.fn((key: string) => store[key] || null),
      setItem: vi.fn((key: string, value: string) => {
        store[key] = value;
      }),
      removeItem: vi.fn((key: string) => {
        delete store[key];
      }),
      clear: vi.fn(() => {
        store = {};
      }),
    };
  })();

  // Replace the real sessionStorage with our mock
  beforeEach(() => {
    vi.stubGlobal('sessionStorage', mockSessionStorage);
    mockSessionStorage.clear();
  });

  afterEach(() => {
    vi.unstubAllGlobals();
    vi.clearAllMocks();
  });

  it('should initialize with empty state', () => {
    const { result } = renderHook(() => useQuizState(5));
    
    const questionId = 'q1';
    const state = result.current.getQuestionState(questionId);
    
    expect(state).toEqual({ selectedAnswer: null, intuition: '' });
    expect(mockSessionStorage.getItem).toHaveBeenCalledWith('quizState');
  });

  it('should update question state and save to sessionStorage', () => {
    const { result } = renderHook(() => useQuizState(5));
    
    const questionId = 'q1';
    const newState = { selectedAnswer: 2, intuition: 'My reasoning' };
    
    act(() => {
      result.current.updateQuestionState(questionId, newState);
    });
    
    const state = result.current.getQuestionState(questionId);
    expect(state).toEqual(newState);
    expect(mockSessionStorage.setItem).toHaveBeenCalledWith('quizState', expect.any(String));
    
    // Verify the stored value is correct
    const storedValue = JSON.parse(mockSessionStorage.setItem.mock.calls[0][1]);
    expect(storedValue).toEqual({ [questionId]: newState });
  });

  it('should partially update question state', () => {
    const { result } = renderHook(() => useQuizState(5));
    
    const questionId = 'q1';
    
    act(() => {
      result.current.updateQuestionState(questionId, { selectedAnswer: 2 });
    });
    
    expect(result.current.getQuestionState(questionId)).toEqual({ 
      selectedAnswer: 2, 
      intuition: '' 
    });
    
    act(() => {
      result.current.updateQuestionState(questionId, { intuition: 'New intuition' });
    });
    
    expect(result.current.getQuestionState(questionId)).toEqual({ 
      selectedAnswer: 2, 
      intuition: 'New intuition' 
    });
  });

  it('should reset quiz state and remove from sessionStorage', () => {
    const { result } = renderHook(() => useQuizState(5));
    
    // Set some state first
    act(() => {
      result.current.updateQuestionState('q1', { selectedAnswer: 1 });
      result.current.updateQuestionState('q2', { intuition: 'Test' });
    });
    
    // Verify state was set
    expect(result.current.getQuestionState('q1').selectedAnswer).toBe(1);
    
    // Reset the state
    act(() => {
      result.current.resetQuizState();
    });
    
    // Verify state was reset
    expect(result.current.getQuestionState('q1')).toEqual({ 
      selectedAnswer: null, 
      intuition: '' 
    });
    expect(mockSessionStorage.removeItem).toHaveBeenCalledWith('quizState');
  });

  it('should load saved state from sessionStorage', () => {
    // Manually set session storage with a saved state
    const savedState = {
      q1: { selectedAnswer: 3, intuition: 'Saved intuition' }
    };
    mockSessionStorage.getItem.mockReturnValueOnce(JSON.stringify(savedState));
    
    const { result } = renderHook(() => useQuizState(5));
    
    // Verify the state was loaded
    expect(result.current.getQuestionState('q1')).toEqual(savedState.q1);
  });
}); 