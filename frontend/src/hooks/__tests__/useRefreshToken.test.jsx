import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import useRefreshToken from '../useRefreshToken';

// Mock axios module
vi.mock('../../axios', () => ({
  axiosDefault: vi.fn()
}));

// Import after mocking
import { axiosDefault } from '../../axios';

describe('useRefreshToken Hook', () => {
  const mockAccessToken = 'new-access-token';
  const mockResponse = { data: { accessToken: mockAccessToken } };
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock localStorage for auth data
    Storage.prototype.setItem = vi.fn();
  });

  it('should call refresh endpoint and update token', async () => {
    // Mock successful API response
    axiosDefault.mockResolvedValueOnce(mockResponse);
    
    // Render the hook
    const { result } = renderHook(() => useRefreshToken());
    
    // Call the refresh function
    const refreshResult = await result.current.refresh();
    
    // Verify API call was made with the correct config
    expect(axiosDefault).toHaveBeenCalledWith({
      method: 'get',
      url: '/api/auth/refresh',
      withCredentials: true
    });
    
    // Check if token is correctly returned
    expect(refreshResult).toEqual({ accessToken: mockAccessToken });
    
    // Check if localStorage was updated
    expect(localStorage.setItem).toHaveBeenCalledWith('authToken', mockAccessToken);
  });

  it('should handle refresh token errors', async () => {
    // Mock error response
    const errorMsg = 'Refresh token expired';
    axiosDefault.mockRejectedValueOnce(new Error(errorMsg));
    
    // Render the hook
    const { result } = renderHook(() => useRefreshToken());
    
    // Expect the refresh function to throw an error
    await expect(result.current.refresh()).rejects.toThrow(errorMsg);
  });

  it('should clear token and redirect on 401 error', async () => {
    // Mock 401 unauthorized error response
    const mockError = { 
      response: { status: 401, data: { message: 'Refresh token expired' } }
    };
    axiosDefault.mockRejectedValueOnce(mockError);
    
    // Mock localStorage clear and window.location
    Storage.prototype.removeItem = vi.fn();
    const originalLocation = window.location;
    delete window.location;
    window.location = { href: '' };
    
    // Render the hook
    const { result } = renderHook(() => useRefreshToken());
    
    // Call the refresh function (catching the expected redirect)
    try {
      await result.current.refresh();
    } catch (error) {
      // Expected error
    }
    
    // Check if localStorage token was removed on 401
    expect(localStorage.removeItem).toHaveBeenCalledWith('authToken');
    
    // Restore original location
    window.location = originalLocation;
  });
}); 