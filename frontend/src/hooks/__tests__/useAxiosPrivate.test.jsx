import { describe, it, expect, vi, beforeEach } from 'vitest';
import { renderHook, waitFor } from '@testing-library/react';
import useAxiosPrivate from '../useAxiosPrivate';

// Mock dependencies
vi.mock('../../axios', () => ({
  axiosPrivate: {
    interceptors: {
      request: { use: vi.fn(), eject: vi.fn() },
      response: { use: vi.fn(), eject: vi.fn() }
    }
  }
}));

vi.mock('../useRefreshToken', () => ({
  default: () => ({ refresh: vi.fn().mockResolvedValue({ accessToken: 'new-token' }) })
}));

// Mock axios module
import { axiosPrivate } from '../../axios';

describe('useAxiosPrivate', () => {
  // Mock token and auth state
  const mockAccessToken = 'test-access-token';
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock localStorage for auth data
    Storage.prototype.getItem = vi.fn((key) => {
      if (key === 'authToken') {
        return mockAccessToken;
      }
      return null;
    });
  });

  it('adds interceptors when hook is called', () => {
    // Render the hook
    renderHook(() => useAxiosPrivate());
    
    // Check that request and response interceptors were added
    expect(axiosPrivate.interceptors.request.use).toHaveBeenCalled();
    expect(axiosPrivate.interceptors.response.use).toHaveBeenCalled();
  });

  it('adds Authorization header with current token', () => {
    // Get the request interceptor function from the hook
    renderHook(() => useAxiosPrivate());
    
    // Get the first argument (onFulfilled function) of the first request.use call
    const requestInterceptor = axiosPrivate.interceptors.request.use.mock.calls[0][0];
    
    // Create a test request config
    const requestConfig = { headers: {} };
    
    // Call the interceptor function
    const result = requestInterceptor(requestConfig);
    
    // Verify the token was added to headers
    expect(result.headers['Authorization']).toBe(`Bearer ${mockAccessToken}`);
  });

  it('ejects interceptors on cleanup', () => {
    // Mock interceptor IDs
    axiosPrivate.interceptors.request.use.mockReturnValue(1);
    axiosPrivate.interceptors.response.use.mockReturnValue(2);
    
    // Render the hook with unmount capability
    const { unmount } = renderHook(() => useAxiosPrivate());
    
    // Trigger unmount
    unmount();
    
    // Verify interceptors were ejected
    expect(axiosPrivate.interceptors.request.eject).toHaveBeenCalledWith(1);
    expect(axiosPrivate.interceptors.response.eject).toHaveBeenCalledWith(2);
  });

  it('refreshes token when response has 403 error', async () => {
    renderHook(() => useAxiosPrivate());
    
    // Get the second argument (onRejected function) of the response.use call
    const responseErrorHandler = axiosPrivate.interceptors.response.use.mock.calls[0][1];
    
    // Create a mock error with 403 status
    const mockError = {
      response: { status: 403 },
      config: { 
        headers: {}, 
        sent: true // Request was already sent
      }
    };
    
    // Call the error handler
    await responseErrorHandler(mockError).catch(() => {});
    
    // Check that the error handler attempted to resubmit the request
    expect(axiosPrivate).toHaveBeenCalled();
  });
}); 