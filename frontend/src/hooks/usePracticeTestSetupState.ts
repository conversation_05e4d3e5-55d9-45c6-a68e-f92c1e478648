import { useState, useEffect } from 'react';

interface PracticeTestState {
  selectedClass: string;
  selectedSubject: string;
  numberOfQuestions: string;
  isSetupComplete: boolean;
}

export const usePracticeTestState = () => {
  const [state, setState] = useState<PracticeTestState>(() => {
    const savedState = sessionStorage.getItem('practiceTestState');
    return savedState
      ? JSON.parse(savedState)
      : {
          selectedClass: '',
          selectedSubject: '',
          numberOfQuestions: '10',
          isSetupComplete: false,
        };
  });

  useEffect(() => {
    sessionStorage.setItem('practiceTestState', JSON.stringify(state));
  }, [state]);

  const updateState = (newState: Partial<PracticeTestState>) => {
    setState((prevState) => ({ ...prevState, ...newState }));
  };

  return { state, updateState };
};