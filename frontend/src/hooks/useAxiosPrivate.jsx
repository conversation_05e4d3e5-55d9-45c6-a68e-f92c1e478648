import {axiosPrivate} from '../axios.jsx';
import { useEffect } from 'react';
import { useRefreshToken } from './useRefreshToken.jsx';
import { useUser } from '../contexts/userContext';

export const useAxiosPrivate = () => {
  const refresh = useRefreshToken();
  const { user } = useUser();

  useEffect(() => {
    // console.error(`user is: ${JSON.stringify(user)}`)
    const requestIntercept = axiosPrivate.interceptors.request.use(
      config => {
        if (!config.headers['Authorization']) {
          console.error(`attaching token: ${user?.accessToken}`);
          config.headers['Authorization'] = `Bearer ${user?.accessToken}`;
        } else {
          console.error(`config header is already authorization set to: ${config.headers['Authorization']}`);
        }

        return config;
      }, (error) => Promise.reject(error)
    );
    const responseIntercept = axiosPrivate.interceptors.response.use(
      response => response,
      async(error) => {
        const prevRequest = error?.config;
        console.error(`got error: ${error}`);
        if (error?.response.status === 403 && !prevRequest?.sent) {
          prevRequest.sent = true;
          const newAccessToken = await refresh();
          prevRequest.headers['Authorization'] = `Bearer ${newAccessToken}`;
          return axiosPrivate(prevRequest);
        }
        return Promise.reject(error);
      }
    )

    return () => {
      axiosPrivate.interceptors.request.eject(requestIntercept);
      axiosPrivate.interceptors.response.eject(responseIntercept);
    }
  }, [user, refresh]);

  return axiosPrivate;
};
