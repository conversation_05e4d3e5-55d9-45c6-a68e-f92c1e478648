import { useEffect, useRef, useState } from 'react';

interface SwipeDirection {
  left: boolean;
  right: boolean;
  up: boolean;
  down: boolean;
}

interface TouchPosition {
  x: number;
  y: number;
}

interface UseMobileInteractionsOptions {
  swipeThreshold?: number;
  preventScroll?: boolean;
  enableSwipe?: boolean;
}

export const useMobileInteractions = (options: UseMobileInteractionsOptions = {}) => {
  const {
    swipeThreshold = 50,
    preventScroll = false,
    enableSwipe = true
  } = options;

  const [isMobile, setIsMobile] = useState(false);
  const [isTouch, setIsTouch] = useState(false);
  const [swipeDirection, setSwipeDirection] = useState<SwipeDirection>({
    left: false,
    right: false,
    up: false,
    down: false
  });

  const touchStart = useRef<TouchPosition | null>(null);
  const touchEnd = useRef<TouchPosition | null>(null);

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor;
      const isMobileDevice = /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase());
      const hasTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      
      setIsMobile(isMobileDevice || window.innerWidth <= 768);
      setIsTouch(hasTouch);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Handle touch events for swipe detection
  const handleTouchStart = (e: TouchEvent) => {
    if (!enableSwipe) return;
    
    touchEnd.current = null;
    touchStart.current = {
      x: e.targetTouches[0].clientX,
      y: e.targetTouches[0].clientY
    };

    if (preventScroll) {
      e.preventDefault();
    }
  };

  const handleTouchMove = (e: TouchEvent) => {
    if (!enableSwipe || !touchStart.current) return;

    if (preventScroll) {
      e.preventDefault();
    }
  };

  const handleTouchEnd = (e: TouchEvent) => {
    if (!enableSwipe || !touchStart.current) return;

    touchEnd.current = {
      x: e.changedTouches[0].clientX,
      y: e.changedTouches[0].clientY
    };

    handleSwipe();
  };

  const handleSwipe = () => {
    if (!touchStart.current || !touchEnd.current) return;

    const distanceX = touchStart.current.x - touchEnd.current.x;
    const distanceY = touchStart.current.y - touchEnd.current.y;
    const isLeftSwipe = distanceX > swipeThreshold;
    const isRightSwipe = distanceX < -swipeThreshold;
    const isUpSwipe = distanceY > swipeThreshold;
    const isDownSwipe = distanceY < -swipeThreshold;

    setSwipeDirection({
      left: isLeftSwipe,
      right: isRightSwipe,
      up: isUpSwipe,
      down: isDownSwipe
    });

    // Reset swipe direction after a short delay
    setTimeout(() => {
      setSwipeDirection({
        left: false,
        right: false,
        up: false,
        down: false
      });
    }, 100);
  };

  // Enhanced button press feedback
  const handleButtonPress = (callback: () => void) => {
    return (e: React.MouseEvent | React.TouchEvent) => {
      e.preventDefault();
      
      // Add haptic feedback on supported devices
      if ('vibrate' in navigator && isMobile) {
        navigator.vibrate(10);
      }

      // Add visual feedback
      const target = e.currentTarget as HTMLElement;
      target.style.transform = 'scale(0.98)';
      
      setTimeout(() => {
        target.style.transform = '';
        callback();
      }, 100);
    };
  };

  // Improved focus management for mobile
  const handleMobileFocus = (element: HTMLElement) => {
    if (isMobile && element) {
      // Scroll element into view with better positioning
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      });

      // Ensure proper focus for screen readers
      element.focus({ preventScroll: true });
    }
  };

  // Touch-friendly event handlers
  const touchHandlers = enableSwipe ? {
    onTouchStart: handleTouchStart,
    onTouchMove: handleTouchMove,
    onTouchEnd: handleTouchEnd
  } : {};

  return {
    isMobile,
    isTouch,
    swipeDirection,
    handleButtonPress,
    handleMobileFocus,
    touchHandlers,
    
    // Utility functions
    getResponsiveSize: (mobile: string, desktop: string) => isMobile ? mobile : desktop,
    getResponsiveSpacing: (mobile: number, desktop: number) => isMobile ? mobile : desktop,
    
    // CSS classes for responsive design
    responsiveClasses: {
      container: isMobile ? 'p-3 space-y-3' : 'p-6 space-y-6',
      button: isMobile ? 'min-h-[44px] px-4 py-2 text-sm' : 'px-6 py-3 text-base',
      input: isMobile ? 'min-h-[44px] text-base' : 'h-10 text-sm',
      modal: isMobile ? 'p-4 max-h-[90vh]' : 'p-6 max-h-[80vh]',
      card: isMobile ? 'p-4 rounded-lg' : 'p-6 rounded-xl',
      text: {
        heading: isMobile ? 'text-lg' : 'text-xl',
        body: isMobile ? 'text-sm' : 'text-base',
        caption: isMobile ? 'text-xs' : 'text-sm'
      }
    }
  };
};

// Hook for detecting scroll overflow (useful for swipe indicators)
export const useScrollOverflow = (ref: React.RefObject<HTMLElement>) => {
  const [hasOverflow, setHasOverflow] = useState(false);

  useEffect(() => {
    const checkOverflow = () => {
      if (ref.current) {
        const { scrollWidth, clientWidth } = ref.current;
        setHasOverflow(scrollWidth > clientWidth);
      }
    };

    checkOverflow();
    window.addEventListener('resize', checkOverflow);
    
    if (ref.current) {
      const resizeObserver = new ResizeObserver(checkOverflow);
      resizeObserver.observe(ref.current);
      
      return () => {
        resizeObserver.disconnect();
        window.removeEventListener('resize', checkOverflow);
      };
    }

    return () => window.removeEventListener('resize', checkOverflow);
  }, [ref]);

  return hasOverflow;
};

// Hook for better keyboard handling on mobile
export const useMobileKeyboard = () => {
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [viewportHeight, setViewportHeight] = useState(window.innerHeight);

  useEffect(() => {
    const handleResize = () => {
      const currentHeight = window.innerHeight;
      const heightDifference = viewportHeight - currentHeight;
      
      // Keyboard is likely visible if height decreased significantly
      setKeyboardVisible(heightDifference > 150);
      
      if (heightDifference <= 150) {
        setViewportHeight(currentHeight);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [viewportHeight]);

  return {
    keyboardVisible,
    adjustedHeight: keyboardVisible ? window.innerHeight : viewportHeight
  };
};
