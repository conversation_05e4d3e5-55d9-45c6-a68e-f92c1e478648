import  {useNavigate, useLocation} from 'react-router-dom'
import { useUser } from '../contexts/userContext';
import { useAxiosPrivate } from './useAxiosPrivate';
import { axiosDefault } from '../axios';
import { useEffect, useState } from 'react';


export const usePageRefresh = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const axiosPrivate = useAxiosPrivate();
  const { user, setUser }  = useUser();
  const [initializationState, setInitializationState] = useState('idle'); // 'idle', 'refreshing', 'fetching', 'complete', 'failed'

  useEffect(() => {
    console.log('usePageRefresh useEffect triggered', {
      initializationState,
      hasAccessToken: !!user?.accessToken,
      userEmail: user?.email
    });

    if (typeof window !== 'undefined' && initializationState === 'idle') {
      const initializeUser = async () => {
        try {
          const email = sessionStorage.getItem('email');
          const role = sessionStorage.getItem('role');

          console.log('Initializing user...', {
            email,
            role,
            hasAccessToken: !!user?.accessToken,
            initializationState,
            userObject: user
          });

          // If no email in sessionStorage, user is not logged in
          if (!email || !role) {
            console.log('No email/role in sessionStorage, user not logged in');
            setInitializationState('complete');
            return;
          }

          // Set basic user info from sessionStorage if not already set
          if (!user?.email) {
            console.log('Setting basic user info from sessionStorage');
            setUser({ email, role });
          }

          // If user has access token, fetch user details
          if (user?.accessToken) {
            console.log('User has access token, fetching details...');
            setInitializationState('fetching');
            const response = await axiosPrivate.post(`/api/details/getDetail${role}`, { email });
            const data = response?.data;
            setUser(data);
            setInitializationState('complete');
            return;
          }

          // If user has email but no access token, try to refresh
          console.log('No access token found, attempting refresh...');
          setInitializationState('refreshing');

          try {
            const refreshResponse = await axiosDefault.post('/api/refresh/', { role }, {
              withCredentials: true,
            });

            if (refreshResponse.data?.accessToken) {
              console.log('Token refreshed successfully');
              // Update user context with new token
              setUser(prev => ({
                ...prev,
                email,
                role,
                accessToken: refreshResponse.data.accessToken
              }));

              // Now fetch user details with the new token
              setInitializationState('fetching');
              const detailsResponse = await axiosDefault.post(`/api/details/getDetail${role}`,
                { email },
                {
                  headers: {
                    'Authorization': `Bearer ${refreshResponse.data.accessToken}`,
                    'Content-Type': 'application/json'
                  },
                  withCredentials: true
                }
              );

              const userData = detailsResponse?.data;
              setUser({...userData, accessToken: refreshResponse.data.accessToken});
              setInitializationState('complete');
            } else {
              throw new Error('No access token received from refresh');
            }
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError);
            setInitializationState('failed');
            // Clear any stale session data
            sessionStorage.removeItem('email');
            sessionStorage.removeItem('role');
            setUser(null);
            // Redirect to login
            navigate('/login', { state: { from: location }, replace: true });
          }

        } catch (error) {
          console.error('Error in initializeUser:', error);
          setInitializationState('failed');
          navigate('/login', { state: { from: location }, replace: true });
        }
      };

      initializeUser();
    }

  }, [user?.accessToken, initializationState, navigate, location, axiosPrivate, setUser]);
};
