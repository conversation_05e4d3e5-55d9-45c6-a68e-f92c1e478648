import { useUser } from '../contexts/userContext';
import  { axiosDefault } from '../axios.jsx';

export const useRefreshToken = () => {
  const { setUser } = useUser();

  const refresh = async () => {
    try {
      const role = sessionStorage.getItem('role');
      if (!role) {
        throw new Error('No role found in sessionStorage');
      }

      const response = await axiosDefault.post('/api/refresh/', {role},{
        withCredentials: true,
      });
      setUser({accessToken: response.data.accessToken});

      return response.data.accessToken;
    } catch (error) {
      console.error('Refresh token error:', error);
      // Clear user context on refresh failure
      setUser(null);
      throw error; // Re-throw so calling code can handle it
    }
  }
  return refresh;
}
