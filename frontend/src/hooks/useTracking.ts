import { useCallback, useEffect, useRef } from 'react';
import { useAnalytics } from '../contexts/analyticsContext';

// Custom hook for enhanced tracking capabilities
export const useTracking = () => {
    const analytics = useAnalytics();
    const startTime = useRef<number>(Date.now());
    const interactionCount = useRef<number>(0);

    // Track time spent on current page/component
    const trackTimeSpent = useCallback((feature?: string) => {
        const timeSpent = Date.now() - startTime.current;
        analytics.trackFeatureUsage(feature || 'page_time', 'time_spent', { 
            duration: timeSpent,
            interactions: interactionCount.current 
        });
    }, [analytics]);

    // Enhanced button click tracking with automatic feature detection
    const trackClick = useCallback((
        element: HTMLElement | string, 
        customMetadata?: any,
        feature?: string
    ) => {
        let elementId: string;
        let elementText: string;
        let elementClass: string;

        if (typeof element === 'string') {
            elementId = element;
            elementText = element;
            elementClass = '';
        } else {
            elementId = element.id || element.className || 'unknown';
            elementText = element.textContent || element.getAttribute('aria-label') || '';
            elementClass = element.className;
        }

        analytics.trackButtonClick(elementId, elementText, feature, {
            elementClass,
            ...customMetadata
        });

        interactionCount.current++;
    }, [analytics]);

    // Track form interactions with validation
    const trackForm = useCallback((
        formId: string, 
        action: 'start' | 'submit' | 'error' | 'abandon',
        formData?: any,
        feature?: string
    ) => {
        if (action === 'submit') {
            analytics.trackFormSubmit(formId, formData, feature);
        } else {
            analytics.trackFeatureUsage(feature || 'form', action, {
                formId,
                formData: action === 'error' ? formData : undefined
            });
        }

        interactionCount.current++;
    }, [analytics]);

    // Track test-specific interactions
    const trackTestAction = useCallback((
        action: 'start' | 'submit' | 'pause' | 'resume' | 'question_answered' | 'time_warning',
        testId?: string,
        questionId?: string,
        metadata?: any
    ) => {
        analytics.trackFeatureUsage('test_taking', action, {
            testId,
            questionId,
            timestamp: new Date().toISOString(),
            ...metadata
        });

        interactionCount.current++;
    }, [analytics]);

    // Track chat interactions for AegisAI
    const trackChatAction = useCallback((
        action: 'message_sent' | 'message_received' | 'conversation_started' | 'conversation_ended' | 'subject_switched',
        conversationId?: string,
        metadata?: any
    ) => {
        analytics.trackFeatureUsage('aegis_ai', action, {
            conversationId,
            timestamp: new Date().toISOString(),
            ...metadata
        });

        interactionCount.current++;
    }, [analytics]);

    // Track file operations
    const trackFileAction = useCallback((
        action: 'upload' | 'download' | 'export' | 'import',
        fileName?: string,
        fileType?: string,
        fileSize?: number,
        feature?: string
    ) => {
        analytics.trackFeatureUsage(feature || 'file_operation', action, {
            fileName,
            fileType,
            fileSize,
            timestamp: new Date().toISOString()
        });

        interactionCount.current++;
    }, [analytics]);

    // Track performance metrics
    const trackPerformance = useCallback((
        metric: 'load_time' | 'response_time' | 'error_rate',
        value: number,
        feature?: string,
        metadata?: any
    ) => {
        analytics.trackFeatureUsage(feature || 'performance', metric, {
            value,
            timestamp: new Date().toISOString(),
            ...metadata
        });
    }, [analytics]);

    // Track user engagement patterns
    const trackEngagement = useCallback((
        action: 'scroll' | 'hover' | 'focus' | 'blur' | 'resize',
        element?: string,
        metadata?: any
    ) => {
        analytics.trackFeatureUsage('engagement', action, {
            element,
            timestamp: new Date().toISOString(),
            ...metadata
        });
    }, [analytics]);

    // Auto-track scroll behavior (throttled)
    const trackScrollBehavior = useCallback(() => {
        let scrollTimeout: NodeJS.Timeout;
        let lastScrollTop = 0;
        let scrollDirection = 'down';

        const handleScroll = () => {
            clearTimeout(scrollTimeout);
            
            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
            scrollDirection = currentScrollTop > lastScrollTop ? 'down' : 'up';
            lastScrollTop = currentScrollTop;

            scrollTimeout = setTimeout(() => {
                const scrollPercentage = Math.round(
                    (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
                );

                trackEngagement('scroll', 'page', {
                    scrollPercentage,
                    scrollDirection,
                    scrollTop: currentScrollTop
                });
            }, 1000); // Throttle to 1 second
        };

        window.addEventListener('scroll', handleScroll, { passive: true });
        
        return () => {
            window.removeEventListener('scroll', handleScroll);
            clearTimeout(scrollTimeout);
        };
    }, [trackEngagement]);

    // Reset tracking state (useful for component unmounting)
    const resetTracking = useCallback(() => {
        startTime.current = Date.now();
        interactionCount.current = 0;
    }, []);

    // Track component lifecycle
    useEffect(() => {
        startTime.current = Date.now();
        interactionCount.current = 0;

        return () => {
            // Track time spent when component unmounts
            trackTimeSpent();
        };
    }, [trackTimeSpent]);

    return {
        // Basic tracking methods
        trackClick,
        trackForm,
        trackTimeSpent,
        
        // Feature-specific tracking
        trackTestAction,
        trackChatAction,
        trackFileAction,
        
        // Performance and engagement
        trackPerformance,
        trackEngagement,
        trackScrollBehavior,
        
        // Utility methods
        resetTracking,
        
        // Direct access to analytics context
        ...analytics
    };
};

// Hook for tracking specific UI components
export const useComponentTracking = (componentName: string, feature?: string) => {
    const tracking = useTracking();
    const mountTime = useRef<number>(Date.now());
    const interactionCount = useRef<number>(0);

    const trackComponentInteraction = useCallback((
        action: string,
        metadata?: any
    ) => {
        tracking.trackFeatureUsage(feature || 'component', action, {
            component: componentName,
            mountTime: mountTime.current,
            interactionCount: interactionCount.current++,
            ...metadata
        });
    }, [tracking, componentName, feature]);

    const trackComponentMount = useCallback(() => {
        trackComponentInteraction('mount');
    }, [trackComponentInteraction]);

    const trackComponentUnmount = useCallback(() => {
        const timeSpent = Date.now() - mountTime.current;
        trackComponentInteraction('unmount', {
            timeSpent,
            totalInteractions: interactionCount.current
        });
    }, [trackComponentInteraction]);

    useEffect(() => {
        trackComponentMount();
        return trackComponentUnmount;
    }, [trackComponentMount, trackComponentUnmount]);

    return {
        trackComponentInteraction,
        ...tracking
    };
};

// Hook for tracking form behavior
export const useFormTracking = (formId: string, feature?: string) => {
    const tracking = useTracking();
    const formStartTime = useRef<number | null>(null);
    const fieldInteractions = useRef<Record<string, number>>({});

    const trackFormStart = useCallback(() => {
        formStartTime.current = Date.now();
        tracking.trackForm(formId, 'start', undefined, feature);
    }, [tracking, formId, feature]);

    const trackFieldInteraction = useCallback((fieldName: string) => {
        fieldInteractions.current[fieldName] = (fieldInteractions.current[fieldName] || 0) + 1;
        tracking.trackFeatureUsage(feature || 'form', 'field_interaction', {
            formId,
            fieldName,
            interactionCount: fieldInteractions.current[fieldName]
        });
    }, [tracking, formId, feature]);

    const trackFormSubmit = useCallback((formData?: any) => {
        const timeSpent = formStartTime.current ? Date.now() - formStartTime.current : 0;
        tracking.trackForm(formId, 'submit', {
            ...formData,
            timeSpent,
            fieldInteractions: fieldInteractions.current
        }, feature);
    }, [tracking, formId, feature]);

    const trackFormError = useCallback((error: string, fieldName?: string) => {
        tracking.trackForm(formId, 'error', {
            error,
            fieldName,
            fieldInteractions: fieldInteractions.current
        }, feature);
    }, [tracking, formId, feature]);

    const trackFormAbandon = useCallback(() => {
        const timeSpent = formStartTime.current ? Date.now() - formStartTime.current : 0;
        tracking.trackForm(formId, 'abandon', {
            timeSpent,
            fieldInteractions: fieldInteractions.current
        }, feature);
    }, [tracking, formId, feature]);

    return {
        trackFormStart,
        trackFieldInteraction,
        trackFormSubmit: trackFormSubmit,
        trackFormError,
        trackFormAbandon,
        trackClick: tracking.trackClick,
        trackFeatureUsage: tracking.trackFeatureUsage,
        trackTimeSpent: tracking.trackTimeSpent,
        trackTestAction: tracking.trackTestAction,
        trackChatAction: tracking.trackChatAction,
        trackFileAction: tracking.trackFileAction,
        trackPerformance: tracking.trackPerformance,
        trackEngagement: tracking.trackEngagement,
        trackScrollBehavior: tracking.trackScrollBehavior,
        resetTracking: tracking.resetTracking,
        setAnalyticsEnabled: tracking.setAnalyticsEnabled,
        isEnabled: tracking.isEnabled
    };
};
