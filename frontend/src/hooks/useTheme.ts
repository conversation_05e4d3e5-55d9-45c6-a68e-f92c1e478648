import { useTheme as useThemeContext } from '../contexts/themeContext';

// Re-export the theme hook for easier imports
export const useTheme = useThemeContext;

export const getThemeDisplayName = (theme: string) => {
  switch (theme) {
    case 'light':
      return 'Light Mode';
    case 'dark':
      return 'Dark Mode';
    case 'system':
      return 'System Theme';
    default:
      return 'Light Mode';
  }
};
