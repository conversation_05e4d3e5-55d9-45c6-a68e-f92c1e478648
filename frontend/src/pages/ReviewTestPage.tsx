import React, { useState, useEffect } from 'react';
import { PlusIcon, TrashIcon, BookOpenIcon, ListBulletIcon, ExclamationCircleIcon, CalendarIcon, XMarkIcon } from '@heroicons/react/24/outline';
import QuestionCard from '../components/QuestionCard';
import QuestionCreator from '../components/QuestionCreator';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { useUser } from '../contexts/userContext';
import { renderLatexContent } from '../components/RenderLatexContent';
import { toast } from "react-toastify";
import { createPortal } from 'react-dom';
import { usePageRefresh } from '../hooks/usePageRefresh';


interface Question {
    _id: string;
    answer: string;
    assignedDiff: string;
    difficulty: number;
    discrimination_parameter: number;
    images?: string[];
    options?: string[];
    question: string;
    solution?: string;
    subtopic: string[];
    topic: string[];
}

const SelectedQuestions: React.FC<{
    question: Question;
    onDelete: () => void;
}> = ({ question, onDelete }) => {
    return (
        <div className="group bg-card rounded-lg border border-border p-3 sm:p-4 hover:border-blue-200 transition-colors">
            <div className="flex items-start gap-2 sm:gap-3">
                <div className="shrink-0">
                    <BookOpenIcon width={16} height={16} className="text-accent sm:w-[18px] sm:h-[18px]" />
                </div>
                <div className="flex-1 min-w-0">
                    <div className="text-xs text-muted-foreground mb-1">
                        {question.topic} › {question.subtopic}
                    </div>
                    <p className="text-xs sm:text-sm text-card-foreground line-clamp-2">{renderLatexContent(question.question)}</p>
                    <div className="flex gap-2 mt-1">
                        <span className="text-xs text-muted-foreground">{question.options?.length} options</span>
                        <span className="text-xs text-muted-foreground">•</span>
                        <span className="text-xs text-muted-foreground">Difficulty: {question.difficulty * 10}</span>
                    </div>
                </div>
                <button
                    onClick={onDelete}
                    className="opacity-50 sm:opacity-100 group-hover:opacity-100 p-1 hover:bg-muted rounded transition-colors"
                >
                    <TrashIcon width={14} height={14} className="text-muted-foreground group-hover:text-danger sm:w-4 sm:h-4 transition-colors" />
                </button>
            </div>
        </div>
    );
};

const ReviewTestPage: React.FC = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const scheduleForm = location.state as any;
    const axiosPrivate = useAxiosPrivate();
    const { user } = useUser();
    const [showCreator, setShowCreator] = useState(false);
    const [selectedQuestions, setSelectedQuestions] = useState<Question[]>([]);
    const [questions, setQuestions] = useState<Question[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [showSidebar, setShowSidebar] = useState(false);
    const [showConfirmationModal, setShowConfirmationModal] = useState(false);
    const [isScheduling, setIsScheduling] = useState(false);

    useEffect(() => {
        const fetchQuestions = async () => {
            try {
                setLoading(true);
                console.log("initiating the recommend api call\n");

                const recommendedQuestions = await axiosPrivate.post('/api/recommend', {
                    numQuestions: scheduleForm.numberOfQuestions,
                    topics: scheduleForm.topics,
                    classId: scheduleForm.class,
                    testType: scheduleForm.testType,
                    subject: scheduleForm.subject,
                    userid: user?.id
                });
                console.error(recommendedQuestions.data['recommendations']);
                const parsedQuestions = JSON.parse(recommendedQuestions.data['recommendations']);
                setQuestions(parsedQuestions);
                setError(null);
            } catch (error) {
                console.error('Error fetching questions:', error);
                setError('Failed to load questions. Please try again.');
            } finally {
                setLoading(false);
            }
        };
        // setSelectedQuestions([]);
        fetchQuestions();
    }, []);

    const handleKeep = (question: Question) => {
        setSelectedQuestions(prev => [...prev, question]);
        setQuestions(prev => prev.filter(q => q._id !== question._id));
    };

    const handleDiscard = (question: Question) => {
        setQuestions(prev => prev.filter(q => q._id !== question._id));
    };

    const handleDelete = (question: Question) => {
        setSelectedQuestions(prev => prev.filter(q => q._id !== question._id));
        setQuestions(prev => [...prev, question]);
    };

    const handleAddNewQuestion = async (newQuestion: Omit<Question, '_id'>) => {
        const addQuestionToast = toast.loading('Creating new question...', { autoClose: false });
        try {
            // Assuming you have an API endpoint to create a new question
            const modifiedQuestion = {
                ...newQuestion,
                topic: newQuestion.topic.join(', '),  // Convert array to string
                subtopic: newQuestion.subtopic.join(', ')  // Convert array to string
            };

            const response = await axiosPrivate.post('/api/questionBank/create', { questionData: modifiedQuestion });
            // console.error("newQuestion", response.data);
            const createdQuestion = response.data.question;
            setSelectedQuestions(prev => [...prev, createdQuestion]);
            setShowCreator(false);
            toast.update(addQuestionToast, {
                render: 'Question created successfully!',
                type: 'success',
                isLoading: false,
                autoClose: 2000
            });
        } catch (error) {
            console.error('Error creating question:', error);
            // alert('Failed to create question. Please try again.');
            toast.update(addQuestionToast, {
                render: 'Failed to create question!',
                type: 'error',
                isLoading: false,
                autoClose: 2000
            });
        }
    };

    const handleScheduleTestClick = () => {
        setShowConfirmationModal(true);
    };

    const handleScheduleTest = async () => {
        if (selectedQuestions.length === 0) {
            alert('Please select at least one question before scheduling the test.');
            setShowConfirmationModal(false);
            return;
        }

        setIsScheduling(true);
        const scheduleTestToast = toast.loading('Scheduling test...', { autoClose: false });
        try {
            const testData = {
                ...scheduleForm,
                numberOfQuestions: selectedQuestions.length,
                questions: selectedQuestions.map(q => q._id),
                teacherId: user?.id
            };
            // only come here for generic tests
            const response = await axiosPrivate.post('/api/test/create', { testData });
            if (response.data.test) {
                toast.update(scheduleTestToast, {
                    render: 'Test scheduled successfully!',
                    type: 'success',
                    isLoading: false,
                    autoClose: 2000
                });
                setShowConfirmationModal(false);
                setTimeout(() => navigate(-1), 2000);
            }
        } catch (error) {
            console.error('Error scheduling test:', error);
            toast.update(scheduleTestToast, {
                render: 'Failed to schedule test!',
                type: 'error',
                isLoading: false,
                autoClose: 2000
            });
            // Don't close modal on error, let user try again or cancel
        } finally {
            setIsScheduling(false);
        }
    };

    return (
        <div className="min-h-screen">
            {/* Main content */}
            <div className="md:mr-80 min-h-screen relative pb-8 sm:pb-0">
                {/* Header */}
                <div className="max-w-2xl mx-auto px-4 sm:px-6 pt-4 sm:pt-6">
                    <div className="flex flex-col sm:flex-row sm:items-center p-4 sm:p-6 border-b bg-card border border-border rounded-lg w-full justify-between shadow-lg gap-4 sm:gap-0">
                        <div className="flex-1">
                            <h1 className="text-xl sm:text-2xl font-['Space_Grotesk'] font-semibold text-primary">Review Test Questions</h1>
                            <p className="text-sm text-muted-foreground mt-1">
                                {(scheduleForm.testType).toUpperCase()} • {scheduleForm.numberOfQuestions} questions
                            </p>
                        </div>
                        <div className="flex gap-2 sm:flex-none">
                            <button
                                onClick={() => setShowCreator(true)}
                                className="flex items-center gap-2 bg-accent text-white px-3 sm:px-4 py-2 rounded-lg hover:bg-accent-dark transition-colors shadow-sm text-sm sm:text-base flex-1 sm:flex-none justify-center"
                            >
                                <PlusIcon width={18} height={18} />
                                <span className="whitespace-nowrap hover:cursor-pointer">Create Question</span>
                            </button>
                            <button
                                onClick={() => setShowSidebar(true)}
                                className="md:hidden flex items-center gap-2 bg-muted text-muted-foreground px-3 py-2 rounded-lg hover:bg-muted/80 transition-colors shadow-sm"
                            >
                                <ListBulletIcon width={18} height={18} />
                            </button>
                        </div>
                    </div>
                </div>

                {/* Cards Container */}
                <div className="flex items-center justify-center px-4 sm:px-6 py-8">
                    <div className="relative w-full max-w-2xl min-h-[400px] sm:min-h-[500px]">
                        {loading ? (
                            <div className="absolute inset-0 flex flex-col items-center justify-center">
                                <div className="animate-spin rounded-full h-10 w-10 sm:h-12 sm:w-12 border-b-2 border-accent mb-4"></div>
                                <p className="text-muted-foreground">Loading questions...</p>
                            </div>
                        ) : error ? (
                            <div className="absolute inset-0 flex flex-col items-center justify-center">
                                <ExclamationCircleIcon width={28} height={28} className="mb-4 text-danger" />
                                <p className="text-danger">{error}</p>
                            </div>
                        ) : questions.length === 0 ? (
                            <div className="absolute inset-0 flex flex-col items-center justify-center">
                                <ExclamationCircleIcon width={28} height={28} className="mb-4 text-muted-foreground" />
                                <p className="text-muted-foreground">No more questions to review</p>
                            </div>
                        ) : (
                            <div className="relative h-[400px] sm:h-[500px] backdrop-blur-3xl">
                                {questions.map((question, index) => (
                                    <QuestionCard
                                        key={question._id}
                                        question={question}
                                        onKeep={() => handleKeep(question)}
                                        onDiscard={() => handleDiscard(question)}
                                        isTopCard={index === 0}
                                        images={(path: string) => path}
                                        style={{
                                            position: 'absolute',
                                            top: `${index * 12}px`,
                                            left: 0,
                                            right: 0,
                                            zIndex: questions.length - index,
                                        }}
                                        className={index === 0 ? "backdrop-blur-3xl shadow-2xl" : ""}
                                    />
                                ))}
                            </div>
                        )}
                    </div>
                </div>


            </div>

            {/* Mobile Schedule Button */}
            <div className="md:hidden max-w-sm mt-12 mx-auto z-50 bottom-24 bg-card border rounded-lg border-border shadow-lg sticky">
                <button
                    onClick={handleScheduleTestClick}
                    disabled={selectedQuestions.length === 0 || isScheduling}
                    className={`w-full py-3 px-4 rounded-lg hover:cursor-pointer flex items-center justify-center gap-2 text-sm
        ${selectedQuestions.length === 0 || isScheduling
                            ? 'bg-muted text-muted-foreground cursor-not-allowed'
                            : 'bg-accent text-white hover:bg-accent-dark'}
        transition-colors`}
                >
                    <CalendarIcon width={18} height={18} />
                    {isScheduling ? 'Scheduling...' : `Schedule Test (${selectedQuestions.length})`}
                </button>
            </div>
            {/* Sidebar Overlay */}
            <div
                className={`fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden transition-opacity duration-300 
                    ${showSidebar ? 'opacity-100' : 'opacity-0 pointer-events-none'}`}
                onClick={() => setShowSidebar(false)}
            />

            {/* Right Sidebar */}
            <div
                className={`fixed right-0 top-0 w-full sm:w-[320px] md:w-80 h-screen border-l border-border
                    bg-card transform transition-transform duration-300 ease-in-out z-50
                    ${showSidebar ? 'translate-x-0' : 'translate-x-full md:translate-x-0'}`}
            >
                <div className="flex items-center justify-between p-4 border-b border-border">
                    <div className="flex items-center gap-2">
                        <ListBulletIcon width={20} height={20} className="text-accent" />
                        <h2 className="text-lg font-['Space_Grotesk'] font-medium text-primary">Selected Questions</h2>
                        <span className="ml-2 bg-accent-light text-accent px-2 py-0.5 rounded text-sm font-medium">
                            {selectedQuestions.length}
                        </span>
                    </div>
                    <button
                        onClick={() => setShowSidebar(false)}
                        className="md:hidden p-2 hover:bg-muted rounded-lg"
                    >
                        <XMarkIcon width={20} height={20} className="text-muted-foreground" />
                    </button>
                </div>

                <div className="overflow-y-auto h-[calc(100vh-145px)]">
                    <div className="p-4 space-y-3">
                        {selectedQuestions.length === 0 ? (
                            <div className="flex flex-col items-center justify-center text-center p-6 text-muted-foreground">
                                <ExclamationCircleIcon width={24} height={24} className="mb-2" />
                                <p className="text-sm">No questions selected yet</p>
                                <p className="text-xs mt-1">Keep questions to see them here</p>
                            </div>
                        ) : (
                            selectedQuestions.map((question) => (
                                <SelectedQuestions
                                    key={question._id}
                                    question={question}
                                    onDelete={() => handleDelete(question)}
                                />
                            ))
                        )}
                    </div>
                </div>

                {/* Schedule Test Button */}
                <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-border bg-card">
                    <button
                        onClick={handleScheduleTestClick}
                        disabled={selectedQuestions.length === 0 || isScheduling}
                        className={`w-full py-2 sm:py-3 px-4 rounded-lg flex items-center hover:cursor-pointer justify-center gap-2 text-sm sm:text-base
        ${selectedQuestions.length === 0 || isScheduling
                                ? 'bg-muted text-muted-foreground cursor-not-allowed'
                                : 'bg-accent text-white hover:bg-accent-dark'}
        transition-colors`}
                    >
                        <CalendarIcon width={18} height={18} className="sm:w-5 sm:h-5" />
                        {isScheduling ? 'Scheduling...' : `Schedule Test (${selectedQuestions.length})`}
                    </button>
                </div>
            </div>

            {showCreator && (
                <QuestionCreator
                    onClose={() => setShowCreator(false)}
                    onAdd={handleAddNewQuestion}
                    images={(path: string) => path}
                    className={scheduleForm.class}
                    subject={scheduleForm.subject}
                />
            )}
            {showConfirmationModal && createPortal(
                <div className="fixed inset-0 backdrop-blur-sm bg-opacity-50 z-50 flex items-center justify-center p-4">
                    <div className="bg-card border border-border shadow-lg rounded-lg w-full max-w-md p-6">
                        <div className="flex items-center gap-3 mb-4">
                            <CalendarIcon width={24} height={24} className="text-accent" />
                            <h3 className="text-xl font-semibold text-primary">Confirm Test Schedule</h3>
                        </div>
                        <p className="text-muted-foreground mb-6">
                            You are about to schedule a test with {selectedQuestions.length} questions. Would you like to proceed?
                        </p>
                        <div className="flex justify-end gap-3">
                            <button
                                onClick={() => setShowConfirmationModal(false)}
                                disabled={isScheduling}
                                className="px-4 py-2 text-muted-foreground hover:cursor-pointer hover:bg-muted rounded-lg transition-colors disabled:text-muted-foreground disabled:cursor-not-allowed disabled:hover:bg-transparent"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleScheduleTest}
                                disabled={isScheduling}
                                className="px-4 py-2 bg-accent text-white hover:cursor-pointer rounded-lg hover:bg-accent-dark transition-colors disabled:bg-muted disabled:text-muted-foreground disabled:cursor-not-allowed"
                            >
                                {isScheduling ? 'Scheduling...' : 'Schedule Test'}
                            </button>
                        </div>
                    </div>
                </div>,
                document.body
            )}

        </div>

    );
};

export default ReviewTestPage;
