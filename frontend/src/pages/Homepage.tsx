import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  Navbar,
  AegisGraderHero,
  AegisGraderFeatures,
  AegisGraderWorkflow,
  FinalCTA,
  Footer
} from '../components/homepage-sections';

const Home = () => {
  const [menuOpen, setMenuOpen] = useState(false);

  const setRegisterModalOpen = () => {};
  const setLoginModalOpen = () => {};

  // State for scroll-based scaling and navbar shrinking
  const [scrollY, setScrollY] = useState(0);
  const [isScrolled, setIsScrolled] = useState(false);

  // Scroll event handler
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;
      setScrollY(currentScrollY);
      setIsScrolled(currentScrollY > 50); // Shrink navbar after 50px scroll
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ ease: 'easeIn', duration: 0.6 }}
      className="min-h-screen text-foreground bg-gradient-to-b from-background to-secondary/10 w-full overflow-x-hidden"
    >
      {/* Navbar */}
      <Navbar
        setRegisterModalOpen={setRegisterModalOpen}
        setLoginModalOpen={setLoginModalOpen}
        isScrolled={isScrolled}
        menuOpen={menuOpen}
        setMenuOpen={setMenuOpen}
        scrollY={scrollY}
      />

      {/* Main Content */}
      <main className="flex-grow overflow-hidden pt-10">
        {/* AegisGrader Hero Section */}
        <AegisGraderHero
          setRegisterModalOpen={setRegisterModalOpen}
          setLoginModalOpen={setLoginModalOpen}
        />

        {/* AegisGrader Features Section */}
        <AegisGraderFeatures />

        {/* AegisGrader Workflow Section */}
        <AegisGraderWorkflow />

        {/* Final CTA Section */}
        <FinalCTA
          setRegisterModalOpen={setRegisterModalOpen}
          setLoginModalOpen={setLoginModalOpen}
        />
      </main>

      {/* Footer */}
      <Footer
        setRegisterModalOpen={setRegisterModalOpen}
        setLoginModalOpen={setLoginModalOpen}
      />


    </motion.div>
  );
};

export default Home;