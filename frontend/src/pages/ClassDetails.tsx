import React, { useState } from 'react';
import OnboardingTour, { TourStep } from '@/components/OnboardingTour';
// Onboarding tour steps for ClassDetails page
const steps: TourStep[] = [
  {
    target: '.tour-back-button',
    title: 'Navigation',
    content: 'Click here to return to your dashboard or previous page.',
    placement: 'bottom',
    disableBeacon: true,
  },
  {
    target: '.tour-tab-navigation',
    title: 'View Options',
    content: 'Switch between viewing your students list and class analytics with these tabs.',
    placement: 'bottom',
  },
  {
    target: '.tour-class-header',
    title: 'Class Information',
    content: 'This header shows your class name, subject, and the total number of enrolled students.',
    placement: 'bottom',
  },
  {
    target: '.tour-student-search',
    title: 'Search Students',
    content: 'Quickly find specific students by typing their name in this search box.',
    placement: 'bottom',
  },
  {
    target: '.tour-student-table-header',
    title: 'Sort Student List',
    content: 'Click on these headers to sort students by name or proficiency level.',
    placement: 'bottom',
  },
  {
    target: '.tour-student-list',
    title: 'Student Profiles',
    content: 'Click on any student to view their detailed performance analytics and learning progress.',
    placement: 'bottom',
  },
  {
    target: '.tour-class-analytics',
    title: 'Class Analytics',
    content: 'Switch to this tab to see overall class performance metrics, trends, and distribution charts.',
    placement: 'top',
  }
];
import { useNavigate, useParams } from 'react-router-dom';
import { useUser } from '../contexts/userContext';
import { Student } from '../types/student';
import ClassAnalytics from '@/components/classAnalytics';
import StudentAnalyticsModal from '@/components/StudentAnalyticsModal';
import { 
    UsersIcon, 
    BookOpenIcon, 
    ArrowLeftIcon, 
    MagnifyingGlassIcon, 
    ArrowDownIcon, 
    ArrowUpIcon 
} from '@heroicons/react/24/outline';

const ClassDetails = () => {
    const { classId } = useParams<{ classId: string }>();
    const { user } = useUser();
    const navigate = useNavigate();
    const [searchTerm, setSearchTerm] = useState('');
    const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
    const [sortBy, setSortBy] = useState<'name'>('name');
    const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);
    const [activeTab, setActiveTab] = useState<'students' | 'analytics'>('students');

    // Find the current class
    const currentClass = user?.classes?.find(c => c._id === classId);

    // If students is actually an object and not an array, you'll need to convert it first
    const allStudents: Student[] = (currentClass?.students as Student[] | undefined)?.map((student) => student) || [];

    const handleBack = () => {
        navigate(-1);
    };

    const handleStudentClick = (student: Student) => {
        setSelectedStudent(student);
    };

    const closeModal = () => {
        setSelectedStudent(null);
    };

    const getSubjectColor = (subject: string) => {
        switch (subject) {
            case "mathematics":
                return "bg-[hsl(var(--subject-math)/0.2)] text-[hsl(var(--subject-math))]";
            case "Science":
                return "bg-[hsl(var(--subject-science)/0.2)] text-[hsl(var(--subject-science))]";
            case "English":
                return "bg-[hsl(var(--subject-english)/0.2)] text-[hsl(var(--subject-english))]";
            case "History":
                return "bg-[hsl(var(--subject-history)/0.2)] text-[hsl(var(--subject-history))]";
            default:
                return "bg-[hsl(var(--muted-foreground)/0.2)] text-[hsl(var(--muted-foreground))]";
        }
    };

    const handleSort = (column: 'name') => {
        if (sortBy === column) {
            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
        } else {
            setSortBy(column);
            setSortOrder('asc');
        }
    };

    // Filter and sort students
    const filteredAndSortedStudents = allStudents
        .filter(student => {
            const fullName = `${student.firstName} ${student.lastName}`.toLowerCase();
            const searchLower = searchTerm.toLowerCase();
            return fullName.includes(searchLower) ||
                   student.username.toLowerCase().includes(searchLower);
        })
        .sort((a, b) => {
            const modifier = sortOrder === 'asc' ? 1 : -1;
            const aFullName = `${a.firstName} ${a.lastName}`;
            const bFullName = `${b.firstName} ${b.lastName}`;
            return modifier * aFullName.localeCompare(bFullName);
        });
        
    // Custom formatter for tooltips
    const percentFormatter = (value: any) => `${value.toFixed(1)}%`;

    return (
        <div className="h-screen flex flex-col p-4 mb-12 pb-16 md:pb-4 overflow-auto">
            {user?.id && (<OnboardingTour
                steps={steps}
                user={user && user.id && typeof user.role === 'string' ? {
                    ...user,
                    id: String(user.id),
                    role: user.role
                } : null}
                tourKey="classdetails-tour"
                onTourComplete={(completed, skipped) => {
                    console.log('ClassDetails tour completed:', {completed, skipped});
                }}
                continuous={true}
                startDelay={500}
                spotlightPadding={8}
                disableScrolling={false}
            />)}
            {/* Back and Tab Navigation Row */}
            <div className="flex items-center justify-between mb-6">
                <button
                    onClick={handleBack}
                    className="flex items-center gap-1 text-muted-foreground hover:text-accent transition-colors bg-card rounded-full py-1 px-3 shadow-sm border border-border tour-back-button"
                >
                    <ArrowLeftIcon className="w-4 h-4" />
                    <span className="text-sm">Back</span>
                </button>
                
                <div className="flex gap-2 tour-tab-navigation">
                    <button 
                        onClick={() => setActiveTab('students')}
                        className={`px-3 py-1 text-sm rounded-full transition-colors ${
                            activeTab === 'students' 
                            ? 'bg-accent text-white font-medium' 
                            : 'bg-card text-muted-foreground hover:bg-secondary-100'
                        }`}
                    >
                        Students
                    </button>
                    <button
                        onClick={() => setActiveTab('analytics')}
                        className={`px-3 py-1 text-sm rounded-full transition-colors ${
                            activeTab === 'analytics' 
                            ? 'bg-accent text-white font-medium' 
                            : 'bg-card text-muted-foreground hover:bg-secondary-100'
                        } tour-class-analytics`}
                    >
                        Analytics
                    </button>
                </div>
            </div>
        
            {/* Simplified Class Header */}
            {currentClass && (
                <div className="mb-6 bg-card rounded-lg shadow-sm border border-border overflow-hidden sticky top-0 tour-class-header">
                    <div className={`p-4 ${getSubjectColor(currentClass.subject)?.split(" ")[0] || 'bg-accent'}`}>
                        <h1 className="text-xl font-semibold font-['Space_Grotesk'] mb-2 text-primary">{currentClass.className}</h1>
                        <div className="flex gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                                <BookOpenIcon className="w-4 h-4" />
                                <p>{currentClass.subject}</p>
                            </div>
                            <div className="flex items-center gap-1">
                                <UsersIcon className="w-4 h-4" />
                                <p>{currentClass.students.length} students</p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            <div className="flex-1 overflow-y-auto">
                {activeTab === 'students' ? (
                    <div className="bg-card border border-border rounded-lg shadow-md flex-1 flex flex-col min-h-0">
                        <div className="p-4 bg-secondary-100 border-b border-border flex justify-start items-center tour-student-search">
                            <div className="relative">
                                <MagnifyingGlassIcon className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" />
                                <input
                                    type="text"
                                    placeholder="Search students..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-9 pr-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-accent-light focus:border-transparent text-sm text-primary"
                                />
                            </div>
                        </div>

                        {/* Column Headers */}
                        <div className="px-6 py-3 bg-secondary-100 border-b border-border grid grid-cols-2 gap-4 tour-student-table-header">
                            <button
                                onClick={() => handleSort('name')}
                                className="flex items-center gap-2 text-xs font-medium text-muted-foreground uppercase tracking-wider hover:text-accent"
                            >
                                Student Name
                                {sortBy === 'name' && (
                                    sortOrder === 'asc' ? <ArrowDownIcon className="w-4 h-4" /> : <ArrowUpIcon className="w-4 h-4" />
                                )}
                            </button>
                        </div>
                        {/* Scrollable Students List */}
                        <div className="flex-1 overflow-y-auto min-h-0">
                            <div className="divide-y divide-border tour-student-list">
                                {filteredAndSortedStudents.map((student) => (
                                    <button
                                        key={student._id}
                                        onClick={() => handleStudentClick(student)}
                                        className="w-full px-6 py-4 hover:bg-secondary-100 transition-colors duration-150 text-left"
                                    >
                                        <div className="flex flex-col">
                                            <span className="font-medium text-primary">{student.firstName} {student.lastName}</span>
                                            <span className="text-sm text-muted-foreground">@{student.username}</span>
                                        </div>
                                    </button>
                                ))}
                            </div>

                            {filteredAndSortedStudents.length === 0 && (
                                <div className="p-6 text-center text-muted-foreground">
                                    No students found matching your search.
                                </div>
                            )}
                        </div>
                    </div>
                ) : (
                    <ClassAnalytics classData={currentClass} />
                )}
            </div>

            {/* Render the StudentAnalyticsModal when a student is selected */}
            {selectedStudent && (
                <StudentAnalyticsModal 
                    student={selectedStudent} 
                    onClose={closeModal} 
                    currentClass={currentClass} 
                />
            )}
        </div>
    );
};

export default ClassDetails;