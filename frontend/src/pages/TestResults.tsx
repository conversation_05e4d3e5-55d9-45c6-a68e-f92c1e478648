import React, { useEffect, useState } from 'react';
import { Bug } from "lucide-react"
import { useLocation, useNavigate } from 'react-router-dom';
import {
    CheckIcon as CheckIconSolid,
    XMarkIcon as XMarkIconSolid,
    ClockIcon,
    ChartBarIcon,
    HomeIcon,
    ArrowLeftIcon,
    EyeIcon,
    EyeSlashIcon,
    QuestionMarkCircleIcon,
    BookOpenIcon,
    PresentationChartLineIcon,
    CalendarDaysIcon,
    AcademicCapIcon,
    TagIcon
} from '@heroicons/react/24/solid';

import { renderLatexContent } from '../components/RenderLatexContent';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { fetchWithCache } from '../utils/cacheUtil';

import {
    Pie<PERSON><PERSON>,
    Pie,
    Cell,
    Tooltip as RechartsTooltip,
    Legend as RechartsLegend,
    Bar<PERSON>hart as RechartsBarChart,
    Bar,
    CartesianGrid,
    XAxis,
    YAxis,
    ResponsiveContainer,
} from 'recharts';
import { createPortal } from 'react-dom';
import { Button } from '@/shadcn/components/ui/button';

interface Question {
    _id: string;
    question: string;
    options: string[];
    answer: string;
    solution?: string;
    images?: string[];
    topic: string;
    subtopics?: string;
    difficulty: number;
}

interface Response {
    questionId: string;
    selectedAnswer: number | null;
    intuition: string;
    timeSpent?: number;
    isCorrect: boolean;
}

interface TestResult {
    testId: string;
    startTime?: Date;
    endTime?: Date;
    totalTimeTaken: number;
    score?: number;
    totalScore?: number;
    responses: Response[];
    proficiencyBefore?: number;
    proficiencyAfter?: number;
    metadata?: {
        device: string;
        browser: string;
        ipAddress: string;
    };
    flaggedForReview?: boolean;
}

interface TestDetails {
    _id: string;
    subject: string;
    testType: string;
    topics: string[];
    testDate: Date;
    startTime: string;
    duration: number;
    numberOfQuestions: number;
    totalMarks: number;
    testInstructions: string;
    class: {
        className: string;
        classStd: string;
    };
}

// Modal Component for Detailed Analytics
const DetailedAnalyticsModal: React.FC<{
    isOpen: boolean;
    onClose: () => void;
    topicPerformance: { [key: string]: { total: number, correct: number } };
    difficultyPerformance: { [key: string]: { total: number, correct: number } };
    pieChartData: { name: string, value: number }[];
    pieColors: string[];
    barChartData: { name: string, timeSpent: number }[];
    // New props for moved metrics
    attemptedRatio: string;
    correctCount: number;
    incorrectCount: number;
    avgTimePerQuestion: string;
    proficiencyChange: string;
    totalQuestions: number;
}> = ({
    isOpen,
    onClose,
    topicPerformance,
    difficultyPerformance,
    pieChartData,
    pieColors,
    barChartData,
    attemptedRatio,
    correctCount,
    incorrectCount,
    avgTimePerQuestion,
    proficiencyChange,
    totalQuestions
}) => {
        if (!isOpen) return null;

        // Helper for displaying metrics in the modal
        const MetricItem: React.FC<{ title: string; value: string | number; valueClass?: string; subText?: string }> = ({ title, value, valueClass = "text-primary", subText }) => (
            <div className="bg-card-alt border border-border rounded-md p-3 text-center"> {/* Adjusted styling */}
                <p className={`text-lg font-semibold ${valueClass}`}>{value}</p>
                <p className="text-xs text-muted-foreground uppercase tracking-wider mt-0.5">{title}</p>
                {subText && <p className="text-xs text-muted-foreground mt-0.5">{subText}</p>}
            </div>
        );

        return createPortal(
            <div className="fixed inset-0 z-50 flex items-center justify-center backdrop-blur-sm bg-opacity-60 p-4">
                <div className="bg-card border border-border rounded-xl shadow-2xl max-w-4xl w-full max-h-[90vh] flex flex-col"> {/* Increased max-w for more content */}
                    <div className="flex items-center justify-between p-4 sm:p-6 border-b border-border">
                        <h2 className="text-xl font-semibold text-primary">Detailed Performance Analysis</h2>
                        <button onClick={onClose} className="text-muted-foreground hover:text-primary">
                            <XMarkIconSolid className="w-6 h-6" />
                        </button>
                    </div>

                    <div className="overflow-y-auto p-4 sm:p-6 space-y-6">
                        {/* Overall Test Metrics - NEW SECTION */}
                        <div className="bg-card-alt border border-border rounded-lg p-4">
                            <h3 className="text-lg font-semibold text-primary mb-4">Overall Test Metrics</h3>
                            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3">
                                <MetricItem title="Attempted" value={attemptedRatio} />
                                <MetricItem title="Correct" value={correctCount} valueClass="text-success" />
                                <MetricItem title="Incorrect" value={incorrectCount} valueClass="text-danger" />
                                <MetricItem title="Avg Time/Q" value={avgTimePerQuestion} subText="per attempted" />
                                {proficiencyChange !== "N/A" ? (
                                    <MetricItem
                                        title="Proficiency"
                                        value={`${parseFloat(proficiencyChange) >= 0 ? '+' : ''}${proficiencyChange}%`}
                                        valueClass={parseFloat(proficiencyChange) >= 0 ? 'text-success' : 'text-danger'}
                                    />
                                ) : (
                                    <MetricItem title="Proficiency" value="N/A" />
                                )}
                            </div>
                        </div>

                        {/* Topic & Difficulty Analysis */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="bg-card-alt border border-border rounded-lg p-4">
                                <h3 className="text-lg font-semibold text-primary mb-3">Topic-wise Performance</h3>
                                <div className="space-y-3">
                                    {Object.entries(topicPerformance).map(([topic, data]) => (
                                        <div key={topic}>
                                            <div className="flex justify-between items-center text-sm mb-1">
                                                <span className="font-medium text-muted-foreground">{topic}</span>
                                                <span className="text-primary">{((data.correct / data.total) * 100 || 0).toFixed(0)}% ({data.correct}/{data.total})</span>
                                            </div>
                                            <div className="w-full bg-border rounded-full h-2">
                                                <div className="bg-accent h-2 rounded-full" style={{ width: `${(data.correct / data.total) * 100 || 0}%` }}></div>
                                            </div>
                                        </div>
                                    ))}
                                    {Object.keys(topicPerformance).length === 0 && <p className="text-sm text-muted-foreground">No topic data available.</p>}
                                </div>
                            </div>
                            <div className="bg-card-alt border border-border rounded-lg p-4">
                                <h3 className="text-lg font-semibold text-primary mb-3">Difficulty-wise Performance</h3>
                                <div className="space-y-3">
                                    {Object.entries(difficultyPerformance).map(([level, data]) => (
                                        <div key={level}>
                                            <div className="flex justify-between items-center text-sm mb-1">
                                                <span className="font-medium text-muted-foreground">{level}</span>
                                                <span className="text-primary">{((data.correct / data.total) * 100 || 0).toFixed(0)}% ({data.correct}/{data.total})</span>
                                            </div>
                                            <div className="w-full bg-border rounded-full h-2">
                                                <div className={`h-2 rounded-full ${level === 'Easy' ? 'bg-success' : level === 'Medium' ? 'bg-yellow-500' : 'bg-danger'}`}
                                                    style={{ width: `${(data.correct / data.total) * 100 || 0}%` }}></div>
                                            </div>
                                        </div>
                                    ))}
                                    {Object.keys(difficultyPerformance).length === 0 && <p className="text-sm text-muted-foreground">No difficulty data available.</p>}
                                </div>
                            </div>
                        </div>

                        {/* Charts Section */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div className="bg-card-alt border border-border rounded-lg p-4">
                                <h3 className="text-lg font-semibold text-primary mb-3 text-center">Answer Distribution</h3>
                                <ResponsiveContainer width="100%" height={250}>
                                    <PieChart>
                                        <Pie data={pieChartData} cx="50%" cy="50%" outerRadius={80} fill="hsl(var(--chart-primary))" dataKey="value" labelLine={false} label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}>
                                            {pieChartData.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={pieColors[index % pieColors.length]} />
                                            ))}
                                        </Pie>
                                        <RechartsTooltip contentStyle={{ backgroundColor: 'var(--card)', border: '1px solid var(--border)', borderRadius: '8px', color: 'var(--primary)' }} />
                                        <RechartsLegend verticalAlign="bottom" wrapperStyle={{ paddingTop: '10px', color: 'var(--muted-foreground)' }} />
                                    </PieChart>
                                </ResponsiveContainer>
                            </div>
                            <div className="bg-card-alt border border-border rounded-lg p-4">
                                <h3 className="text-lg font-semibold text-primary mb-3 text-center">Time Spent per Question (seconds)</h3>
                                <ResponsiveContainer width="100%" height={250}>
                                    <RechartsBarChart data={barChartData} margin={{ top: 5, right: 0, left: 0, bottom: 5 }}>
                                        <CartesianGrid strokeDasharray="3 3" stroke="var(--border)" />
                                        <XAxis dataKey="name" stroke="var(--primary)" />
                                        <YAxis stroke="var(--primary)" />
                                        <RechartsTooltip contentStyle={{ backgroundColor: 'var(--card)', border: '1px solid var(--border)', borderRadius: '8px', color: 'var(--primary)' }} />
                                        <Bar dataKey="timeSpent" fill="var(--accent)" />
                                    </RechartsBarChart>
                                </ResponsiveContainer>
                            </div>
                        </div>
                    </div>
                </div>
            </div>,
            document.body
        );
    };

const cleanQuestionText = (questionText: string): string => {
    if (!questionText) return '';
    return questionText.replace(/\boptions:\s*[\s\S]*$/i, '').trim();
};

const cleanOptionText = (optionText: string): string => {
    if (!optionText) return '';
    return optionText.replace(/^[A-Za-z][\)\.\:]\s*/, '');
}

const TestResults: React.FC = () => {
    const axiosPrivate = useAxiosPrivate();
    const navigate = useNavigate();
    const location = useLocation();
    const studentId = (location.state as any)?.studentId ?? '';
    const testId = (location.state as any)?.testId ?? '';
    const userType = (location.state as any)?.userType ?? 'student';

    const [testResult, setTestResult] = useState<TestResult | null>(null);
    const [questions, setQuestions] = useState<Question[]>([]);
    const [testDetails, setTestDetails] = useState<TestDetails | null>(null);
    const [solutionVisible, setSolutionVisible] = useState<{ [key: string]: boolean }>({});
    const [isAnalyticsModalOpen, setIsAnalyticsModalOpen] = useState(false);

    const prefix = 'https://aegisscholar-platform-web.s3.ap-south-1.amazonaws.com/uploads/images/questions/';

    const renderQuestionImages = (question: Question, type: 'question' | 'answer') => {
        // ... (implementation remains the same)
        if (!question.images || question.images.length === 0) return null;
        return question.images
            .filter(image => type === 'question' ? !image.includes('_a') : image.includes('_a'))
            .map((image, index) => {
                const imageName = image.split("/").pop();
                const imagePath = `${prefix}${imageName}`;
                return (
                    <img
                        key={index}
                        src={imagePath}
                        alt={`${type === 'question' ? 'Question' : 'Solution'} Image ${index + 1}`}
                        className="w-full max-w-md h-auto object-contain rounded-lg mb-4 border border-border"
                    />
                );
            });
    };

    const addQuestionForReview = async (questionId: string) => {
      try {
        const formData = new FormData();
        console.log(`got student id: ${studentId}\n`);
        formData.append('comment', "QuestionId: "+questionId);
        formData.append('type', 'bug')
        formData.append('id', studentId);
        formData.append('role', 'Student'); // maybe capital S
        
        await axiosPrivate.post('/api/feedback/feed/', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          }
        });
      } catch (error) {
        console.error("Error sending the question for review: ", error);
      }
    }

    useEffect(() => {
        const fetchTestResults = async () => {
            if (!studentId || !testId) {
                console.error('Student ID or Test ID is missing.');
                navigate('/student-dashboard', { replace: true, state: { error: 'Missing test identifiers.' } }); // Navigate away if critical info is missing
                return;
            }
            try {
                const data = await fetchWithCache(axiosPrivate, `/api/testResults/${studentId}/${testId}`);
                console.error("data", data);
                if (data && data.test && data.questions) {
                    setTestResult(data.test);
                    setQuestions(data.questions);
                    if (data.testDetails) {
                        setTestDetails(data.testDetails);
                    }
                } else {
                    console.error('Test data is not in expected format:', data);
                    navigate('/student-dashboard', { replace: true, state: { error: 'Failed to load test results.' } });
                }
            } catch (error) {
                console.error('Failed to fetch test results:', error);
                navigate('/student-dashboard', { replace: true, state: { error: 'Failed to load test results.' } });
            }
        };
        fetchTestResults();
    }, [axiosPrivate, studentId, testId, navigate]);

    if (!testResult || questions.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center h-screen text-xl font-semibold text-primary bg-background">
                <svg className="animate-spin h-10 w-10 text-accent mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading Test Results...
            </div>
        );
    }

    const totalQuestions = questions.length;
    const { totalTimeTaken, responses } = testResult;

    const calculatedScore = responses.filter(response => {
        const question = questions.find(q => q._id === response.questionId);
        if (!question || response.selectedAnswer === null || response.selectedAnswer === undefined) return false; // Ensure selectedAnswer is not undefined
        return question.answer === question.options[response.selectedAnswer as number];
    }).length;
    const score = testResult.totalScore ?? calculatedScore;
    const accuracy = totalQuestions > 0 ? ((score / totalQuestions) * 100).toFixed(1) : "0.0";

    const unansweredCount = responses.filter(response => response.selectedAnswer === null || response.selectedAnswer === undefined).length;
    const answeredCount = totalQuestions - unansweredCount;
    const correctAnswersCount = score;
    const incorrectAnswers = answeredCount - correctAnswersCount;

    const timeSpentResponses = responses.filter(response => typeof response.timeSpent === 'number');
    const totalTimeSpentOnAnswered = timeSpentResponses.reduce((acc, response) => acc + (response.timeSpent || 0), 0);
    const avgTimeSpent = answeredCount > 0 ? totalTimeSpentOnAnswered / answeredCount : 0;

    const proficiencyImprovement = (testResult.proficiencyBefore !== undefined && testResult.proficiencyAfter !== undefined)
        ? (testResult.proficiencyAfter * 100 - testResult.proficiencyBefore * 100).toFixed(1)
        : "N/A";

    const formatTime = (totalSeconds: number) => {
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = Math.round(totalSeconds % 60); // Use Math.round for cleaner seconds
        return `${hours > 0 ? hours.toString().padStart(2, '0') + ':' : ''}${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    };

    const pieChartData = [
        { name: 'Correct', value: correctAnswersCount },
        { name: 'Incorrect', value: incorrectAnswers },
        { name: 'Unanswered', value: unansweredCount },
    ];
    const PIE_COLORS = ['hsl(var(--success))', 'hsl(var(--danger))', 'hsl(var(--muted-foreground))']; // Green (Correct), Red (Incorrect), Gray (Unanswered)

    const barChartData = questions.map((q, index) => {
        const resp = responses.find(r => r.questionId === q._id);
        return { name: `Q${index + 1}`, timeSpent: resp && typeof resp.timeSpent === 'number' ? Number((resp.timeSpent).toFixed(1)) : 0 }; // ensure timeSpent is a number
    });

    const topicPerformance = questions.reduce((acc: { [key: string]: { total: number, correct: number } }, q) => {
        if (!q.topic) return acc; // Skip if topic is undefined/empty
        if (!acc[q.topic]) acc[q.topic] = { total: 0, correct: 0 };
        acc[q.topic].total++;
        const response = responses.find(r => r.questionId === q._id);
        if (response && response.selectedAnswer !== null && response.selectedAnswer !== undefined && q.options[response.selectedAnswer as number] === q.answer) {
            acc[q.topic].correct++;
        }
        return acc;
    }, {});

    const difficultyPerformance = questions.reduce((acc: { [key: string]: { total: number, correct: number } }, q) => {
        const diffLevel = q.difficulty <= 3 ? 'Easy' : q.difficulty <= 6 ? 'Medium' : 'Hard';
        if (!acc[diffLevel]) acc[diffLevel] = { total: 0, correct: 0 };
        acc[diffLevel].total++;
        const response = responses.find(r => r.questionId === q._id);
        if (response && response.selectedAnswer !== null && response.selectedAnswer !== undefined && q.options[response.selectedAnswer as number] === q.answer) {
            acc[diffLevel].correct++;
        }
        return acc;
    }, {});


    return (
        <div className="min-h-screen bg-background">
            <main className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 pb-16">
                <header className="relative mb-8 md:mb-12">
                    <button
                        onClick={() => userType === 'teacher' ? navigate(-1) : navigate("/student-dashboard", { replace: true })}
                        className="absolute top-0 left-0 z-10 flex items-center gap-1 text-sm text-muted-foreground hover:text-accent transition-colors bg-card rounded-full py-2 px-4 shadow border border-border hover:border-accent"
                    >
                        <ArrowLeftIcon className="w-4 h-4" />
                        Back
                    </button>
                    <div className="text-center pt-12 sm:pt-0">
                        <h1 className="text-3xl sm:text-4xl font-bold text-primary">Test Results</h1>
                        <p className="mt-1 text-md text-muted-foreground">Review your performance summary and detailed answers.</p>
                    </div>
                </header>

                {/* Test Details Section */}
                {testDetails && (
                    <section className="mb-6">
                        <div className="bg-card/40 border border-border/40 rounded-lg p-3 shadow-sm">
                            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-2 text-center">
                                <div className="bg-card border border-border/20 rounded-md p-2.5 hover:shadow-sm transition-all duration-200">
                                    <AcademicCapIcon className="w-4 h-4 text-muted-foreground/80 mx-auto mb-1" />
                                    <p className="text-xs text-muted-foreground/80 mb-0.5">Subject</p>
                                    <p className="text-sm font-medium text-primary truncate">{testDetails.subject}</p>
                                </div>
                                <div className="bg-card border border-border/20 rounded-md p-2.5 hover:shadow-sm transition-all duration-200">
                                    <TagIcon className="w-4 h-4 text-muted-foreground/80 mx-auto mb-1" />
                                    <p className="text-xs text-muted-foreground/80 mb-0.5">Topics</p>
                                    <p className="text-sm font-medium text-primary truncate" title={testDetails.topics.length > 0 ? testDetails.topics.join(', ') : 'General'}>
                                        {testDetails.topics.length > 0 ? testDetails.topics.join(', ') : 'General'}
                                    </p>
                                </div>
                                <div className="bg-card border border-border/20 rounded-md p-2.5 hover:shadow-sm transition-all duration-200">
                                    <CalendarDaysIcon className="w-4 h-4 text-muted-foreground/80 mx-auto mb-1" />
                                    <p className="text-xs text-muted-foreground/80 mb-0.5">Date</p>
                                    <p className="text-sm font-medium text-primary">
                                        {new Date(testDetails.testDate).toLocaleDateString('en-US', {
                                            month: 'short',
                                            day: 'numeric',
                                            year: 'numeric'
                                        })}
                                    </p>
                                </div>
                                <div className="bg-card border border-border/20 rounded-md p-2.5 hover:shadow-sm transition-all duration-200">
                                    <ClockIcon className="w-4 h-4 text-muted-foreground/80 mx-auto mb-1" />
                                    <p className="text-xs text-muted-foreground/80 mb-0.5">Duration</p>
                                    <p className="text-sm font-medium text-primary">{testDetails.duration} min</p>
                                </div>
                                <div className="bg-card border border-border/20 rounded-md p-2.5 hover:shadow-sm transition-all duration-200">
                                    <QuestionMarkCircleIcon className="w-4 h-4 text-muted-foreground/80 mx-auto mb-1" />
                                    <p className="text-xs text-muted-foreground/80 mb-0.5">Questions</p>
                                    <p className="text-sm font-medium text-primary">{testDetails.numberOfQuestions}</p>
                                </div>
                                {testDetails.class && (
                                    <div className="bg-card border border-border/20 rounded-md p-2.5 hover:shadow-sm transition-all duration-200">
                                        <BookOpenIcon className="w-4 h-4 text-muted-foreground/80 mx-auto mb-1" />
                                        <p className="text-xs text-muted-foreground/80 mb-0.5">Class</p>
                                        <p className="text-sm font-medium text-primary truncate">
                                            {testDetails.class.className}
                                        </p>
                                    </div>
                                )}
                            </div>
                        </div>
                    </section>
                )}

                {/* Combined Section for Stats and Button */}
                <section className="mb-8 md:mb-12 flex flex-col md:flex-row items-center gap-4 md:gap-6">
                    {/* Main Summary Stats Card - Smaller and Flexible */}
                    <div className="w-full md:flex-1 bg-card border border-border rounded-xl shadow-md p-4"> {/* p-4 for reduced padding */}
                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-x-4 gap-y-3 text-center sm:divide-x divide-border">
                            {/* Score */}
                            <div className="sm:px-2 py-1"> {/* Added py-1 for a little vertical space when stacked */}
                                <ChartBarIcon className="w-8 h-8 text-accent mx-auto mb-1.5" /> {/* Smaller icon & margin */}
                                <div className="text-2xl lg:text-3xl font-bold text-accent">{score} / {totalQuestions}</div> {/* Adjusted font size */}
                                <p className="mt-0.5 text-xs text-muted-foreground">Score</p> {/* Smaller text */}
                            </div>
                            {/* Accuracy */}
                            <div className="sm:px-2 py-1">
                                <CheckIconSolid className="w-8 h-8 text-success mx-auto mb-1.5" />
                                <div className="text-2xl lg:text-3xl font-bold text-success">{accuracy}%</div>
                                <p className="mt-0.5 text-xs text-muted-foreground">Accuracy</p>
                            </div>
                            {/* Time Taken */}
                            <div className="sm:px-2 py-1">
                                <ClockIcon className="w-8 h-8 text-warning mx-auto mb-1.5" />
                                <div className="text-2xl lg:text-3xl font-bold text-warning">{formatTime(totalTimeTaken)}</div>
                                <p className="mt-0.5 text-xs text-muted-foreground">Time Taken</p>
                            </div>
                        </div>
                    </div>

                    {/* Detailed Analysis Button - Aligned */}
                    <div className="w-full md:w-auto flex-shrink-0"> {/* Ensures button takes its own width on md+ screens */}
                        <button
                            onClick={() => setIsAnalyticsModalOpen(true)}
                            className="w-full md:w-auto flex items-center justify-center px-8 py-3 bg-accent text-accent-foreground rounded-lg hover:bg-accent/90 transition-colors text-base font-medium shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-accent focus:ring-offset-2 focus:ring-offset-background"
                        // Kept original button padding and text size. Adjusted text to accent-foreground and hover to accent/90 for theme consistency.
                        >
                            <PresentationChartLineIcon className="mr-2 h-5 w-5" />
                            View Detailed Analysis
                        </button>
                    </div>
                </section>
                {/* Detailed Question Review Section */}
                <section>
                    <h2 className="text-2xl font-semibold text-primary mb-6 text-center sm:text-left">Detailed Question Review</h2>
                    <div className="space-y-6">
                        {questions.map((question, index) => {
                            const response = responses.find(r => r.questionId === question._id);
                            const studentSelectedOptionIndex = response?.selectedAnswer;
                            const studentAnswered = studentSelectedOptionIndex !== null && studentSelectedOptionIndex !== undefined;
                            const isCorrect = studentAnswered && response?.isCorrect;
                            // console.error("question", question)
                            // console.error("response", response)
                            // console.error("studentSelectedOptionIndex", studentSelectedOptionIndex)
                            // console.error("studentAnswered", studentAnswered)
                            // console.error("isCorrect", isCorrect)

                            return (
                                <div key={question._id} className="bg-card rounded-xl shadow-md p-5 sm:p-6 border border-border">
                                    <div className="flex flex-col sm:flex-row items-start justify-between mb-3 gap-2">
                                        <p className="text-lg font-semibold text-primary">Question {index + 1}</p>
                                        {studentAnswered ? (
                                            isCorrect ? (
                                                <span className="flex items-center text-xs font-semibold text-success bg-success/10 px-2.5 py-1 rounded-full border border-success/30"> {/* Using Tailwind opacity for bg/border */}
                                                    <CheckIconSolid className="w-4 h-4 mr-1.5 flex-shrink-0" /> Correct
                                                </span>
                                            ) : (
                                                <span className="flex items-center text-xs font-semibold text-danger bg-danger/10 px-2.5 py-1 rounded-full border border-danger/30">
                                                    <XMarkIconSolid className="w-4 h-4 mr-1.5 flex-shrink-0" /> Incorrect
                                                </span>
                                            )
                                        ) : (
                                            <span className="flex items-center text-xs font-semibold text-muted-foreground bg-muted/10 px-2.5 py-1 rounded-full border border-border">
                                                <QuestionMarkCircleIcon className="w-4 h-4 mr-1.5 flex-shrink-0" /> Unanswered
                                            </span>
                                        )}
                                    </div>

                                    <div className="text-primary prose prose-sm max-w-none mb-4">
                                        {renderLatexContent(cleanQuestionText(question.question))}
                                    </div>

                                    {question.images && question.images.length > 0 && (
                                        <div className="grid grid-cols-1 gap-2 my-4">
                                            {renderQuestionImages(question, 'question')}
                                        </div>
                                    )}

                                    <div className="space-y-2 mb-4">
                                        {question.options.map((option, idx) => {
                                            const isThisOptionSelectedByStudent = studentSelectedOptionIndex === idx;
                                            const cleanCorrectAnswer = (question.answer || '')
                                                .replace(/^[\s,]+|[\s,]+$/g, ''); // Remove leading/trailing commas and whitespace
                                            
                                            const cleanSelectedAnswer = (option || '')
                                                .replace(/^[\s,]+|[\s,]+$/g, '') // Remove leading/trailing commas and whitespace
                                                .replace(/^[A-Za-z][\)\.\:]\s*/, ''); // Remove option prefixes like "a)", "A.", "B:", etc.
                                            
                                            const isThisTheCorrectAnswer = cleanCorrectAnswer === cleanSelectedAnswer;

                                            let optionClasses = "p-3 border rounded-lg flex justify-between items-center text-sm transition-colors ";
                                            let icon = null;

                                            if (isThisOptionSelectedByStudent) {
                                                if (isThisTheCorrectAnswer) {
                                                    optionClasses += "bg-success/10 border-success/50 text-success font-medium ring-2 ring-success/70";
                                                    icon = <CheckIconSolid className="w-5 h-5 text-success flex-shrink-0" />;
                                                } else {
                                                    optionClasses += "bg-danger/10 border-danger/50 text-danger font-medium ring-2 ring-danger/70";
                                                    icon = <XMarkIconSolid className="w-5 h-5 text-danger flex-shrink-0" />;
                                                }
                                            } else if (isThisTheCorrectAnswer) {
                                                optionClasses += "bg-accent/5 border-accent/30 text-accent";
                                                icon = <span className="text-xs font-semibold text-accent px-1.5 py-0.5 rounded-full bg-accent/10">Correct Answer</span>;
                                            } else {
                                                optionClasses += "border-border bg-card-alt text-primary";
                                            }

                                            return (
                                                <div key={idx} className={optionClasses}>
                                                    <span className="flex-grow mr-2">{renderLatexContent(cleanOptionText(option))}</span>
                                                    {icon}
                                                </div>
                                            );
                                        })}
                                    </div>

                                    {response?.intuition && (
                                        <div className="mt-4 p-3 bg-muted/20 rounded-lg border border-border"> {/* Using muted for intuition bg */}
                                            <p className="text-xs font-semibold text-primary mb-1">Your Intuition:</p>
                                            <p className="text-sm text-muted-foreground">{renderLatexContent(response.intuition)}</p>
                                        </div>
                                    )}

                                    <div className="flex">
                                    {question.solution && (
                                        <div className="mt-4">
                                            <button
                                                onClick={() => setSolutionVisible(prev => ({ ...prev, [question._id]: !prev[question._id] }))}
                                                className="flex items-center px-4 py-2 text-sm font-medium text-accent bg-accent/10 rounded-lg hover:bg-accent/20 transition-colors focus:outline-none focus:ring-2 ring-accent/50"
                                            >
                                                {solutionVisible[question._id] ? <EyeSlashIcon className="w-5 h-5 mr-2" /> : <EyeIcon className="w-5 h-5 mr-2" />}
                                                {solutionVisible[question._id] ? "Hide Solution" : "Show Solution"}
                                            </button>
                                            {solutionVisible[question._id] && (
                                                <div className="mt-3 p-4 bg-muted/20 rounded-lg border border-border prose prose-sm max-w-none">
                                                    {renderQuestionImages(question, 'answer')}
                                                    {renderLatexContent(question.solution)}
                                                </div>
                                            )}
                                        </div>
                                    )}
                                    <Button
                                        variant="outline"
                                        className="mt-4 ml-2 flex items-centre px-4 py-2 text-sm font-medium text-accent bg-accent/10 rounded-lg hover:bg-accent/20 transition-colors focus:outline-none focus:ring-2 ring-accent/50"
                                        onClick={() => addQuestionForReview(question._id)}> 
                                            <Bug className="w-h h-5 mr-2"/> Mark Question for inspection
                                    </Button>
                                    </div>
                                </div>
                            );
                        })}
                    </div>
                </section>

                <div className="mt-2 md:mt-4 flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-4">
                    <button
                        onClick={() => userType === 'teacher' ? navigate(-1) : navigate('/student-dashboard')}
                        className="w-full sm:w-auto flex items-center justify-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors text-base font-medium shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background" // Adjusted button color
                    >
                        <HomeIcon className="mr-2 h-5 w-5" />
                        Back to Dashboard
                    </button>
                </div>
            </main>

            <DetailedAnalyticsModal
                isOpen={isAnalyticsModalOpen}
                onClose={() => setIsAnalyticsModalOpen(false)}
                topicPerformance={topicPerformance}
                difficultyPerformance={difficultyPerformance}
                pieChartData={pieChartData}
                pieColors={PIE_COLORS}
                barChartData={barChartData}
                // Pass the new props
                attemptedRatio={`${answeredCount}/${totalQuestions}`}
                correctCount={correctAnswersCount}
                incorrectCount={incorrectAnswers}
                avgTimePerQuestion={formatTime(avgTimeSpent)}
                proficiencyChange={proficiencyImprovement}
                totalQuestions={totalQuestions}
            />
        </div>
    );
};

export default TestResults;
