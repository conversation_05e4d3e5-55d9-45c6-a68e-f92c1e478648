import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser } from '../contexts/userContext'; // Assuming this path is correct
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts';
import { UsersIcon, ArrowTrendingUpIcon, ClockIcon, TrophyIcon, AcademicCapIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'; // Added AcademicCapIcon, ExclamationTriangleIcon
import OnboardingTour, { TourStep } from '@/components/OnboardingTour'; // Assuming you have a TourStep type defined


const steps: TourStep[] = [
  {
    target: '.tour-analytics-header',
    title: 'Analytics Dashboard',
    content: 'Welcome to your Analytics Dashboard! This is your central hub for monitoring class performance and student progress across all your classes.',
    placement: 'bottom',
    disableBeacon: true,
  },
  {
    target: '.tour-class-cards',
    title: 'Class Overview',
    content: 'Here you can see all your assigned classes with their key performance metrics. Each card provides at-a-glance analytics for that specific class.',
    placement: 'bottom',
  },
  {
    target: '.tour-class-header',
    title: 'Class Information',
    content: 'Each card displays the class name, subject, and total number of enrolled students for quick identification.',
    placement: 'bottom',
  },
  {
    target: '.tour-key-metrics',
    title: 'Performance Metrics',
    content: 'These key metrics show your class\'s performance at a glance: Average Proficiency indicates overall understanding, while Completion Rate shows student engagement levels.',
    placement: 'right',
  },
  {
    target: '.tour-proficiency-chart',
    title: 'Proficiency Distribution',
    content: 'This chart categorizes students by performance level: Advanced (top performers), Proficient (meeting standards), Developing (approaching standards), and Beginning (needs additional support).',
    placement: 'left',
  },
  {
    target: '.tour-class-card',
    title: 'Interactive Cards',
    content: 'Click any class card to access detailed analytics, including individual student performance data and topic-specific insights.',
    placement: 'bottom',
  }
];

// Custom Tooltip for Pie Chart
const CustomPieTooltip = ({ active, payload }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload; // Access the original data object
    const total = payload[0].payload.totalForPercentage; // Get total from payload if passed
    const percentage = total > 0 ? ((data.value / total) * 100).toFixed(1) : 0;
    return (
      <div className="bg-card border border-border shadow-lg p-3 rounded-md">
        <p className="text-sm text-primary font-semibold">{`${data.name}`}</p>
        <p className="text-xs text-muted-foreground">{`Students: ${data.value}`}</p>
        <p className="text-xs text-muted-foreground">{`Percentage: ${percentage}%`}</p>
      </div>
    );
  }
  return null;
};


// Analytics Overview Card Component
const ClassAnalyticsCard = ({ classData, onClick }: { classData: any, onClick?: () => void }) => {
  const analytics = classData.analytics?.classPerformance || {};
  
  const rawDistributionData = [
    { name: 'Advanced', value: analytics.proficiencyDistribution?.advanced?.length || 0 },
    { name: 'Proficient', value: analytics.proficiencyDistribution?.proficient?.length || 0 },
    { name: 'Developing', value: analytics.proficiencyDistribution?.developing?.length || 0 },
    { name: 'Beginning', value: analytics.proficiencyDistribution?.beginning?.length || 0 },
  ];

  const totalProficiencyStudents = rawDistributionData.reduce((sum, item) => sum + item.value, 0);

  // Add total to each data point for percentage calculation in tooltip
  const distributionData = rawDistributionData.map(item => ({
    ...item,
    totalForPercentage: totalProficiencyStudents
  }));

  const COLORS = ['hsl(var(--chart-primary))', 'hsl(var(--chart-secondary))', 'hsl(var(--chart-tertiary))', 'hsl(var(--danger))']; // Primary, Secondary, Tertiary, Danger

  const hasProficiencyData = totalProficiencyStudents > 0;

  return (
    <div
      className="bg-card rounded-lg border border-border p-6 flex flex-col space-y-6 hover:shadow-md transition-shadow duration-200 cursor-pointer tour-class-card"
      onClick={onClick}
      tabIndex={0}
      role="button"
      onKeyDown={e => { if (e.key === 'Enter' || e.key === ' ') { onClick && onClick(); } }}
    >
      {/* Card Header */}
      <div className="pb-4 border-b border-border tour-class-header">
        <div className="flex justify-between items-start mb-1">
          <h3 className="text-xl font-semibold text-primary">{classData.className}</h3>
          <div className="flex items-center gap-2 bg-accent/10 px-3 py-1 rounded-full text-xs">
            <UsersIcon className="w-4 h-4 text-accent" />
            <span className="font-medium text-accent">{classData.students?.length || 0} Students</span>
          </div>
        </div>
        <div className="flex items-center text-sm text-muted-foreground">
          <AcademicCapIcon className="w-4 h-4 mr-2 text-muted-foreground/80" />
          <span>{classData.subject}</span>
        </div>
      </div>

      {/* Key Metrics Section */}
      <div>
        <h4 className="text-md font-semibold text-primary mb-3">Key Metrics</h4>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 tour-key-metrics">
          <div className="bg-background p-4 rounded-lg border border-border/70">
            <div className="flex items-center gap-2 mb-1">
              <ArrowTrendingUpIcon className="w-5 h-5 text-accent" />
              <span className="text-sm text-muted-foreground">Avg. Proficiency</span>
            </div>
            <span className="text-3xl font-semibold text-primary">
              {(analytics.averageProficiency * 100 || 0).toFixed(1)}%
            </span>
          </div>
          <div className="bg-background p-4 rounded-lg border border-border/70">
            <div className="flex items-center gap-2 mb-1">
              <TrophyIcon className="w-5 h-5 text-accent" />
              <span className="text-sm text-muted-foreground">Completion Rate</span>
            </div>
            <span className="text-3xl font-semibold text-primary">
              {(analytics.testCompletionRate || 0).toFixed(1)}%
            </span>
          </div>
        </div>
      </div>

      {/* Proficiency Distribution Section */}
      <div>
        <h4 className="text-md font-semibold text-primary mb-3">Proficiency Distribution</h4>
        {hasProficiencyData ? (
          <div className="h-60 w-full tour-proficiency-chart"> {/* Increased height for pie chart area */}
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={distributionData}
                  cx="50%"
                  cy="50%"
                  innerRadius={50} // Slightly smaller inner radius
                  outerRadius={80} // Slightly smaller outer radius
                  paddingAngle={3}
                  dataKey="value"
                  labelLine={false}
                  // label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`} // Optional: if you want labels on slices
                >
                  {distributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip content={<CustomPieTooltip />} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        ) : (
          <div className="h-60 flex flex-col items-center justify-center text-center bg-background rounded-lg border border-border/70 p-4">
            <ExclamationTriangleIcon className="w-12 h-12 text-muted-foreground/50 mb-2" />
            <p className="text-sm text-muted-foreground font-medium">No proficiency data available</p>
            <p className="text-xs text-muted-foreground/80">Student performance data will appear here once available.</p>
          </div>
        )}
        
        {hasProficiencyData && (
          <ul className="mt-4 space-y-2">
            {distributionData.map((item, index) => (
              <li key={item.name} className="flex items-center justify-between text-sm">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 rounded-full" style={{ backgroundColor: COLORS[index % COLORS.length] }} />
                  <span className="text-muted-foreground">{item.name}</span>
                </div>
                <span className="font-medium text-primary">{item.value}</span>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

const Analytics = () => {
  const { user } = useUser();
  const teacherClasses = user?.classes || [];
  const navigate = useNavigate();

  const handleClassClick = (classItem: any) => {
    // Use the same logic as ClassList: route based on role
    if (user?.role === 'Teacher') {
      navigate(`/ClassDetails/${classItem._id}`);
    } else {
      navigate(`/subjectDetails/${classItem._id}`);
    }
  };

  return (
    <div className="min-h-screen pb-16 p-4 sm:p-6 lg:p-8 bg-background">
      {user?.id && (<OnboardingTour
          steps={steps}
          user={
            user && user.id && typeof user.role === 'string'
                ? {...user, id: String(user.id), role: user.role}
                : null
          }
          tourKey="teacher-analytics"
          onTourComplete={(completed, skipped) => {
            console.log('Analytics tour completed:', {completed, skipped});
          }}
          continuous={true}
          startDelay={500}
          spotlightPadding={10}
      />)}
      <header className="mb-8 tour-analytics-header">
        <h1 className="text-3xl font-bold text-primary">Classes Overview</h1>
        <p className="text-md text-muted-foreground mt-1">Teacher's Dashboard & Performance Analytics</p>
      </header>
      {teacherClasses.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 tour-class-cards">
          {teacherClasses.map((cls) => (
            <ClassAnalyticsCard
              key={cls._id || cls.className}
              classData={cls}
              onClick={() => handleClassClick(cls)}
            />
          ))}
        </div>
      ) : (
        <div className="flex flex-col items-center justify-center text-center bg-card border border-border rounded-lg p-10 min-h-[300px]">
          <AcademicCapIcon className="w-16 h-16 text-muted-foreground/50 mb-4" />
          <h2 className="text-xl font-semibold text-primary mb-2">No Classes Found</h2>
          <p className="text-muted-foreground">
            You are not currently assigned to any classes, or class data is not yet available.
          </p>
          <p className="text-sm text-muted-foreground/80 mt-1">
            If you believe this is an error, please contact your administrator.
          </p>
        </div>
      )}
    </div>
  );
};

export default Analytics;