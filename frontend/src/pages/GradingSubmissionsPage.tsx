import React from 'react';
import GradingSubmissions from '@/components/GradingSubmissions';

const GradingSubmissionsPage: React.FC = () => {
    return (
        <div className="h-screen w-full bg-background p-2 overflow-hidden">
            <div className="h-full flex flex-col">
                {/* Fixed Header */}
                <header className="flex-shrink-0 flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 sm:gap-3 py-4">
                    <div className="flex items-center gap-3 min-w-0 flex-1">
                        <div className="min-w-0 flex-1">
                            <h1 className="text-3xl font-['Space_Grotesk'] font-bold text-foreground truncate">
                                Grading Submissions
                            </h1>
                            <p className="text-xs sm:text-sm text-muted-foreground line-clamp-1">
                                View and manage your test grading submissions
                            </p>
                        </div>
                    </div>
                </header>

                {/* Scrollable Content */}
                <div className="flex-1 overflow-hidden">
                    <GradingSubmissions />
                </div>
            </div>
        </div>
    );
};

export default GradingSubmissionsPage;
