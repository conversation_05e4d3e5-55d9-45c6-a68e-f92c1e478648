import React, { useState, useEffect } from 'react';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { useNavigate } from 'react-router-dom';
import { 
  BookOpenIcon, 
  AcademicCapIcon, 
  ListBulletIcon, 
  ArrowRightIcon,
  ArrowTrendingUpIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
import { usePracticeTestState } from '../hooks/usePracticeTestSetupState';
import { usePageRefresh } from '../hooks/usePageRefresh';
import Background from '../components/Background';

interface OptionCardProps {
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  label: string;
  value: string;
  isSelected: boolean;
  onClick: () => void;
}

const OptionCard: React.FC<OptionCardProps> = ({ icon: Icon, label, value, isSelected, onClick }) => (
  <div
    onClick={onClick}
    className={`relative overflow-hidden group cursor-pointer transition-all duration-300
                p-3 sm:p-4 rounded-xl border-2 hover:shadow-lg transform hover:-translate-y-1
                ${isSelected ? 'border-blue-600 bg-blue-50' : 'border-gray-200 hover:border-blue-600'}`}
  >
    <div className="flex items-center space-x-3">
      <div className={`p-2 rounded-lg ${isSelected ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-600'} 
                    group-hover:bg-blue-600 group-hover:text-white transition-colors duration-300`}>
        <Icon className="h-4 w-4 sm:h-5 sm:w-5" />
      </div>
      <div>
        <div className="font-medium text-gray-800 text-sm sm:text-base">{label}</div>
      </div>
    </div>
  </div>
);

const PracticeTestSetup: React.FC = () => {
  const navigate = useNavigate();
  const { state, updateState } = usePracticeTestState();
  const { selectedClass, selectedSubject, numberOfQuestions } = state;
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [showError, setShowError] = useState<boolean>(false);
  const [selectedDuration, setSelectedDuration] = useState<string>('');

  const axiosPrivate = useAxiosPrivate();

  usePageRefresh();

  const steps = [
    {
      title: "Class Level",
      description: "Select your academic year",
      icon: AcademicCapIcon,
      options: ['Class 6', 'Class 7', 'Class 8', 'Class 9', 'Class 10'],
      value: selectedClass,
      setter: (value: string) => updateState({ selectedClass: value })
    },
    {
      title: "Subject",
      description: "Choose what you want to practice",
      icon: BookOpenIcon,
      options: ['Math', 'Science', 'English', 'History', 'Geography'],
      value: selectedSubject,
      setter: (value: string) => updateState({ selectedSubject: value })
    },
    {
      title: "Questions",
      description: "How many questions do you want?",
      icon: ListBulletIcon,
      options: ['5', '10', '15', '20', '25'],
      value: numberOfQuestions,
      setter: (value: string) => updateState({ numberOfQuestions: value })
    },
    {
      title: "Duration",
      description: "Set your time limit",
      icon: ClockIcon,
      options: ['15 min', '30 min', '45 min', '60 min', 'No Limit'],
      value: selectedDuration,
      setter: setSelectedDuration
    }
  ];

  const handleContinue = () => {
    if (steps[currentStep].value) {
      if (currentStep < steps.length - 1) {
        setCurrentStep(prev => prev + 1);
      } else {
        updateState({ isSetupComplete: true });
        navigate('/test');
      }
    } else {
      setShowError(true);
      setTimeout(() => setShowError(false), 2000);
    }
  };

  return (
    <div className="min-h-screen flex items-center overflow-hidden justify-center p-4 sm:p-4 bg-background">
      {/* <Background /> */}
      
      <div className="max-w-2xl w-full">
        {/* Progress Steps */}
        <div className="flex justify-between mb-6 sm:mb-8 relative px-2">
          {steps.map((step, index) => (
            <div key={index} className="flex flex-col items-center relative z-10">
              <div 
                className={`w-8 h-8 sm:w-10 sm:h-10 rounded-full flex items-center justify-center
                          transition-all duration-500 ${index <= currentStep 
                          ? 'bg-accent text-white' 
                          : 'bg-secondary-100 text-muted-foreground'}`}
              >
                <step.icon className="h-4 w-4 sm:h-5 sm:w-5" />
              </div>
              <div className={`mt-2 text-xs sm:text-sm font-medium transition-colors duration-500
                            ${index <= currentStep ? 'text-accent' : 'text-muted-foreground'}
                            hidden xs:block`}>
                {step.title}
              </div>
            </div>
          ))}
          {/* Progress Line */}
          <div className="absolute top-4 sm:top-5 left-0 h-0.5 bg-black w-full z-9">
            <div 
              className="h-full bg-accent transition-all duration-500"
              style={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
            />
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-card border border-border rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 mb-4 sm:mb-6">
          <div className="text-center mb-4 sm:mb-6">
            <h2 className="text-xl sm:text-2xl font-bold text-primary mb-1 sm:mb-2">{steps[currentStep].title}</h2>
            <p className="text-sm sm:text-base text-muted-foreground">{steps[currentStep].description}</p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-4 sm:mb-6">
            {steps[currentStep].options.map((option) => (
              <OptionCard
                key={option}
                icon={steps[currentStep].icon}
                label={option}
                value={`Select ${option}`}
                isSelected={steps[currentStep].value === option}
                onClick={() => steps[currentStep].setter(option)}
              />
            ))}
          </div>

          <div className="flex justify-between items-center">
            <button
              onClick={() => setCurrentStep(prev => Math.max(0, prev - 1))}
              className={`px-4 sm:px-6 py-2 rounded-lg font-medium text-sm sm:text-base transition-all duration-300
                         ${currentStep === 0 
                         ? 'text-gray-400 cursor-not-allowed' 
                         : 'text-muted-foreground hover:bg-secondary-100'}`}
              disabled={currentStep === 0}
            >
              Back
            </button>
            <button
              onClick={handleContinue}
              className="bg-accent text-white px-4 sm:px-6 py-2 rounded-lg font-medium text-sm sm:text-base
                       hover:bg-accent-dark transition-all duration-300 flex items-center space-x-2
                       focus:outline-hidden focus:ring-2 focus:ring-accent-light focus:ring-offset-2"
            >
              <span>{currentStep === steps.length - 1 ? 'Start Test' : 'Continue'}</span>
              <ArrowRightIcon className="h-3 w-3 sm:h-4 sm:w-4" />
            </button>
          </div>

          {showError && (
            <div className="mt-3 sm:mt-4 text-center text-danger text-sm sm:text-base animate-bounce">
              Please select an option to continue
            </div>
          )}
        </div>

        {/* Target Score Card */}
        <div className="bg-card border border-border rounded-xl p-3 sm:p-4 flex items-center space-x-3 sm:space-x-4">
          <div className="bg-secondary-100 p-2 rounded-lg">
            <ArrowTrendingUpIcon className="h-4 w-4 sm:h-5 sm:w-5 text-accent" />
          </div>
          <div>
            <div className="font-medium text-primary text-sm sm:text-base">Target Score</div>
            <div className="text-xs sm:text-sm text-muted-foreground">
              We recommend aiming for at least 80% correct answers for optimal learning
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PracticeTestSetup;
