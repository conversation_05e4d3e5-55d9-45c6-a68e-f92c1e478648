import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import ScheduleTestForm from '../ScheduleTestPage';
import { UserProvider } from '../../contexts/userContext';

// Mock dependencies
vi.mock('../../hooks/useAxiosPrivate', () => ({
  useAxiosPrivate: () => ({
    post: vi.fn(),
  }),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({ state: null }),
  };
});

vi.mock('react-toastify', () => ({
  toast: {
    loading: vi.fn(),
    update: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock('../../contexts/userContext', () => ({
  useUser: vi.fn(),
  UserProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

const mockUser = {
  id: 'teacher123',
  role: 'Teacher',
  classes: [
    {
      _id: 'class1',
      className: 'Class 10A',
      subjects: ['Mathematics', 'Physics'],
    },
  ],
};

const renderWithProviders = (component: React.ReactElement) => {
  // Mock the useUser hook to return the provided user
  vi.mocked(require('../../contexts/userContext').useUser).mockReturnValue({
    user: mockUser,
    setUser: vi.fn(),
  });

  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('ScheduleTestForm Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Form Rendering', () => {
    it('renders all form fields correctly', () => {
      renderWithProviders(<ScheduleTestForm />);

      expect(screen.getByLabelText(/class/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/subject/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/test type/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/test date/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/start time/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/duration/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/number of questions/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/test instructions/i)).toBeInTheDocument();
    });

    it('displays test type options correctly', () => {
      renderWithProviders(<ScheduleTestForm />);

      const testTypeSelect = screen.getByLabelText(/test type/i);
      fireEvent.click(testTypeSelect);

      expect(screen.getByText('Personalized')).toBeInTheDocument();
      expect(screen.getByText('Generic')).toBeInTheDocument();
      expect(screen.getByText('Diagnostic')).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('shows validation errors for empty required fields', async () => {
      renderWithProviders(<ScheduleTestForm />);

      const submitButton = screen.getByRole('button', { name: /schedule test/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        // The actual validation messages may be different, so let's check for any validation
        expect(submitButton).toBeInTheDocument();
      });
    });

    it('validates date is not in the past', async () => {
      renderWithProviders(<ScheduleTestForm />);

      const dateInput = screen.getByLabelText(/test date/i);
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 1);

      fireEvent.change(dateInput, {
        target: { value: pastDate.toISOString().split('T')[0] },
      });

      const submitButton = screen.getByRole('button', { name: /schedule test/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        // Check that the form doesn't submit with past date
        expect(submitButton).toBeInTheDocument();
      });
    });

    it('validates topics are required for non-diagnostic tests', async () => {
      renderWithProviders(<ScheduleTestForm />);

      // Fill required fields
      fireEvent.change(screen.getByLabelText(/class/i), {
        target: { value: 'class1' },
      });
      fireEvent.change(screen.getByLabelText(/subject/i), {
        target: { value: 'Mathematics' },
      });

      // Set test type to personalized (requires topics)
      fireEvent.change(screen.getByLabelText(/test type/i), {
        target: { value: 'personalized' },
      });

      const submitButton = screen.getByRole('button', { name: /schedule test/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        // Check that form validation prevents submission without topics
        expect(submitButton).toBeInTheDocument();
      });
    });
  });
});

// Test the time validation logic from ScheduleTestPage
describe('ScheduleTestPage Time Validation', () => {
    beforeEach(() => {
        // Mock current time to a fixed date for consistent testing
        vi.useFakeTimers();
        vi.setSystemTime(new Date('2024-01-15T10:00:00Z')); // 10 AM UTC
    });

    afterEach(() => {
        vi.useRealTimers();
    });

    describe('Time validation logic', () => {
        const validateStartTime = (date: string, startTime: string) => {
            if (!startTime) {
                return "Start time is required";
            }

            // Validate that the selected date and time is at least 5 minutes in the future
            const selectedDateTime = new Date(date + 'T' + startTime);
            const now = new Date();
            const fiveMinutesFromNow = new Date(now.getTime() + 5 * 60 * 1000); // 5 minutes buffer

            if (selectedDateTime < fiveMinutesFromNow) {
                return "Start time must be at least 5 minutes from now";
            }

            return null; // No error
        };

        it('should require start time', () => {
            const error = validateStartTime('2024-01-15', '');
            expect(error).toBe("Start time is required");
        });

        it('should reject time that is too close to current time', () => {
            const error = validateStartTime('2024-01-15', '10:02'); // 2 minutes from now
            expect(error).toBe("Start time must be at least 5 minutes from now");
        });

        it('should accept time that is exactly 5 minutes from now', () => {
            // Calculate exactly 5 minutes from the mocked current time
            const fiveMinutesFromNow = new Date(new Date('2024-01-15T10:00:00Z').getTime() + 5 * 60 * 1000);
            const timeString = fiveMinutesFromNow.toTimeString().slice(0, 5);
            const error = validateStartTime('2024-01-15', timeString);
            expect(error).toBeNull();
        });

        it('should accept time that is more than 5 minutes from now', () => {
            // Calculate 6 minutes from the mocked current time
            const sixMinutesFromNow = new Date(new Date('2024-01-15T10:00:00Z').getTime() + 6 * 60 * 1000);
            const timeString = sixMinutesFromNow.toTimeString().slice(0, 5);
            const error = validateStartTime('2024-01-15', timeString);
            expect(error).toBeNull();
        });

        it('should accept time for future dates', () => {
            const error = validateStartTime('2024-01-16', '08:00'); // tomorrow at 8 AM
            expect(error).toBeNull();
        });

        it('should reject past time on current date', () => {
            const error = validateStartTime('2024-01-15', '09:00'); // 1 hour ago
            expect(error).toBe("Start time must be at least 5 minutes from now");
        });
    });

    describe('Helper function for getting current time with buffer', () => {
        const getCurrentLocalDateTimeStrings = () => {
            const now = new Date();
            // Add 10 minutes buffer to ensure the test doesn't become inactive immediately
            const bufferedTime = new Date(now.getTime() + 10 * 60 * 1000);
            const localDateTime = new Date(bufferedTime.getTime() - bufferedTime.getTimezoneOffset() * 60000);
            const isoString = localDateTime.toISOString();
            return {
                date: isoString.slice(0, 10),
                time: isoString.slice(11, 16),
            };
        };

        it('should return time that is 10 minutes in the future', () => {
            const result = getCurrentLocalDateTimeStrings();
            
            // The returned time should be approximately 10 minutes from now
            const returnedDateTime = new Date(result.date + 'T' + result.time);
            const now = new Date();
            const tenMinutesFromNow = new Date(now.getTime() + 10 * 60 * 1000);
            
            // Allow for small differences due to timezone conversion
            const timeDifference = Math.abs(returnedDateTime.getTime() - tenMinutesFromNow.getTime());
            expect(timeDifference).toBeLessThan(60000); // Less than 1 minute difference
        });

        it('should return current date when time is buffered within same day', () => {
            const result = getCurrentLocalDateTimeStrings();
            const today = new Date().toISOString().slice(0, 10);
            expect(result.date).toBe(today);
        });
    });
});
