import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '../../utils/test-utils';
import { <PERSON>rowserRouter } from 'react-router-dom';
import StudentDashboard from '../StudentDashboard';

// Mock components used in StudentDashboard
vi.mock('../../components/Sidebar', () => ({
  default: () => <div data-testid="mocked-sidebar">Sidebar</div>,
}));

vi.mock('../../components/CourseCard', () => ({
  default: ({ title, subject, progress }: any) => (
    <div data-testid="mocked-course-card">
      {title} - {subject} - Progress: {progress}%
    </div>
  ),
}));

vi.mock('../../components/TaskList', () => ({
  default: ({ tasks }: any) => (
    <div data-testid="mocked-task-list">
      Tasks: {tasks.length} items
    </div>
  ),
}));

vi.mock('../../components/RecentActivity', () => ({
  default: () => <div data-testid="mocked-recent-activity">Recent Activity</div>,
}));

// Mock API calls
vi.mock('../../axios', () => ({
  axiosDefault: vi.fn(),
  axiosPrivate: vi.fn(),
}));

import { axiosPrivate } from '../../axios';

describe('StudentDashboard', () => {
  const mockedAxiosPrivate = axiosPrivate as unknown as ReturnType<typeof vi.fn>;
  
  // Mock user data
  const mockUser = {
    id: '123',
    username: 'testuser',
    email: '<EMAIL>',
    userType: 'Student',
  };
  
  // Mock course data
  const mockCourses = [
    { id: '1', title: 'Introduction to Algebra', subject: 'Math', progress: 65 },
    { id: '2', title: 'Basic Chemistry', subject: 'Science', progress: 40 },
  ];
  
  // Mock task data
  const mockTasks = [
    { id: '1', title: 'Complete Assignment 1', dueDate: '2023-05-15', completed: false },
    { id: '2', title: 'Study for Quiz', dueDate: '2023-05-18', completed: true },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock responses for API calls
    mockedAxiosPrivate.mockImplementation((config) => {
      if (config.url === '/api/courses/student') {
        return Promise.resolve({ data: mockCourses });
      } else if (config.url === '/api/tasks') {
        return Promise.resolve({ data: mockTasks });
      }
      return Promise.reject(new Error('Not mocked'));
    });
    
    // Mock localStorage for user data
    Storage.prototype.getItem = vi.fn((key) => {
      if (key === 'user') {
        return JSON.stringify(mockUser);
      }
      return null;
    });
  });

  it('renders the dashboard with all sections', async () => {
    render(
      <BrowserRouter>
        <StudentDashboard />
      </BrowserRouter>
    );
    
    // Check that the sidebar is rendered
    expect(screen.getByTestId('mocked-sidebar')).toBeInTheDocument();
    
    // Check for main dashboard sections
    expect(screen.getByText(/welcome back/i)).toBeInTheDocument();
    
    // Wait for data loading
    await waitFor(() => {
      // Check that courses are rendered
      expect(screen.getAllByTestId('mocked-course-card')).toHaveLength(2);
      
      // Check that tasks are rendered
      expect(screen.getByTestId('mocked-task-list')).toBeInTheDocument();
      expect(screen.getByText(/tasks: 2 items/i)).toBeInTheDocument();
      
      // Check for recent activity section
      expect(screen.getByTestId('mocked-recent-activity')).toBeInTheDocument();
    });
  });

  it('displays appropriate message when there are no courses', async () => {
    // Override the mock to return empty courses
    mockedAxiosPrivate.mockImplementation((config) => {
      if (config.url === '/api/courses/student') {
        return Promise.resolve({ data: [] });
      } else if (config.url === '/api/tasks') {
        return Promise.resolve({ data: mockTasks });
      }
      return Promise.reject(new Error('Not mocked'));
    });
    
    render(
      <BrowserRouter>
        <StudentDashboard />
      </BrowserRouter>
    );
    
    // Wait for data loading
    await waitFor(() => {
      // Check for "no courses" message
      expect(screen.getByText(/no courses found/i)).toBeInTheDocument();
    });
  });

  it('handles API error states gracefully', async () => {
    // Mock API error
    mockedAxiosPrivate.mockRejectedValue(new Error('Network error'));
    
    render(
      <BrowserRouter>
        <StudentDashboard />
      </BrowserRouter>
    );
    
    // Wait for error message
    await waitFor(() => {
      expect(screen.getByText(/could not load data/i)).toBeInTheDocument();
    });
  });
}); 