import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import ReviewTestPage from '../ReviewTestPage';
import { UserProvider } from '../../contexts/userContext';

// Mock dependencies
vi.mock('../../hooks/useAxiosPrivate', () => ({
  useAxiosPrivate: () => ({
    post: vi.fn(),
  }),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      state: {
        class: 'class1',
        subject: 'Mathematics',
        testType: 'generic',
        numberOfQuestions: 10,
        topics: ['Algebra'],
        date: '2024-12-25',
        startTime: '10:00',
        duration: 60,
        totalMarks: 100,
        instructions: 'Test instructions',
      },
    }),
  };
});

vi.mock('react-toastify', () => ({
  toast: {
    loading: vi.fn(),
    update: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
  },
}));

vi.mock('../../components/QuestionCard', () => ({
  default: ({ question, onKeep, onDiscard, isTopCard }: any) => (
    <div data-testid="question-card" data-top-card={isTopCard}>
      <h3>{question.question}</h3>
      <button onClick={() => onKeep(question)} data-testid="keep-button">
        Keep
      </button>
      <button onClick={() => onDiscard(question)} data-testid="discard-button">
        Discard
      </button>
    </div>
  ),
}));

vi.mock('../../components/QuestionCreator', () => ({
  default: ({ onSave, onCancel }: any) => (
    <div data-testid="question-creator">
      <button onClick={() => onSave({ question: 'New question', _id: 'new1' })}>
        Save Question
      </button>
      <button onClick={onCancel}>Cancel</button>
    </div>
  ),
}));

vi.mock('../../contexts/userContext', () => ({
  useUser: vi.fn(),
  UserProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

const mockUser = {
  id: 'teacher123',
  role: 'Teacher',
  classes: [
    {
      _id: 'class1',
      className: 'Class 10A',
      subjects: ['Mathematics', 'Physics'],
    },
  ],
};

const mockQuestions = [
  {
    _id: 'q1',
    question: 'What is 2 + 2?',
    options: ['3', '4', '5', '6'],
    answer: '4',
    topic: ['Algebra'],
    subtopic: ['Basic Operations'],
    difficulty: 0.5,
  },
  {
    _id: 'q2',
    question: 'What is 3 × 3?',
    options: ['6', '9', '12', '15'],
    answer: '9',
    topic: ['Algebra'],
    subtopic: ['Multiplication'],
    difficulty: 0.6,
  },
  {
    _id: 'q3',
    question: 'Solve for x: 2x + 4 = 10',
    options: ['2', '3', '4', '5'],
    answer: '3',
    topic: ['Algebra'],
    subtopic: ['Linear Equations'],
    difficulty: 0.7,
  },
];

const renderWithProviders = (component: React.ReactElement) => {
  // Mock the useUser hook to return the provided user
  vi.mocked(require('../../contexts/userContext').useUser).mockReturnValue({
    user: mockUser,
    setUser: vi.fn(),
  });

  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('ReviewTestPage', () => {
  let mockAxios: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockAxios = vi.fn();
    vi.mocked(require('../../hooks/useAxiosPrivate').useAxiosPrivate).mockReturnValue({
      post: mockAxios,
    });
  });

  describe('Component Rendering', () => {
    it('renders the review test page with header information', async () => {
      mockAxios
        .mockResolvedValueOnce({ data: mockUser }) // getDetailTeacher
        .mockResolvedValueOnce({ data: { recommendations: JSON.stringify(mockQuestions) } }); // recommend

      renderWithProviders(<ReviewTestPage />);

      await waitFor(() => {
        expect(screen.getByText('Review Test Questions')).toBeInTheDocument();
        expect(screen.getByText(/GENERIC • 10 questions/i)).toBeInTheDocument();
      });
    });

    it('displays loading state initially', () => {
      mockAxios.mockImplementation(() => new Promise(() => {})); // Never resolves

      renderWithProviders(<ReviewTestPage />);

      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    it('shows question cards when questions are loaded', async () => {
      mockAxios
        .mockResolvedValueOnce({ data: mockUser })
        .mockResolvedValueOnce({ data: { recommendations: JSON.stringify(mockQuestions) } });

      renderWithProviders(<ReviewTestPage />);

      await waitFor(() => {
        expect(screen.getAllByTestId('question-card')).toHaveLength(3);
        expect(screen.getByText('What is 2 + 2?')).toBeInTheDocument();
      });
    });
  });

  describe('Question Management', () => {
    beforeEach(async () => {
      mockAxios
        .mockResolvedValueOnce({ data: mockUser })
        .mockResolvedValueOnce({ data: { recommendations: JSON.stringify(mockQuestions) } });

      renderWithProviders(<ReviewTestPage />);

      await waitFor(() => {
        expect(screen.getAllByTestId('question-card')).toHaveLength(3);
      });
    });

    it('allows keeping questions', async () => {
      const keepButtons = screen.getAllByTestId('keep-button');
      fireEvent.click(keepButtons[0]);

      await waitFor(() => {
        // Question should be moved to selected questions
        expect(screen.getAllByTestId('question-card')).toHaveLength(2);
      });
    });

    it('allows discarding questions', async () => {
      const discardButtons = screen.getAllByTestId('discard-button');
      fireEvent.click(discardButtons[0]);

      await waitFor(() => {
        // Question should be removed from available questions
        expect(screen.getAllByTestId('question-card')).toHaveLength(2);
      });
    });

    it('updates selected questions count', async () => {
      const keepButtons = screen.getAllByTestId('keep-button');
      fireEvent.click(keepButtons[0]);
      fireEvent.click(keepButtons[1]);

      await waitFor(() => {
        expect(screen.getByText(/Schedule Test \(2\)/)).toBeInTheDocument();
      });
    });
  });

  describe('Question Creator', () => {
    beforeEach(async () => {
      mockAxios
        .mockResolvedValueOnce({ data: mockUser })
        .mockResolvedValueOnce({ data: { recommendations: JSON.stringify(mockQuestions) } });

      renderWithProviders(<ReviewTestPage />);

      await waitFor(() => {
        expect(screen.getAllByTestId('question-card')).toHaveLength(3);
      });
    });

    it('opens question creator when add button is clicked', () => {
      const addButton = screen.getByText(/add question/i);
      fireEvent.click(addButton);

      expect(screen.getByTestId('question-creator')).toBeInTheDocument();
    });

    it('creates new question successfully', async () => {
      mockAxios.mockResolvedValueOnce({
        data: { question: { _id: 'new1', question: 'New question' } },
      });

      const addButton = screen.getByText(/add question/i);
      fireEvent.click(addButton);

      const saveButton = screen.getByText('Save Question');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(mockAxios).toHaveBeenCalledWith('/api/questionBank/create', expect.any(Object));
        expect(screen.queryByTestId('question-creator')).not.toBeInTheDocument();
      });
    });

    it('handles question creation errors', async () => {
      mockAxios.mockRejectedValueOnce(new Error('Creation failed'));

      const addButton = screen.getByText(/add question/i);
      fireEvent.click(addButton);

      const saveButton = screen.getByText('Save Question');
      fireEvent.click(saveButton);

      await waitFor(() => {
        expect(require('react-toastify').toast.update).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            render: 'Failed to create question!',
            type: 'error',
          })
        );
      });
    });
  });

  describe('Test Scheduling', () => {
    beforeEach(async () => {
      mockAxios
        .mockResolvedValueOnce({ data: mockUser })
        .mockResolvedValueOnce({ data: { recommendations: JSON.stringify(mockQuestions) } });

      renderWithProviders(<ReviewTestPage />);

      await waitFor(() => {
        expect(screen.getAllByTestId('question-card')).toHaveLength(3);
      });

      // Keep some questions
      const keepButtons = screen.getAllByTestId('keep-button');
      fireEvent.click(keepButtons[0]);
      fireEvent.click(keepButtons[1]);
    });

    it('disables schedule button when no questions selected', async () => {
      // Discard all kept questions by refreshing the component
      mockAxios
        .mockResolvedValueOnce({ data: mockUser })
        .mockResolvedValueOnce({ data: { recommendations: JSON.stringify(mockQuestions) } });

      renderWithProviders(<ReviewTestPage />);

      await waitFor(() => {
        const scheduleButton = screen.getByText(/Schedule Test \(0\)/);
        expect(scheduleButton).toBeDisabled();
      });
    });

    it('enables schedule button when questions are selected', async () => {
      await waitFor(() => {
        const scheduleButton = screen.getByText(/Schedule Test \(2\)/);
        expect(scheduleButton).not.toBeDisabled();
      });
    });

    it('shows confirmation modal when schedule button is clicked', async () => {
      const scheduleButton = screen.getByText(/Schedule Test \(2\)/);
      fireEvent.click(scheduleButton);

      expect(screen.getByText(/confirm test scheduling/i)).toBeInTheDocument();
    });

    it('schedules test successfully', async () => {
      mockAxios.mockResolvedValueOnce({
        data: { test: { _id: 'test123' } },
      });

      const scheduleButton = screen.getByText(/Schedule Test \(2\)/);
      fireEvent.click(scheduleButton);

      const confirmButton = screen.getByText(/confirm/i);
      fireEvent.click(confirmButton);

      await waitFor(() => {
        expect(mockAxios).toHaveBeenCalledWith('/api/test/create', expect.any(Object));
        expect(require('react-toastify').toast.update).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            render: 'Test scheduled successfully!',
            type: 'success',
          })
        );
      });
    });

    it('handles test scheduling errors', async () => {
      mockAxios.mockRejectedValueOnce(new Error('Scheduling failed'));

      const scheduleButton = screen.getByText(/Schedule Test \(2\)/);
      fireEvent.click(scheduleButton);

      const confirmButton = screen.getByText(/confirm/i);
      fireEvent.click(confirmButton);

      await waitFor(() => {
        expect(require('react-toastify').toast.update).toHaveBeenCalledWith(
          expect.any(String),
          expect.objectContaining({
            render: 'Failed to schedule test!',
            type: 'error',
          })
        );
      });
    });
  });

  describe('Error Handling', () => {
    it('handles question fetching errors', async () => {
      mockAxios
        .mockResolvedValueOnce({ data: mockUser })
        .mockRejectedValueOnce(new Error('Failed to fetch questions'));

      renderWithProviders(<ReviewTestPage />);

      await waitFor(() => {
        expect(screen.getByText(/error loading questions/i)).toBeInTheDocument();
      });
    });

    it('handles empty question response', async () => {
      mockAxios
        .mockResolvedValueOnce({ data: mockUser })
        .mockResolvedValueOnce({ data: { recommendations: '[]' } });

      renderWithProviders(<ReviewTestPage />);

      await waitFor(() => {
        expect(screen.getByText(/no more questions to review/i)).toBeInTheDocument();
      });
    });
  });
});
