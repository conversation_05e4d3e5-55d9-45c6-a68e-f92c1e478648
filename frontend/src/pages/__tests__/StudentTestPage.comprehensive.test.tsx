import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import StudentTestPage from '../StudentTestPage';
import { UserProvider } from '../../contexts/userContext';

// Mock dependencies
vi.mock('../../hooks/useAxiosPrivate', () => ({
  useAxiosPrivate: () => ({
    get: vi.fn(),
    post: vi.fn(),
  }),
}));

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => vi.fn(),
    useLocation: () => ({
      state: {
        testId: 'test123',
        testMode: 'scheduled',
      },
    }),
  };
});

vi.mock('react-toastify', () => ({
  toast: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
  },
}));

vi.mock('@/utils/cacheUtil', () => ({
  fetchWithCache: vi.fn(),
}));

vi.mock('../../components/RenderLatexContent', () => ({
  renderLatexContent: (content: string) => content,
}));

vi.mock('../../contexts/userContext', () => ({
  useUser: vi.fn(),
  UserProvider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

const mockUser = {
  id: 'student123',
  role: 'Student',
  name: 'John Doe',
};

const mockTestData = {
  _id: 'test123',
  class: [{ className: 'Class 10A' }],
  subject: 'Mathematics',
  topics: ['Algebra', 'Geometry'],
  testDate: new Date('2024-12-25'),
  numberOfQuestions: 3,
  duration: 60,
  totalMarks: 100,
  testInstructions: 'Read all questions carefully.',
  startTime: '10:00',
  active: true,
  questions: [
    {
      _id: 'q1',
      question: 'What is $$2 + 2$$?',
      options: ['3', '4', '5', '6'],
      images: [],
      topic: 'Algebra',
      subtopic: 'Basic Operations',
    },
    {
      _id: 'q2',
      question: 'Solve: $$x^2 = 9$$',
      options: ['±2', '±3', '±4', '±5'],
      images: [],
      topic: 'Algebra',
      subtopic: 'Quadratic Equations',
    },
    {
      _id: 'q3',
      question: 'What is the area of a circle with radius $$r = 3$$?',
      options: ['6π', '9π', '12π', '18π'],
      images: [],
      topic: 'Geometry',
      subtopic: 'Circle',
    },
  ],
};

const renderWithProviders = (component: React.ReactElement) => {
  // Mock the useUser hook to return the provided user
  vi.mocked(require('../../contexts/userContext').useUser).mockReturnValue({
    user: mockUser,
    setUser: vi.fn(),
  });

  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('StudentTestPage - Comprehensive Tests', () => {
  let mockFetchWithCache: any;
  let mockAxios: any;
  let mockNavigate: any;

  beforeEach(() => {
    vi.clearAllMocks();
    mockFetchWithCache = vi.fn();
    mockAxios = { get: vi.fn(), post: vi.fn() };
    mockNavigate = vi.fn();
    
    vi.mocked(require('@/utils/cacheUtil').fetchWithCache).mockImplementation(mockFetchWithCache);
    vi.mocked(require('../../hooks/useAxiosPrivate').useAxiosPrivate).mockReturnValue(mockAxios);
    vi.mocked(require('react-router-dom').useNavigate).mockReturnValue(mockNavigate);
    
    // Mock timer functions
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  describe('Test Loading and Initialization', () => {
    it('loads test data and questions correctly', async () => {
      mockFetchWithCache.mockResolvedValue({ test: mockTestData });

      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(screen.getByText('What is $$2 + 2$$?')).toBeInTheDocument();
        expect(screen.getByText('Question 1 of 3')).toBeInTheDocument();
      });
    });

    it('displays test timer correctly', async () => {
      mockFetchWithCache.mockResolvedValue({ test: mockTestData });

      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(screen.getByText(/60:00/)).toBeInTheDocument(); // 60 minutes
      });
    });

    it('shows test subject and details', async () => {
      mockFetchWithCache.mockResolvedValue({ test: mockTestData });

      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(screen.getByText('Mathematics')).toBeInTheDocument();
        expect(screen.getByText('Class 10A')).toBeInTheDocument();
      });
    });
  });

  describe('Question Navigation', () => {
    beforeEach(async () => {
      mockFetchWithCache.mockResolvedValue({ test: mockTestData });
      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(screen.getByText('What is $$2 + 2$$?')).toBeInTheDocument();
      });
    });

    it('navigates to next question', async () => {
      const nextButton = screen.getByRole('button', { name: /next/i });
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText('Solve: $$x^2 = 9$$')).toBeInTheDocument();
        expect(screen.getByText('Question 2 of 3')).toBeInTheDocument();
      });
    });

    it('navigates to previous question', async () => {
      // Go to second question first
      const nextButton = screen.getByRole('button', { name: /next/i });
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText('Question 2 of 3')).toBeInTheDocument();
      });

      // Go back to first question
      const prevButton = screen.getByRole('button', { name: /previous/i });
      fireEvent.click(prevButton);

      await waitFor(() => {
        expect(screen.getByText('What is $$2 + 2$$?')).toBeInTheDocument();
        expect(screen.getByText('Question 1 of 3')).toBeInTheDocument();
      });
    });

    it('disables previous button on first question', () => {
      const prevButton = screen.getByRole('button', { name: /previous/i });
      expect(prevButton).toBeDisabled();
    });

    it('shows submit button on last question', async () => {
      // Navigate to last question
      const nextButton = screen.getByRole('button', { name: /next/i });
      fireEvent.click(nextButton); // Question 2
      fireEvent.click(nextButton); // Question 3

      await waitFor(() => {
        expect(screen.getByText('Question 3 of 3')).toBeInTheDocument();
        expect(screen.getByRole('button', { name: /submit test/i })).toBeInTheDocument();
      });
    });
  });

  describe('Answer Selection', () => {
    beforeEach(async () => {
      mockFetchWithCache.mockResolvedValue({ test: mockTestData });
      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(screen.getByText('What is $$2 + 2$$?')).toBeInTheDocument();
      });
    });

    it('allows selecting an answer', () => {
      const option = screen.getByLabelText('4');
      fireEvent.click(option);

      expect(option).toBeChecked();
    });

    it('allows changing answer selection', () => {
      const option1 = screen.getByLabelText('3');
      const option2 = screen.getByLabelText('4');

      fireEvent.click(option1);
      expect(option1).toBeChecked();

      fireEvent.click(option2);
      expect(option2).toBeChecked();
      expect(option1).not.toBeChecked();
    });

    it('persists answers when navigating between questions', async () => {
      // Select answer on first question
      const option = screen.getByLabelText('4');
      fireEvent.click(option);

      // Navigate to next question
      const nextButton = screen.getByRole('button', { name: /next/i });
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText('Question 2 of 3')).toBeInTheDocument();
      });

      // Navigate back to first question
      const prevButton = screen.getByRole('button', { name: /previous/i });
      fireEvent.click(prevButton);

      await waitFor(() => {
        expect(screen.getByText('Question 1 of 3')).toBeInTheDocument();
        expect(screen.getByLabelText('4')).toBeChecked();
      });
    });
  });

  describe('Timer Functionality', () => {
    beforeEach(async () => {
      mockFetchWithCache.mockResolvedValue({ test: mockTestData });
      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(screen.getByText('What is $$2 + 2$$?')).toBeInTheDocument();
      });
    });

    it('counts down timer correctly', async () => {
      expect(screen.getByText(/60:00/)).toBeInTheDocument();

      // Advance timer by 1 minute
      vi.advanceTimersByTime(60000);

      await waitFor(() => {
        expect(screen.getByText(/59:00/)).toBeInTheDocument();
      });
    });

    it('shows warning when time is running low', async () => {
      // Set timer to 5 minutes remaining
      vi.advanceTimersByTime(55 * 60 * 1000); // 55 minutes passed

      await waitFor(() => {
        expect(screen.getByText(/05:00/)).toBeInTheDocument();
        // Should show warning styling
      });
    });

    it('auto-submits test when timer expires', async () => {
      mockAxios.post.mockResolvedValue({ data: { success: true } });

      // Advance timer to expiry
      vi.advanceTimersByTime(60 * 60 * 1000); // 60 minutes

      await waitFor(() => {
        expect(mockAxios.post).toHaveBeenCalledWith('/api/test/submit', expect.any(Object));
      });
    });
  });

  describe('Test Submission', () => {
    beforeEach(async () => {
      mockFetchWithCache.mockResolvedValue({ test: mockTestData });
      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(screen.getByText('What is $$2 + 2$$?')).toBeInTheDocument();
      });
    });

    it('submits test with all responses', async () => {
      mockAxios.post.mockResolvedValue({ data: { success: true } });

      // Answer all questions
      fireEvent.click(screen.getByLabelText('4')); // Q1

      const nextButton = screen.getByRole('button', { name: /next/i });
      fireEvent.click(nextButton);
      await waitFor(() => screen.getByText('Question 2 of 3'));
      fireEvent.click(screen.getByLabelText('±3')); // Q2

      fireEvent.click(nextButton);
      await waitFor(() => screen.getByText('Question 3 of 3'));
      fireEvent.click(screen.getByLabelText('9π')); // Q3

      // Submit test
      const submitButton = screen.getByRole('button', { name: /submit test/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(mockAxios.post).toHaveBeenCalledWith('/api/test/submit', {
          userId: 'student123',
          testId: 'test123',
          responses: expect.arrayContaining([
            expect.objectContaining({
              questionId: 'q1',
              selectedAnswer: '4',
            }),
            expect.objectContaining({
              questionId: 'q2',
              selectedAnswer: '±3',
            }),
            expect.objectContaining({
              questionId: 'q3',
              selectedAnswer: '9π',
            }),
          ]),
          startTime: expect.any(String),
          endTime: expect.any(String),
          subject: 'Mathematics',
        });
      });
    });

    it('handles submission errors gracefully', async () => {
      mockAxios.post.mockRejectedValue(new Error('Submission failed'));

      // Navigate to last question and submit
      const nextButton = screen.getByRole('button', { name: /next/i });
      fireEvent.click(nextButton);
      fireEvent.click(nextButton);

      await waitFor(() => screen.getByText('Question 3 of 3'));

      const submitButton = screen.getByRole('button', { name: /submit test/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(require('react-toastify').toast.error).toHaveBeenCalledWith(
          expect.stringContaining('Failed to submit test')
        );
      });
    });

    it('shows confirmation dialog before submission', async () => {
      // Navigate to last question
      const nextButton = screen.getByRole('button', { name: /next/i });
      fireEvent.click(nextButton);
      fireEvent.click(nextButton);

      await waitFor(() => screen.getByText('Question 3 of 3'));

      const submitButton = screen.getByRole('button', { name: /submit test/i });
      fireEvent.click(submitButton);

      expect(screen.getByText(/confirm submission/i)).toBeInTheDocument();
    });
  });

  describe('LaTeX Rendering', () => {
    beforeEach(async () => {
      mockFetchWithCache.mockResolvedValue({ test: mockTestData });
      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(screen.getByText('What is $$2 + 2$$?')).toBeInTheDocument();
      });
    });

    it('renders LaTeX equations in questions', () => {
      expect(screen.getByText('What is $$2 + 2$$?')).toBeInTheDocument();
    });

    it('renders LaTeX equations in options', async () => {
      // Navigate to question with LaTeX in options
      const nextButton = screen.getByRole('button', { name: /next/i });
      fireEvent.click(nextButton);

      await waitFor(() => {
        expect(screen.getByText('Solve: $$x^2 = 9$$')).toBeInTheDocument();
        expect(screen.getByLabelText('±3')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('handles test loading errors', async () => {
      mockFetchWithCache.mockRejectedValue(new Error('Failed to load test'));

      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(require('react-toastify').toast.error).toHaveBeenCalledWith(
          expect.stringContaining('Failed to load test')
        );
      });
    });

    it('handles inactive test status', async () => {
      const inactiveTest = { ...mockTestData, active: false };
      mockFetchWithCache.mockResolvedValue({ test: inactiveTest });

      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(screen.getByText(/test is not currently active/i)).toBeInTheDocument();
      });
    });

    it('handles missing test data', async () => {
      mockFetchWithCache.mockResolvedValue({ test: null });

      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(require('react-toastify').toast.error).toHaveBeenCalledWith(
          expect.stringContaining('Test not found')
        );
      });
    });
  });

  describe('Accessibility', () => {
    beforeEach(async () => {
      mockFetchWithCache.mockResolvedValue({ test: mockTestData });
      renderWithProviders(<StudentTestPage />);

      await waitFor(() => {
        expect(screen.getByText('What is $$2 + 2$$?')).toBeInTheDocument();
      });
    });

    it('has proper ARIA labels for radio buttons', () => {
      const options = screen.getAllByRole('radio');
      options.forEach(option => {
        expect(option).toHaveAttribute('aria-labelledby');
      });
    });

    it('maintains focus management during navigation', () => {
      const nextButton = screen.getByRole('button', { name: /next/i });
      fireEvent.focus(nextButton);
      expect(nextButton).toHaveFocus();
    });

    it('has proper heading structure', () => {
      expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
      expect(screen.getByRole('heading', { level: 2 })).toBeInTheDocument();
    });
  });
});
