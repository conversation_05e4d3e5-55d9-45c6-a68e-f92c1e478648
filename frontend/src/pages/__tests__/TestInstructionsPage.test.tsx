import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../utils/test-utils';
import { <PERSON>rowserRouter } from 'react-router-dom';
import TestInstructionsPage from '../TestInstructionsPage';

// Mock the hooks and components
vi.mock('../../hooks/useAxiosPrivate', () => ({
  useAxiosPrivate: () => ({
    get: vi.fn().mockResolvedValue({
      data: {
        test: {
          _id: 'test123',
          subject: 'Mathematics',
          topics: ['Algebra', 'Geometry'],
          testDate: '2024-01-15',
          numberOfQuestions: 20,
          duration: 60,
          testInstructions: 'Please read all questions carefully.',
          totalMarks: 100,
          startTime: '10:00',
          active: true,
          createdBy: 'teacher123',
          class: [{ classStd: 'Class 10', className: 'Class 10-A', id: 'class123' }]
        }
      }
    })
  })
}));

vi.mock('../../contexts/userContext', () => ({
  useUser: () => ({
    user: {
      id: 'user123',
      username: '<PERSON>',
      email: '<EMAIL>'
    }
  })
}));

vi.mock('../../components/LoadingSpinner', () => ({
  default: () => <div data-testid="loading-spinner">Loading...</div>
}));

vi.mock('@/utils/cacheUtil', () => ({
  fetchWithCache: vi.fn().mockResolvedValue({
    test: {
      _id: 'test123',
      subject: 'Mathematics',
      topics: ['Algebra', 'Geometry'],
      testDate: '2024-01-15',
      numberOfQuestions: 20,
      duration: 60,
      testInstructions: 'Please read all questions carefully.',
      totalMarks: 100,
      startTime: '10:00',
      active: true,
      createdBy: 'teacher123',
      class: [{ classStd: 'Class 10', className: 'Class 10-A', id: 'class123' }]
    }
  })
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({
      state: {
        testId: 'test123',
        testMode: 'scheduled'
      }
    })
  };
});

// Mock react-toastify
vi.mock('react-toastify', () => ({
  toast: {
    error: vi.fn(),
    warning: vi.fn(),
    loading: vi.fn()
  },
  ToastContainer: () => <div data-testid="toast-container" />
}));

describe('TestInstructionsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderTestInstructionsPage = () => {
    return render(
      <BrowserRouter>
        <TestInstructionsPage />
      </BrowserRouter>
    );
  };

  it('renders loading spinner initially', () => {
    renderTestInstructionsPage();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('renders test instructions page with test details', async () => {
    renderTestInstructionsPage();
    
    // Wait for loading to complete
    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    // Check if main elements are rendered
    expect(screen.getByText('Test Instructions')).toBeInTheDocument();
    expect(screen.getByText('Mathematics')).toBeInTheDocument();
    expect(screen.getByText('20')).toBeInTheDocument(); // Number of questions
    expect(screen.getByText('60 minutes')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument(); // Total marks
  });

  it('shows custom test instructions when available', async () => {
    renderTestInstructionsPage();
    
    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    expect(screen.getByText('Specific Test Instructions')).toBeInTheDocument();
    expect(screen.getByText('Please read all questions carefully.')).toBeInTheDocument();
  });

  it('shows general guidelines', async () => {
    renderTestInstructionsPage();
    
    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    expect(screen.getByText('General Guidelines')).toBeInTheDocument();
    expect(screen.getByText(/Ensure you have a stable internet connection/)).toBeInTheDocument();
    expect(screen.getByText(/Do not refresh the page/)).toBeInTheDocument();
  });

  it('shows student information', async () => {
    renderTestInstructionsPage();
    
    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    expect(screen.getByText('Student Information')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('disables start test button when not agreed', async () => {
    renderTestInstructionsPage();
    
    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    const startButton = screen.getByRole('button', { name: /start test/i });
    expect(startButton).toBeDisabled();
  });

  it('enables start test button when agreed', async () => {
    renderTestInstructionsPage();
    
    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    const checkbox = screen.getByRole('checkbox');
    const startButton = screen.getByRole('button', { name: /start test/i });
    
    expect(startButton).toBeDisabled();
    
    fireEvent.click(checkbox);
    
    expect(startButton).not.toBeDisabled();
  });

  it('navigates back to dashboard when go back is clicked', async () => {
    renderTestInstructionsPage();
    
    await waitFor(() => {
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
    });

    const goBackButton = screen.getByRole('button', { name: /go back/i });
    fireEvent.click(goBackButton);
    
    expect(mockNavigate).toHaveBeenCalledWith('/student-dashboard');
  });
});
