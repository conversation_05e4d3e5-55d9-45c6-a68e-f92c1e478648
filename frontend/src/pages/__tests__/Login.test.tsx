import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '../../utils/test-utils';
import { BrowserRouter } from 'react-router-dom';
import Login from '../Login';

// Define mocked navigate function
const mockedNavigate = vi.fn();

// Mock useNavigate
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockedNavigate,
  };
});

// Mock API functions
vi.mock('../../api', () => ({
  login: vi.fn(),
}));

import { login } from '../../api';

describe('Login Page', () => {
  const mockedLogin = login as unknown as ReturnType<typeof vi.fn>;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const renderLoginPage = () => {
    return render(
      <BrowserRouter>
        <Login />
      </BrowserRouter>
    );
  };

  it('renders login form with all required fields', () => {
    renderLoginPage();
    
    // Check for form elements
    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
    
    // Check for user type selection
    expect(screen.getByLabelText(/student/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/teacher/i)).toBeInTheDocument();
  });

  it('validates form inputs and shows error messages', async () => {
    renderLoginPage();
    
    // Submit the form without filling in any fields
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    fireEvent.click(submitButton);
    
    // Check for validation error messages
    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });

  it('submits the form with valid input and redirects on success', async () => {
    // Mock successful login response
    mockedLogin.mockResolvedValueOnce({ 
      token: 'fake-token', 
      user: { id: '123', username: 'testuser' } 
    });
    
    renderLoginPage();
    
    // Fill in the form
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    const studentRadio = screen.getByLabelText(/student/i);
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'password123' } });
    fireEvent.click(studentRadio);
    
    // Submit the form
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    fireEvent.click(submitButton);
    
    // Verify API call
    await waitFor(() => {
      expect(mockedLogin).toHaveBeenCalledWith('Student', '<EMAIL>', 'password123');
    });
    
    // Verify navigation happened
    await waitFor(() => {
      expect(mockedNavigate).toHaveBeenCalledWith('/student/dashboard');
    });
  });

  it('displays an error message on login failure', async () => {
    // Mock login failure
    const errorMsg = 'Invalid credentials';
    mockedLogin.mockRejectedValueOnce({ 
      response: { data: { message: errorMsg } } 
    });
    
    renderLoginPage();
    
    // Fill in the form
    const emailInput = screen.getByLabelText(/email/i);
    const passwordInput = screen.getByLabelText(/password/i);
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.change(passwordInput, { target: { value: 'wrongpassword' } });
    
    // Submit the form
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    fireEvent.click(submitButton);
    
    // Check for error message
    await waitFor(() => {
      expect(screen.getByText(errorMsg)).toBeInTheDocument();
    });
  });

  it('navigates to registration page when register link is clicked', () => {
    renderLoginPage();
    
    // Find and click the register link
    const registerLink = screen.getByText(/register/i);
    fireEvent.click(registerLink);
    
    // Verify navigation happened
    expect(mockedNavigate).toHaveBeenCalledWith('/register');
  });
}); 