import React, { useEffect, useState } from 'react';
import {
    EnvelopeIcon, PhoneIcon, AcademicCapIcon,
    PencilIcon, CheckIcon, XMarkIcon, ArrowLeftStartOnRectangleIcon,
    CameraIcon,
    MapPinIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-toastify';
import { useUser } from '../contexts/userContext';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import { useNavigate } from 'react-router-dom';

import { createPortal } from 'react-dom';
import { fetchWithCache } from '@/utils/cacheUtil';
import FeedBackComp from '@/components/Feedback';
import ThemeToggle from '@/components/ThemeToggle';

interface ProfileImage {
    filename: string;
    contentType: string;
    imageData: string; // Base64 encoded string
}
interface UserProfile {
    firstName: string;
    lastName: string;
    username: string;
    email: string;
    phoneNumber: string;
    occupation: string;
    profileImage?: ProfileImage;
}

const motivationalQuotes = [
    "Keep learning, keep growing!",
    "Every day is a chance to learn something new.",
    "Teaching is the one profession that creates all others.",
    "Education is the passport to the future.",
    "Empower, inspire, achieve.",
];

const ProfilePage: React.FC = () => {
    const { user, setUser } = useUser();
    const [isEditing, setIsEditing] = useState(false);
    const [userProfile, setUserProfile] = useState<UserProfile>({
        firstName: user?.firstName ?? '',
        lastName: user?.lastName ?? '',
        username: user?.username ?? '',
        email: user?.email ?? '',
        phoneNumber: '',
        occupation: user?.role ?? '',
        profileImage: user?.profileImage // <-- Prefer user.profileImage
    });

    const [schoolInfo, setSchoolInfo] = useState<any>({});

    const [showLogoutModal, setShowLogoutModal] = useState(false);
    const [about, setAbout] = useState<string>("Passionate learner and technology enthusiast.");
    const [editedProfile, setEditedProfile] = useState<UserProfile>({ ...userProfile });
    const [avatarPreview, setAvatarPreview] = useState<string>(userProfile.profileImage?.imageData ?? "");
    const [isAvatarUploading, setIsAvatarUploading] = useState(false);

    const axiosPrivate = useAxiosPrivate();
    const navigate = useNavigate();
    console.log('User Profile:', userProfile);


    useEffect(() => {
        console.error('User:', user);
        if (!user?.schoolCode) {
            return;
        }
        const fetchSchoolInfo = async () => {
            try {
                const response = await fetchWithCache(axiosPrivate, `/api/details/getSchoolDetailsByCode/${user?.schoolCode}`);
                console.error('School Info:', response);
                if (response) {
                    setSchoolInfo(response);
                }
            } catch (error) {
                console.error('Error fetching school info:', error);
            }
        };
        if (user?.role) {
            fetchSchoolInfo();
        }
    }, [user?.role, axiosPrivate]);

    const handleLogout = async () => {
        const logoutToast = toast.loading('Logging out...', { position: "top-right", toastId: 'logout' });
        try {
            let payload: any = {};
            if (user?.role === 'Student') {
                payload = { admissionNumber: user?.admissionNumber, email: user?.email };
            } else {
                payload = { email: user?.email };
            }
            await axiosPrivate.put(`/api/logout/${user?.role}`, payload);
            toast.update(logoutToast, {
                render: 'Logged out successfully!',
                type: 'success',
                isLoading: false,
                autoClose: 1500
            });
        } catch (error) {
            toast.update(logoutToast, {
                render: 'Failed to logout!',
                type: 'error',
                isLoading: false,
                autoClose: 2000
            });
        } finally {
            setUser(null);
            sessionStorage.clear();
            setTimeout(() => navigate('/'), 1500);
        }
    };

    const syncServ = async (orig_username: string, new_email: string, new_uname: string) => {
        try {
            await axiosPrivate.post(`/api/details/editDetail${user?.role}`, { o_uname: orig_username, new_email: new_email, new_uname: new_uname });
        } catch (e) {
            console.error(`error: ${e}`);
        }
    };

    const handleSave = async () => {
        const saveToast = toast.loading('Saving profile...', { position: "top-right" });
        try {
            await syncServ(userProfile.username, editedProfile.email, editedProfile.username);
            setUserProfile({ ...editedProfile });
            setIsEditing(false);
            toast.update(saveToast, {
                render: 'Profile updated successfully!',
                type: 'success',
                isLoading: false,
                autoClose: 1500
            });
        } catch (error) {
            toast.update(saveToast, {
                render: 'Failed to update profile!',
                type: 'error',
                isLoading: false,
                autoClose: 2000
            });
        }
    };

    const handleCancel = () => {
        setEditedProfile({ ...userProfile });
        setIsEditing(false);
    };

    const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (!file) {
            toast.error('No file selected');
            return;
        }
        if (!file.type.startsWith('image/')) {
            toast.error('Please select a valid image file');
            return;
        }
        if (file.size > 10 * 1024 * 1024) { // 10MB limit
            toast.error('Image size exceeds 10MB limit');
            return;
        }

        // Start upload process
        setIsAvatarUploading(true);
        const uploadToast = toast.loading('Uploading avatar...', { 
            position: "top-right",
            toastId: 'avatar-upload'
        });

        // Preview the image immediately
        const reader = new FileReader();
        reader.onloadend = () => {
            setAvatarPreview(reader.result as string);
        };
        reader.readAsDataURL(file);

        // Upload to backend using FormData
        try {
            const formData = new FormData();
            formData.append('profileImage', file);
            formData.append('userId', user?.id || '');
            formData.append('role', user?.role || '');
            
            await axiosPrivate.post(`/api/details/uploadProfileImage`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });

            // Update the user profile state
            setUserProfile(prev => ({
                ...prev,
                profileImage: {
                    filename: file.name,
                    contentType: file.type,
                    imageData: reader.result as string
                }
            }));

            toast.update(uploadToast, {
                render: 'Avatar uploaded successfully!',
                type: 'success',
                isLoading: false,
                autoClose: 2000
            });

            // Show refresh notification after a short delay
            setTimeout(() => {
                toast.info('Refresh the page or login again to see avatar changes everywhere', {
                    position: "top-right",
                    autoClose: 5000,
                    toastId: 'avatar-refresh-notice'
                });
            }, 2500);

        } catch (error) {
            console.error('Error uploading avatar:', error);
            toast.update(uploadToast, {
                render: 'Failed to upload avatar',
                type: 'error',
                isLoading: false,
                autoClose: 3000
            });
            // Reset preview on error
            setAvatarPreview(userProfile.profileImage?.imageData ?? "");
        } finally {
            setIsAvatarUploading(false);
        }
        
        e.target.value = '';
    };

    // Pick a random motivational quote
    const quote = motivationalQuotes[
        (user?.username?.charCodeAt(0) || 1) % motivationalQuotes.length
    ];


    return (
        <div className="min-h-screen bg-background flex flex-col items-center pb-16">
            {/* Hero Header */}
            <div className="w-full bg-card py-6 px-3 sm:px-6 md:px-10 flex flex-col sm:flex-row items-center sm:items-end justify-between gap-4 sm:gap-6 shadow-sm">
                <div className="flex items-center gap-4 sm:gap-6 w-full sm:w-auto">
                    <div className="relative group">
                        {/* Avatar */}
                        <div className="w-24 h-24 sm:w-32 sm:h-32 md:w-36 md:h-36 rounded-full bg-gradient-to-tr from-accent to-primary flex items-center justify-center text-4xl sm:text-5xl md:text-6xl font-extrabold text-[hsl(var(--primary-foreground))] shadow-xl overflow-hidden relative" style={{ minWidth: '6rem' }}>
                            {(
                                avatarPreview && avatarPreview.startsWith('data:')
                            ) ? (
                                <img
                                    src={avatarPreview}
                                    alt="Avatar"
                                    className="w-full h-full object-cover rounded-full"
                                />
                            ) : userProfile.profileImage?.imageData && userProfile.profileImage.imageData.startsWith('data:') ? (
                                <img
                                    src={userProfile.profileImage.imageData}
                                    alt="Avatar"
                                    className="w-full h-full object-cover rounded-full"
                                />
                            ) : (
                                userProfile.username[0]
                            )}
                            
                            {/* Upload Progress Overlay */}
                            {isAvatarUploading && (
                                <div className="absolute inset-0 backdrop-blur-sm rounded-full flex items-center justify-center">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[hsl(var(--primary-foreground))]"></div>
                                </div>
                            )}
                        </div>

                        {/* Camera Icon on Hover - Always visible, not dependent on editing state */}
                        <label className="absolute inset-0 rounded-full cursor-pointer group-hover:bg-[hsl(var(--foreground)/0.3)] transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100"
                            title="Change avatar"
                            tabIndex={0}
                            aria-label="Change avatar"
                        >
                            <CameraIcon className="w-8 h-8 text-[hsl(var(--primary-foreground))] drop-shadow-lg" />
                            <input
                                type="file"
                                accept="image/*"
                                className="hidden"
                                onChange={handleAvatarChange}
                                disabled={isAvatarUploading}
                            />
                        </label>

                        {/* File size hint */}
                        <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            <div className="text-xs text-muted-foreground bg-card px-2 py-1 rounded shadow-sm whitespace-nowrap">
                                Max: 10MB
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col gap-1 sm:gap-2 mt-6 sm:mt-0">
                        <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-primary leading-tight">{userProfile.firstName && userProfile.lastName ? userProfile.firstName + " " + userProfile.lastName : userProfile.username}</h1>
                        <span className="inline-block px-3 py-1 rounded-full bg-accent/10 text-accent text-xs sm:text-sm font-semibold uppercase shadow-sm w-fit">
                            {userProfile.occupation}
                        </span>
                        <span className="text-muted-foreground text-xs sm:text-base italic mt-1">{quote}</span>
                    </div>
                </div>
                <div className="flex gap-2 sm:gap-3 w-full sm:w-auto justify-end mt-4 sm:mt-0">
                    {isEditing ? (
                        <>
                            <button
                                onClick={handleCancel}
                                className="bg-card hover:bg-card/70 text-primary px-4 py-2 rounded-xl font-medium shadow transition-all duration-200"
                            >
                                <XMarkIcon className="w-5 h-5 inline mr-1" /> Cancel
                            </button>
                            <button
                                onClick={handleSave}
                                className="bg-accent hover:bg-accent/70 text-accent-foreground px-4 py-2 rounded-xl font-medium shadow transition-all duration-200"
                            >
                                <CheckIcon className="w-5 h-5 inline mr-1" /> Save
                            </button>
                        </>
                    ) : (
                        <>
                            <div className='block lg:hidden md:hidden px-4 py-2'>
                                <ThemeToggle size="default" />
                            </div>
                            <button
                                onClick={() => setIsEditing(true)}
                                className="bg-accent/10 hover:bg-accent/20 text-accent px-4 py-2 rounded-xl font-medium shadow transition-all duration-200"
                            >
                                <PencilIcon className="w-5 h-5 inline mr-1" /> Edit
                            </button>
                            <button
                                onClick={() => setShowLogoutModal(true)}
                                className="bg-card hover:bg-card/80 text-danger px-4 py-2 rounded-xl font-medium shadow transition-all duration-200"
                                title="Logout"
                            >
                                <ArrowLeftStartOnRectangleIcon className="w-5 h-5 inline mr-1" /> Logout
                            </button>
                        </>
                    )}
                </div>
            </div>

            {/* Main Content */}
            <div className="flex-1 w-full flex flex-col items-center justify-start mt-4">
                <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4 md:px-0 lg:px-0 px-2">
                    <div className="bg-card rounded-xl border border-border p-4 sm:p-6 md:p-8 flex flex-col gap-6 shadow-none">
                        {/* Account Info */}
                        <div>
                            <h2 className="text-base sm:text-lg md:text-xl font-bold text-primary mb-2 sm:mb-3 tracking-tight">Account Info</h2>
                            <div className="flex items-center gap-2 sm:gap-3 mb-2">
                                <EnvelopeIcon className="w-5 h-5 text-accent" />
                                {isEditing ? (
                                    <input
                                        type="email"
                                        value={editedProfile.email}
                                        onChange={e => setEditedProfile({ ...editedProfile, email: e.target.value })}
                                        className="flex-1 border-b border-border py-2 focus:border-accent outline-none text-primary bg-transparent text-sm sm:text-base transition-all duration-200"
                                    />
                                ) : (
                                    <span className="flex-1 text-primary text-sm sm:text-base">{userProfile.email}</span>
                                )}
                            </div>
                            {/* <div className="flex items-center gap-2 sm:gap-3">
                                <PhoneIcon className="w-5 h-5 text-accent" />
                                {isEditing ? (
                                    <input
                                        type="tel"
                                        value={editedProfile.phoneNumber}
                                        onChange={e => setEditedProfile({ ...editedProfile, phoneNumber: e.target.value })}
                                        className="flex-1 border-b border-border py-2 focus:border-accent outline-none text-primary bg-transparent text-sm sm:text-base transition-all duration-200"
                                    />
                                ) : (
                                    <span className="flex-1 text-primary text-sm sm:text-base">{userProfile.phoneNumber}</span>
                                )}
                            </div> */}
                        </div>
                        {/* Institute Info */}
                        {user?.role === 'Student' && !user?.schoolCode ? (
                            <div className="flex flex-col items-center justify-center text-muted-foreground text-sm sm:text-base">
                                <p className="text-sm sm:text-base">You are not associated with any institute.</p>
                            </div>
                        ) : (
                            <div>
                                <h2 className="text-base sm:text-lg md:text-xl font-bold text-primary mb-2 sm:mb-3 tracking-tight">Institute Information</h2>
                                <div className="flex items-center gap-2 sm:gap-3 mb-1">
                                    <AcademicCapIcon className="w-5 h-5 text-accent" />
                                    <span className="text-primary text-sm sm:text-base font-medium">{schoolInfo.name}</span>
                                </div>
                                <div className="flex items-center gap-2 sm:gap-3 mb-1">
                                    <MapPinIcon className="w-5 h-5 text-accent" />
                                    <span className="text-muted-foreground text-sm sm:text-base">{schoolInfo.pincode}</span>
                                </div>
                                {/* <div className="flex items-center gap-2 sm:gap-3 mb-1">
                                <EnvelopeIcon className="w-5 h-5 text-accent" />
                                <span className="text-muted-foreground text-sm sm:text-base">{schoolInfo.phone}</span>
                            </div> */}
                                <div className="flex items-center gap-2 sm:gap-3">
                                    <PhoneIcon className="w-5 h-5 text-accent" />
                                    <span className="text-muted-foreground text-sm sm:text-base">{schoolInfo.phone}</span>
                                </div>
                            </div>
                        )}
                    </div>
                    {/* <div className="bg-card rounded-xl border border-border p-4 sm:p-6 md:p-8 flex flex-col gap-6 shadow-none h-fit">
                        <div>
                            <h2 className="text-base sm:text-lg md:text-xl font-bold text-primary mb-2 sm:mb-3 tracking-tight">Security Settings</h2>
                            <div className="flex flex-col gap-2 sm:gap-3">
                                <div className="flex items-center justify-between py-2 border-b border-border last:border-b-0">
                                    <div className="flex items-center gap-2 sm:gap-3">
                                        <LockClosedIcon className="w-5 h-5 text-accent" />
                                        <span className="text-sm sm:text-base">Change Password</span>
                                    </div>
                                    <button className="text-accent text-xs font-medium hover:underline rounded-lg px-2 py-1 transition-all duration-200">
                                        Change
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div> */}
                </div>
            </div>
            {/* Logout Confirmation Modal */}
            {showLogoutModal && createPortal(
                <div className="fixed inset-0 backdrop-blur-sm z-50 flex items-center justify-center">
                    <div className="bg-card border border-border rounded-2xl shadow-xl p-4 w-full max-w-sm transition-shadow duration-200">
                        <h2 className="text-xl font-bold text-primary mb-2">Confirm Logout</h2>
                        <p className="text-muted-foreground mb-2">Are you sure you want to logout?</p>
                        <div className="flex justify-end gap-2">
                            <button
                                onClick={() => setShowLogoutModal(false)}
                                className="px-3 py-1 rounded-xl bg-card text-primary hover:bg-card/80 transition-all duration-200"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={() => {
                                    setShowLogoutModal(false);
                                    handleLogout();
                                }}
                                className="px-3 py-1 rounded-xl bg-danger text-danger-foreground hover:bg-danger/80 transition-all duration-200"
                            >
                                Logout
                            </button>
                        </div>
                    </div>
                </div>,
                document.body
            )}
            <FeedBackComp id={user?.id} role={user?.role} />
        </div>
    );
};

export default ProfilePage;