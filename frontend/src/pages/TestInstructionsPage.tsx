import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import LoadingSpinner from '../components/LoadingSpinner';
import { fetchWithCache } from '@/utils/cacheUtil';
import {
    ExclamationTriangleIcon,
    CheckCircleIcon,
    InformationCircleIcon,
    DocumentTextIcon
} from '@heroicons/react/24/outline';
import { toast, ToastContainer } from 'react-toastify';

interface TestItem {
    id: string;
    class: ClassDetails[];
    subject: string;
    topics: string;
    date: Date;
    numOfQuestions: number;
    duration: number;
    instructions: string;
    totalMarks: number;
    startTime: string;
    active: boolean;
    teacherId: string;
}

interface ClassDetails {
    className: string;
    classStd: string;
    id: string;
}

const TestInstructionsPage: React.FC = () => {
    const navigate = useNavigate();
    const location = useLocation();
    const testData = location.state;
    const axiosPrivate = useAxiosPrivate();

    const [testDetails, setTestDetails] = useState<TestItem | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [agreed, setAgreed] = useState<boolean>(false);

    // Format test data to TestItem format
    const formatTestData = (test: any): TestItem => ({
        id: test._id,
        subject: test.subject,
        class: Array.isArray(test.class) ? test.class : [test.class],
        topics: Array.isArray(test.topics) ? test.topics.join(', ') : test.topics,
        date: new Date(test.testDate),
        numOfQuestions: test.numberOfQuestions,
        duration: test.duration,
        instructions: test.testInstructions || '',
        totalMarks: test.totalMarks,
        startTime: test.startTime,
        active: test.active,
        teacherId: test.createdBy
    });

    const loadTestDetails = async () => {
        const loadingToast = toast.loading('Loading test details...', { position: 'top-center' });
        try {
            if (!testData?.testId) {
                toast.update(loadingToast, {
                    render: 'No testId found, redirecting to test page for practice test',
                    type: 'info',
                    isLoading: false,
                    autoClose: 1000
                });
                navigate('/test', { state: testData });
                return;
            }

            const endpoint = `/api/test/${testData.testId}`;
            const data = await fetchWithCache(axiosPrivate, endpoint);
            const test = formatTestData(data.test);
            setTestDetails(test);
            toast.update(loadingToast, {
                render: 'Test details loaded!',
                type: 'success',
                isLoading: false,
                autoClose: 1000
            });
        } catch (error) {
            toast.update(loadingToast, {
                render: 'Failed to load test details. Please try again.',
                type: 'error',
                isLoading: false,
                autoClose: 2000
            });
            navigate('/student-dashboard');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        loadTestDetails();
    }, []);

    const handleStartTest = () => {
        if (!agreed) {
            toast.warning("Please agree to the instructions before starting the test.");
            return;
        }

        const startToast = toast.loading("Starting test...", { autoClose: false });
        setTimeout(() => {
            toast.dismiss(startToast);
            navigate("/test", {
                state: {
                    testId: testData.testId,
                    testMode: testData.testMode || "scheduled"
                }
            });
        }, 2000);
    };

    const handleGoBack = () => {
        navigate('/student-dashboard');
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center h-screen">
                <LoadingSpinner />
            </div>
        );
    }

    if (!testDetails) {
        return (
            <div className="flex items-center justify-center h-screen">
                <div className="text-center">
                    <p className="text-lg text-foreground/70">Test not found</p>
                    <button
                        onClick={handleGoBack}
                        className="mt-4 px-4 py-2 bg-accent text-white rounded-md hover:bg-accent/90"
                    >
                        Go Back to Dashboard
                    </button>
                </div>
            </div>
        );
    }

    const formatDateTime = (date: Date, startTime: string) => {
        const dateStr = date.toLocaleDateString('en-IN', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
        return `${dateStr} at ${startTime}`;
    };
    const theme = localStorage.getItem('theme') || 'light';

    return (
        <div className="min-h-screen bg-background p-4">
            <ToastContainer
                position="top-right"
                autoClose={2000}
                hideProgressBar={true}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme={theme === 'dark' ? 'dark' : 'light'}
            />

            <div className="max-w-7xl mx-auto">
                {/* Header */}
                <div className="bg-card border border-border rounded-lg shadow-sm p-6 mb-4">
                    <div className="flex items-center gap-3">
                        <InformationCircleIcon className="h-8 w-8 text-accent" />
                        <div>
                            <h1 className="text-2xl font-bold text-primary">Test Instructions</h1>
                            <p className="text-foreground/70">Please read carefully before starting your test</p>
                        </div>
                    </div>
                </div>

                {/* Three Column Layout */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                    {/* Left Column - General Guidelines */}
                    <div className="bg-card border border-border rounded-lg shadow-sm p-6">
                        <h2 className="text-xl font-semibold text-primary mb-4 flex items-center gap-2">
                            <CheckCircleIcon className="h-5 w-5" />
                            General Guidelines
                        </h2>

                        <div className="space-y-4 text-sm text-foreground/80">
                            <div className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                                <p>Ensure you have a stable internet connection throughout the test.</p>
                            </div>
                            <div className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                                <p>Do not refresh the page or navigate away during the test.</p>
                            </div>
                            <div className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                                <p>The test will auto-submit when time expires.</p>
                            </div>
                            <div className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                                <p>You can navigate between questions using the question navigator.</p>
                            </div>
                            <div className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                                <p>Make sure to mark your answers before moving to the next question.</p>
                            </div>
                            <div className="flex items-start gap-2">
                                <div className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></div>
                                <p>Right-click is disabled during the test for security purposes.</p>
                            </div>
                        </div>
                    </div>

                    {/* Middle Column - Always Present */}
                    <div className="bg-card border border-border rounded-lg shadow-sm p-6">
                        {testDetails.instructions ? (
                            <>
                                <h2 className="text-xl font-semibold text-primary mb-4 flex items-center gap-2">
                                    <ExclamationTriangleIcon className="h-5 w-5" />
                                    Specific Test Instructions
                                </h2>
                                <div className="bg-accent/5 border border-accent/20 rounded-lg p-4">
                                    <p className="text-primary whitespace-pre-wrap">{testDetails.instructions}</p>
                                </div>
                            </>
                        ) : (
                            <>
                                <h2 className="text-xl font-semibold text-primary mb-4 flex items-center gap-2">
                                    <CheckCircleIcon className="h-5 w-5" />
                                    Test Reminders
                                </h2>
                                <div className="space-y-4 text-sm text-foreground/80">
                                    <div className="bg-accent/5 border border-accent/20 rounded-lg p-4">
                                        <p className="text-primary font-medium mb-2">📝 Before You Begin:</p>
                                        <ul className="space-y-2 text-sm">
                                            <li>• Have a pen and paper ready for calculations</li>
                                            <li>• Ensure your device is fully charged</li>
                                            <li>• Close all unnecessary applications</li>
                                            <li>• Find a quiet, well-lit environment</li>
                                        </ul>
                                    </div>
                                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <p className="text-blue-800 font-medium mb-2">💡 Pro Tips:</p>
                                        <ul className="space-y-2 text-sm text-blue-700">
                                            <li>• Read each question carefully before answering</li>
                                            <li>• Use the question navigator to review your answers</li>
                                            <li>• Don't spend too much time on difficult questions</li>
                                        </ul>
                                    </div>
                                </div>
                            </>
                        )}
                    </div>

                    {/* Right Column - Test Information */}
                    <div className="bg-card border border-border rounded-lg shadow-sm p-6">
                        <h2 className="text-xl font-semibold text-primary mb-4 flex items-center gap-2">
                            <DocumentTextIcon className="h-5 w-5" />
                            Test Information
                        </h2>

                        <div className="space-y-3">
                            <div>
                                <label className="block text-sm font-medium text-foreground/70">Subject</label>
                                <p className="text-base font-medium text-primary">{testDetails.subject}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-foreground/70">Topics</label>
                                <p className="text-base text-primary">{testDetails.topics}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-foreground/70">Class</label>
                                <p className="text-base text-primary">
                                    {testDetails.class?.[0]?.classStd || 'N/A'}
                                </p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-foreground/70">Duration</label>
                                <p className="text-base font-medium text-primary flex items-center gap-1">
                                    {testDetails.duration} minutes
                                </p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-foreground/70">Questions</label>
                                <p className="text-base font-medium text-primary">{testDetails.numOfQuestions}</p>
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-foreground/70">Total Marks</label>
                                <p className="text-base font-medium text-primary">{testDetails.totalMarks}</p>
                            </div>
                        </div>

                        <div className="mt-4 pt-4 border-t border-border">
                            <label className="block text-sm font-medium text-foreground/70 mb-2">Scheduled Time</label>
                            <p className="text-base text-primary">
                                {formatDateTime(testDetails.date, testDetails.startTime)}
                            </p>
                        </div>
                    </div>
                </div>

                {/* Bottom Section - Agreement and Actions */}
                <div className="mt-4 bg-card border border-border rounded-lg shadow-sm p-6">
                    <div className="max-w-2xl mx-auto">
                        <label className="flex items-start gap-3 cursor-pointer mb-6">
                            <input
                                type="checkbox"
                                checked={agreed}
                                onChange={(e) => setAgreed(e.target.checked)}
                                className="mt-1 h-4 w-4 text-accent focus:ring-accent border-border rounded"
                            />
                            <span className="text-sm text-foreground/80">
                                I have read and understood all the instructions above. I agree to follow the test guidelines and understand that any violation may result in test cancellation.
                            </span>
                        </label>

                        <div className="flex gap-4 justify-center">
                            <button
                                onClick={handleGoBack}
                                className="px-8 py-3 border border-border rounded-md text-foreground hover:bg-muted transition-colors"
                            >
                                Go Back
                            </button>
                            <button
                                onClick={handleStartTest}
                                disabled={!agreed}
                                className={`px-8 py-3 rounded-md font-medium transition-colors ${agreed
                                        ? 'bg-accent text-white hover:bg-accent/90'
                                        : 'bg-muted text-muted-foreground cursor-not-allowed'
                                    }`}
                            >
                                Start Test
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default TestInstructionsPage;
