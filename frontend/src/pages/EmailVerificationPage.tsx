import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';
import Background from '../components/Background';
import AegisScholarLogoWithoutText from '../assets/AegisScholarLogoIcon';
import { CheckBadgeIcon, XCircleIcon, ExclamationTriangleIcon, ShieldCheckIcon, ArrowRightEndOnRectangleIcon } from '@heroicons/react/24/outline';
import { fetchWithCache } from '@/utils/cacheUtil';

const useQuery = () => {
    return new URLSearchParams(useLocation().search);
};

const EmailVerificationPage: React.FC = () => {
    const query = useQuery();
    const token = query.get('token');
    const navigate = useNavigate();
    const [verificationStatus, setVerificationStatus] = useState({
        success: false,
        message: '',
        loading: true
    });
    const axiosPrivate = useAxiosPrivate();
    const [resendEmail, setResendEmail] = useState('');
    const [resendTimer, setResendTimer] = useState<number>(30);
    const [canResend, setCanResend] = useState<boolean>(false);

    useEffect(() => {
        let timer: ReturnType<typeof setTimeout>;

        if (!verificationStatus.success && !verificationStatus.loading && resendTimer > 0) {
            timer = setInterval(() => {
                setResendTimer(prev => {
                    if (prev <= 1) {
                        setCanResend(true);
                        return 0;
                    }
                    return prev - 1;
                });
            }, 1000);
        }

        return () => clearInterval(timer);
    }, [verificationStatus, resendTimer]);

    useEffect(() => {
        const verifyEmail = async () => {
            if (!token) {
                setVerificationStatus({
                    success: false,
                    message: 'Verification token is missing or has expired',
                    loading: false
                });
                return;
            }

            try {
                const data = await fetchWithCache(axiosPrivate, `/api/auth/verify-email?token=${token}`);
                setVerificationStatus({
                    success: true,
                    message: data.message || 'Email verified successfully!',
                    loading: false
                });

                // If there's user email in the response
                if (data.email) {
                    setResendEmail(data.email);
                }
            } catch (error: any) {
                setVerificationStatus({
                    success: false,
                    message: error.response?.data?.message || 'Verification failed. Please try again.',
                    loading: false
                });

                // If there's user email in the error response
                if (error.response?.data?.email) {
                    setResendEmail(error.response.data.email);
                }
            }
        };

        verifyEmail();
    }, [token, axiosPrivate]);

    const redirectToLogin = () => navigate('/login');
    const handleResendEmail = async () => {
        if (!canResend || !resendEmail) return;

        try {
            await axiosPrivate.post('/api/auth/resend-verification-email', { email: resendEmail });

            // Reset timer after resend
            setResendTimer(30);
            setCanResend(false);
        } catch (err) {
            console.error('Failed to resend verification email');
        }
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-background px-4 sm:px-6 lg:px-8">
            <div className="w-full max-w-md mx-auto">
                <div className="backdrop-blur-sm bg-card border border-border rounded-2xl shadow-lg p-6 sm:p-8">
                    <div className="flex justify-center mb-6">
                        <AegisScholarLogoWithoutText
                            className="w-16 h-16"
                            style={{ fill: 'var(--color-accent)' }}
                        />
                    </div>

                    {verificationStatus.loading ? (
                        <div className="text-center space-y-4">
                            <div className="mx-auto animate-spin w-16 h-16 border-4 border-accent border-t-transparent rounded-full"></div>
                            <h1 className="text-xl sm:text-2xl font-semibold text-primary">Verifying your email...</h1>
                            <p className="text-muted-foreground">This may take a moment</p>
                        </div>
                    ) : verificationStatus.success ? (
                        <div className="text-center space-y-6">
                            <h1 className="text-2xl sm:text-3xl font-bold text-primary">Verification Complete!</h1>
                            <div className="mx-auto bg-success-light rounded-full w-20 h-20 flex items-center justify-center">
                                <CheckBadgeIcon className="text-green-500" width={40} height={40} />
                            </div>

                            <div className="bg-success-light p-4 rounded-lg border border-success-light">
                                <p className="text-primary">{verificationStatus.message}</p>
                            </div>

                            <div className="flex items-center justify-center space-x-2 text-muted-foreground">
                                <ShieldCheckIcon className="h-5 w-5 text-accent" />
                                <span>Your account is now active</span>
                            </div>

                            <button
                                onClick={redirectToLogin}
                                className="w-full hover:cursor-pointer px-6 py-3 bg-accent text-white rounded-full hover:bg-accent-dark transition-all duration-300 flex items-center justify-center space-x-2"
                            >
                                <ArrowRightEndOnRectangleIcon className="h-5 w-5" />
                                <span>Proceed to Login</span>
                            </button>
                        </div>
                    ) : (
                        <div className="text-center space-y-6">
                            <div className="mx-auto bg-danger-light rounded-full w-20 h-20 flex items-center justify-center">
                                <XCircleIcon className="text-danger" width={40} height={40} />
                            </div>

                            <h1 className="text-2xl sm:text-3xl font-bold text-primary">Verification Failed</h1>

                            <div className="bg-danger-light p-4 rounded-lg border border-danger-light">
                                <p className="text-primary">{verificationStatus.message}</p>
                            </div>

                            <div className="flex items-center justify-center space-x-2 text-muted-foreground">
                                <ExclamationTriangleIcon className="h-5 w-5 text-accent" />
                                <span>Link may have expired or is invalid</span>
                            </div>

                            <div className="space-y-4">
                                <button
                                    onClick={handleResendEmail}
                                    disabled={!canResend}
                                    className={`w-full px-6 py-3 rounded-full flex items-center justify-center space-x-2 transition-all duration-300 ${canResend
                                            ? 'bg-accent text-white hover:bg-accent-dark'
                                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                                        }`}
                                >
                                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                    </svg>
                                    <span>
                                        {canResend
                                            ? 'Resend Verification Email'
                                            : `Resend available in ${resendTimer}s`
                                        }
                                    </span>
                                </button>

                                <button
                                    onClick={() => navigate('/login')}
                                    className="w-full px-6 py-3 bg-card border border-border text-primary rounded-full hover:bg-secondary-100 transition-all duration-300"
                                >
                                    Return to Login
                                </button>
                            </div>

                            <p className="text-xs text-muted-foreground">
                                If you continue to have problems, please contact{' '}
                                <a href="mailto:<EMAIL>" className="text-accent hover:underline">
                                    <EMAIL>
                                </a>
                            </p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default EmailVerificationPage;