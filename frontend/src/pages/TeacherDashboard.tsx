import { useState, useEffect } from 'react';
import { useUser } from '../contexts/userContext';
import ClassList from '../components/ClassList';
import ViewUpcomingTest from '../components/ViewUpcomingTest';
import TeacherTestHistory from '../components/TeacherTestHistory';
import AegisScholarLogoWithoutText from '@/assets/AegisScholarLogoIcon';
import FeedBackComp from '@/components/Feedback';
import OnboardingTour, {TourStep} from '@/components/OnboardingTour';

const steps: TourStep[] = [
  {
    target: '.class-list',
    title: 'Manage Your Classes',
    content: 'This is where you can view and manage all your classes. Click on any class card to see student details, create assignments, and track progress.',
    placement: 'bottom',
    disableBeacon: true,
  },
  {
    target: '.upcoming-tests',
    title: 'Upcoming Tests & Assessments',
    content: 'Stay on top of your schedule! This panel shows all your upcoming tests and assessments. You can quickly see what\'s coming up, edit test details, or create new assessments by clicking the "+" button.',
    placement: 'right',
  },
  {
    target: '.test-history',
    title: 'Test Results & Analytics',
    content: 'Review your teaching impact here! Access detailed results from past tests, view student performance analytics, and identify areas where your classes might need additional support.',
    placement: 'right'
  }
];

const TeacherDashboard = () => {
  const { user, setUser } = useUser();

  return (
    <div className="w-full flex lg:h-screen lg:overflow-hidden min-h-screen overflow-x-hidden">
      {user?.id && (<OnboardingTour
          steps={steps}
          user={
            user && user.id && typeof user.role === 'string'
                ? {...user, id: String(user.id), role: user.role}
                : null
          }
          tourKey="teacher-dashboard"
          onTourComplete={(completed, skipped) => {
            console.log('Teacher tour completed:', {completed, skipped});
          }}
          startDelay={500}
          continuous={true}
          spotlightPadding={8}
      />)}

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto flex flex-col gap-4 p-4 pb-6 mb-12">
        {/* Header - Fixed height */}
        <div className="flex items-center gap-3 p-2">
          <div className='flex flex-row items-start'>
            <AegisScholarLogoWithoutText
              className="w-12 h-12 lg:hidden mr-2"
              style={{ fill: 'var(--color-accent)' }}
            />
            <div className="flex flex-col">
              <small className="text-sm sm:text-base font-[Space_Grotesk] text-muted-foreground">Hello</small>
              <h1 className="text-xl lg:text-2xl font-[Space_Grotesk] font-bold text-primary">{user?.username}!</h1>
            </div>
          </div>
        </div>

        {/* Main Content - Reordered for mobile */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-4 min-h-0">
          {/* Classes Section - Moves below stats on mobile */}
          <div className="order-1 lg:order-2 lg:col-span-3 flex flex-col">
            <div className="flex-1 overflow-auto class-list">
              <ClassList />
            </div>
          </div>

          {/* Left Column - Moves below classes on mobile */}
          <div className="order-2 lg:order-1 col-span-1 flex flex-col gap-4 min-h-0">
            {/* Scheduled Tests Section */}
            <div className="overflow-hidden h-[300px] md:h-[350px] lg:h-full lg:border-r lg:pr-4 upcoming-tests">
              <ViewUpcomingTest />
            </div>

            {/* Test History Section */}
            <div className="overflow-hidden h-[300px] md:h-[350px] lg:h-full lg:border-r lg:pr-4 test-history">
              <TeacherTestHistory />
            </div>
          </div>
        </div>
      </div>
      <FeedBackComp id={user?.id} role={user?.role}/>
    </div>
  );

};

export default TeacherDashboard;