import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent } from '@testing-library/react';
import { UserProvider, useUser } from '../userContext';

// Mock component that uses the context
const TestConsumer = () => {
  const { user, setUser } = useUser();
  
  const login = (userData: any) => {
    setUser(userData);
  };
  
  const logout = () => {
    setUser(null);
    localStorage.removeItem('authToken');
  };
  
  const isLoggedIn = !!user;
  
  return (
    <div>
      <div data-testid="user-data">{JSON.stringify(user)}</div>
      <div data-testid="login-status">{isLoggedIn ? 'Logged In' : 'Logged Out'}</div>
      <button onClick={() => login({ id: '123', username: 'testuser', email: '<EMAIL>', usertype: 'Student' })}>
        Login
      </button>
      <button onClick={logout}>Logout</button>
    </div>
  );
};

// Wrapper component with provider
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  return <UserProvider>{children}</UserProvider>;
};

describe('UserContext', () => {
  const setItemMock = vi.fn();
  const removeItemMock = vi.fn();
  
  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock sessionStorage
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: vi.fn().mockImplementation((key) => {
          if (key === 'email') {
            return null; // Start with no user by default
          }
          return null;
        }),
        setItem: setItemMock,
        removeItem: removeItemMock
      },
      writable: true
    });
  });

  it('provides default values when no user is logged in', () => {
    render(
      <TestWrapper>
        <TestConsumer />
      </TestWrapper>
    );
    
    // Check default state (no user)
    expect(screen.getByTestId('user-data').textContent).toBe('null');
    expect(screen.getByTestId('login-status').textContent).toBe('Logged Out');
  });

  it('loads user from sessionStorage on initial render if available', () => {
    // Mock a logged in user in sessionStorage
    window.sessionStorage.getItem = vi.fn().mockImplementation((key) => {
      if (key === 'email') {
        return '<EMAIL>';
      }
      if (key === 'role') {
        return 'Teacher';
      }
      return null;
    });
    
    render(
      <TestWrapper>
        <TestConsumer />
      </TestWrapper>
    );
    
    // User should be loaded from sessionStorage
    expect(screen.getByTestId('user-data').textContent).toContain('<EMAIL>');
    expect(screen.getByTestId('login-status').textContent).toBe('Logged In');
  });

  it('updates context and sessionStorage on login', () => {
    render(
      <TestWrapper>
        <TestConsumer />
      </TestWrapper>
    );
    
    // Verify initial state
    expect(screen.getByTestId('login-status').textContent).toBe('Logged Out');
    
    // Trigger login
    const loginButton = screen.getByText('Login');
    fireEvent.click(loginButton);
    
    // Verify state was updated
    expect(screen.getByTestId('user-data').textContent).toContain('testuser');
    expect(screen.getByTestId('login-status').textContent).toBe('Logged In');
    
    // Verify sessionStorage was updated
    expect(setItemMock).toHaveBeenCalledWith('email', '<EMAIL>');
  });

  it('clears context and sessionStorage on logout', () => {
    // Start with logged in user in context
    window.sessionStorage.getItem = vi.fn().mockImplementation((key) => {
      if (key === 'email') {
        return '<EMAIL>';
      }
      if (key === 'role') {
        return 'Student';
      }
      return null;
    });
    
    render(
      <TestWrapper>
        <TestConsumer />
      </TestWrapper>
    );
    
    // Verify user is logged in
    expect(screen.getByTestId('login-status').textContent).toBe('Logged In');
    
    // Trigger logout
    const logoutButton = screen.getByText('Logout');
    fireEvent.click(logoutButton);
    
    // Verify state was updated
    expect(screen.getByTestId('user-data').textContent).toBe('null');
    expect(screen.getByTestId('login-status').textContent).toBe('Logged Out');
    
    // Verify localStorage was cleared for authentication token
    expect(removeItemMock).toHaveBeenCalledWith('email');
    expect(removeItemMock).toHaveBeenCalledWith('role');
  });
}); 