import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { useUser } from './userContext';
import { useLocation } from 'react-router-dom';
import { useAxiosPrivate } from '../hooks/useAxiosPrivate';

// Analytics service for API calls
class AnalyticsService {
    private axiosInstance: any = null;
    private sessionId: string | null = null;
    private isEnabled = true;
    private queue: any[] = [];
    private isOnline = navigator.onLine;

    constructor(axiosInstance?: any) {
        this.axiosInstance = axiosInstance;
        this.sessionId = this.generateSessionId();
        this.setupEventListeners();
    }

    setAxiosInstance(axiosInstance: any) {
        console.log('Analytics: Setting axios instance', !!axiosInstance);
        this.axiosInstance = axiosInstance;
    }

    isReady(): boolean {
        return !!this.axiosInstance && this.isEnabled;
    }

    private generateSessionId(): string {
        return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
    }

    private setupEventListeners() {
        // Handle online/offline status
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.flushQueue();
        });
        
        window.addEventListener('offline', () => {
            this.isOnline = false;
        });

        // Handle page unload
        window.addEventListener('beforeunload', () => {
            this.endSession();
        });

        // Handle visibility change (tab switching)
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.trackInteraction({
                    interactionType: 'navigation',
                    page: window.location.pathname,
                    elementType: 'tab',
                    metadata: { action: 'tab_hidden' }
                });
            } else {
                this.trackInteraction({
                    interactionType: 'navigation',
                    page: window.location.pathname,
                    elementType: 'tab',
                    metadata: { action: 'tab_visible' }
                });
            }
        });
    }

    private async makeRequest(endpoint: string, data: any, method = 'POST') {
        if (!this.isEnabled) {
            console.log('Analytics: Request skipped - analytics disabled');
            return;
        }

        if (!this.axiosInstance) {
            console.warn('Analytics: No axios instance available, queueing request');
            this.queue.push({ endpoint, data, method });
            return;
        }

        console.log(`Analytics: Making ${method} request to /api/analytics${endpoint}`, data);

        try {
            const response = await this.axiosInstance({
                method,
                url: `/api/analytics${endpoint}`,
                data,
            });

            console.log('Analytics: Request successful:', response.data);
            return response.data;
        } catch (error: any) {
            console.warn('Analytics request failed:', error);
            console.warn('Analytics error details:', {
                message: error.message,
                status: error.response?.status,
                statusText: error.response?.statusText,
                data: error.response?.data
            });

            // Queue the request for retry if offline, network error, or auth error
            if (!this.isOnline || error.code === 'NETWORK_ERROR' || error.response?.status === 401 || error.response?.status === 403) {
                console.log('Analytics: Queueing request for retry due to:', error.response?.status || error.code || 'network error');
                this.queue.push({ endpoint, data, method });
            }
        }
    }

    flushQueue() {
        console.log(`Analytics: Flushing ${this.queue.length} queued requests`);
        while (this.queue.length > 0) {
            const { endpoint, data, method } = this.queue.shift();
            this.makeRequest(endpoint, data, method);
        }
    }

    async startSession(userId: string, userType: string) {
        if (!userId || !userType) {
            console.log('Analytics: Cannot start session - missing userId or userType', { userId, userType });
            return;
        }

        console.log('Analytics: Starting session for user', { userId, userType, sessionId: this.sessionId });

        const sessionData = {
            userId,
            userType,
            sessionId: this.sessionId,
            userAgent: navigator.userAgent,
            screenResolution: `${screen.width}x${screen.height}`,
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            location: {} // Could be enhanced with geolocation if permitted
        };

        console.log('Analytics: Session data being sent:', sessionData);
        const result = await this.makeRequest('/session/start', sessionData);
        console.log('Analytics: Session start result:', result);
        return result;
    }

    async endSession() {
        if (!this.sessionId) return;

        return this.makeRequest('/session/end', { sessionId: this.sessionId });
    }

    async trackInteraction(interactionData: any) {
        if (!this.sessionId) return;

        const data = {
            sessionId: this.sessionId,
            timestamp: new Date().toISOString(),
            ...interactionData
        };

        return this.makeRequest('/interaction', data);
    }

    setEnabled(enabled: boolean) {
        this.isEnabled = enabled;
    }

    getSessionId() {
        return this.sessionId;
    }

    getDebugInfo() {
        return {
            sessionId: this.sessionId,
            isEnabled: this.isEnabled,
            hasAxiosInstance: !!this.axiosInstance,
            queueLength: this.queue.length,
            isOnline: this.isOnline
        };
    }
}

// Analytics context interface
interface AnalyticsContextType {
    trackPageView: (page: string, previousPage?: string, loadTime?: number) => void;
    trackButtonClick: (elementId: string, elementText?: string, feature?: string, metadata?: any) => void;
    trackFormSubmit: (formId: string, formData?: any, feature?: string) => void;
    trackFeatureUsage: (feature: string, action?: string, metadata?: any) => void;
    trackNavigation: (from: string, to: string, method?: string) => void;
    trackSearch: (query: string, results?: number, feature?: string) => void;
    trackError: (error: string, page: string, feature?: string) => void;
    setAnalyticsEnabled: (enabled: boolean) => void;
    isEnabled: boolean;
    getDebugInfo: () => any;
}

const AnalyticsContext = createContext<AnalyticsContextType | undefined>(undefined);

// Analytics provider component
export const AnalyticsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const { user } = useUser();
    const location = useLocation();
    const axiosPrivate = useAxiosPrivate();
    const analyticsService = useRef(new AnalyticsService());
    const [isEnabled, setIsEnabled] = useState(() => {
        const stored = localStorage.getItem('analytics_enabled');
        return stored !== null ? stored === 'true' : true; // Default to true if not set
    });
    const previousPage = useRef<string>('');
    const pageLoadTime = useRef<number>(Date.now());

    // Set axios instance when available and flush queue
    useEffect(() => {
        if (axiosPrivate) {
            console.log('Analytics: Setting axios instance and flushing queue');
            analyticsService.current.setAxiosInstance(axiosPrivate);
            analyticsService.current.flushQueue();
        }
    }, [axiosPrivate]);

    // Initialize session when user is available
    useEffect(() => {
        console.log('Analytics: useEffect triggered', {
            user: user ? { id: user.id, role: user.role } : null,
            isEnabled
        });

        if (user && user.id && user.role && isEnabled) {
            console.log('Analytics: Starting session for user:', user.id, user.role);
            analyticsService.current.startSession(user.id, user.role);
        } else {
            console.log('Analytics: Session not started - missing requirements', {
                hasUser: !!user,
                hasUserId: !!(user?.id),
                hasUserRole: !!(user?.role),
                isEnabled
            });
        }
    }, [user, isEnabled]);

    // Track page views on route changes
    useEffect(() => {
        if (isEnabled && user) {
            const currentPage = location.pathname;
            const loadTime = Date.now() - pageLoadTime.current;
            
            trackPageView(currentPage, previousPage.current, loadTime);
            previousPage.current = currentPage;
            pageLoadTime.current = Date.now();
        }
    }, [location.pathname, isEnabled, user]);

    // Set analytics enabled/disabled
    useEffect(() => {
        analyticsService.current.setEnabled(isEnabled);
    }, [isEnabled]);

    const trackPageView = (page: string, prevPage?: string, loadTime?: number) => {
        if (!user || !isEnabled) {
            console.log('Analytics: trackPageView skipped', { hasUser: !!user, isEnabled });
            return;
        }

        console.log('Analytics: trackPageView called', { page, prevPage, loadTime, userId: user.id });
        analyticsService.current.trackInteraction({
            userId: user.id,
            userType: user.role,
            interactionType: 'page_view',
            page,
            previousPage: prevPage,
            loadTime,
            feature: getFeatureFromPage(page)
        });
    };

    const trackButtonClick = (elementId: string, elementText?: string, feature?: string, metadata?: any) => {
        if (!user || !isEnabled) {
            console.log('Analytics: trackButtonClick skipped', { hasUser: !!user, isEnabled });
            return;
        }

        console.log('Analytics: trackButtonClick called', { elementId, elementText, feature, userId: user.id });
        analyticsService.current.trackInteraction({
            userId: user.id,
            userType: user.role,
            interactionType: 'button_click',
            page: location.pathname,
            elementType: 'button',
            elementId,
            elementText,
            feature: feature || getFeatureFromPage(location.pathname),
            metadata
        });
    };

    const trackFormSubmit = (formId: string, formData?: any, feature?: string) => {
        if (!user || !isEnabled) return;

        analyticsService.current.trackInteraction({
            userId: user.id,
            userType: user.role,
            interactionType: 'form_submit',
            page: location.pathname,
            elementType: 'form',
            elementId: formId,
            feature: feature || getFeatureFromPage(location.pathname),
            metadata: { formData }
        });
    };

    const trackFeatureUsage = (feature: string, action?: string, metadata?: any) => {
        if (!user || !isEnabled) return;

        analyticsService.current.trackInteraction({
            userId: user.id,
            userType: user.role,
            interactionType: 'feature_usage',
            page: location.pathname,
            feature,
            metadata: { action, ...metadata }
        });
    };

    const trackNavigation = (from: string, to: string, method?: string) => {
        if (!user || !isEnabled) return;

        analyticsService.current.trackInteraction({
            userId: user.id,
            userType: user.role,
            interactionType: 'navigation',
            page: to,
            previousPage: from,
            metadata: { method }
        });
    };

    const trackSearch = (query: string, results?: number, feature?: string) => {
        if (!user || !isEnabled) return;

        analyticsService.current.trackInteraction({
            userId: user.id,
            userType: user.role,
            interactionType: 'search',
            page: location.pathname,
            feature: feature || getFeatureFromPage(location.pathname),
            metadata: { query, results }
        });
    };

    const trackError = (error: string, page: string, feature?: string) => {
        if (!user || !isEnabled) return;

        analyticsService.current.trackInteraction({
            userId: user.id,
            userType: user.role,
            interactionType: 'error',
            page,
            feature: feature || getFeatureFromPage(page),
            metadata: { error }
        });
    };

    const setAnalyticsEnabled = (enabled: boolean) => {
        setIsEnabled(enabled);
        localStorage.setItem('analytics_enabled', enabled.toString());
    };

    const getDebugInfo = () => {
        return {
            ...analyticsService.current.getDebugInfo(),
            user: user ? { id: user.id, role: user.role } : null,
            location: location.pathname,
            axiosPrivateAvailable: !!axiosPrivate
        };
    };

    // Helper function to determine feature from page path
    const getFeatureFromPage = (page: string): string => {
        if (page.includes('aegis-ai')) return 'aegis_ai';
        if (page.includes('test') || page.includes('practice')) return 'test_taking';
        if (page.includes('dashboard')) return 'dashboard';
        if (page.includes('analytics')) return 'analytics';
        if (page.includes('profile')) return 'profile';
        if (page.includes('class')) return 'class_management';
        if (page.includes('grading')) return 'grading';
        if (page.includes('schedule')) return 'schedule_test';
        if (page.includes('subject')) return 'subject_details';
        if (page.includes('feedback')) return 'feedback';
        return 'general';
    };

    const contextValue: AnalyticsContextType = {
        trackPageView,
        trackButtonClick,
        trackFormSubmit,
        trackFeatureUsage,
        trackNavigation,
        trackSearch,
        trackError,
        setAnalyticsEnabled,
        isEnabled,
        getDebugInfo
    };

    return (
        <AnalyticsContext.Provider value={contextValue}>
            {children}
        </AnalyticsContext.Provider>
    );
};

// Hook to use analytics
export const useAnalytics = () => {
    const context = useContext(AnalyticsContext);
    if (context === undefined) {
        throw new Error('useAnalytics must be used within an AnalyticsProvider');
    }
    return context;
};
