import axios from 'axios';

const BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://api.aegisscholar.com' 
  : 'http://localhost:8080';

export const axiosDefault = axios.create(
  { 
    baseURL: BASE_URL,
    withCredentials: true,
  }
);

export const axiosPrivate = axios.create({ 
  baseURL: BASE_URL,
  headers: {'Content-Type': 'application/json'},
  withCredentials: true
});

axiosPrivate.interceptors.request.use((config) => {
  if (config.data instanceof FormData) {
      // Explicitly remove any content-type header for FormData
      delete config.headers['Content-Type'];
      // Or if needed, explicitly set it (though not usually necessary):
      // config.headers['Content-Type'] = 'multipart/form-data';
  } else {
      config.headers['Content-Type'] = 'application/json';
  }
  return config;
});
