import { Node, NodeProps } from "@xyflow/react";
import { BaseNode } from "@/shadcn/components/base-node";

type LabeledGroupNode = Node<{
  label: string;
}>;

export function LabeledGroupNode({ data, selected }: NodeProps<LabeledGroupNode>) {
  return (
    <BaseNode
      selected={selected}
      className="bg-card/70 h-full rounded-sm overflow-hidden p-0 border border-border"
    >
      {data.label && (
        <div className="bg-secondary w-fit p-2 text-xs rounded-br-sm text-secondary-foreground border border-border">
          {data.label}
        </div>
      )}
    </BaseNode>
  );
}

LabeledGroupNode.displayName = "LabeledGroupNode";
