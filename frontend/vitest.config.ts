import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import { fileURLToPath } from 'url';
import path from 'path';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: 'jsdom',
    setupFiles: ['./src/setupTests.ts'],
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
    },
    include: [
      'src/__tests__/**/*.{test,spec}.{ts,tsx}',
      'src/components/__tests__/**/*.{test,spec}.{ts,tsx}',
      'src/contexts/__tests__/**/*.{test,spec}.{ts,tsx}',
      'src/hooks/__tests__/**/*.{test,spec}.{ts,tsx}',
      'src/utils/__tests__/**/*.{test,spec}.{ts,tsx}',
      'src/api/__tests__/**/*.{test,spec}.{ts,tsx}',
      'src/pages/__tests__/**/*.{test,spec}.{ts,tsx}'
    ],
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
}); 