import jwt from 'jsonwebtoken';
import { config } from 'dotenv';

export const verifyJWT = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  if (!authHeader) {
    console.error(`[ERROR] authorization header not found in the request\n`);
    return res.sendStatus(401);
  }

  // console.error(authHeader);
  const token = authHeader.split(' ')[1];

  jwt.verify(token, process.env.ACCESS_TOKEN_SECRET, (err, decoded) => {
    if (err) {
      console.error(`[ERROR] jwt verification failed because: ${err.message}`)
      return res.sendStatus(403);
    }
    req.id = decoded.id;
    next();
  });
}

