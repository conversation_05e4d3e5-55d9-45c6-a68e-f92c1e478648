import CreditService from '../services/creditService.js';
import Student from '../models/Student.js';
import Teacher from '../models/Teacher.js';

/**
 * Middleware to check if user has sufficient credits for a specific operation
 * @param {number} requiredCredits - Number of credits required for the operation
 * @param {string} feature - Feature name for logging purposes
 */
export const checkCredits = (requiredCredits = 1, feature = 'AEGIS_GRADER') => {
    return async (req, res, next) => {
        try {
            const userId = req.id; // From JWT verification middleware
            
            if (!userId) {
                return res.status(401).json({
                    success: false,
                    message: 'User authentication required',
                    code: 'AUTH_REQUIRED'
                });
            }

            // Determine user type by checking both collections
            let userType = null;
            let user = await Student.findById(userId).select('role credits');
            
            if (user) {
                userType = 'Student';
            } else {
                user = await Teacher.findById(userId).select('role credits');
                if (user) {
                    userType = 'Teacher';
                }
            }

            if (!user) {
                return res.status(404).json({
                    success: false,
                    message: 'User not found',
                    code: 'USER_NOT_FOUND'
                });
            }

            // Check if user has sufficient credits
            const currentBalance = user.credits?.balance || 0;
            
            if (currentBalance < requiredCredits) {
                return res.status(402).json({
                    success: false,
                    message: 'Insufficient credits',
                    code: 'INSUFFICIENT_CREDITS',
                    data: {
                        currentBalance,
                        requiredCredits,
                        shortfall: requiredCredits - currentBalance,
                        feature
                    }
                });
            }

            // Add user info to request for use in subsequent middleware/controllers
            req.user = {
                id: userId,
                type: userType,
                credits: {
                    balance: currentBalance,
                    totalEarned: user.credits?.totalEarned || 0,
                    totalSpent: user.credits?.totalSpent || 0
                }
            };

            next();
        } catch (error) {
            console.error('Credit check middleware error:', error);
            return res.status(500).json({
                success: false,
                message: 'Error checking credits',
                code: 'CREDIT_CHECK_ERROR'
            });
        }
    };
};

/**
 * Middleware to get user's credit information without blocking the request
 * Useful for endpoints that need to display credit info but don't require credits
 */
export const getCreditInfo = async (req, res, next) => {
    try {
        const userId = req.id; // From JWT verification middleware
        
        if (!userId) {
            req.user = { credits: { balance: 0, totalEarned: 0, totalSpent: 0 } };
            return next();
        }

        // Determine user type by checking both collections
        let userType = null;
        let user = await Student.findById(userId).select('role credits billing');
        
        if (user) {
            userType = 'Student';
        } else {
            user = await Teacher.findById(userId).select('role credits billing');
            if (user) {
                userType = 'Teacher';
            }
        }

        if (!user) {
            req.user = { credits: { balance: 0, totalEarned: 0, totalSpent: 0 } };
            return next();
        }

        // Add user info to request
        req.user = {
            id: userId,
            type: userType,
            credits: {
                balance: user.credits?.balance || 0,
                totalEarned: user.credits?.totalEarned || 0,
                totalSpent: user.credits?.totalSpent || 0,
                lastUpdated: user.credits?.lastUpdated
            },
            billing: {
                customerId: user.billing?.customerId,
                lastPurchaseDate: user.billing?.lastPurchaseDate,
                totalAmountSpent: user.billing?.totalAmountSpent || 0
            }
        };

        next();
    } catch (error) {
        console.error('Get credit info middleware error:', error);
        req.user = { credits: { balance: 0, totalEarned: 0, totalSpent: 0 } };
        next();
    }
};

/**
 * Middleware specifically for AegisGrader submissions
 * Checks for 1 credit per answer sheet to be evaluated
 */
export const checkAegisGraderCredits = async (req, res, next) => {
    try {
        const { answerSheets } = req.body;
        
        if (!answerSheets || !Array.isArray(answerSheets)) {
            return res.status(400).json({
                success: false,
                message: 'Invalid answer sheets data',
                code: 'INVALID_DATA'
            });
        }

        const requiredCredits = answerSheets.length; // 1 credit per answer sheet
        
        // Use the general credit check middleware with dynamic credit requirement
        const creditCheckMiddleware = checkCredits(requiredCredits, 'AEGIS_GRADER');
        return creditCheckMiddleware(req, res, next);
        
    } catch (error) {
        console.error('AegisGrader credit check error:', error);
        return res.status(500).json({
            success: false,
            message: 'Error checking credits for AegisGrader',
            code: 'CREDIT_CHECK_ERROR'
        });
    }
};

/**
 * Utility function to check credits programmatically
 * @param {string} userId - User ID
 * @param {string} userType - User type ('Student' or 'Teacher')
 * @param {number} requiredCredits - Required credits
 * @returns {Promise<{hasCredits: boolean, currentBalance: number, shortfall?: number}>}
 */
export const checkUserCredits = async (userId, userType, requiredCredits) => {
    try {
        const hasSufficient = await CreditService.hasSufficientCredits(userId, userType, requiredCredits);
        const currentBalance = await CreditService.getCreditBalance(userId, userType);
        
        return {
            hasCredits: hasSufficient,
            currentBalance,
            shortfall: hasSufficient ? 0 : requiredCredits - currentBalance
        };
    } catch (error) {
        console.error('Error checking user credits:', error);
        return {
            hasCredits: false,
            currentBalance: 0,
            shortfall: requiredCredits
        };
    }
};
