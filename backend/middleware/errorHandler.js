// backend/middleware/errorHandler.js
import AppError from '../utils/AppError.js';
import mongoose from 'mongoose';

/**
 * Handle Mongoose database errors and convert to AppError
 */
const handleMongooseError = (err) => {
    // Handle validation errors
    if (err instanceof mongoose.Error.ValidationError) {
        const errors = Object.values(err.errors).map(e => ({
            field: e.path,
            message: e.message
        }));
        return AppError.validationError('Validation Error', errors);
    }
    
    // Handle duplicate key errors
    if (err.code === 11000) {
        const field = Object.keys(err.keyValue)[0];
        return AppError.conflict(`Duplicate value for ${field}`);
    }
    
    // Handle cast errors (invalid ObjectId, etc)
    if (err instanceof mongoose.Error.CastError) {
        return AppError.badRequest(`Invalid ${err.path}: ${err.value}`);
    }
    
    // Handle other mongoose errors
    return AppError.serverError('Database error');
};

/**
 * Handle JWT authentication errors
 */
const handleJWTError = (err) => {
    if (err.name === 'TokenExpiredError') {
        return AppError.unauthorized('Your token has expired. Please log in again.');
    }
    return AppError.unauthorized('Invalid token. Please log in again.');
};

/**
 * Global error handler middleware
 */
export const globalErrorHandler = (err, req, res, next) => {
    // Log error details in development or for server errors
    if (process.env.NODE_ENV === 'development' || err.statusCode >= 500) {
        console.error('Error:', {
            name: err.name,
            message: err.message,
            statusCode: err.statusCode,
            stack: err.stack
        });
    }

    let error = err;

    // Convert common errors to AppError
    if (err instanceof mongoose.Error) {
        error = handleMongooseError(err);
    } else if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
        error = handleJWTError(err);
    } else if (!(err instanceof AppError)) {
        // For non-operational errors, create a generic server error
        // but preserve the original message for logging
        error = AppError.serverError(
            process.env.NODE_ENV === 'production' 
                ? 'Something went wrong' 
                : err.message
        );
    }

    // Include any metadata from the error
    const metadata = error.metadata || {};
    
    // Send response
    res.status(error.statusCode).json({
        status: error.status,
        message: error.message,
        errors: error.errors,
        ...metadata,
        ...(process.env.NODE_ENV === 'development' && {
            stack: error.stack
        })
    });
};
