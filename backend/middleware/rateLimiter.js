import { RateLimiterMemory } from 'rate-limiter-flexible';

const ipLimiter = new RateLimiterMemory({
    points: 20, // 20 attempts
    duration: 3600, // per 1 hour
  });

export const rateLimiterMiddleware = async (req, res, next) => {
    try {
        await ipLimiter.consume(req.ip, 1);
        next();
    } catch (error) {
        const timeLeft = Math.floor(error.msBeforeNext / 1000) || 1;
        res.status(429).json({
            message: `Too many requests. Please try again after ${timeLeft} seconds.`
        });
    }
};