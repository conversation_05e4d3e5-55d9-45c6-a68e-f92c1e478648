from PyPDF2 import Pdf<PERSON><PERSON><PERSON>, PdfWriter
import re
from mistralai import Mistral
import time
import multiprocessing
import json
import sys
import os
import requests
from langchainParser import langChainRun

api_key = "5bgmhuuDopk6pnxIg1Tb9MMRzfz3PNpN"

client = Mistral(api_key=api_key)

def fileupload(file_path):
    uploaded_pdf = client.files.upload(
        file={
            "file_name": file_path,
            "content": open(file_path, "rb"),
        },
        purpose="ocr"
    )

    signed_url = client.files.get_signed_url(file_id=uploaded_pdf.id)

    ocr_response = client.ocr.process(
        model="mistral-ocr-latest",
        document={
            "type": "document_url",
            "document_url": signed_url.url,
        },
        include_image_base64=True
    )

    try: 
        return ocr_response.model_dump_json()
    except Exception as e:
        print(f"Got error: {e} returning the textual format")
        return ocr_response.text()

def breakPages(fullFilename, pages=(0,1)):
    reader = PdfReader(fullFilename)
    writer = PdfWriter()
    writer.add_page(reader.pages[pages[0]])
    if (pages[1] != -1):
        writer.add_page(reader.pages[pages[1]])

    with open(f'{fullFilename[:-4]}_page_{pages[0]}-{pages[1]}.pdf', 'wb') as out:
        writer.write(out)

    return f'{fullFilename[:-4]}_page_{pages[0]}-{pages[1]}.pdf'

def callapi(filename):
    url = "https://api.va.landing.ai/v1/tools/agentic-document-analysis"
    files = {
      "pdf": open(filename, "rb")
    }
    headers = {
      "Authorization": "Basic MzUzbTdtbnF5NGEzbTF6c2hiZGFuOkhYZ3g4MUNzS1lJUlNLNVVzTmRxNmd0ckFqZTgxRmlt",
    }
    try:
        response = requests.post(url, files=files, headers=headers)
        print("response: ", response.text)
        if (response.text == "Too Many Requests"):
            time.sleep(10)
            response = requests.post(url, files=files, headers=headers)
        answer = response.json()
        return answer
    except Exception as e:
        print("Error while callapi: ", e)


def worker(filepath, startPage=0):
    readerMain = PdfReader(filepath)
    lenPdf = len(readerMain.pages)
    data = []
    for i in range(startPage, lenPdf, 2):
        if (i+1 >= lenPdf):
            filename = breakPages(filepath, (i, -1))
        else:
            filename = breakPages(filepath, (i, i+1))
        data.append(callapi(filename))
        os.remove(filename)
    # extractedQuestions = langChainRun(data)
    # print(extractedQuestions)
    # return extractedQuestions

def mistralworker(filepath, topicList, subtopicList):
    response = fileupload(filepath)
    extractedQuestions = langChainRun(json.loads(response), topicList, subtopicList)
    if (extractedQuestions != None):
        extractedQuestionsFinal = [q for page in extractedQuestions for q in page]
        for q in extractedQuestionsFinal:
            q['qimg'] = []
            q['aimg'] = []
        return insertImages(json.loads(response), extractedQuestionsFinal)
    return extractedQuestions

def insertImages(imagelist, questionslist):
    imgpat=r"img-\d+\.jpeg"
    for question in questionslist:
        # print("Got question: ", question)
        matchq = []
        matcha = []
        matchq.extend(re.findall(imgpat, question['passage']))
        matchq.extend(re.findall(imgpat, question['question']))
        matcha.extend(re.findall(imgpat, question['answer']))
        matcha.extend(re.findall(imgpat, question['solution']))
        # print("got match: ", matchq, matcha)
        if len(matchq) > 0:
            # there is a question image required here
            matchq = list(set(matchq))
            # find the image in the imagelist
            image = ""
            for page in imagelist['pages']:
                for image in page['images']:
                    if (image['id'] in matchq):
                        imagemap = {}
                        imagemap[image['id'].replace('.', '_')] = image['image_base64']
                        question['qimg'].append(imagemap)
                    # [{"img1" : "lkjasdflkjsdlkfj"},
                    # {"img2" : "lkjasdflkjsdlkfj"}]

        if len(matcha) > 0:
            # there is a question image required here
            matcha = list(set(matcha))
            # find the image in the imagelist
            image = ""
            for page in imagelist['pages']:
                for image in page['images']:
                    if (image['id'] in matcha):
                        imagemap = {}
                        imagemap[image['id'].replace('.', '_')] = image['image_base64']
                        question['aimg'].append(imagemap)
    return questionslist

def main():
    try:
        data = json.loads(sys.argv[1])
        fullfilepath = data['filepath']
        topicList = data['topiclist']
        subtopicList = data['subtopiclist']
        print("[PYTHON][INFO] topiclist: ", topicList, file=sys.stderr)
        print("[PYTHON][INFO] subtopiclist: ", subtopicList, file=sys.stderr)
        filepathslist = []
        finalQuestionsList = []
        QuestionsList = []
        for i in os.walk(fullfilepath):
            for j in i[2]: # basically the files in that directory
                if j.endswith(".pdf"):
                    filepathslist.append(os.path.join(i[0], j))
        for file in filepathslist:
            questions = mistralworker(file, topicList, subtopicList)
            if(questions != None):
                QuestionsList.append(questions)

        finalQuestionsList = [q for file in QuestionsList for q in file]
        # with open('outputfinal.json', 'w') as f:
        #     f.write(json.dumps(finalQuestionsList))
        print(json.dumps(finalQuestionsList))
    except Exception as e:
        raise RuntimeError("[ERROR]: ", e)


main()
