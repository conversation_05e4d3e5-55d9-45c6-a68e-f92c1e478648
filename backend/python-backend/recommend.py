import sys
import json
from collaborative_filtering import CollaborativeFiltering
import random

def Recommend():
    # data = json.loads(sys.argv[1])
    data = json.loads(sys.stdin.read())
    knowledgeGraph = data['knowledgeGraph']
    # print(f"[Mehul] [DEBUG INFO] knowledgegraph is: {knowledgeGraph}", file=sys.stderr);
    questionBank = data['questionBank']
    random.shuffle(questionBank)
    # questionBank = data['questionBank']
    numQuestions = data['numQuestions']
    testtype = data['testtype']

    cf_model = CollaborativeFiltering()
    if testtype == "generic":
        cf_model.fillUserInfo(knowledgeGraph)
        listOfQuestions = cf_model.recommendQuestions(questionBank, numQuestions)
    elif testtype == "diagnostic":
        cf_model.fillUserInfo(knowledgeGraph)
        print(f"[Mehul] [INFO] questionbank is: {questionBank}", file=sys.stderr)
        listOfQuestions = cf_model.recommendQuestionsDiagnostic(questionBank, numQuestions)
    elif testtype == "personalized" or testtype == "personalizedIndividual":
        # for personlized we'll get knowledge graph slightly differently
        alltestList = []
        for studentikg in knowledgeGraph:
            cf_model.fillUserInfo(studentikg)
            random.shuffle(questionBank)
            studentTestMap = {"studentId": studentikg['studentId'], "questions": cf_model.recommendQuestions(questionBank, numQuestions)}
            alltestList.append(studentTestMap)
            cf_model.clearUserInfo()
        listOfQuestions = alltestList
    else:
        listOfQuestions = ""
    print(json.dumps(listOfQuestions))
Recommend()
