{"cells": [{"cell_type": "code", "execution_count": 8, "id": "6dd9bde0-116b-4b9f-a11d-c7b4026d47d0", "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "Building module pyirt.util.clib failed: [\"ModuleNotFoundError: No module named 'distutils'\\n\"]", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/AegisScholar/platform-web/venv/lib/python3.12/site-packages/pyximport/_pyximport3.py:314\u001b[0m, in \u001b[0;36mPyxImportLoader.create_module\u001b[0;34m(self, spec)\u001b[0m\n\u001b[1;32m    313\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 314\u001b[0m     so_path \u001b[38;5;241m=\u001b[39m \u001b[43mbuild_module\u001b[49m\u001b[43m(\u001b[49m\u001b[43mspec\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpyxfilename\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mspec\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43morigin\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpyxbuild_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_pyxbuild_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    315\u001b[0m \u001b[43m                           \u001b[49m\u001b[43minplace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_inplace\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlanguage_level\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_language_level\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    316\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpath \u001b[38;5;241m=\u001b[39m so_path\n", "File \u001b[0;32m~/AegisScholar/platform-web/venv/lib/python3.12/site-packages/pyximport/_pyximport3.py:179\u001b[0m, in \u001b[0;36mbuild_module\u001b[0;34m(name, pyxfilename, pyxbuild_dir, inplace, language_level)\u001b[0m\n\u001b[1;32m    177\u001b[0m handle_dependencies(pyxfilename)\n\u001b[0;32m--> 179\u001b[0m extension_mod, setup_args \u001b[38;5;241m=\u001b[39m \u001b[43mget_distutils_extension\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpyxfilename\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlanguage_level\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    180\u001b[0m build_in_temp \u001b[38;5;241m=\u001b[39m pyxargs\u001b[38;5;241m.\u001b[39mbuild_in_temp\n", "File \u001b[0;32m~/AegisScholar/platform-web/venv/lib/python3.12/site-packages/pyximport/_pyximport3.py:103\u001b[0m, in \u001b[0;36mget_distutils_extension\u001b[0;34m(modname, pyxfilename, language_level)\u001b[0m\n\u001b[1;32m    102\u001b[0m     pyxfilename \u001b[38;5;241m=\u001b[39m pyxfilename\u001b[38;5;241m.\u001b[39mencode(sys\u001b[38;5;241m.\u001b[39mgetfilesystemencoding())\n\u001b[0;32m--> 103\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mdistutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mextension\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Extension\n\u001b[1;32m    104\u001b[0m extension_mod \u001b[38;5;241m=\u001b[39m Extension(name \u001b[38;5;241m=\u001b[39m modname, sources\u001b[38;5;241m=\u001b[39m[pyxfilename])\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'distutils'", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[0;32mIn[8], line 2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mnumpy\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mnp\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mpyi<PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m irt\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01mSubskillMIRTModel\u001b[39;00m:\n\u001b[1;32m      5\u001b[0m     \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, num_subskills):\n", "File \u001b[0;32m~/AegisScholar/platform-web/venv/lib/python3.12/site-packages/pyirt/__init__.py:2\u001b[0m\n\u001b[1;32m      1\u001b[0m __all__ \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_pyirt\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124msolver\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mutil\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_pyirt\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m irt\n", "File \u001b[0;32m~/AegisScholar/platform-web/venv/lib/python3.12/site-packages/pyirt/_pyirt.py:2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# -*-coding:utf-8-*-\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01ms<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m model\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdao\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m localDAO\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mlogger\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m <PERSON>gger\n", "File \u001b[0;32m~/AegisScholar/platform-web/venv/lib/python3.12/site-packages/pyirt/solver/model.py:17\u001b[0m\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtime\u001b[39;00m\n\u001b[1;32m     15\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mcopy\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m deepcopy\n\u001b[0;32m---> 17\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mutil\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m clib, tools\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m optimizer\n\u001b[1;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01malgo\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m update_theta_distribution\n", "File \u001b[0;32m~/AegisScholar/platform-web/venv/lib/python3.12/site-packages/pyirt/util/__init__.py:8\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mpyximport\u001b[39;00m\n\u001b[1;32m      7\u001b[0m pyximport\u001b[38;5;241m.\u001b[39minstall(build_dir\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m/tmp/pyximport/\u001b[39m\u001b[38;5;124m\"\u001b[39m, build_in_temp\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[0;32m----> 8\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m clib\n", "File \u001b[0;32m~/AegisScholar/platform-web/venv/lib/python3.12/site-packages/pyximport/_pyximport3.py:332\u001b[0m, in \u001b[0;36mPyxImportLoader.create_module\u001b[0;34m(self, spec)\u001b[0m\n\u001b[1;32m    329\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mtraceback\u001b[39;00m\n\u001b[1;32m    330\u001b[0m exc \u001b[38;5;241m=\u001b[39m \u001b[38;5;167;01mImportError\u001b[39;00m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mBuilding module \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m failed: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m (\n\u001b[1;32m    331\u001b[0m     spec\u001b[38;5;241m.\u001b[39mname, traceback\u001b[38;5;241m.\u001b[39mformat_exception_only(\u001b[38;5;241m*\u001b[39msys\u001b[38;5;241m.\u001b[39mexc_info()[:\u001b[38;5;241m2\u001b[39m])))\n\u001b[0;32m--> 332\u001b[0m \u001b[38;5;28;01mraise\u001b[39;00m exc\u001b[38;5;241m.\u001b[39mwith_traceback(tb)\n", "File \u001b[0;32m~/AegisScholar/platform-web/venv/lib/python3.12/site-packages/pyximport/_pyximport3.py:314\u001b[0m, in \u001b[0;36mPyxImportLoader.create_module\u001b[0;34m(self, spec)\u001b[0m\n\u001b[1;32m    312\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mcreate_module\u001b[39m(\u001b[38;5;28mself\u001b[39m, spec):\n\u001b[1;32m    313\u001b[0m     \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 314\u001b[0m         so_path \u001b[38;5;241m=\u001b[39m \u001b[43mbuild_module\u001b[49m\u001b[43m(\u001b[49m\u001b[43mspec\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpyxfilename\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mspec\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43morigin\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpyxbuild_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_pyxbuild_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    315\u001b[0m \u001b[43m                               \u001b[49m\u001b[43minplace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_inplace\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlanguage_level\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_language_level\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    316\u001b[0m         \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mpath \u001b[38;5;241m=\u001b[39m so_path\n\u001b[1;32m    317\u001b[0m         spec\u001b[38;5;241m.\u001b[39morigin \u001b[38;5;241m=\u001b[39m so_path\n", "File \u001b[0;32m~/AegisScholar/platform-web/venv/lib/python3.12/site-packages/pyximport/_pyximport3.py:179\u001b[0m, in \u001b[0;36mbuild_module\u001b[0;34m(name, pyxfilename, pyxbuild_dir, inplace, language_level)\u001b[0m\n\u001b[1;32m    176\u001b[0m \u001b[38;5;28;01<PERSON>ert\u001b[39;00m os\u001b[38;5;241m.\u001b[39mpath\u001b[38;5;241m.\u001b[39mexists(pyxfilename), \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mPath does not exist: \u001b[39m\u001b[38;5;132;01m%s\u001b[39;00m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;241m%\u001b[39m pyxfilename\n\u001b[1;32m    177\u001b[0m handle_dependencies(pyxfilename)\n\u001b[0;32m--> 179\u001b[0m extension_mod, setup_args \u001b[38;5;241m=\u001b[39m \u001b[43mget_distutils_extension\u001b[49m\u001b[43m(\u001b[49m\u001b[43mname\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpyxfilename\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlanguage_level\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    180\u001b[0m build_in_temp \u001b[38;5;241m=\u001b[39m pyxargs\u001b[38;5;241m.\u001b[39mbuild_in_temp\n\u001b[1;32m    181\u001b[0m sargs \u001b[38;5;241m=\u001b[39m pyxargs\u001b[38;5;241m.\u001b[39msetup_args\u001b[38;5;241m.\u001b[39mcopy()\n", "File \u001b[0;32m~/AegisScholar/platform-web/venv/lib/python3.12/site-packages/pyximport/_pyximport3.py:103\u001b[0m, in \u001b[0;36mget_distutils_extension\u001b[0;34m(modname, pyxfilename, language_level)\u001b[0m\n\u001b[1;32m     99\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(pyxfilename, \u001b[38;5;28mstr\u001b[39m):\n\u001b[1;32m    100\u001b[0m     \u001b[38;5;66;03m# distutils is stupid in Py2 and requires exactly 'str'\u001b[39;00m\n\u001b[1;32m    101\u001b[0m     \u001b[38;5;66;03m# => encode accidentally coerced unicode strings back to str\u001b[39;00m\n\u001b[1;32m    102\u001b[0m     pyxfilename \u001b[38;5;241m=\u001b[39m pyxfilename\u001b[38;5;241m.\u001b[39mencode(sys\u001b[38;5;241m.\u001b[39mgetfilesystemencoding())\n\u001b[0;32m--> 103\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mdistutils\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mextension\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Extension\n\u001b[1;32m    104\u001b[0m extension_mod \u001b[38;5;241m=\u001b[39m Extension(name \u001b[38;5;241m=\u001b[39m modname, sources\u001b[38;5;241m=\u001b[39m[pyxfilename])\n\u001b[1;32m    105\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m language_level \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n", "\u001b[0;31mImportError\u001b[0m: Building module pyirt.util.clib failed: [\"ModuleNotFoundError: No module named 'distutils'\\n\"]"]}], "source": ["import numpy as np\n", "from pyirt import irt\n", "\n", "class SubskillMIRTModel:\n", "    def __init__(self, num_subskills):\n", "        self.num_subskills = num_subskills\n", "        self.theta_estimates = None\n", "        self.item_params = None\n", "\n", "    def fit(self, response_data, item_subskills):\n", "        \"\"\"\n", "        Fit the MIRT model to the response data.\n", "        \n", "        :param response_data: List of tuples (student_id, item_id, response)\n", "        :param item_subskills: Dictionary mapping item_id to list of subskills\n", "        \"\"\"\n", "        # Prepare data for pyirt\n", "        self.item_subskills = item_subskills\n", "        data = [(str(sid), str(iid), resp) for sid, iid, resp in response_data]\n", "        \n", "        # Fit the model\n", "        self.theta_estimates, self.item_params = irt(data, num_dim=self.num_subskills)\n", "\n", "    def update_proficiency(self, student_id, item_responses):\n", "        \"\"\"\n", "        Update the proficiency estimates for a student based on new item responses.\n", "        \n", "        :param student_id: ID of the student\n", "        :param item_responses: List of tuples (item_id, response)\n", "        :return: Updated subskill proficiency estimates\n", "        \"\"\"\n", "        if self.theta_estimates is None or self.item_params is None:\n", "            raise ValueError(\"Model has not been fitted yet.\")\n", "\n", "        current_theta = self.theta_estimates.get(str(student_id), np.zeros(self.num_subskills))\n", "        \n", "        for item_id, response in item_responses:\n", "            item_params = self.item_params.get(str(item_id))\n", "            if item_params is None:\n", "                continue\n", "            \n", "            a_params, b_param, _ = item_params\n", "            \n", "            # Update theta for each subskill associated with the item\n", "            for subskill in self.item_subskills.get(item_id, []):\n", "                p = self._irf(current_theta[subskill], a_params[subskill], b_param)\n", "                current_theta[subskill] += self._calculate_update(p, response, a_params[subskill])\n", "\n", "        self.theta_estimates[str(student_id)] = current_theta\n", "        return current_theta\n", "\n", "    def _irf(self, theta, a, b):\n", "        \"\"\"Item Response Function (2PL model)\"\"\"\n", "        return 1 / (1 + np.exp(-a * (theta - b)))\n", "\n", "    def _calculate_update(self, p, response, a, learning_rate=0.1):\n", "        \"\"\"Calculate the update for a single subskill\"\"\"\n", "        return learning_rate * a * (response - p)\n", "\n", "    def get_proficiency(self, student_id):\n", "        \"\"\"Get the current proficiency estimates for a student\"\"\"\n", "        return self.theta_estimates.get(str(student_id), np.zeros(self.num_subskills))\n"]}, {"cell_type": "code", "execution_count": 6, "id": "77a0df43-0ab9-486b-863f-167828d5fa8f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Requirement already satisfied: numpy in /Users/<USER>/AegisScholar/platform-web/venv/lib/python3.12/site-packages (2.1.1)\n", "Collecting distutils-pytest\n", "  Downloading distutils-pytest-0.2.1.tar.gz (9.0 kB)\n", "  Installing build dependencies ... \u001b[?25ldone\n", "\u001b[?25h  Getting requirements to build wheel ... \u001b[?25ldone\n", "\u001b[?25h  Preparing metadata (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25hCollecting pytest (from distutils-pytest)\n", "  Downloading pytest-8.3.3-py3-none-any.whl.metadata (7.5 kB)\n", "Collecting iniconfig (from pytest->distutils-pytest)\n", "  Downloading iniconfig-2.0.0-py3-none-any.whl.metadata (2.6 kB)\n", "Requirement already satisfied: packaging in /Users/<USER>/AegisScholar/platform-web/venv/lib/python3.12/site-packages (from pytest->distutils-pytest) (24.1)\n", "Collecting pluggy<2,>=1.5 (from pytest->distutils-pytest)\n", "  Downloading pluggy-1.5.0-py3-none-any.whl.metadata (4.8 kB)\n", "Downloading pytest-8.3.3-py3-none-any.whl (342 kB)\n", "Downloading pluggy-1.5.0-py3-none-any.whl (20 kB)\n", "Downloading iniconfig-2.0.0-py3-none-any.whl (5.9 kB)\n", "Building wheels for collected packages: distutils-pytest\n", "  Building wheel for distutils-pytest (pyproject.toml) ... \u001b[?25ldone\n", "\u001b[?25h  Created wheel for distutils-pytest: filename=distutils_pytest-0.2.1-py3-none-any.whl size=7962 sha256=28b772801c34a53fd519e928c13a1d898a7423edead12ae3c08424b9e646b79e\n", "  Stored in directory: /Users/<USER>/Library/Caches/pip/wheels/ea/61/dc/8d3d159962f4910288b24240ad539bb30dcac75a44a633e96a\n", "Successfully built distutils-pytest\n", "Installing collected packages: pluggy, iniconfig, pytest, distutils-pytest\n", "Successfully installed distutils-pytest-0.2.1 iniconfig-2.0.0 pluggy-1.5.0 pytest-8.3.3\n"]}], "source": ["!pip3 install numpy distutils-pytest"]}, {"cell_type": "code", "execution_count": 1, "id": "175b0e47-e2ac-4d51-bcb8-2b7d9bb99668", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting setuptools\n", "  Using cached setuptools-75.1.0-py3-none-any.whl.metadata (6.9 kB)\n", "Using cached setuptools-75.1.0-py3-none-any.whl (1.2 MB)\n", "Installing collected packages: setuptools\n", "Successfully installed setuptools-75.1.0\n"]}], "source": ["!pip3 install setuptools"]}, {"cell_type": "code", "execution_count": 2, "id": "7facfb97", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ResponseData: [(6, 8, 0), (10, 9, 0), (9, 7, 1), (7, 15, 1), (1, 4, 0), (10, 2, 0), (4, 6, 1), (4, 3, 1), (1, 13, 0), (8, 15, 1), (2, 2, 1), (7, 7, 1), (7, 14, 0), (6, 10, 0), (2, 2, 0), (3, 4, 0), (8, 1, 1), (10, 15, 1), (1, 8, 0), (9, 10, 1), (1, 2, 1), (8, 6, 1), (6, 13, 1), (2, 11, 0), (6, 6, 0), (8, 14, 1), (3, 1, 1), (9, 12, 0), (8, 11, 1), (8, 5, 0), (1, 12, 0), (10, 11, 1), (8, 1, 0), (10, 6, 1), (6, 14, 1), (7, 7, 0), (8, 6, 1), (8, 7, 0), (2, 13, 0), (10, 8, 1), (7, 15, 1), (4, 2, 0), (6, 13, 1), (5, 8, 1), (8, 3, 0), (9, 9, 1), (8, 5, 1), (6, 3, 0), (5, 4, 0), (2, 9, 1)]\n", "\n", "ItemSubskills: {1: [1], 2: [4, 2, 3], 3: [0, 3, 1], 4: [1, 4], 5: [0], 6: [4], 7: [4, 0], 8: [1], 9: [4, 0, 1], 10: [3, 0, 4], 11: [4], 12: [0], 13: [1, 0, 4], 14: [4], 15: [2, 0]}\n", "\n", "ItemResponses: {'student_id': 1, 'responses': [(3, 1), (6, 1), (1, 0), (8, 1), (7, 0), (10, 1), (6, 0), (15, 0), (4, 1), (10, 1)]}\n", "\n", "UserItemMatrix: [[0.3656657173957688, 0.8134139236226084, 0.2900312298807679, 0.41203090669514564, 0.3083097923415967, 0.7789683347425169, 0.420969954012526, 0.07975747086364515, 0.484463015087254, 0.3520137775400438, 0.9186337839875037, 0.7337616154392226, 0.5495017383511732, 0.332185695057611, 0.8504614353969145], [0.43600204892089756, 0.8509992821140817, 0.0637263638874842, 0.33949016817674726, 0.5451799358662371, 0.6905923135896114, 0.5457292712740035, 0.7305059648497548, 0.5689422282985509, 0.10684217967179055, 0.6594785645797969, 0.6929444110872778, 0.20144873015398257, 0.4982714899451053, 0.7447514996027302], [0.21817506544537368, 0.29608680759652606, 0.7309131807432756, 0.9677922682804532, 0.4288465565044469, 0.7151840073647628, 0.7773123350066025, 0.047938021576201306, 0.2708853111456214, 0.23010556271626614, 0.7753291199534208, 0.9994701348113657, 0.5403225197361068, 0.9268197204033055, 0.33619838247266765], [0.7178709451708285, 0.7115958661618158, 0.20769451692390073, 0.540454964624792, 0.5728289661110633, 0.08215982271987898, 0.4178214014883699, 0.064977594786927, 0.49729859056396974, 0.6201352049613652, 0.7113532468856654, 0.05821906890989803, 0.876631370875609, 0.8360362666342254, 0.7300578825683154], [0.07496481606996508, 0.21903950174831843, 0.884973268534018, 0.3299496075735522, 0.7009622298374005, 0.516929014669816, 0.6890627025919226, 0.27333255249277777, 0.2858100526304923, 0.5477255313862435, 0.4699732834218193, 0.7243102343935959, 0.6308073718916738, 0.3441753338123884, 0.885228449888351], [0.4539121064946414, 0.4154842952006613, 0.634927130998292, 0.9471383319066341, 0.740387352459455, 0.7829721422301732, 0.8286566092686346, 0.1314753825971351, 0.26005418629702604, 0.7621551224912086, 0.6715306925644842, 0.9701004608168682, 0.5269519031410004, 0.9790323454736622, 0.9506675607625412], [0.7189003245275831, 0.748735843975373, 0.035460980758606264, 0.6855838904653673, 0.5928465906387249, 0.6911541372778387, 0.8239089071613406, 0.2974439220108135, 0.596826542306608, 0.2779993565091715, 0.4760516033993034, 0.17017352983644163, 0.7962736393555516, 0.46267370859943047, 0.2548282989618965], [0.6516183757200484, 0.682454190474018, 0.5800716709031162, 0.5268138656909864, 0.3789723483007996, 0.07403563372908539, 0.6831565552296611, 0.10458305168805138, 0.31407510975314545, 0.5433340470210448, 0.6586515437761779, 0.9324499053727127, 0.9218756835321513, 0.4520978567410445, 0.39119198237611774], [0.7733569247933719, 0.4120904595503908, 0.8987608767863623, 0.3678298672608038, 0.11949501543030994, 0.9775101045751419, 0.8642525820227682, 0.5367073675207742, 0.3451378662608041, 0.7502860365651156, 0.8812920280305344, 0.9191627879837956, 0.820852667133489, 0.7909779471659272, 0.6429203039380712], [0.38776904413761215, 0.17607272650987904, 0.6310485240988842, 0.9115346154877613, 0.6637391616805467, 0.3855696713661496, 0.6792540636462485, 0.7913152479528818, 0.7827529555913164, 0.5436885244965914, 0.027067829860292658, 0.7988345735562826, 0.7307046182414295, 0.611755738704976, 0.7696929058834312]]\n"]}], "source": ["import random\n", "import numpy as np\n", "\n", "# 1. ResponseData: List of tuples representing (student_id, item_id, response)\n", "num_students = 10\n", "num_items = 15\n", "response_data = [(random.randint(1, num_students), random.randint(1, num_items), random.randint(0, 1))\n", "                 for _ in range(50)]  # 50 random responses\n", "\n", "print(\"ResponseData:\", response_data)\n", "\n", "# 2. ItemSubskills: Mapping of each item_id to a list of subskills\n", "num_subskills = 5\n", "item_subskills = {item_id: random.sample(range(num_subskills), random.randint(1, 3))\n", "                  for item_id in range(1, num_items + 1)}\n", "\n", "print(\"\\nItemSubskills:\", item_subskills)\n", "\n", "# 3. ItemResponses: Response data for a specific student (student_id=1)\n", "item_responses = {\n", "    \"student_id\": 1,\n", "    \"responses\": [(random.randint(1, num_items), random.randint(0, 1)) for _ in range(10)]  # 10 responses for student 1\n", "}\n", "\n", "print(\"\\nItemResponses:\", item_responses)\n", "\n", "# 4. UserItemMatrix: Random matrix representing user-item interactions\n", "user_item_matrix = np.random.rand(num_students, num_items).tolist()\n", "\n", "print(\"\\nUserItemMatrix:\", user_item_matrix)\n"]}, {"cell_type": "code", "execution_count": null, "id": "c9e13238", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}