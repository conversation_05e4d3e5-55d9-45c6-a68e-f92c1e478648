{"cells": [{"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fit MIRT response: {'message': 'MIRT model fitted successfully'}\n", "Update Proficiency response: {'updated_proficiency': [-0.9104960829046472, -1.5404619489547098, -0.15907075345326638, -0.5137953937343238, -1.425148206664767]}\n", "Get Proficiency response: {'proficiency': [-0.9104960829046472, -1.5404619489547098, -0.15907075345326638, -0.5137953937343238, -1.425148206664767]}\n", "Fit CF response: {'message': 'Collaborative filtering model fitted successfully'}\n", "Recommendations response: {'recommendations': [1, 14, 10, 7, 6]}\n"]}], "source": ["import requests\n", "\n", "# Base URL for the FastAPI server (ensure the server is running on this URL)\n", "base_url = \"http://127.0.0.1:8000\"\n", "\n", "# 1. Send ResponseData to /fit_mirt endpoint\n", "response_data = {\n", "    \"data\": [\n", "        (6, 8, 0), (10, 9, 0), (9, 7, 1), (7, 15, 1), (1, 4, 0), (10, 2, 0), \n", "        (4, 6, 1), (4, 3, 1), (1, 13, 0), (8, 15, 1), (2, 2, 1), (7, 7, 1), \n", "        (7, 14, 0), (6, 10, 0), (2, 2, 0), (3, 4, 0), (8, 1, 1), (10, 15, 1),\n", "        (1, 8, 0), (9, 10, 1), (1, 2, 1), (8, 6, 1), (6, 13, 1), (2, 11, 0),\n", "        (6, 6, 0), (8, 14, 1), (3, 1, 1), (9, 12, 0), (8, 11, 1), (8, 5, 0),\n", "        (1, 12, 0), (10, 11, 1), (8, 1, 0), (10, 6, 1), (6, 14, 1), (7, 7, 0),\n", "        (8, 6, 1), (8, 7, 0), (2, 13, 0), (10, 8, 1), (7, 15, 1), (4, 2, 0),\n", "        (6, 13, 1), (5, 8, 1), (8, 3, 0), (9, 9, 1), (8, 5, 1), (6, 3, 0),\n", "        (5, 4, 0), (2, 9, 1)\n", "    ]\n", "}\n", "item_subskills = {\n", "    \"data\": {\n", "        1: [1], 2: [4, 2, 3], 3: [0, 3, 1], 4: [1, 4], 5: [0], 6: [4],\n", "        7: [4, 0], 8: [1], 9: [4, 0, 1], 10: [3, 0, 4], 11: [4], 12: [0],\n", "        13: [1, 0, 4], 14: [4], 15: [2, 0]\n", "    }\n", "}\n", "\n", "# print(item_subskills[\"data\"])\n", "# print(\"response data:\", [(str(sid), str(iid), resp) for sid, iid, resp in response_data[\"data\"]])\n", "\n", "# Fit the MIRT model\n", "fit_mirt_payload = {\n", "    \"response_data\": {\n", "        \"data\": [\n", "            (6, 8, 0), (10, 9, 0), (9, 7, 1), (7, 15, 1), (1, 4, 0), (10, 2, 0), \n", "            (4, 6, 1), (4, 3, 1), (1, 13, 0), (8, 15, 1), (2, 2, 1), (7, 7, 1), \n", "            (7, 14, 0), (6, 10, 0), (2, 2, 0), (3, 4, 0), (8, 1, 1), (10, 15, 1),\n", "            (1, 8, 0), (9, 10, 1), (1, 2, 1), (8, 6, 1), (6, 13, 1), (2, 11, 0),\n", "            (6, 6, 0), (8, 14, 1), (3, 1, 1), (9, 12, 0), (8, 11, 1), (8, 5, 0),\n", "            (1, 12, 0), (10, 11, 1), (8, 1, 0), (10, 6, 1), (6, 14, 1), (7, 7, 0),\n", "            (8, 6, 1), (8, 7, 0), (2, 13, 0), (10, 8, 1), (7, 15, 1), (4, 2, 0),\n", "            (6, 13, 1), (5, 8, 1), (8, 3, 0), (9, 9, 1), (8, 5, 1), (6, 3, 0),\n", "            (5, 4, 0), (2, 9, 1)\n", "        ]\n", "    },\n", "    \"item_subskills\": {\n", "        \"data\": {\n", "            \"1\": [1], \"2\": [4, 2, 3], \"3\": [0, 3, 1], \"4\": [1, 4], \"5\": [0], \"6\": [4],\n", "            \"7\": [4, 0], \"8\": [1], \"9\": [4, 0, 1], \"10\": [3, 0, 4], \"11\": [4], \"12\": [0],\n", "            \"13\": [1, 0, 4], \"14\": [4], \"15\": [2, 0]\n", "        }\n", "    }\n", "}\n", "\n", "# Fit the MIRT model\n", "fit_mirt_response = requests.post(f\"{base_url}/fit_mirt\", json=fit_mirt_payload)\n", "print(\"Fit MIRT response:\", fit_mirt_response.json())\n", "\n", "# 2. Update proficiency for a specific student with ItemResponses\n", "item_responses = {\n", "    \"student_id\": 1,\n", "    \"responses\": [(3, 1), (6, 1), (1, 0), (8, 1), (7, 0), (10, 1), (6, 0), (15, 0), (4, 1), (10, 1)]\n", "}\n", "\n", "update_proficiency_response = requests.post(f\"{base_url}/update_proficiency\", json=item_responses)\n", "print(\"Update Proficiency response:\", update_proficiency_response.json())\n", "\n", "# 3. Retrieve proficiency of a specific student\n", "student_id = 1\n", "get_proficiency_response = requests.get(f\"{base_url}/get_proficiency/{student_id}\")\n", "print(\"Get Proficiency response:\", get_proficiency_response.json())\n", "\n", "# 4. Fit collaborative filtering model with UserItemMatrix\n", "user_item_matrix = {\n", "    \"matrix\": [\n", "        [0.3657, 0.8134, 0.2900, 0.4120, 0.3083, 0.7789, 0.4210, 0.0798, 0.4845, 0.3520, 0.9186, 0.7338, 0.5495, 0.3322, 0.8505],\n", "        [0.4360, 0.8510, 0.0637, 0.3395, 0.5452, 0.6906, 0.5457, 0.7305, 0.5689, 0.1068, 0.6595, 0.6929, 0.2014, 0.4983, 0.7448],\n", "        [0.2182, 0.2961, 0.7309, 0.9678, 0.4288, 0.7152, 0.7773, 0.0479, 0.2709, 0.2301, 0.7753, 0.9995, 0.5403, 0.9268, 0.3362],\n", "        [0.7179, 0.7116, 0.2077, 0.5405, 0.5728, 0.0822, 0.4178, 0.0650, 0.4973, 0.6201, 0.7114, 0.0582, 0.8766, 0.8360, 0.7301],\n", "        [0.0750, 0.2190, 0.8850, 0.3300, 0.7010, 0.5169, 0.6891, 0.2733, 0.2858, 0.5477, 0.4700, 0.7243, 0.6308, 0.3442, 0.8852],\n", "        [0.4539, 0.4155, 0.6349, 0.9471, 0.7404, 0.7830, 0.8287, 0.1315, 0.2601, 0.7622, 0.6715, 0.9701, 0.5270, 0.9790, 0.9507],\n", "        [0.7189, 0.7487, 0.0355, 0.6856, 0.5928, 0.6912, 0.8239, 0.2974, 0.5968, 0.2780, 0.4761, 0.1702, 0.7963, 0.4627, 0.2548],\n", "        [0.6516, 0.6825, 0.5801, 0.5268, 0.3790, 0.0740, 0.6832, 0.1046, 0.3141, 0.5433, 0.6587, 0.9324, 0.9219, 0.4521, 0.3912],\n", "        [0.7734, 0.4121, 0.8988, 0.3678, 0.1195, 0.9775, 0.8643, 0.5367, 0.3451, 0.7503, 0.8813, 0.9192, 0.8209, 0.7910, 0.6429],\n", "        [0.3878, 0.1761, 0.6310, 0.9115, 0.6637, 0.3856, 0.6793, 0.7913, 0.7828, 0.5437, 0.0271, 0.7988, 0.7307, 0.6118, 0.7697]\n", "    ]\n", "}\n", "\n", "fit_cf_response = requests.post(f\"{base_url}/fit_cf\", json=user_item_matrix)\n", "print(\"Fit CF response:\", fit_cf_response.json())\n", "\n", "# 5. Get recommendations for a user\n", "user_id = 1\n", "n_recommendations = 5\n", "recommendations_response = requests.get(f\"{base_url}/recommend/{user_id}?n_recommendations={n_recommendations}\")\n", "print(\"Recommendations response:\", recommendations_response.json())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.6"}}, "nbformat": 4, "nbformat_minor": 2}