import json
import sys
from collaborative_filtering import CollaborativeFiltering

def parseSolutions(filename):
    with open(filename, 'r') as file:
        data = json.load(file)

    return data

def main():
    combData = json.loads(sys.stdin.read())
    assert(combData is not None and type(combData) == type(dict())), "combData must not be empty and it should be of type dictionary"
    questions = combData['questions']
    filename = combData['filename']

    assert(filename is not None and filename != ""), "Filename cannot be None or empty"
    assert(questions is not None and len(questions) > 0), "Questions cannot be None or empty"

    stringCompModel = CollaborativeFiltering()
    solutions = parseSolutions(filename)

    for i in range(len(solutions)):
        for j in range(len(questions)):
            if stringCompModel.compareStringsMostly(questions[j]["question"], solutions[i]["question"]):
                # need to compare the same question
                if not stringCompModel.compareStringsMostly(questions[j]["answer"], solutions[i]["correct"]):
                    print(f"[PYTHON][ERROR] Answer mismatch for question: {questions[j]['answer']}, solution: {solutions[i]}", file=sys.stderr)
                break

main()

    
    
