import numpy as np
import warnings
import pickle
import os
from sklearn.decomposition import TruncatedSVD
from sklearn.preprocessing import StandardScaler
from datetime import datetime

class GenericMIRTModel:
    VERSION = '2.1.0'
    
    def __init__(self, subjects_config=None, learning_rate=0.1, batch_size=32):
        """
        Initialize the model with continuous learning capabilities
        
        :param subjects_config: Dictionary mapping subjects to their subskills
        :param learning_rate: Learning rate for continuous updates (default: 0.1)
        :param batch_size: Size of batches for mini-batch updates (default: 32)
        """
        self.subjects_config = subjects_config or {}
        self.subskill_map = {}
        self._initialize_subskill_map()
        
        self.theta_estimates = None
        self.item_params = None
        self.student_map = {}
        self.item_map = {}
        self.item_metadata = {}
        self.correct_answers = {}
        
        # Continuous learning parameters
        self.learning_rate = learning_rate
        self.batch_size = batch_size
        self.response_buffer = []  # Store recent responses for batch updates
        
        # Training statistics
        self.training_stats = {
            'total_responses': 0,
            'batch_updates': 0,
            'average_accuracy': []
        }

    def _initialize_subskill_map(self):
        """Initialize the subskill map from the subjects configuration"""
        index = 0
        self.subskill_map.clear()  # Clear existing mappings
        for subject, config in self.subjects_config.items():
            for subskill in config['subskills']:
                # Create a unique key for each subject-subskill combination
                subskill_key = f"{subject}_{subskill}"
                self.subskill_map[subskill_key] = index
                index += 1
        self.num_subskills = len(self.subskill_map)
        if self.num_subskills == 0:
            # Set a minimum number of subskills if no subjects are configured
            self.num_subskills = 1

    def _prepare_data_matrix(self, response_data):
        """
        Prepare a robust data matrix from response data
        
        :param response_data: List of tuples (student_id, item_id, response)
        :return: Prepared 2D data matrix
        """
        # Create mappings for students and items
        students = sorted(set(sid for sid, _, _, _ in response_data))
        items = sorted(set(iid for _, iid, _, _ in response_data))
        
        self.student_map = {sid: i for i, sid in enumerate(students)}
        self.item_map = {iid: i for i, iid in enumerate(items)}

        # Prepare data matrix
        data_matrix = np.zeros((len(students), len(items)))
        for sid, iid, resp, _ in response_data:
            data_matrix[self.student_map[sid], self.item_map[str(iid)]] = resp

        return data_matrix

    def add_subject(self, subject_name, subskills, grade_levels):
        """
        Add a new subject to the model
        
        :param subject_name: Name of the subject
        :param subskills: List of subskills for this subject
        :param grade_levels: List of applicable grade levels
        """
        self.subjects_config[subject_name] = {
            'subskills': subskills,
            'grade_levels': grade_levels
        }
        self._initialize_subskill_map()

    def get_subject_proficiency(self, student_id, subject):
        """
        Get proficiency estimates for a specific subject
        
        :param student_id: Student identifier
        :param subject: Subject name
        :return: Dictionary mapping subskills to proficiency estimates
        """
        if student_id not in self.student_map:
            raise ValueError(f"Student ID {student_id} not found in the model.")
            
        if subject not in self.subjects_config:
            raise ValueError(f"Subject {subject} not found in the model configuration.")
            
        proficiency = self.get_proficiency(student_id)
        subject_proficiency = {}
        
        for subskill in self.subjects_config[subject]['subskills']:
            subskill_key = f"{subject}_{subskill}"
            if subskill_key in self.subskill_map:
                index = self.subskill_map[subskill_key]
                subject_proficiency[subskill] = proficiency[index]
                
        return subject_proficiency

    def get_proficiency(self, student_id):
        """Get the current proficiency estimates for a student"""
        if self.theta_estimates is None:
            raise ValueError("Model has not been fitted yet.")
        
        student_index = self.student_map.get(student_id)
        if student_index is None:
            raise ValueError(f"Student ID {student_id} not found in the model.")
        
        return self.theta_estimates[student_index].tolist()

    def _update_model_parameters(self, batch_data):
        """
        Update model parameters using mini-batch of responses
        
        :param batch_data: List of (student_id, item_id, response, grade_level)
        """
        # Prepare batch matrix
        batch_matrix = np.zeros((len(self.student_map), len(self.item_map)))
        for student_id, item_id, response, _ in batch_data:
            student_idx = self.student_map[student_id]
            item_idx = self.item_map[str(item_id)]
            batch_matrix[student_idx, item_idx] = response

        # Compute gradient updates
        with warnings.catch_warnings():
            warnings.simplefilter('ignore', RuntimeWarning)
            
            # Update theta estimates
            theta_grad = np.zeros_like(self.theta_estimates)
            item_grad = np.zeros_like(self.item_params)
            
            for student_idx in range(batch_matrix.shape[0]):
                for item_idx in range(batch_matrix.shape[1]):
                    if batch_matrix[student_idx, item_idx] != 0:  # If we have a response
                        # Compute prediction error
                        pred = self._predict_response(
                            self.theta_estimates[student_idx],
                            self.item_params[item_idx]
                        )
                        error = batch_matrix[student_idx, item_idx] - pred
                        
                        # Update gradients
                        theta_grad[student_idx] += error * self.item_params[item_idx]
                        item_grad[item_idx] += error * self.theta_estimates[student_idx]
            
            # Apply updates with learning rate
            self.theta_estimates += self.learning_rate * theta_grad
            self.item_params += self.learning_rate * item_grad
            
            # Normalize parameters
            self.theta_estimates = np.clip(self.theta_estimates, -3, 3)
            self.item_params = np.clip(self.item_params, -3, 3)

    def _predict_response(self, theta, item_params):
        """Predict probability of correct response"""
        logit = np.sum(theta * item_params)
        return self._irf(logit)

    def _irf(self, theta, discrimination=1.0, difficulty=0.0, guessing=0.2):
        """Item Response Function"""
        return guessing + (1 - guessing) / (1 + np.exp(-discrimination * (theta - difficulty)))

    def update_proficiency(self, student_id, item_responses, grade_level):
        """Update proficiency estimates and trigger continuous learning if needed"""
        if self.theta_estimates is None:
            self.theta_estimates = np.zeros((len(self.student_map) if self.student_map else 1, self.num_subskills))
        
        if self.item_params is None:
            self.item_params = np.ones((len(self.item_map) if self.item_map else 1, self.num_subskills))

        # Convert responses to the format needed for buffer
        new_responses = [(student_id, item_id, response, grade_level) 
                        for item_id, response in item_responses]
        
        # Add to response buffer
        self.response_buffer.extend(new_responses)
        self.training_stats['total_responses'] += len(new_responses)
        
        # Get or create student index
        student_index = self.student_map.get(student_id)
        if student_index is None:
            student_index = len(self.student_map)
            self.student_map[student_id] = student_index
            # Expand theta_estimates array if needed
            if student_index >= self.theta_estimates.shape[0]:
                new_theta = np.zeros((student_index + 1, self.num_subskills))
                new_theta[:self.theta_estimates.shape[0]] = self.theta_estimates
                self.theta_estimates = new_theta

        current_theta = self.theta_estimates[student_index].copy()

        # Process each response
        for item_id, response in item_responses:
            item_id_str = str(item_id)
            if item_id_str not in self.item_metadata:
                print(f"Warning: Item {item_id} not found in metadata")
                continue
                
            metadata = self.item_metadata[item_id_str]
            if metadata['grade_level'] != grade_level:
                print(f"Warning: Item {item_id} grade level doesn't match student grade level")
                continue

            subject = metadata['subject']
            item_subskills = [f"{subject}_{subskill}" for subskill in metadata['subskills']]
            is_correct = (response == self.correct_answers.get(item_id_str, response))

            for subskill_key in item_subskills:
                if subskill_key in self.subskill_map:
                    subskill_index = self.subskill_map[subskill_key]
                    p = self._irf(current_theta[subskill_index], 1.0)
                    update = 0.1 * (1 if is_correct else -1) * (1 - p)
                    current_theta[subskill_index] += update

        # If buffer reaches batch size, trigger parameter update
        if len(self.response_buffer) >= self.batch_size:
            self._update_model_parameters(self.response_buffer)
            self.training_stats['batch_updates'] += 1
            
            # Calculate and store accuracy
            batch_accuracy = self._calculate_batch_accuracy(self.response_buffer)
            self.training_stats['average_accuracy'].append(batch_accuracy)
            
            # Clear buffer
            self.response_buffer = []

        # Update theta estimates
        self.theta_estimates[student_index] = current_theta
        return current_theta.tolist()

    def _calculate_batch_accuracy(self, batch_data):
        """Calculate prediction accuracy for a batch of responses"""
        correct_predictions = 0
        total_predictions = len(batch_data)
        
        for student_id, item_id, actual_response, _ in batch_data:
            student_idx = self.student_map[student_id]
            item_idx = self.item_map[str(item_id)]
            
            predicted_prob = self._predict_response(
                self.theta_estimates[student_idx],
                self.item_params[item_idx]
            )
            predicted_response = 1 if predicted_prob >= 0.5 else 0
            
            if predicted_response == actual_response:
                correct_predictions += 1
        
        return correct_predictions / total_predictions if total_predictions > 0 else 0.0

    def get_training_stats(self):
        """Get current training statistics"""
        stats = self.training_stats.copy()
        if self.training_stats['average_accuracy']:
            stats['current_accuracy'] = np.mean(self.training_stats['average_accuracy'][-10:])
        return stats

    def fit(self, response_data, item_metadata, correct_answers, save_path=None):
        """
        Initial model fitting with provision for continuous learning
        """
        try:
            # Validate item metadata
            self._validate_item_metadata(item_metadata)
            
            # Store metadata and correct answers
            self.item_metadata = item_metadata
            self.correct_answers = correct_answers

            # Prepare data matrix
            data_matrix = self._prepare_data_matrix(response_data)

            # Initialize theta estimates with proper dimensions
            self.theta_estimates = np.zeros((data_matrix.shape[0], self.num_subskills))
            
            # Initialize item parameters with proper dimensions
            self.item_params = np.ones((data_matrix.shape[1], self.num_subskills))

            with warnings.catch_warnings():
                warnings.simplefilter('ignore', RuntimeWarning)
                
                # Scale the data
                scaler = StandardScaler()
                data_matrix_scaled = scaler.fit_transform(data_matrix)
                data_matrix_scaled = np.nan_to_num(data_matrix_scaled, 0)

                # For each subskill, initialize corresponding parameters
                for subskill_key, index in self.subskill_map.items():
                    # Find items that test this subskill
                    relevant_items = [
                        item_id for item_id, meta in self.item_metadata.items()
                        if any(f"{meta['subject']}_{s}" == subskill_key for s in meta['subskills'])
                    ]
                    
                    if relevant_items:
                        # Initialize with small random values
                        self.theta_estimates[:, index] = np.random.normal(0, 0.1, size=data_matrix.shape[0])
                        self.item_params[:, index] = np.random.normal(1, 0.1, size=data_matrix.shape[1])

            # Initialize training statistics
            self.training_stats['total_responses'] = len(response_data)
            self.training_stats['batch_updates'] = 0
            initial_accuracy = self._calculate_batch_accuracy(response_data)
            self.training_stats['average_accuracy'] = [initial_accuracy]

            if save_path:
                self.save_model(save_path)
            return self

        except Exception as e:
            raise ValueError(f"Error fitting MIRT model: {str(e)}")

        except Exception as e:
            raise ValueError(f"Error fitting MIRT model: {str(e)}")

    def _validate_item_metadata(self, item_metadata):
        """Validate item metadata against the configured subjects"""
        for item_id, metadata in item_metadata.items():
            subject = metadata.get('subject')
            grade_level = metadata.get('grade_level')
            subskills = metadata.get('subskills', [])

            if subject not in self.subjects_config:
                raise ValueError(f"Invalid subject '{subject}' for item {item_id}")

            if grade_level not in self.subjects_config[subject]['grade_levels']:
                raise ValueError(f"Invalid grade level {grade_level} for item {item_id}")

            for subskill in subskills:
                subskill_key = f"{subject}_{subskill}"
                if subskill_key not in self.subskill_map:
                    raise ValueError(f"Invalid subskill '{subskill}' for subject '{subject}' in item {item_id}")

    def save_model(self, filepath):
        """
        Save model state including continuous learning parameters
        
        :param filepath: Path where to save the model
        :return: True if successful
        """
        try:
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            model_state = {
                'version': self.VERSION,
                'subjects_config': self.subjects_config,
                'subskill_map': self.subskill_map,
                'num_subskills': self.num_subskills,
                'theta_estimates': self.theta_estimates,
                'item_params': self.item_params,
                'student_map': self.student_map,
                'item_map': self.item_map,
                'item_metadata': self.item_metadata,
                'correct_answers': self.correct_answers,
                'learning_rate': self.learning_rate,
                'batch_size': self.batch_size,
                'training_stats': self.training_stats,
                'response_buffer': self.response_buffer,
                'saved_at': datetime.now().isoformat()
            }
            
            with open(filepath, 'wb') as f:
                pickle.dump(model_state, f)
            return True
            
        except Exception as e:
            raise ValueError(f"Error saving model: {str(e)}")

    @classmethod
    def load_model(cls, filepath):
        """
        Load model state including continuous learning parameters
        
        :param filepath: Path to the saved model file
        :return: Loaded model instance
        """
        try:
            with open(filepath, 'rb') as f:
                model_state = pickle.load(f)

            if model_state['version'] != cls.VERSION:
                raise ValueError(f"Model version mismatch. Expected {cls.VERSION}, got {model_state['version']}")

            # Create new model instance with saved configuration
            model = cls(
                subjects_config=model_state['subjects_config'],
                learning_rate=model_state['learning_rate'],
                batch_size=model_state['batch_size']
            )

            # Restore all saved attributes
            model.subskill_map = model_state['subskill_map']
            model.num_subskills = model_state['num_subskills']
            model.theta_estimates = model_state['theta_estimates']
            model.item_params = model_state['item_params']
            model.student_map = model_state['student_map']
            model.item_map = model_state['item_map']
            model.item_metadata = model_state['item_metadata']
            model.correct_answers = model_state['correct_answers']
            model.training_stats = model_state['training_stats']
            model.response_buffer = model_state['response_buffer']

            return model
            
        except Exception as e:
            raise ValueError(f"Error loading model: {str(e)}")
        
# Example usage
def example_usage():
    # Create a directory for model storage
    model_dir = "model_storage"
    os.makedirs(model_dir, exist_ok=True)

    # Define subjects configuration with all subskills
    subjects_config = {
        'mathematics': {
            'subskills': ['algebra', 'geometry', 'calculus'],
            'grade_levels': [9, 10, 11, 12]
        },
        'science': {
            'subskills': ['physics', 'chemistry', 'biology'],
            'grade_levels': [9, 10, 11, 12]
        }
    }

    # Initialize model with continuous learning parameters
    model = GenericMIRTModel(
        subjects_config=subjects_config,
        learning_rate=0.05,
        batch_size=32,
    )

    # Initial training data with responses for multiple subskills
    response_data = [
        ('student1', '1', 1, 9),
        ('student1', '2', 0, 9),
        ('student2', '1', 1, 9),
        ('student1', '3', 1, 9),
        ('student1', '4', 0, 9)
    ]

    # Item metadata for all questions including initial and new ones
    item_metadata = {
        '1': {'subject': 'mathematics', 'grade_level': 9, 'subskills': ['algebra']},
        '2': {'subject': 'science', 'grade_level': 9, 'subskills': ['physics']},
        '3': {'subject': 'mathematics', 'grade_level': 9, 'subskills': ['geometry']},
        '4': {'subject': 'science', 'grade_level': 9, 'subskills': ['chemistry']}
    }

    # Correct answers for all questions
    correct_answers = {'1': 1, '2': 1, '3': 1, '4': 1}

    # Step 1: Fit the model with all training data
    model.fit(response_data, item_metadata, correct_answers)
    
    # Save the initial model
    save_path = os.path.join(model_dir, f"mirt_model_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl")
    try:
        model.save_model(save_path)
        print(f"Model saved to: {save_path}")
    except Exception as e:
        print(f"Error saving model: {e}")
        return

    # Step 2: Load the saved model
    try:
        loaded_model = GenericMIRTModel.load_model(save_path)
        print("Model loaded successfully")
    except Exception as e:
        print(f"Error loading model: {e}")
        return
    
    # Verify the loaded model
    original_proficiency = model.get_proficiency('student1')
    loaded_proficiency = loaded_model.get_proficiency('student1')
    print("\nVerifying loaded model:")
    print("Original proficiency:", original_proficiency)
    print("Loaded model proficiency:", loaded_proficiency)
    print("Number of subskills:", loaded_model.num_subskills)
    print("Subskill map:", loaded_model.subskill_map)
    print("Proficiency estimates match:", np.allclose(original_proficiency, loaded_proficiency))

    # Step 3: Update with new responses
    new_responses = [
        ('5', 1),  # New question for geometry
        ('6', 0)   # New question for chemistry
    ]

    # Add metadata for new questions
    new_item_metadata = {
        '5': {'subject': 'mathematics', 'grade_level': 9, 'subskills': ['geometry']},
        '6': {'subject': 'science', 'grade_level': 9, 'subskills': ['chemistry']}
    }
    loaded_model.item_metadata.update(new_item_metadata)
    loaded_model.correct_answers.update({'5': 1, '6': 1})

    # Update proficiency
    updated_proficiency = loaded_model.update_proficiency('student1', new_responses, 9)

    # Save updated model
    updated_save_path = os.path.join(model_dir, f"mirt_model_updated_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl")
    try:
        loaded_model.save_model(updated_save_path)
        print(f"\nUpdated model saved to: {updated_save_path}")
    except Exception as e:
        print(f"Error saving updated model: {e}")
        return
    
    # Print final results with detailed proficiency by subject
    print("\nFinal Results:")
    print("Updated proficiency vector:", updated_proficiency)
    
    # Print proficiency by subject
    for subject in subjects_config.keys():
        subject_prof = loaded_model.get_subject_proficiency('student1', subject)
        print(f"\n{subject.capitalize()} proficiency:")
        for subskill, value in subject_prof.items():
            print(f"  {subskill}: {value:.3f}")
    
    print("\nTraining Stats:", loaded_model.get_training_stats())

if __name__ == "__main__":
    example_usage()