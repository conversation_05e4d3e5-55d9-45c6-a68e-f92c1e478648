from typing import Op<PERSON>, <PERSON><PERSON>, <PERSON>, Dict, List
import requests
import json
from fastapi import FastAPI, HTTPException, Depends, Query
from pydantic import BaseModel, Field
import numpy as np
from mirt_model import GenericMIRTModel
from collaborative_filtering import CollaborativeFiltering
from functools import lru_cache

app = FastAPI(
    title="Learning Analytics API",
    description="API for student proficiency tracking and question recommendations",
    version="1.0.0"
)

# Dependencies
@lru_cache()
def get_mirt_model():
    model = GenericMIRTModel(num_subskills=5)
    try:
        return GenericMIRTModel.load_model('models/mirt_model.pkl')
    except:
        return model

cf_model = CollaborativeFiltering()

# Pydantic models
class ResponseData(BaseModel):
    data: List[Tuple[str, str, int]] = Field(
        ...,
        description="List of (student_id, item_id, response) tuples"
    )

class ItemSubskills(BaseModel):
    data: Dict[str, List[str]] = Field(
        ...,
        description="Mapping of item_id to list of subskills"
    )

class CorrectAnswers(BaseModel):
    data: Dict[str, int] = Field(
        ...,
        description="Mapping of item_id to correct answer"
    )

class ItemResponses(BaseModel):
    student_id: str = Field(..., description="Unique identifier for the student")
    responses: List[Tuple[str, int]] = Field(
        ...,
        description="List of (item_id, response) tuples"
    )

class UserItemMatrix(BaseModel):
    matrix: List[List[float]] = Field(
        ...,
        description="User-item interaction matrix"
    )

class UserSubjectProf(BaseModel):
    data: Dict[str, Union[Tuple[float, List[str]], float]] = Field(
        ...,
        description="User subject proficiency data"
    )
    numquestions: Optional[int] = Field(
        None,
        description="Number of questions to recommend",
        ge=1
    )
    # KnowledgeGraph: object = Field(
    #     ...,
    #     description="User knowledge graph"
    # )

# API endpoints
@app.post("/fit_mirt", response_model=dict)
async def fit_mirt(
    response_data: ResponseData,
    item_subskills: ItemSubskills,
    correct_answers: CorrectAnswers,
    mirt_model: GenericMIRTModel = Depends(get_mirt_model)
):
    try:
        mirt_model.fit(response_data.data, item_subskills.data, correct_answers.data)
        mirt_model.save_model('models/mirt_model.pkl')
        return {"message": "MIRT model fitted successfully", "status": "success"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Model fitting failed: {str(e)}")

@app.post("/update_proficiency", response_model=dict)
async def update_proficiency(
    item_responses: ItemResponses,
    mirt_model: GenericMIRTModel = Depends(get_mirt_model)
):
    try:
        # Debug prints
        print(f"Received responses: {item_responses.responses}")
        print(f"Model item parameters shape: {mirt_model.item_parameters.shape if hasattr(mirt_model, 'item_parameters') else 'No item parameters'}")
        
        # Validate item IDs exist in model
        item_ids = [resp[0] for resp in item_responses.responses]
        if hasattr(mirt_model, 'item_map'):
            print(f"Model item map: {mirt_model.item_map}")
            invalid_items = [item_id for item_id in item_ids if item_id not in mirt_model.item_map]
            if invalid_items:
                raise ValueError(f"Items {invalid_items} not found in model training data")
        
        # Validate response values are binary
        invalid_responses = [resp for resp in item_responses.responses if resp[1] not in [0, 1]]
        if invalid_responses:
            raise ValueError(f"Invalid response values: {invalid_responses}. Must be 0 or 1")

        updated_proficiency = mirt_model.update_proficiency(
            item_responses.student_id,
            item_responses.responses
        )
        return {
            "updated_proficiency": updated_proficiency,
            "student_id": item_responses.student_id
        }
    except ValueError as ve:
        raise HTTPException(status_code=400, detail=str(ve))
    except Exception as e:
        import traceback
        error_trace = traceback.format_exc()
        raise HTTPException(
            status_code=400,
            detail=f"Failed to update proficiency: {str(e)}\nTrace: {error_trace}"
        )

@app.get("/get_proficiency/{student_id}", response_model=dict)
async def get_proficiency(
    student_id: str,
    mirt_model: GenericMIRTModel = Depends(get_mirt_model)
):
    try:
        proficiency = mirt_model.get_proficiency(student_id)
        return {"student_id": student_id, "proficiency": proficiency}
    except Exception as e:
        raise HTTPException(
            status_code=404,
            detail=f"Failed to get proficiency for student {student_id}: {str(e)}"
        )

@app.post("/fit_cf")
async def fit_cf(user_item_matrix: UserItemMatrix):
    try:
        matrix = np.array(user_item_matrix.matrix)
        cf_model.fit(matrix)
        return {"message": "Collaborative filtering model fitted successfully"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

@app.post("/initnRec")
async def initUserProf(usersubprof: UserSubjectProf):
    data = usersubprof.data
    numQuestions = usersubprof.numquestions
    cf_model.fillUserInfo(data)
    questionBank = None
    url = 'http://localhost:8080/api/questionbank/questions'
    try:
        response = requests.get(url)
        if (response.status_code == 200):
            questionBank = response.json()
    except Exception as e:
        return {"message": str(e), 'status': 500}
    assert questionBank is not None, "questionbank could not be read"
    listOfQuestions = None
    if (numQuestions is not None):
        listOfQuestions = cf_model.recommendQuestions(questionBank, numQuestions)
    else:
        listOfQuestions = cf_model.recommendQuestions(questionBank)
    return {"message" :"User filled successfully\n", 'questions' : listOfQuestions}

@app.get("/recommend/{user_id}")
async def recommend(user_id: int, n_recommendations: int = 5):
    try:
        recommendations = cf_model.recommend(user_id, n_recommendations)
        return {"recommendations": recommendations}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
