from sklearn.metrics.pairwise import cosine_similarity
from sklearn.feature_extraction.text import TfidfVectorizer
import json
import sys

class SpecialSet:
    def __init__(self, thresh=0.6):
        self.thresh = thresh
        self.seen = []
        self.tokenizer = TfidfVectorizer()
        self.seen_text = []

    def __is_similar(self, question, all_text):
        all_strings = self.seen_text + [all_text]
        vectorCur = self.tokenizer.fit_transform(all_strings)
        # print(f"vecotrcur : {vectorCur}")
        cosine_sim = cosine_similarity(vectorCur[-1], vectorCur[:-1])
        max_sim = cosine_sim.max()
        # print(f"max_sim: {max_sim}")
        return max_sim > self.thresh

    def compareString(self, question):
        if ('passage' in question.keys()):
            return (question['passage'] + ' ' + question['question']).strip()
        else:
            return question['question'].strip()


    def add(self, question):
        questiontext = self.compareString(question)
        if (len(self.seen) == 0):
            self.seen.append(question)
            self.seen_text.append(questiontext)
            return True
        if not self.__is_similar(question, questiontext):
            self.seen.append(question)
            self.seen_text.append(questiontext)
            return True
        return False

    def __iter__(self):
        return iter(self.seen)


def main():
    print(f"[PYTHON][INFO] argc: {len(sys.argv)}", file=sys.stderr)
    special_set = SpecialSet(thresh=0.9)
    allQuestions = sys.stdin.read()
    presentquestions = json.loads(allQuestions)['apq']
    questions = json.loads(allQuestions)['questions']
    addedquestions = []

    for presentquestion in presentquestions:
        special_set.add(presentquestion)

    for question in questions:
        if (special_set.add(question)):
            addedquestions.append(question)

    print(json.dumps(addedquestions))

if __name__ == "__main__":
    main()
