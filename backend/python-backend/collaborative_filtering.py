from os import error
import random
import sys
import numpy as np

class CollaborativeFiltering:
    def __init__(self):
        self.userSubjectProficiency = None
        self.ratios = []

    def compareStringsMostly(self, str1, str2):
        if (abs(len(str1) - len(str2)) > 3):
            return False
        n = min(len(str1), len(str2))
        threshold = n*0.8
        countSame = 0
        for i in range(n):
            if (str1[i] == str2[i]):
                countSame += 1
        return (countSame >= n-threshold)

    def findInKnowledgeGraph(self, field, value):
        assert self.userSubjectProficiency != None, "User not initialized"
        for i in self.userSubjectProficiency:
            # if (i['nodeId'][field] == value):
            if (self.compareStringsMostly(i['nodeId'][field], value)):
                return i
        return None

    def clearUserInfo(self):
        self.userSubjectProficiency = None
        self.ratios = []

    def fillUserInfo(self, userSubjectProficiency, ratio = [0.1, 0.7, 0.2]):
        '''
        userSubject proficiency needs to be in the following format:
        {
        'topic' : [overalprof(float), ['subtopics']],
        'subtopic' : profs (float)
        ...
        }

        ratios are of format: [hard, medium, easy] need to add up to 1
        '''
        self.userSubjectProficiency = userSubjectProficiency['curriculumProgress']
        eps = 0.001
        assert np.sum(ratio) == 1 or (np.sum(ratio) > 1-eps and np.sum(ratio) < 1+eps), "Invalid question ratios provided"
        self.ratios = ratio

    def aggregate(self, topicProfs):
        '''Assign weights to subtopic proficiency and topic proficiency  to get an estimate of proficiency for the given question'''
        finalprof = 0
        # let's give equal weightage to each topic and within the topic i want to give more weightage to the subtopics
        # so the formula comes out as final = sigma 1/len(topics)(0.4*topic + 0.6*(0.5 * sumofsub))
        topicwisearrangement = [] # [[topicprf, [subtopicprof...]], ...]
        topicWeightage = 0.3
        assert self.userSubjectProficiency != None, "user not initialized for recommendation"

        for topic in topicProfs:
            profs = []
            for subid in topic[1]:
                prof = self.findInKnowledgeGraph("_id", subid)
                if (prof == None):
                    continue
                profs.append(prof['proficiency'])
            topicwisearrangement.append([topic[0], profs])
        finalprof = 0
        for each in topicwisearrangement:
            try:
                finalprof += (1/len(topicwisearrangement))*(topicWeightage * each[0] + (1-topicWeightage) * np.average(each[1]))
            except Exception as e:
                raise RuntimeError(f"[ERROR]: {str(e)}")

        return finalprof

    def computeDiffForUser(self, question):
        '''
        question is of format: {
        'question' : '...',
        'option' : ['...'],
        'answer' : '...',
        'solution': '...',
        'images' : ['...'],
        'topic': '...,...',
        'subtopics': '...,...',
        'difficulty' : '...',
        'dicriminate parameter': '...'
        }
        '''
        questiontopics = [topic.strip() for topic in question['topic'].split(',')]
        questionsubtopics = [st.strip() for st in question['subtopic'].split(',')]
        StudentTopicProfs = [] # user proficiency for all related topics
        StudentSubtopicProfs = [] # user proficiency for all related subtopic
        for topic in questiontopics:
            assert self.userSubjectProficiency != None, "user not initialized for recommendation"
            try:
                # StudentTopicProfs[topic] = self.userSubjectProficiency[topic][0]
                subjectDetail = self.findInKnowledgeGraph('name', topic)
                print(f"[INFO] topic : {topic}", file=sys.stderr)
                assert subjectDetail != None, "topic not found in knowledge graph"
                # print(f"[INFO] subject detail is: {subjectDetail}", file=sys.stderr)
                subtopics = subjectDetail['nodeId']['children']
                StudentTopicProfs.append([subjectDetail['proficiency'], subtopics])
            except Exception as e:
                raise RuntimeError("[ERROR] failing for topic: '{}', {}".format(topic, str(e)))
                
        for subtopic in questionsubtopics:
            assert self.userSubjectProficiency != None, "user not initialized for recommendation"
            try:
                subjectDetail = self.findInKnowledgeGraph('name', subtopic)
                print(f"[INFO] subtopic : {subtopic}", file=sys.stderr)
                assert subjectDetail != None, "subtopic not found in knowledge graph"
                subtopics = subjectDetail['nodeId']['children']
                StudentSubtopicProfs.append([subjectDetail['proficiency'], subtopics])
            except Exception as e:
                raise RuntimeError("[ERROR] failing for subtopic: '{}', {}".format(subtopic, str(e)))


        userProficiency = self.aggregate(StudentTopicProfs)
        mediumMargin = 0.2

        if (userProficiency > question['difficulty']):
            return 'easy'
        elif (userProficiency > question['difficulty']-mediumMargin):
            return 'medium'
        else:
            return 'hard'

    def recommendQuestionsDiagnostic(self, questionBank, numquestions=10):
        chosenQuestions = []
        topicSubtopicDict = {}
        assert self.userSubjectProficiency != None, "user not initialized for recommendation"
        numtopics = 0
        for i in self.userSubjectProficiency:
            # print(f" individual value: {i}", file=sys.stderr)
            if (i['nodeId']['type'] == 'Chapter'):
                numtopics += 1
            if (topicSubtopicDict.__contains__(i['nodeId']["name"])):
                continue
            else:
                topicSubtopicDict[i['nodeId']["name"]] = False

        print(f"[INFO] number of topics: {numtopics}", file=sys.stderr)
        
        for question in questionBank:
            if (question['options'] is None):
                continue
            if (len(chosenQuestions) >= numtopics):
                break
            questiontopics = question['topic'].split(',')
            questiontopics = [topic.strip() for topic in questiontopics]
            # questiontopics.extend(question['subtopic'].split(','))
            print(f"[INFO] questiontopics is: {questiontopics}", file=sys.stderr)
            questiontopics = [topic.strip() for topic in questiontopics]
            chosen = False
            for k in questiontopics:
                if (not topicSubtopicDict.__contains__(k) or not topicSubtopicDict[k]):
                    # choose this question if not already chosen
                    if (not chosen):
                        chosenQuestions.append(question)
                        chosen = True
                    topicSubtopicDict[k] = True
        print(f"[INFO] number of chosenQuestions: {len(chosenQuestions)}", file=sys.stderr)
        return random.sample(chosenQuestions, numquestions) if (len(chosenQuestions) > numquestions) else chosenQuestions

    def recommendQuestions(self, questionBank, n_recom=5):
        hardQuest = [] # need to be 10%
        medQuest = [] # need to be 70%
        easyQuest = [] # need to be 20%
        totalQuestions = []
        for question in questionBank:
            if (question['options'] is None):
                print(f"[PYTHON][Warning] {question} current question has no options, skipping", file=sys.stderr)
                continue
            if (len(hardQuest) + len(medQuest) + len(easyQuest) == n_recom):
                break
            if (self.computeDiffForUser(question) == 'hard' and len(hardQuest) < n_recom*self.ratios[0]):
                question['assignedDiff']= 'hard'
                hardQuest.append(question)
            elif (self.computeDiffForUser(question) == 'medium' and len(medQuest) < n_recom*self.ratios[1]):
                question['assignedDiff']= 'easy'
                medQuest.append(question)
            elif (self.computeDiffForUser(question) == 'easy' and len(medQuest) < n_recom*self.ratios[2]):
                question['assignedDiff']= 'easy'
                easyQuest.append(question)
        if (len(hardQuest) + len(medQuest) + len(easyQuest) != n_recom):
            # could not find the exact ratio in the question bank
            # for now just fill it with any difficulty questions so that we don't run out of recommendations
            extraQuestions = n_recom - len(hardQuest) - len(medQuest) - len(easyQuest)
            for question in questionBank:
                if (question['options'] is None):
                    print(f"[PYTHON][Warning] {question} current question has no options, skipping", file=sys.stderr)
                    continue
                if (extraQuestions == 0):
                    break
                if (extraQuestions and question not in medQuest and question not in hardQuest and question not in easyQuest):
                    question['assignedDiff']= self.computeDiffForUser(question)
                    if (question['assignedDiff'] == 'easy'):
                        easyQuest.append(question)
                    elif (question['assignedDiff'] == 'medium'):
                        medQuest.append(question)
                    else:
                        hardQuest.append(question)
                    extraQuestions -= 1
            if (extraQuestions != 0):
                print("[PYTHON][ERROR] Too many questions requested returning the ones available\n", file=sys.stderr);
            totalQuestions = hardQuest.copy()
            totalQuestions.extend(medQuest)
            totalQuestions.extend(easyQuest)
            return totalQuestions
        else:
            totalQuestions = hardQuest.copy()
            totalQuestions.extend(medQuest)
            totalQuestions.extend(easyQuest)
            return totalQuestions

