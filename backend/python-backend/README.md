# MIRT and Collaborative Filtering API

This Python API provides endpoints for Multidimensional Item Response Theory (MIRT) modeling and Collaborative Filtering recommendations.

## Setup

1. Ensure you have Python 3.7+ installed.

2. Create a virtual environment:
   ```
   python -m venv venv
   ```

3. Activate the virtual environment:
   - On Windows:
     ```
     venv\Scripts\activate
     ```
   - On macOS and Linux:
     ```
     source venv/bin/activate
     ```

4. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

## Running the API

To start the API server, run:

```
uvicorn main:app --reload
```

The API will be available at `http://localhost:8000`.

## Endpoints

- `POST /fit_mirt`: Fit the MIRT model
- `POST /update_proficiency`: Update student proficiency
- `GET /get_proficiency/{student_id}`: Get proficiency for a student
- `POST /fit_cf`: Fit the Collaborative Filtering model
- `GET /recommend/{user_id}`: Get recommendations for a user

## Files

- `main.py`: FastAPI application and route definitions
- `mirt_model.py`: MIRT model implementation
- `collaborative_filtering.py`: Collaborative Filtering model implementation
- `requirements.txt`: Required Python packages

## Notes

- Make sure to keep your virtual environment activated while working on this project.
- For production deployment, consider using a production-grade ASGI server like Gunicorn.