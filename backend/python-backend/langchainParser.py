import os
import time
import sys
import json
# from langchain.chat_models import init_chat_model
from google import genai
from pydantic import BaseModel, Field
from typing import TypedDict, Annotated, Optional, List


def initialize():
    if not os.environ.get("GOOGLE_API_KEY"):
      os.environ["GOOGLE_API_KEY"] = "AIzaSyBy3chjlLN7ZVnXm-BI_tSflqzs3gdBSPA"

def langChainRun(data, topics, subtopics):
    initialize()
    class Question(BaseModel):
        """QnA Pair to add to the database."""
        passage: str = Field(description="Passage on which the questions are based. by default this should be empty")
        question: str = Field(description="The actual question")
        options: Optional[list[str]] = Field(description='the options for the question')
        answer: str = Field(description="The answer to the question")
        difficulty: float = Field(description="The difficulty of the question from 0 to 1")
        solution: str = Field(description="The solution to the question")
        topic: str = Field(description=f"The comma separated topics to which the question belongs from the following list: ```{topics}```")
        subtopic: str = Field(description=f"The comma separated list of subtopic to which the question belongs from the following list: ```{subtopics}```")
        bloomTaxonomy: str = Field(description="The bloom taxonomy(basically which category the question belongs to) of the question out of the following: [Create, Evaluate, Analyze, Apply, Understand, Remember]")
        differentiatingParameter: float = Field(description='The differentiating parameter of the question from 0 to 1')

    client = genai.Client(api_key=os.environ['GOOGLE_API_KEY'])

    # llm = init_chat_model("mistral-small-latest", model_provider="mistralai")
    # llm = init_chat_model("gemini-2.0-flash-001", model_provider="google_vertexai")
    # structured_llm = llm.with_structured_output(AllQuestions)
    # output = []
    prompt = """You are an expert json file formatter who extracts all the relevant data from any given json file. Your task is to extract all the questions and answer pairs from the following textual data which is broken in different pages. Take care of the following points:
    1. Question and answer maybe split up in different pages(structures). Handle these cases appropriately by merging the parts on the different pages into 1 question.
    2. Images are provided in base 64 encoded format in the data and there tags are mentioned in the question. Include the image tags in the output and skip the base64 encoded images.
    3. The topic field is mandatory and must be from the following list: ```{topics}```. In case of multiple topics applicable, separate them with a comma.
    4. The subtopic field is mandatory and must be from the following list: ```{subtopics}```. In case of multiple subtopics applicable, separate them with a comma.
    5. The bloom taxonomy should be from the list provided exactly.
    6. The output should be in clean and proper json format containing only the question-answer object as specified in the schema. There may be some latex expressions in the input data so handle those so that the final object is proper json.
    7. There may be back slashes do not escape those and assume that they are already escaped.
    8. An optional passage field is provided in the schema. This should only be used for the case where a passage or article is given and next couple of questions are based on that passage otherwise keep this field empty.

    Provide the output in proper json format.
    Here is the data you need to process: {data}
    """
    try:
        # maximum context length of mistral model is 131072 tokens
        # so we need to send only the current and the next pages as data
        questions = []
        response = None

        pages = data['pages']
        for pageIndex in range(0, len(pages), 1):
            # if (pageIndex+1 >= len(pages)):
            #     consPages = str(pages[pageIndex])
            # else:
            #     consPages = str(pages[pageIndex])+','+str(pages[pageIndex+1])
            try:
                response = client.models.generate_content(
                    model='gemini-2.0-flash',
                    contents=prompt.format(topics=topics, subtopics=subtopics, data=pages[pageIndex]),
                    config={
                        'response_mime_type': 'application/json',
                        'response_schema': list[Question],
                        'temperature': 0,
                    },
                ).text
                questions.append(json.loads(response))
            except genai.errors.ClientError as e:
                # retru after some time
                print(f"[PYTHON][ERROR] hit the gemini rate limiter waiting for a minute", file=sys.stderr)
                waittime = 60 # later do this dynamically
                time.sleep(waittime)
                response = client.models.generate_content(
                    model='gemini-2.0-flash',
                    contents=prompt.format(topics=topics, subtopics=subtopics, data=pages[pageIndex]),
                    config={
                        'response_mime_type': 'application/json',
                        'response_schema': list[Question],
                        'temperature': 0,
                    },
                ).text
                questions.append(json.loads(response))


    except Exception as e:
        raise RuntimeError(f"LLM extraction failed with error: {e}")
    return questions

