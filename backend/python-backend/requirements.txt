absl-py
accelerate
acres
aiohappyeyeballs
aiohttp
aiosignal
albucore
annotated-types
antlr4-python3-runtime
anyio
appnope
argon2-cffi
argon2-cffi-bindings
arrow
astor
asttokens
async-lru
attrs
babel
beautifulsoup4
bert-score
black
bleach
blis
boto3
braceexpand
Brotli
catalogue
certifi
cffi
charset-normalizer
ci-info
click
cloudpathlib
cloudpickle
cloup
cohere
colorama
colorlog
comm
confection
configobj
configparser
cryptography
cycler
cymem
Cython
dataclasses-json
datasets
debugpy
decorator
defusedxml
Deprecated
dill
dirtyjson
distro
docker-pycreds
docling
docling-core
docling-ibm-models
docling-parse
easyocr
einops
et_xmlfile
etelemetry
evaluate
executing
fairscale
fast-langdetect
fastapi
fastavro
fastjsonschema
fasttext-predict
fasttext-wheel
filelock
filetype
fire
fonttools
fqdn
frozenlist
fsspec
ftfy
fvcore
gitdb
GitPython
glcontext
graphviz
greenlet
groq
grpcio
h11
httpcore
httplib2
httpx
httpx-sse
huggingface-hub
hydra-core
icecream
idna
imageio
imgaug
importlib_resources
iopath
ipykernel
isodate
isoduration
isosurfaces
jedi
Jinja2
jiter
jmespath
joblib
json5
jsonlines
jsonpatch
jsonpointer
jsonref
jsonschema
jsonschema-specifications
jupyter_client
jupyter_core
jupyter-events
jupyter-lsp
jupyter_server
jupyter_server_terminals
jupyterlab
jupyterlab_pygments
jupyterlab_server
langchain
langchain-community
langchain-core
langchain-mistralai
langchain-text-splitters
langcodes
langdetect
langsmith
language_data
latex2mathml
lazy_loader
llama-cloud
llama-cloud-services
llama-index
llama-index-agent-openai
llama-index-cli
llama-index-core
llama-index-embeddings-openai
llama-index-indices-managed-llama-cloud
llama-index-llms-openai
llama-index-multi-modal-llms-openai
llama-index-postprocessor-cohere-rerank
llama-index-program-openai
llama-index-question-gen-openai
llama-index-readers-file
llama-index-readers-llama-parse
llama-parse
lmdb
loguru
looseversion
lxml
ManimPango
mapbox_earcut
marisa-trie
Markdown
markdown-it-py
marko
MarkupSafe
marshmallow
matplotlib-inline
mdurl
mistune
moderngl
moderngl-window
mpire
mpmath
msgpack
multidict
multiprocess
murmurhash
mypy-extensions
nbclient
nbconvert
nbformat
nest-asyncio
nibabel
ninja
nipype
nltk
notebook
notebook_shim
numpy
omegaconf
openai
opencv-contrib-python
opencv-python
opencv-python-headless
openpyxl
opt-einsum
orjson
outcome
overrides
packaging
pandas
pandocfilters
parameterized
parso
pathlib
pathspec
pdf2image
pdfminer.six
pexpect
pillow
pip
platformdirs
portalocker
preshed
prometheus_client
prompt_toolkit
propcache
protobuf
prov
psutil
ptyprocess
pure_eval
puremagic
py-cpuinfo
pyarrow
pybind11
pyclipper
pycocotools
pycparser
pydantic
pydantic_core
pydantic-settings
pydot
pydub
pyemd
pyglet
PyGLM
Pygments
pyobjc-core
pyobjc-framework-Cocoa
pyparsing
pypdf
PyPDF2
pypdfium2
PySocks
pytextrank
python-bidi
python-dateutil
python-docx
python-dotenv
python-json-logger
python-pptx
pytils
pytz
pyxnat
PyYAML
pyzmq
RapidFuzz
ray
rdflib
referencing
regex
requests
requests-toolbelt
rfc3339-validator
rfc3986-validator
rich
robust-downloader
rpds-py
Rtree
s3transfer
safetensors
scikit-learn
screeninfo
seaborn
selenium
semchunk
Send2Trash
sentencepiece
sentry-sdk
setproctitle
setuptools
shapely
shellingham
simplejson
simsimd
six
skia-pathops
smart-open
smmap
sniffio
sortedcontainers
soupsieve
spacy-legacy
spacy-loggers
SQLAlchemy
srsly
srt
stack-data
starlette
stringzilla
striprtf
SudachiDict-core
SudachiPy
svgelements
sympy
tabulate
tenacity
tensorboard
tensorboard-data-server
tensorboardX
termcolor
terminado
thinc
thop
threadpoolctl
tiktoken
timm
tinycss2
tokenizers
toml
tomli
torch
torchdata
torchvision
tornado
tqdm
traitlets
traits
transformers
trio
trio-websocket
typer
types-python-dateutil
types-requests
typing_extensions
typing-inspect
tzdata
ultralytics-thop
uri-template
urllib3
uvicorn
Wand
wandb
wasabi
watchdog
wcwidth
weasel
webcolors
webencodings
websocket-client
Werkzeug
wheel
wordninja
wrapt
wsproto
XlsxWriter
xxhash
yacs
yarl
zstandard
