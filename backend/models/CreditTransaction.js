import { Schema, model } from 'mongoose';

// Enum-like constants for transaction types
export const TRANSACTION_TYPES = {
    PURCHASE: 'PURCHASE',
    USAGE: 'USAGE',
    REFUND: 'REFUND',
    BONUS: 'BONUS',
    INITIAL_GRANT: 'INITIAL_GRANT'
};

// Enum-like constants for transaction status
export const TRANSACTION_STATUS = {
    PENDING: 'PENDING',
    COMPLETED: 'COMPLETED',
    FAILED: 'FAILED',
    CANCELLED: 'CANCELLED'
};

// Enum-like constants for usage types
export const USAGE_TYPES = {
    AEGIS_GRADER: 'AEGIS_GRADER',
    FUTURE_FEATURE: 'FUTURE_FEATURE'
};

const CreditTransactionSchema = new Schema({
    // User Information
    userId: { 
        type: Schema.Types.ObjectId, 
        required: true,
        index: true
    },
    userType: { 
        type: String, 
        enum: ['Student', 'Teacher'], 
        required: true 
    },
    
    // Transaction Details
    transactionId: { 
        type: String, 
        required: true, 
        unique: true,
        index: true
    },
    type: { 
        type: String, 
        enum: Object.values(TRANSACTION_TYPES), 
        required: true,
        index: true
    },
    status: { 
        type: String, 
        enum: Object.values(TRANSACTION_STATUS), 
        default: TRANSACTION_STATUS.PENDING,
        index: true
    },
    
    // Credit Information
    creditAmount: { 
        type: Number, 
        required: true,
        min: 0
    },
    balanceBefore: { 
        type: Number, 
        required: true,
        min: 0
    },
    balanceAfter: { 
        type: Number, 
        required: true,
        min: 0
    },
    
    // Payment Information (for purchases)
    payment: {
        razorpayOrderId: { type: String },
        razorpayPaymentId: { type: String },
        razorpaySignature: { type: String },
        amount: { type: Number }, // Amount in rupees
        currency: { type: String, default: 'INR' },
        packageType: { type: String }, // e.g., 'PROFESSIONAL_500', 'PREMIUM_10000', 'CUSTOM_500'
        packageName: { type: String }, // e.g., '500 Credits', '1000 Credits', '10000 Credits'
    },
    
    // Usage Information (for credit consumption)
    usage: {
        feature: { 
            type: String, 
            enum: Object.values(USAGE_TYPES)
        },
        description: { type: String }, // e.g., 'AegisGrader evaluation for Math test'
        relatedId: { type: String }, // e.g., AegisGrader submission ID
        metadata: { type: Schema.Types.Mixed } // Additional context data
    },
    
    // Timestamps
    createdAt: { 
        type: Date, 
        default: Date.now,
        index: true
    },
    updatedAt: { 
        type: Date, 
        default: Date.now 
    },
    completedAt: { type: Date },
    
    // Additional Information
    description: { type: String },
    notes: { type: String }, // Internal notes
    ipAddress: { type: String },
    userAgent: { type: String }
}, { 
    collection: 'creditTransactions',
    timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' }
});

// Indexes for better query performance
CreditTransactionSchema.index({ userId: 1, createdAt: -1 });
CreditTransactionSchema.index({ type: 1, status: 1 });
CreditTransactionSchema.index({ 'payment.razorpayOrderId': 1 });
CreditTransactionSchema.index({ 'payment.razorpayPaymentId': 1 });
CreditTransactionSchema.index({ 'usage.feature': 1, createdAt: -1 });

// Pre-save middleware to update timestamps
CreditTransactionSchema.pre('save', function(next) {
    if (this.isModified() && !this.isNew) {
        this.updatedAt = new Date();
    }
    
    // Set completedAt when status changes to COMPLETED
    if (this.isModified('status') && this.status === TRANSACTION_STATUS.COMPLETED && !this.completedAt) {
        this.completedAt = new Date();
    }
    
    next();
});

// Static methods for common queries
CreditTransactionSchema.statics.findByUser = function(userId, userType, options = {}) {
    const query = { userId, userType };
    
    if (options.type) query.type = options.type;
    if (options.status) query.status = options.status;
    if (options.feature) query['usage.feature'] = options.feature;
    
    return this.find(query)
        .sort({ createdAt: -1 })
        .limit(options.limit || 50);
};

CreditTransactionSchema.statics.findPendingPayments = function(userId, userType) {
    return this.find({
        userId,
        userType,
        type: TRANSACTION_TYPES.PURCHASE,
        status: TRANSACTION_STATUS.PENDING
    }).sort({ createdAt: -1 });
};

CreditTransactionSchema.statics.getTotalSpent = function(userId, userType) {
    return this.aggregate([
        {
            $match: {
                userId,
                userType,
                type: TRANSACTION_TYPES.USAGE,
                status: TRANSACTION_STATUS.COMPLETED
            }
        },
        {
            $group: {
                _id: null,
                totalCredits: { $sum: '$creditAmount' }
            }
        }
    ]);
};

CreditTransactionSchema.statics.getTotalPurchased = function(userId, userType) {
    return this.aggregate([
        {
            $match: {
                userId,
                userType,
                type: TRANSACTION_TYPES.PURCHASE,
                status: TRANSACTION_STATUS.COMPLETED
            }
        },
        {
            $group: {
                _id: null,
                totalCredits: { $sum: '$creditAmount' },
                totalAmount: { $sum: '$payment.amount' }
            }
        }
    ]);
};

// Instance methods
CreditTransactionSchema.methods.markFailed = function(reason) {
    this.status = TRANSACTION_STATUS.FAILED;
    this.notes = reason;
    this.updatedAt = new Date();
    return this.save();
};

CreditTransactionSchema.methods.markCompleted = function() {
    this.status = TRANSACTION_STATUS.COMPLETED;
    this.completedAt = new Date();
    this.updatedAt = new Date();
    return this.save();
};

CreditTransactionSchema.methods.markCancelled = function(reason) {
    this.status = TRANSACTION_STATUS.CANCELLED;
    this.notes = reason;
    this.updatedAt = new Date();
    return this.save();
};

// Instance methods
CreditTransactionSchema.methods.markCompleted = function() {
    this.status = TRANSACTION_STATUS.COMPLETED;
    this.completedAt = new Date();
    return this.save();
};

CreditTransactionSchema.methods.markFailed = function(reason) {
    this.status = TRANSACTION_STATUS.FAILED;
    if (reason) this.notes = reason;
    return this.save();
};

export default model('CreditTransaction', CreditTransactionSchema);
