import { model, Schema } from "mongoose";

const TestConfigSchema = new Schema({
  testId: { type: Schema.Types.ObjectId, ref: 'TestHistory', required: true },
  isAdaptive: { type: Boolean, default: false },
  allowBatchProcessing: { type: Boolean, default: false },
  maxUsersForRealTime: { type: Number, default: 0 },
  diagnosticTest: { type: Boolean, default: false }
}, {collection: 'TestConfig'});

export default model('TestConfig', TestConfigSchema)
