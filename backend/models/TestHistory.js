import { model, Schema } from "mongoose";

const TestHistorySchema = new Schema({
    class: {type : Schema.Types.ObjectId, ref: 'Class', required: true},
    subject: {type : String, required: true},
    testType: {type : String, required: true},
    topics: {type : [String], required: true},
    questions: {type : [Schema.Types.ObjectId], ref: 'Question'},
    testDate: {type : Date, required: true},
    startTime: {type : String, required: true},
    duration: {type : Number, required: true},
    numberOfQuestions: {type : Number, required: true},
    totalMarks: {type : Number, required: true},
    testInstructions: {type : String, default: ""},
    active: {type : Boolean, default: true},
    lastStatusUpdate: {
        type: Date,
        default: Date.now
    },
    createdBy: {type : Schema.Types.ObjectId, ref: 'Teacher', required: true},
}, { timestamps: true , collection: 'testHistory'});

export default model('TestHistory', TestHistorySchema);