import mongoose from 'mongoose';

const CurriculumProgressSchema = new mongoose.Schema({
  // Dynamic model reference using refPath
  nodeId: { 
    type: mongoose.Schema.Types.ObjectId, 
    required: true,
    refPath: 'nodeModel'
  },
  // Store the model name for the nodeId reference
  nodeModel: {
    type: String,
    required: true,
    default: 'CurriculumNode' // Default will be overridden by our method
  },
  subject: {
    type: String,
    required: true // Helps determine the correct dynamic model
  },
  status: { 
    type: String, 
    enum: ['Not Started', 'In Progress', 'Completed'], 
    default: 'Not Started' 
  },
  proficiency: { 
    type: Number, 
    default: 0 
  }
});

const StudentCurriculumSchema = new mongoose.Schema({
  studentId: { 
    type: mongoose.Schema.Types.ObjectId, 
    ref: 'Student', 
    required: true, 
  },
  subject: { 
    type: String,
    required: true
  },
  curriculumProgress: [CurriculumProgressSchema],
  totalProficiency: { 
    type: Number, 
    default: 0 
  }
});

// Add this to your schema
StudentCurriculumSchema.index({ studentId: 1, subject: 1 }, { unique: true });

// Schema method to set nodeModel based on subject
StudentCurriculumSchema.methods.setNodeModelForSubject = function(progressItem, subject) {
  progressItem.nodeModel = `CurriculumNode_${subject}`;
  progressItem.subject = subject;
  console.error(`Updated nodeModel to ${progressItem.nodeModel} for subject ${subject}`);
};

export const StudentCurriculumModel = mongoose.model(
  'StudentCurriculum',
  StudentCurriculumSchema,
  'studentKnowledgeGraph'
);
