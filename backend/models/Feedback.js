import mongoose from 'mongoose';

const feedbackSchema = new mongoose.Schema({
  userId: { type: String, required: true },
  role: {type: String, required: true },
  feedbackType: { type: String, enum: ['like', 'dislike', 'bug'], required: true },
  comment: { type: String, default: '' },
  createdAt: { type: Date, default: Date.now },
  images: [{
      filename: { // Original filename
        type: String,
        required: true
      },
      imageData: { // The binary data
        type: Buffer,
        required: true
      },
      contentType: { // The MIME type
        type: String,
        required: true
      }
    }]
}, { collection: 'Feedback' });

const Feedback = mongoose.model('Feedback', feedbackSchema);
export default Feedback;
