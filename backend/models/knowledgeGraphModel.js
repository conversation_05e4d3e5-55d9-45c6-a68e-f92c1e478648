import mongoose, { Schema, model as _model } from 'mongoose';

const knowledgeGraphSchema = new Schema({
  type: { 
    type: String, 
    enum: ['Chapter', 'Subtopic'], 
    required: true 
  },
  name: { 
    type: String, 
    required: true 
  },
  description: { 
    type: String 
  },
  class: {
    type: String
  },
  parents: [{ 
    type: Schema.Types.ObjectId,
    // Using refPath instead of ref to dynamically determine the model
    refPath: '_parentModel'
  }],
  children: [{ 
    type: Schema.Types.ObjectId,
    // Using refPath instead of ref to dynamically determine the model
    refPath: '_childModel'
  }],
  // Hidden fields to support dynamic referencing
  _parentModel: {
    type: String,
    default: function() {
      return this.constructor.modelName;
    },
  },
  _childModel: {
    type: String,
    default: function() {
      return this.constructor.modelName;
    },
  },
  order: {
    type: Number,
    default: 0
  }
}, { timestamps: true });

knowledgeGraphSchema.set('toJSON', {
  transform: function(doc, ret) {
    delete ret._parentModel;
    delete ret._childModel;
    return ret;
  }
});

knowledgeGraphSchema.statics.buildCurriculumGraph = async function(curriculumData, relatedSubtopics) {
  const CurriculumNode = this;
  
  const nodeMap = new Map();

  // Create chapter nodes
  const chapterNodes = await Promise.all(curriculumData.map(async (chapter, chapterIndex) => {
    const chapterNode = new CurriculumNode({
      class: chapter.class,
      type: 'Chapter',
      name: chapter.chapter.name,
      description: chapter.chapter.description,
      order: chapterIndex
    });
    nodeMap.set(chapterNode.name, chapterNode);
    return chapterNode;
  }));

  await CurriculumNode.insertMany(chapterNodes);

  // Create subtopic nodes and link them to chapters
  for (let chapterIndex = 0; chapterIndex < curriculumData.length; chapterIndex++) {
    const chapter = curriculumData[chapterIndex];
    const parentChapterNode = chapterNodes[chapterIndex];

    const subtopicNodes = await Promise.all(chapter.subtopics.map(async (subtopic, subtopicIndex) => {
      const subtopicNode = new CurriculumNode({
        type: 'Subtopic',
        name: subtopic.name,
        description: subtopic.description,
        parents: [parentChapterNode._id],
        order: subtopicIndex
      });
      
      parentChapterNode.children.push(subtopicNode._id);
      nodeMap.set(subtopic.name, subtopicNode);
      
      return subtopicNode;
    }));

    // Save subtopic nodes
    await CurriculumNode.insertMany(subtopicNodes);
    await parentChapterNode.save();
  }

  if (relatedSubtopics) {
    
    for (const relation of relatedSubtopics) {
      const fromNode = nodeMap.get(relation.from);
      const toNode = nodeMap.get(relation.to);
      
      if (fromNode && toNode) {
        // Add cross-reference between subtopics
        fromNode.children.push(toNode._id);
        toNode.parents.push(fromNode._id);
        
        await fromNode.save();
        await toNode.save();
      }
    }
  }

  return chapterNodes;
};

knowledgeGraphSchema.statics.visualizeCurriculum = async function() {
  const CurriculumNode = this;
  const modelName = this.modelName;
  
  const chapters = await CurriculumNode.find({ type: 'Chapter' })
    .populate({
      path: 'children',
      model: modelName,
      options: { sort: { order: 1 } },
      populate: [
        {
          path: 'children',
          model: modelName,
          select: 'name type'
        },
        {
          path: 'parents',
          model: modelName,
          select: 'name type'
        }
      ]
    })
    .sort({ order: 1 });

  return chapters.map(chapter => ({
    chapter: {
      name: chapter.name,
      description: chapter.description
    },
    subtopics: chapter.children.map(subtopic => ({
      name: subtopic.name,
      description: subtopic.description,
      nextSubtopics: subtopic.children
        .filter(child => child.type === 'Subtopic')
        .map(child => child.name),
      previousSubtopics: subtopic.parents
        .filter(parent => parent.type === 'Subtopic')
        .map(parent => parent.name)
    }))
  }));
};

knowledgeGraphSchema.statics.visualizeCurriculumClassWise = async function(classDetail) {
  const CurriculumNode = this;
  const modelName = this.modelName;
  
  const chapters = await CurriculumNode.find({ class: classDetail }) // don't just need the chapter also need the subtopic
    .populate({
      path: 'children',
      model: modelName,
      options: { sort: { order: 1 } },
      populate: [
        {
          path: 'children',
          model: modelName,
          select: 'name type'
        },
        {
          path: 'parents',
          model: modelName,
          select: 'name type'
        }
      ]
    })
    .sort({ order: 1 });

  return chapters.map(chapter => ({
    chapter: {
      name: chapter.name,
      description: chapter.description
    },
    subtopics: chapter.children.map(subtopic => ({
      name: subtopic.name,
      description: subtopic.description,
      nextSubtopics: subtopic.children
        .filter(child => child.type === 'Subtopic')
        .map(child => child.name),
      previousSubtopics: subtopic.parents
        .filter(parent => parent.type === 'Subtopic')
        .map(parent => parent.name)
    }))
  }));
};
// Named export for creating subject-specific models
export const createKnowledgeGraphModel = (subject) => {
  const modelName = `CurriculumNode_${subject}`;
  const collectionName = `${subject.toLowerCase()}KnowledgeGraph`;

  // Check if model is already registered
  if (mongoose.models[modelName]) {
    return mongoose.models[modelName];
  }

  // Model doesn't exist, create it
  return _model(modelName, knowledgeGraphSchema, collectionName);
};
