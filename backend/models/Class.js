import { Schema, model } from "mongoose";

const ClassAnalyticsSchema = new Schema({
  // Class performance overview
  classPerformance: {
      averageProficiency: { type: Number },
      proficiencyDistribution: {
          advanced: [{ type: String }],   
          proficient: [{ type: String }],  
          developing: [{ type: String }], 
          beginning: [{ type: String }]   
      },
      testCompletionRate: { type: Number },
      averageTestScore: { type: Number },
      proficiencyTrend: [{                  // Proficiency trend over time
          date: { type: Date },
          averageProficiency: { type: Number }
      }]
  },
  
  // Topic-level insights across the class
  topicInsights: [{
      topicId: { type: String},
      averageProficiency: { type: Number },
      struggleRate: { type: Number },       // Percentage of students struggling
      masteryRate: { type: Number },        // Percentage of students at mastery
      // commonMisconceptions: [String],
      timeSpentAverage: { type: Number }
  }],
  
  // Student groupings based on needs
  studentGroups: {
      needsIntervention: [{ 
          studentId: { type: Schema.Types.ObjectId, ref: 'Student' },
          keyGaps: [{ type: Schema.Types.ObjectId}],
          bloomTaxonomyWeakness: [{ type: String }]
      }],
      onTrack: [{ type: Schema.Types.ObjectId, ref: 'Student' }],
      accelerated: [{ 
          studentId: { type: Schema.Types.ObjectId, ref: 'Student' },
          readyForTopics: [{ type: Schema.Types.ObjectId}],
          bloomTaxonomyStrengths: [{ type: String }]
      }]
  },
  
  // Test insights
  testAnalytics: [{
      testId: { type: Schema.Types.ObjectId, ref: 'TestHistory' },
      participationRate: { type: Number },
      averageScore: { type: Number },
      averageCompletionTime: { type: Number },
      difficultyRating: { type: Number },
      questionEffectiveness: [{
          questionId: { type: Schema.Types.ObjectId, ref: 'Question' },
          discriminationIndex: { type: Number }, // How well question differentiates strong/weak students
          difficultyIndex: { type: Number }      // Percentage of students answering correctly
      }]
  }],
  
  // Class curriculum coverage
  curriculumProgress: {
      topicsCovered: [{ 
          topicId: { type: String },
          dateIntroduced: { type: Date },
          classMasteryLevel: { type: Number }
      }]
      // paceMetrics: {
      //     plannedVsActual: { type: Number },  // Ratio of planned to actual progress
      //     estimatedCompletionDate: { type: Date }
      // }
  },
  
  lastUpdated: { type: Date, default: Date.now }
});

const ClassSchema = new Schema({
  classStd:{
    type: String,
    required: true,
  },
  classCode: {
    type: String,
    required: true,
    unique: true,
  },
  schoolCode: {
    type: Schema.Types.String,
    ref: "School",
    required: true,
  },
  teacherId: [
    {
      type: Schema.Types.ObjectId,
      ref: "Teacher",
    },
  ],
  subject: {
    type: String,
    required: true,
  },
  className: {
    type: String,
    required: true,
  },
  students: [
    {
      type: Schema.Types.ObjectId,
      ref: "Student",
    },
  ],
  analytics: ClassAnalyticsSchema,
});

export default model("Class", ClassSchema);
