import mongoose from 'mongoose';

const llmRateLimitSchema = new mongoose.Schema({
  studentId: {
    type: String,
    required: true,
  },
  subject: {
    type: String,
    required: true,
  },
  requestType: {
    type: String,
    required: true,
    enum: ['progress', 'pathway', 'strategies', 'topic', 'next'],
  },
  requestCount: {
    type: Number,
    default: 0
  },
  lastRequestTime: {
    type: Date,
    default: Date.now
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: 1800 // Document will be automatically deleted after 30 minutes
  }
});

// Compound index for efficient querying
llmRateLimitSchema.index({ studentId: 1, subject: 1, requestType: 1 });

const LLMRateLimit = mongoose.model('LLMRateLimit', llmRateLimitSchema);

export default LLMRateLimit; 