import mongoose from 'mongoose';

const messageSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true
  },
  type: {
    type: String,
    enum: ['user', 'assistant'],
    required: true
  },
  content: {
    type: String,
    required: function() {
      // Allow empty content for loading messages
      return !this.isLoading;
    }
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
}, { _id: false });

const chatConversationSchema = new mongoose.Schema({
  id: {
    type: String,
    required: true,
    unique: true
  },
  userId: {
    type: String,
    required: true,
    index: true
  },
  title: {
    type: String,
    required: true
  },
  subject: {
    type: String,
    required: true,
    index: true
  },
  messages: [messageSchema],
  // Teacher-specific fields
  teacherId: {
    type: String,
    index: true
  },
  classId: {
    type: String,
    index: true
  },
  isTeacherConversation: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, {
  collection: 'chatConversations',
  timestamps: false // Disable automatic timestamps to avoid conflicts
});

// Create TTL index for 7 days (7 * 24 * 60 * 60 = 604800 seconds)
chatConversationSchema.index({ createdAt: 1 }, { expireAfterSeconds: 604800 });

// Compound index for efficient querying by user and subject
chatConversationSchema.index({ userId: 1, subject: 1, updatedAt: -1 });

// Compound index for teacher conversations
chatConversationSchema.index({ teacherId: 1, classId: 1, subject: 1, updatedAt: -1 });

// Update the updatedAt field before saving
chatConversationSchema.pre('save', function(next) {
  this.updatedAt = new Date();
  next();
});

const ChatConversation = mongoose.model('ChatConversation', chatConversationSchema);

export default ChatConversation;
