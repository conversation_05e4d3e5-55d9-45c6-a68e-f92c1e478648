// models/Question.js
import mongoose from 'mongoose';

const questionSchema = new mongoose.Schema({
    question: { type: String, required: true },
    options: { type: [String]},
    answer: { type: String},
    solution: { type: String },
    images: { type: [String], default: [] },
    topic: { type: String, required: true },
    subtopic: { type: String },
    difficulty: { type: Number, default: 0.5 },
    discrimination_parameter: { type: Number, default: 0.5 },
});

// Factory function to create Question model with dynamic collection name
export const createQuestionModel = (collectionName = 'questionBank_class10_mathematics') => {
    return mongoose.model('Question', questionSchema, collectionName);
};

// const Question = mongoose.model('Question', questionSchema);
// export default Question;
