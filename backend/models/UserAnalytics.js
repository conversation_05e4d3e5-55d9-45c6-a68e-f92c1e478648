import { Schema, model } from 'mongoose';

// User Session Schema for tracking user sessions
const UserSessionSchema = new Schema({
    userId: { type: Schema.Types.ObjectId, required: true, index: true },
    userType: { type: String, enum: ['Student', 'Teacher', 'Admin'], required: true, index: true },
    sessionId: { type: String, required: true, unique: true, index: true },
    startTime: { type: Date, required: true, default: Date.now },
    endTime: { type: Date },
    duration: { type: Number }, // in seconds
    deviceInfo: {
        userAgent: { type: String },
        browser: { type: String },
        os: { type: String },
        device: { type: String }, // mobile, tablet, desktop
        screenResolution: { type: String },
        timezone: { type: String }
    },
    ipAddress: { type: String },
    location: {
        country: { type: String },
        region: { type: String },
        city: { type: String }
    },
    isActive: { type: Boolean, default: true },
    lastActivity: { type: Date, default: Date.now }
}, { 
    collection: 'userSessions',
    timestamps: true 
});

// User Interaction Schema for tracking specific user interactions
const UserInteractionSchema = new Schema({
    userId: { type: Schema.Types.ObjectId, required: true, index: true },
    sessionId: { type: String, required: true, index: true },
    userType: { type: String, enum: ['Student', 'Teacher', 'Admin'], required: true, index: true },
    
    // Interaction details
    interactionType: { 
        type: String, 
        enum: [
            'page_view', 'button_click', 'form_submit', 'navigation', 
            'feature_usage', 'test_action', 'chat_interaction', 'file_upload',
            'search', 'filter', 'sort', 'export', 'download'
        ], 
        required: true, 
        index: true 
    },
    
    // Page/Route information
    page: { type: String, required: true, index: true }, // current route/page
    previousPage: { type: String }, // previous route/page
    
    // Element details
    elementType: { type: String }, // button, link, input, etc.
    elementId: { type: String },
    elementClass: { type: String },
    elementText: { type: String },
    
    // Feature-specific data
    feature: { 
        type: String, 
        enum: [
            'aegis_ai', 'test_taking', 'dashboard', 'analytics', 
            'profile', 'class_management', 'grading', 'schedule_test',
            'practice_test', 'subject_details', 'feedback'
        ],
        index: true
    },
    
    // Additional context data
    metadata: {
        testId: { type: Schema.Types.ObjectId },
        classId: { type: Schema.Types.ObjectId },
        subjectId: { type: String },
        questionId: { type: Schema.Types.ObjectId },
        chatConversationId: { type: Schema.Types.ObjectId },
        formData: { type: Schema.Types.Mixed }, // for form submissions
        searchQuery: { type: String }, // for search interactions
        filterCriteria: { type: Schema.Types.Mixed }, // for filter interactions
        customData: { type: Schema.Types.Mixed } // flexible field for additional data
    },
    
    // Performance metrics
    loadTime: { type: Number }, // page load time in ms
    responseTime: { type: Number }, // interaction response time in ms
    
    timestamp: { type: Date, default: Date.now, index: true }
}, { 
    collection: 'userInteractions',
    timestamps: true 
});

// Page Analytics Schema for tracking page-level metrics
const PageAnalyticsSchema = new Schema({
    page: { type: String, required: true, index: true },
    date: { type: Date, required: true, index: true },
    
    // Traffic metrics
    totalViews: { type: Number, default: 0 },
    uniqueVisitors: { type: Number, default: 0 },
    
    // User type breakdown
    userTypeBreakdown: {
        students: { type: Number, default: 0 },
        teachers: { type: Number, default: 0 },
        admins: { type: Number, default: 0 }
    },
    
    // Engagement metrics
    averageTimeOnPage: { type: Number, default: 0 }, // in seconds
    bounceRate: { type: Number, default: 0 }, // percentage
    exitRate: { type: Number, default: 0 }, // percentage
    
    // Performance metrics
    averageLoadTime: { type: Number, default: 0 }, // in ms
    
    // Device breakdown
    deviceBreakdown: {
        mobile: { type: Number, default: 0 },
        tablet: { type: Number, default: 0 },
        desktop: { type: Number, default: 0 }
    }
}, { 
    collection: 'pageAnalytics',
    timestamps: true 
});

// Feature Usage Analytics Schema
const FeatureUsageSchema = new Schema({
    feature: { 
        type: String, 
        required: true, 
        index: true,
        enum: [
            'aegis_ai', 'test_taking', 'dashboard', 'analytics', 
            'profile', 'class_management', 'grading', 'schedule_test',
            'practice_test', 'subject_details', 'feedback'
        ]
    },
    date: { type: Date, required: true, index: true },
    
    // Usage metrics
    totalUsage: { type: Number, default: 0 },
    uniqueUsers: { type: Number, default: 0 },
    
    // User type breakdown
    userTypeUsage: {
        students: { type: Number, default: 0 },
        teachers: { type: Number, default: 0 },
        admins: { type: Number, default: 0 }
    },
    
    // Engagement metrics
    averageSessionDuration: { type: Number, default: 0 }, // in seconds
    averageInteractionsPerSession: { type: Number, default: 0 },
    
    // Success metrics (feature-specific)
    successMetrics: {
        completionRate: { type: Number, default: 0 }, // for tests, forms, etc.
        errorRate: { type: Number, default: 0 },
        retryRate: { type: Number, default: 0 }
    }
}, { 
    collection: 'featureUsage',
    timestamps: true 
});

// User Behavior Patterns Schema
const UserBehaviorSchema = new Schema({
    userId: { type: Schema.Types.ObjectId, required: true, index: true },
    userType: { type: String, enum: ['Student', 'Teacher', 'Admin'], required: true },
    
    // Behavioral patterns
    mostUsedFeatures: [{
        feature: { type: String },
        usageCount: { type: Number },
        lastUsed: { type: Date }
    }],
    
    // Navigation patterns
    commonPaths: [{
        path: [{ type: String }], // sequence of pages
        frequency: { type: Number },
        averageDuration: { type: Number } // total time for this path
    }],
    
    // Time-based patterns
    activityPatterns: {
        peakHours: [{ type: Number }], // hours of day (0-23)
        peakDays: [{ type: Number }], // days of week (0-6)
        sessionFrequency: { type: Number }, // sessions per week
        averageSessionDuration: { type: Number }
    },
    
    // Engagement metrics
    engagementScore: { type: Number, default: 0 }, // calculated score 0-100
    retentionMetrics: {
        firstVisit: { type: Date },
        lastVisit: { type: Date },
        totalSessions: { type: Number, default: 0 },
        totalTimeSpent: { type: Number, default: 0 } // in seconds
    },
    
    lastUpdated: { type: Date, default: Date.now }
}, { 
    collection: 'userBehavior',
    timestamps: true 
});

// Indexes for performance optimization
UserSessionSchema.index({ userId: 1, startTime: -1 });
UserSessionSchema.index({ userType: 1, startTime: -1 });
UserSessionSchema.index({ sessionId: 1, startTime: -1 });

UserInteractionSchema.index({ userId: 1, timestamp: -1 });
UserInteractionSchema.index({ sessionId: 1, timestamp: -1 });
UserInteractionSchema.index({ userType: 1, interactionType: 1, timestamp: -1 });
UserInteractionSchema.index({ page: 1, timestamp: -1 });
UserInteractionSchema.index({ feature: 1, timestamp: -1 });

PageAnalyticsSchema.index({ page: 1, date: -1 });
FeatureUsageSchema.index({ feature: 1, date: -1 });
UserBehaviorSchema.index({ userId: 1, userType: 1 });

// TTL indexes for data retention (optional - adjust retention period as needed)
UserSessionSchema.index({ createdAt: 1 }, { expireAfterSeconds: 31536000 }); // 1 year
UserInteractionSchema.index({ createdAt: 1 }, { expireAfterSeconds: 31536000 }); // 1 year

export const UserSession = model('UserSession', UserSessionSchema);
export const UserInteraction = model('UserInteraction', UserInteractionSchema);
export const PageAnalytics = model('PageAnalytics', PageAnalyticsSchema);
export const FeatureUsage = model('FeatureUsage', FeatureUsageSchema);
export const UserBehavior = model('UserBehavior', UserBehaviorSchema);
