import mongoose from 'mongoose';


const aiQuestionSchema = new mongoose.Schema({
  passage: {type: String},
  question: { type: String },
  options: { type: [String]},
  answer: { type: String},
  difficulty: { type: Number},
  solution: { type: String},
  topic: { type: String},
  ansimage: [{ type: Map, of: String }],
  queimage: [{ type: Map, of: String}],
  subtopic: { type: String, },
  bloomTaxonomy: {type: String},
  differentiatingParameter: { type: Number, default: 0.5},
  metaDataCollection: {type: String, default: ""},
}, {collection: 'AIQuestions'});

const aiQuestion = mongoose.model('AIQuestion', aiQuestionSchema);
export default aiQuestion;
