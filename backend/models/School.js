import { Schema, model } from "mongoose";

const SchoolSchema = new Schema({
    schoolCode: { type: String, unique: true },
    name: { type: String, required: true },
    address: { type: String},
    city: { type: String},
    state: { type: String },
    pincode: { type: Number, required: true },
    phone: { type: Number, required: true },
    email: { type: String},
    website: { type: String},
    students: [{ type: Schema.Types.ObjectId, ref: 'Student' }],
    classes: [{ type: Schema.Types.ObjectId, ref: 'Class' }],
    teachers: [{ type: Schema.Types.ObjectId, ref: 'Teacher' }]
}, { collection: 'schools' , timestamps: true});

export default model('School', SchoolSchema);