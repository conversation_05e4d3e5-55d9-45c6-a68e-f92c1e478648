import { model, Schema } from "mongoose";

const QuestionTempSchema = new Schema({
  passage: {type: String},
  question: { type: String },
  options: { type: [String]},
  answer: { type: String},
  difficulty: { type: Number},
  solution: { type: String},
  topics: { type: String},
  ansimage: [{ type: Map, of: String }],
  queimage: [{ type: Map, of: String}],
  subtopics: { type: String, },
  bloomTaxonomy: {type: String},
  differentiatingParameter: { type: Number, default: 0.5},
  metaDataCollection: {type: String, default: ""},
}, {collection: 'questionBank_class10_computer'});

const QuestionTemp = model('QuestionTemp', QuestionTempSchema);
export default QuestionTemp;
