import { GoogleGenerativeAI } from "@google/generative-ai";
import Anthropic from "@anthropic-ai/sdk";
import fs from "fs";
import dotenv from "dotenv";

dotenv.config(); 

async function gemini(prompt, model_name) {
    const genAI = new GoogleGenerativeAI("AIzaSyAEVkmdTwgKAQjQ-v08q47b4U3F5GINDZI");
    const model = genAI.getGenerativeModel({ model: model_name });
  const promptValue= await prompt;
  console.error(`sending prompt: ${promptValue}`);
    const result = await model.generateContent(promptValue);
  console.error(`got result: ${result}`);
    return result.response.text();
}

let questions;

function loadQuestions() {
  return new Promise((resolve, reject) => {
    fs.readFile("./Unique_QB.json", function(err, data) { 
      // Check for errors 
      if (err) {
        reject(err);
        return;
      }

      // Converting to JSON 
      try {
        questions = JSON.parse(data); 
        // console.error("Questions loaded:", questions);
        resolve();
      } catch (error) {
        reject(error);
      }
    });
  });
}

// const prompt = "Give me 20 MCQ questions from class 10 NCERT physics text book in JSON format based on increasing difficulties and also tag the subskills with each question.";

async function makeprompts(index) {
  let question = JSON.stringify(questions[index]);
  const prompt = ` for question of the following json format ${question} add fields: topic, subtopic. And assign them from the following list of topics: [Real Numbers, Polynomials, Pair of Linear Equations in Two Variables, Quadratic Equations, Arithmetic Progressions, Triangles, Coordinate Geometry, Introduction to Trigonometry, Some Applications of Trigonometry, Circles, Constructions, Areas Related to Circles, Surface Areas and Volumes, Statistics, Probability], and the following list of subtopics: [Euclid's Division Lemma,Fundamental Theorem of Arithmetic,Revisiting Irrational Numbers,Revisiting Rational Numbers and Their Decimal Expansions, Geometrical Meaning of the Zeroes of a Polynomial, Relationship Between Zeroes and Coefficients of a Polynomial, Division Algorithm for Polynomials, Graphical Method of Solution of a Pair of Linear Equations, Algebraic Methods of Solving a Pair of Linear Equations:, Substitution Method, Elimination Method, Cross Multiplication Method, Equations Reducible to a Pair of Linear Equations, Standard Form of a Quadratic Equation, Factorisation, Completing the Square, Quadratic Formula, Nature of Roots, nth Term of an Arithmetic Progression (AP), Sum of First n Terms of an AP, Similar Figures, Basic Proportionality Theorem (Thales’ Theorem), Criteria for Similarity of Triangles, Areas of Similar Triangles, Pythagoras Theorem and Its Converse, Distance Formula, Section Formula, Area of a Triangle, Trigonometric Ratios, Trigonometric Ratios of Some Specific Angles, Trigonometric Ratios of Complementary Angles, Trigonometric Identities, Heights and Distances (Angle of Elevation and Angle of Depression), Circles, Tangent to a Circle, Number of Tangents from a Point to a Circle, Division of a Line Segment, Construction of Tangents to a Circle, Perimeter and Area of a Circle, Areas of Sector and Segment of a Circle, Areas of Combinations of Plane Figures, Surface Area of Combination of solids, Surface Area of Sphere, Volume of a Combination of Solids, Volume of a Sphere, Conversion of Solid from One Shape to Another, Mean of Grouped Data, Mode of Grouped Data, Median of Grouped Data, Graphical Representation of Cumulative Frequency, Classical Definition of Probability, Probability of an Event]. If there are multiple topics or subtopics in the question seperate them with a comma`;
  // console.error(prompt);
  return prompt;
}

async function run() {
  for (let i =0; i < questions.length; i++) {
    const prompt = makeprompts(i);
    console.error(`got i: ${i}`);
    try {
        // For Gemini
          const result = await gemini(prompt, "gemini-1.5-pro");
          console.error(result);
          fs.writeFileSync("./data/TaggedBank.json", result);
        
        // For Claude
        // const result = await claude(prompt, "claude-3-sonnet");
        

        // fs.writeFileSync("./data/questionBank.json", result.slice(7,-4));
    } catch (error) {
        console.error("An error occurred:", error);
    }
  }
}

import axios from "axios";

async function sendPrompt(prompt) {
  const value = await prompt;
  try {
    // console.error(`sending prompt: ${value}`)
      const response = await axios.post('http://localhost:11434/api/generate', {
        model: "mistral:latest",
        prompt: value,
        options: {
        "temperature": 0.8,
        "num_thread": 3,
        "seed": Math.floor(Math.random() * 10000)
        },
      stream: false,
      format: "json"
      });
      console.error("finished generating response\n");
      return response.data.response;
  } catch (error) {
    console.error('Error sending prompt:', error);
    
  }
}

async function runMistral() {
  for (let i = 219; i < questions.length; i++) {
    const prompt = makeprompts(i);
    console.error(`got i: ${i}`);
    try {
        // For mistral
          const result = await sendPrompt(prompt);
          console.error(result);
          fs.appendFileSync("./data/TaggedBank.json", result);
        
    } catch (error) {
        console.error("An error occurred:", error);
    }
  }
}
// run();
async function main() {
  await loadQuestions();
  runMistral();
}
main();
