# Dynamic Proficiency Calculation System

## Overview

The proficiency calculation system has been updated to dynamically calculate `overallProficiency` values based on actual curriculum node proficiencies instead of using hardcoded default values.

## Key Changes

### 1. Model Updates

#### Student Model (`backend/models/Student.js`)
- **Before**: `overallProficiency: { type: Number, required: true, default: 0.5 }`
- **After**: `overallProficiency: { type: Number, required: true, default: 0 }`

The default value has been changed from 0.5 to 0, and the field is now dynamically calculated.

### 2. New Services

#### Proficiency Calculation Service (`backend/services/proficiencyCalculationService.js`)

**Main Functions:**

- `calculateOverallProficiencyFromCurriculum(curriculumProgress, options)`: Core calculation function
- `updateSubjectOverallProficiency(studentId, subject, options)`: Update proficiency for a specific student/subject
- `batchUpdateOverallProficiency(updates, options)`: Batch update multiple students
- `migrateHardcodedProficiencies(options)`: Migrate existing hardcoded values

**Weighting Strategies:**

1. **Equal Weighting** (default): All nodes have equal weight
2. **Node Type Weighting**: Different weights for chapters vs subtopics
3. **Order Weighting**: Later topics get higher weights (complexity-based)

**Configuration Options:**
```javascript
{
  weightingStrategy: 'equal' | 'nodeType' | 'order',
  topicWeight: 1.2,      // Weight for chapter nodes
  subtopicWeight: 1.0    // Weight for subtopic nodes
}
```

### 3. Updated Services

#### Test Service (`backend/services/testService.js`)
- Updated `updateUserProficiency()` to use new calculation service
- Removed hardcoded proficiency calculations
- Added automatic proficiency recalculation after test submissions

#### Student Curriculum Service (`backend/services/studentCurriculumService.js`)
- Added proficiency recalculation when curriculum progress is updated
- Triggers recalculation in `updateNodeProgress()` method

### 4. New API Endpoints

#### Proficiency Routes (`backend/routes/proficiencyRoutes.js`)

**Available Endpoints:**

1. **PUT** `/api/proficiency/recalculate/:studentId/:subject`
   - Recalculate proficiency for specific student/subject
   - Body: `{ weightingStrategy, topicWeight, subtopicWeight }`

2. **POST** `/api/proficiency/batch-recalculate`
   - Batch recalculate for multiple students
   - Body: `{ updates: [{studentId, subject}], weightingStrategy, topicWeight, subtopicWeight }`

3. **POST** `/api/proficiency/migrate`
   - Migrate all hardcoded proficiency values
   - Body: `{ dryRun: boolean, batchSize: number, weightingStrategy, topicWeight, subtopicWeight }`

4. **GET** `/api/proficiency/stats/:studentId/:subject`
   - Get detailed proficiency statistics for a student

### 5. Migration Script

#### Migration Script (`backend/scripts/migrateProficiencies.js`)

**Usage:**
```bash
# Dry run (no changes)
node backend/scripts/migrateProficiencies.js --dry-run

# Actual migration with custom batch size
node backend/scripts/migrateProficiencies.js --batch-size=50

# Full migration
node backend/scripts/migrateProficiencies.js
```

## Calculation Logic

### Basic Formula

```javascript
overallProficiency = (Σ(proficiency_i × weight_i)) / Σ(weight_i)
```

Where:
- `proficiency_i` = proficiency score for curriculum node i (0-100)
- `weight_i` = weight assigned to curriculum node i

### Weighting Strategies

#### 1. Equal Weighting
All nodes have weight = 1

#### 2. Node Type Weighting
- Chapter nodes: weight = `topicWeight` (default: 1.2)
- Subtopic nodes: weight = `subtopicWeight` (default: 1.0)

#### 3. Order Weighting
- weight = 1 + (node.order × 0.1)
- Later topics get higher weights

### Edge Case Handling

1. **Empty curriculum progress**: Returns 0
2. **Invalid proficiency values**: Filtered out
3. **Missing node data**: Skipped in calculation
4. **Zero total weight**: Returns 0
5. **Out-of-range values**: Clamped to 0-100

## Integration Points

### Automatic Recalculation Triggers

1. **Test Submission** (`submitTestController.js`)
   - After updating individual node proficiencies
   - Uses nodeType weighting strategy

2. **Curriculum Progress Updates** (`studentCurriculumService.js`)
   - When node status changes to 'Completed'
   - When proficiency scores are updated

3. **Manual Triggers** (API endpoints)
   - Individual student recalculation
   - Batch recalculation
   - Migration operations

### Database Updates

The system updates two locations:
1. `Student.subjects.$.overallProficiency` (stored as 0-1 range)
2. `StudentCurriculum.totalProficiency` (stored as 0-100 range)

## Migration Strategy

### Phase 1: Deploy New System
1. Deploy new calculation service
2. Update existing services to use new calculations
3. Add new API endpoints

### Phase 2: Migrate Existing Data
1. Run migration script with `--dry-run` to assess impact
2. Perform actual migration in batches
3. Verify results using stats endpoint

### Phase 3: Monitor and Optimize
1. Monitor calculation performance
2. Adjust weighting strategies based on usage
3. Optimize batch sizes for large datasets

## Performance Considerations

1. **Batch Processing**: Migration processes students in configurable batches
2. **Efficient Queries**: Uses targeted MongoDB queries with projections
3. **Error Handling**: Continues processing even if individual updates fail
4. **Logging**: Comprehensive logging for monitoring and debugging

## Testing

### Unit Tests
- Test calculation functions with various input scenarios
- Test edge cases (empty data, invalid values)
- Test different weighting strategies

### Integration Tests
- Test API endpoints
- Test automatic recalculation triggers
- Test migration script

### Performance Tests
- Test with large datasets
- Measure calculation performance
- Test batch processing efficiency

## Monitoring

### Metrics to Track
1. Calculation accuracy vs previous system
2. Performance impact on test submissions
3. Migration success rates
4. API endpoint usage

### Logging
- All proficiency calculations are logged
- Migration progress is tracked
- Errors are logged with context

## Future Enhancements

1. **Advanced Weighting**: Machine learning-based weight optimization
2. **Real-time Updates**: WebSocket-based proficiency updates
3. **Analytics Dashboard**: Visual proficiency trends and insights
4. **Custom Weighting**: Allow teachers to set custom node weights
