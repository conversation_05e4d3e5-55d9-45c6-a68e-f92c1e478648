import Student from "../models/Student.js";
import TestHistory from "../models/TestHistory.js";
import Class from "../models/Class.js";
import TestConfig from "../models/TestConfig.js";
import { createQuestionModel } from "../models/Question.js";
import { StudentCurriculumModel } from "../models/StudentKnowledgeGraphModel.js";
import { createKnowledgeGraphModel } from "../models/knowledgeGraphModel.js";
import { updateSubjectOverallProficiency } from "./proficiencyCalculationService.js";
import mongoose from 'mongoose';

async function fetchStudentById(userId) {
    try {
        const student = await Student.findById(userId).lean();
        if (!student) {
            console.warn(`User with ID ${userId} not found.`);
            return null;
        }

        // Get all subjects for the student
        const populatedSubjects = await Promise.all(student.subjects.map(async (subject) => {
            // Get the correct model name for this subject
            const curriculumNodeModel = `CurriculumNode_${subject.subjectName}`;

            // Ensure the model is registered
            if (!mongoose.models[curriculumNodeModel]) {
                createKnowledgeGraphModel(subject.subjectName);
            }

            // Populate the knowledge graph data
            const populatedSubject = await Student.populate(subject, {
                path: "knowledgeGraphId",
                model: "StudentCurriculum",
                populate: {
                    path: "curriculumProgress.nodeId",
                    model: curriculumNodeModel,
                }
            });

            return populatedSubject;
        }));

        // Replace the subjects array with populated data
        student.subjects = populatedSubjects;

        return student;
    } catch (error) {
        console.error(`Error fetching user with ID ${userId}:`, error);
        
    }
}

export async function getUserSubject(userId, subject) {
    try {
        const student = await fetchStudentById(userId);
        if (!student) return null;
        if (!subject) {
            console.warn(`Subject is undefined for user ${userId}`);
            return null;
        }

        const studentSubjects = student.subjects;
        const givenSubject = studentSubjects.find(
            (sub) => sub.subjectName && sub.subjectName.toLowerCase() === subject.toLowerCase()
        );
        return givenSubject;
    } catch (error) {
        console.error(`Error in getUserSubject for userId ${userId}:`, error);
        
    }
}

export async function getQuestion(questionId, collectionName = "questionBank_class10_math_temp") {
    try {
        // Replace with actual database query in production
        const question = await createQuestionModel(collectionName).findById(questionId);
        return question;
    } catch (error) {
        console.error(`Error fetching question with ID ${questionId}:`, error);
        
    }
}

export async function getCollectionNameTest(testid, subject) {
    try {
        // Construct the collection name based on test ID and subject
        const testHistory = await TestHistory.findOne({ _id: testid });
        const classObj = await Class.findOne({ _id: testHistory.class });
        const collectionName = `questionBank_class${classObj.classStd}_${subject.toLowerCase()}`;
        return collectionName;
    } catch (error) {
        console.error(`Error constructing collection name for testId ${testid} and subject ${subject}:`, error);
        
    }
}

export async function getCollectionNameClass(classid, subject) {
    try {
        // Construct the collection name based on test ID and subject
        const classObj = await Class.findOne({ _id: classid });
        // console.error("subject: ", subject);
        // console.error("classobj: ", classObj);
        const collectionName = `questionBank_class${classObj.classStd}_${subject.toLowerCase()}`;
        return collectionName;
    } catch (error) {
        console.error(`Error constructing collection name for class id ${classid} and subject ${subject}:`, error);
        
    }
}
export async function updateUserProficiency(userId, newProficiency, subject) {
    try {
        // Validate inputs
        if (!userId) {
            console.warn("updateUserProficiency called without a userId");
            return null;
        }

        if (!subject) {
            console.warn(`updateUserProficiency called without a subject for user ${userId}`);
            return null;
        }

        // Find and update the student document directly using findOneAndUpdate
        const student = await getUserSubject(userId, subject);

        if (!student || !student.knowledgeGraphId) {
            console.warn(`${subject} subject or knowledge graph not found for user ${userId}`);
            return;
        }

        // Deep copy the progress array to modify it safely
        const originalProgress = JSON.parse(JSON.stringify(student.knowledgeGraphId.curriculumProgress));
        let updatedProgress = JSON.parse(JSON.stringify(originalProgress)); // Work with this copy
        const parentsToUpdateStatus = new Set(); // Remove <string> type annotation

        // First pass: Update proficiencies and direct subtopic statuses
        updatedProgress = updatedProgress.map(progress => { // Remove : any type annotation
            // Ensure nodeId exists and has a name property
            if (!progress.nodeId || typeof progress.nodeId.name !== 'string') {
                console.warn('Skipping progress item due to missing or invalid nodeId.name:', progress);
                return progress; // Return unchanged if invalid
            }

            const subskill = progress.nodeId.name.trim();
            if (newProficiency.subskills[subskill] !== undefined) {
                // Clamp proficiency to minimum 0
                const clampedProficiency = Math.max(0, newProficiency.subskills[subskill]);
                // Multiply by 100 and round before storing as integer
                const storedProficiency = Math.round(clampedProficiency * 100);
                progress.proficiency = storedProficiency;
                // console.error(`Updated subskill proficiency-> ${subskill}`, progress.proficiency);

                // Update status if it was 'Not Started'
                if (progress.status === 'Not Started') {
                    progress.status = 'In Progress';
                    // console.error(`Updated status for subtopic ${subskill} to In Progress`);
                    // Add parents to the set for later status update
                    if (progress.nodeId.parents && Array.isArray(progress.nodeId.parents)) {
                        progress.nodeId.parents.forEach(parentId => parentsToUpdateStatus.add(parentId.toString())); // Remove : any type annotation
                    }
                }
            }
            return progress;
        });

        // Second pass: Update parent statuses
        updatedProgress = updatedProgress.map(progress => { // Remove : any type annotation
            // Ensure nodeId exists and has _id property before accessing toString()
            if (progress.nodeId && progress.nodeId._id && parentsToUpdateStatus.has(progress.nodeId._id.toString()) && progress.status === 'Not Started') {
                progress.status = 'In Progress';
                // console.error(`Updated status for parent topic ${progress.nodeId.name} (${progress.nodeId._id}) to In Progress`);
            }
            return progress;
        });

        // Find and update the StudentCurriculum document
        await StudentCurriculumModel.findOneAndUpdate(
            { _id: student.knowledgeGraphId._id },
            {
                $set: {
                    curriculumProgress: updatedProgress
                }
            },
            { new: true }
        );

        // Update the subject's overall proficiency using the new calculation service
        await updateSubjectOverallProficiency(userId, subject, {
            weightingStrategy: 'nodeType',
            topicWeight: 1.2,
            subtopicWeight: 1.0
        });

        // console.error(`User ${userId}'s proficiency updated successfully`);
        return student;

    } catch (error) {
        console.error(`Error updating proficiency for user ${userId}:`, error);
        
    }
}

export async function getTestConfig(testId) {
    try {
        // console.error(`Got test id: ${testId}`)
        const config = await TestConfig.findOne({ testId: testId });
        if (!config) {
            console.err("Error fetching test configuration for testid: ${testid}");
            console.error("Test configuration not found");
        }
        return config;
    } catch (error) {
        console.error(
            `Error fetching test configuration for testId ${testId}:`,
            error
        );
        
    }
}

export async function getActiveUserCount(testId) {
    try {
        // Replace with actual logic to fetch active user count
        const activeUsers = 50;
        // console.error(`Active users for test ${testId}:`, activeUsers);
        return activeUsers;
    } catch (error) {
        console.error(
            `Error fetching active user count for testId ${testId}:`,
            error
        );
        
    }
}

export function calculateProbability(theta, a, b, c) {
    // a = discrimination, b = difficulty, c = guessing
    const exponent = -a * (theta - b);
    return c + (1 - c) * (1 / (1 + Math.exp(exponent)));
}

export function updateProficiency(theta, probCorrect, response, learningRate) {
    const ans = theta + learningRate * (response - probCorrect);
    return Math.max(0, Math.min(1, ans)); // Clamp between 0 and 1
}

export async function getNextQuestion(proficiency) {
    try {
        // Replace with actual logic for adaptive question selection
        const question = {
            questionId: "nextQuestion123",
            questionText: "What is 2+2?",
        };
        // console.error("Next question based on proficiency:", question);
        return question;
    } catch (error) {
        console.error(
            `Error fetching next question for proficiency ${proficiency}:`,
            error
        );
        
    }
}

export async function processBatchTest(userId, responses, subject) {
    try {
        let proficiency = 0.5; // Initial proficiency

        for (const { questionId, response } of responses) {
            const question = await getQuestion(questionId);
            const probCorrect = calculateProbability(
                proficiency,
                question.a,
                question.b,
                question.c
            );
            proficiency = updateProficiency(proficiency, probCorrect, response, 0.1);
        }

        await updateUserProficiency(userId, proficiency, subject);
        // console.error(`Final proficiency for user ${userId}:`, proficiency);
        return proficiency;
    } catch (error) {
        console.error(`Error processing batch test for user ${userId}:`, error);
        
    }
}

export const updateSubskillProficiencies = (currentProficiencies, question, response, learningRate = 0.1) => {
    const updatedProficiencies = { currentProficiencies: { ...currentProficiencies } }; // Clone to avoid mutation
    // console.error("before update proficiencies->", updatedProficiencies.currentProficiencies);

    const { weight = 0.5, discrimination_parameter: a, difficulty: b, c = 0.25 } = question;

    // Iterate over the subskills using Object.keys
    for (const subskill of Object.keys(updatedProficiencies.currentProficiencies)) {
        // Calculate probability of correct response for this subskill
        const probCorrect = calculateProbability(
            currentProficiencies[subskill] || 0, // Default to 0 if subskill not present
            a,
            b,
            c
        );

        // Update proficiency for this subskill
        updatedProficiencies.currentProficiencies[subskill] = updateProficiency(
            currentProficiencies[subskill] || 0,
            probCorrect,
            response,
            learningRate * weight // Adjust learning rate by subskill weight
        );
    }

    return updatedProficiencies;
};

// distribution of weight of subskill in the knowledge graph
export const calculateOverallProficiency = (proficiencies, subskills) => {
    if (!subskills || subskills.length === 0) return 0;

    let totalWeight = 0;
    let weightedSum = 0;

    for (const subskill of subskills) {
        const proficiency = proficiencies[subskill.id] || 0;
        weightedSum += proficiency * subskill.weight;
        totalWeight += subskill.weight;
    }

    return totalWeight > 0 ? weightedSum / totalWeight : 0;
};

// evaluate the correctness of the responses
export async function evaluateResponses(collectionName, oldResponses) {
    return Promise.all(
        oldResponses?.map(async (response) => {
            const questionData = await getQuestion(response.questionId, collectionName);

            // Handle case where there's no selected answer
            if (response.selectedAnswer === null || response.selectedAnswer === undefined) {
                return {
                    ...response,
                    response: 0
                };
            }

            // Clean both answers by trimming whitespace and commas
            const cleanCorrectAnswer = (questionData?.answer || '')
                .replace(/^[\s,]+|[\s,]+$/g, ''); // Remove leading/trailing commas and whitespace

            const cleanSelectedAnswer = (questionData?.options[response.selectedAnswer] || '')
                .replace(/^[\s,]+|[\s,]+$/g, '') // Remove leading/trailing commas and whitespace
                .replace(/^[A-Za-z][\)\.]\s*/, ''); // Remove option prefixes like "a)", "A.", etc.

            const isCorrect = cleanCorrectAnswer === cleanSelectedAnswer ? 1 : 0;

            return {
                ...response,
                response: isCorrect
            };
        })
    );
};
