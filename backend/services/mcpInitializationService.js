import mongoMcpService from './mongoMcpService.js';

/**
 * MCP Initialization Service
 * Handles the startup and health monitoring of MongoDB MCP server
 */
class McpInitializationService {
  constructor() {
    this.initializationAttempts = 0;
    this.maxRetries = 3;
    this.retryDelay = 5000; // 5 seconds
    this.healthCheckInterval = null;
  }

  /**
   * Initialize MCP service with retry logic
   */
  async initializeWithRetry() {
    console.log('[MCP INIT] Starting MongoDB MCP service initialization...');
    
    while (this.initializationAttempts < this.maxRetries) {
      try {
        this.initializationAttempts++;
        console.log(`[MCP INIT] Attempt ${this.initializationAttempts}/${this.maxRetries}`);
        
        const success = await mongoMcpService.initialize();
        
        if (success) {
          console.log('[MCP INIT] MongoDB MCP service initialized successfully');
          this.startHealthCheck();
          return true;
        } else {
          throw new Error('MCP initialization returned false');
        }
      } catch (error) {
        console.error(`[MCP INIT] Attempt ${this.initializationAttempts} failed:`, error.message);
        
        if (this.initializationAttempts < this.maxRetries) {
          console.log(`[MCP INIT] Retrying in ${this.retryDelay / 1000} seconds...`);
          await this.delay(this.retryDelay);
        }
      }
    }

    console.warn('[MCP INIT] Failed to initialize MongoDB MCP service after all attempts');
    console.warn('[MCP INIT] Chatbot will fall back to traditional data fetching');
    return false;
  }

  /**
   * Start health check monitoring
   */
  startHealthCheck() {
    // Check MCP service health every 30 seconds
    this.healthCheckInterval = setInterval(async () => {
      try {
        if (!mongoMcpService.isReady()) {
          console.warn('[MCP HEALTH] Service not ready, attempting reconnection...');
          await mongoMcpService.initialize();
        }
      } catch (error) {
        console.error('[MCP HEALTH] Health check failed:', error.message);
      }
    }, 30000);

    console.log('[MCP INIT] Health check monitoring started');
  }

  /**
   * Stop health check monitoring
   */
  stopHealthCheck() {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('[MCP INIT] Health check monitoring stopped');
    }
  }

  /**
   * Graceful shutdown
   */
  async shutdown() {
    console.log('[MCP INIT] Shutting down MCP services...');
    
    this.stopHealthCheck();
    
    try {
      await mongoMcpService.disconnect();
      console.log('[MCP INIT] MCP services shut down successfully');
    } catch (error) {
      console.error('[MCP INIT] Error during shutdown:', error.message);
    }
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      isReady: mongoMcpService.isReady(),
      initializationAttempts: this.initializationAttempts,
      healthCheckActive: this.healthCheckInterval !== null
    };
  }

  /**
   * Utility function for delays
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Create singleton instance
const mcpInitializationService = new McpInitializationService();

export default mcpInitializationService;
