import Class from "../models/Class.js"; // Assuming your Class model is here

async function generateUniqueClassCode() {
  const codeLength = 6;
  const maxAttempts = 5;
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    // Generate code with specific format
    const code = generateCode(codeLength);
    
    // Check if code exists
    const existingClass = await Class.findOne({ classCode: code });
    
    if (!existingClass) {
      return code;
    }
    
    attempts++;
  }
  
  console.error('Unable to generate unique class code');
}

function generateCode(length) {
  // Format: 2 letters + 4 numbers
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '0123456789';
  
  let code = '';
  
  // Add 2 random letters
  for (let i = 0; i < 2; i++) {
    code += letters.charAt(Math.floor(Math.random() * letters.length));
  }
  
  // Add 4 random numbers
  for (let i = 0; i < 4; i++) {
    code += numbers.charAt(Math.floor(Math.random() * numbers.length));
  }
  
  return code;
}

export { generateUniqueClassCode };
