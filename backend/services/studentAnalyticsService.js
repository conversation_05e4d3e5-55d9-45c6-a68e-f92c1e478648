import Student from "../models/Student.js";
import TestHistory from "../models/TestHistory.js";
import { getCollectionNameTest, getQuestion } from '../services/testService.js';

/**
 * Calculate proficiency change using weighted average
 * @param {number} oldProficiency - Previous proficiency score (0-1)
 * @param {number} newScore - New test score (0-1)
 * @param {number} weight - Weight for new score (default 0.3)
 * @returns {number} Updated proficiency score
 */
const calculateProficiencyChange = (oldProficiency = 0.5, newScore = 0, weight = 0.3) => {
    // Validate inputs
    oldProficiency = Math.max(0, Math.min(1, oldProficiency));
    newScore = Math.max(0, Math.min(1, newScore));
    weight = Math.max(0, Math.min(1, weight));
    
    return oldProficiency + (weight * (newScore - oldProficiency));
};

/**
 * Calculate consistency score based on score variance
 * @param {number[]} previousScores - Array of previous test scores (0-100)
 * @param {number} currentScore - Current test score (0-100)
 * @returns {number} Consistency score (0-1)
 */
const calculateConsistencyScore = (previousScores = [], currentScore = 0) => {
    if (!Array.isArray(previousScores) || previousScores.length === 0) return 1;
    
    // Filter and validate scores
    const validScores = previousScores.filter(score => 
        typeof score === 'number' && !isNaN(score) && score >= 0 && score <= 100
    );
    
    if (validScores.length === 0) return 1;
    
    // Include current score in variance calculation
    const allScores = [...validScores, currentScore];
    const mean = allScores.reduce((sum, score) => sum + score, 0) / allScores.length;
    const variance = allScores.reduce((sum, score) => 
        sum + Math.pow(score - mean, 2), 0) / allScores.length;
    
    // Normalize variance and convert to consistency score
    const normalizedVariance = Math.sqrt(variance) / 100;
    return Math.max(0, Math.min(1, 1 - normalizedVariance));
};

/**
 * Calculate confidence score based on accuracy and time efficiency
 * @param {Array} responses - Test responses
 * @param {Map} questionData - Map of question data
 * @returns {Promise<number>} Confidence score (0-1)
 */
const calculateConfidenceScore = async (responses = [], questionData = new Map()) => {
    if (!Array.isArray(responses) || responses.length === 0) return 0;
    
    let correctCount = 0;
    let totalTime = 0;
    let expectedTime = 0;
    let validResponses = 0;
    
    for (const response of responses) {
        if (!response || typeof response !== 'object') continue;
        
        const qData = questionData.get(response.questionId);
        if (!qData) continue;
        
        validResponses++;
        if (response.response === true) correctCount++;
        
        const timeSpent = typeof response.timeSpent === 'number' ? Math.max(0, response.timeSpent) : 0;
        const expectedT = typeof qData.question.expectedTime === 'number' ? 
            Math.max(1, qData.question.expectedTime) : 60; // Minimum 1 second to avoid division by zero
        
        totalTime += timeSpent;
        expectedTime += expectedT;
    }
    
    if (validResponses === 0) return 0;
    
    const accuracyScore = correctCount / validResponses;
    // Calculate time efficiency with bounds checking
    const timeEfficiencyRatio = (expectedTime > 0 && totalTime > 0) ? 
        Math.min(expectedTime / totalTime, 2) : 1; // Cap at 2x efficiency
    
    return Math.max(0, Math.min(1, (accuracyScore * 0.7) + (timeEfficiencyRatio * 0.3)));
};

/**
 * Fetch and cache question data for responses
 * @param {Array} responses - Test responses
 * @param {string} collectionName - Collection name for questions
 * @returns {Promise<Map>} Map of question data
 */
const fetchQuestionData = async (responses, collectionName) => {
    const questionData = new Map();
    const batchSize = 10;
    
    // Filter out invalid responses early
    const validResponses = responses.filter(r => r && r.questionId);
    
    // Process in batches to avoid overwhelming the database
    for (let i = 0; i < validResponses.length; i += batchSize) {
        const batch = validResponses.slice(i, i + batchSize);
        const promises = batch.map(async (response) => {
            try {
                const question = await getQuestion(response.questionId, collectionName);
                if (!question) return null;
                
                const subtopics = question.subtopic ? 
                    question.subtopic.split(',').map(s => s.trim()).filter(s => s.length > 0) : 
                    [];
                
                return {
                    questionId: response.questionId,
                    question,
                    response,
                    subtopics
                };
            } catch (error) {
                console.warn(`Failed to fetch question ${response.questionId}:`, error.message);
                return null;
            }
        });
        
        const results = await Promise.all(promises);
        results.forEach(result => {
            if (result) {
                questionData.set(result.questionId, result);
            }
        });
    }
    
    return questionData;
};

/**
 * Calculate comprehensive test metrics
 * @param {Array} responses - Test responses
 * @param {Object} attemptedTestRecord - Test attempt record
 * @returns {Object} Test metrics
 */
const calculateTestMetrics = (responses, attemptedTestRecord) => {
    const validResponses = responses.filter(r => r && typeof r === 'object');
    const correctAnswers = validResponses.filter(r => r.response === true).length;
    const totalQuestions = validResponses.length;
    
    if (totalQuestions === 0) {
        return {
            correctAnswers: 0,
            totalQuestions: 0,
            score: 0,
            avgTimePerQuestion: 0,
            accuracyRate: 0,
            timeEfficiency: 0
        };
    }
    
    const score = (correctAnswers / totalQuestions) * 100;
    const totalTime = validResponses.reduce((sum, r) => sum + Math.max(0, r.timeSpent || 0), 0);
    const avgTimePerQuestion = totalTime / totalQuestions;
    const accuracyRate = correctAnswers / totalQuestions;
    
    // Time efficiency: correct answers per unit time (prevent division by zero)
    const timeEfficiency = validResponses.reduce((eff, r) => {
        const timeSpent = Math.max(0.1, r.timeSpent || 0.1); // Minimum 0.1 to avoid division by zero
        return eff + (r.response === true ? 1 / timeSpent : 0);
    }, 0) / totalQuestions;
    
    return {
        correctAnswers,
        totalQuestions,
        score,
        avgTimePerQuestion,
        accuracyRate,
        timeEfficiency
    };
};

/**
 * Process strengths and weaknesses from subskill proficiency data
 * @param {Object} subskillProficiencyMap - Map of subskill proficiencies
 * @param {Map} questionData - Question data map
 * @param {number} strengthThreshold - Threshold for strengths (default 0.7)
 * @param {number} weaknessThreshold - Threshold for weaknesses (default 0.4)
 * @returns {Promise<Object>} Strengths and weaknesses arrays
 */
const processStrengthsAndWeaknesses = async (subskillProficiencyMap, questionData, strengthThreshold = 0.7, weaknessThreshold = 0.4) => {
    const strengthsUpdates = [];
    const weaknessesUpdates = [];
    
    if (!subskillProficiencyMap || typeof subskillProficiencyMap !== 'object') {
        return { strengthsUpdates, weaknessesUpdates };
    }
    
    for (const [subskill, proficiency] of Object.entries(subskillProficiencyMap)) {
        if (typeof proficiency !== 'number' || isNaN(proficiency) || proficiency < 0 || proficiency > 1) continue;
        
        // Find relevant responses for this subskill
        const relevantResponses = Array.from(questionData.values())
            .filter(qData => qData.subtopics.includes(subskill))
            .map(qData => qData.response);
        
        // Only calculate confidence if we have relevant responses
        const confidenceScore = relevantResponses.length > 0 ? 
            await calculateConfidenceScore(relevantResponses, questionData) : 0;
        
        const entry = {
            subtopicId: subskill,
            proficiency: Math.round(proficiency * 100) / 100,
            confidenceScore: Math.round(confidenceScore * 100) / 100,
            responseCount: relevantResponses.length,
            lastAssessed: new Date()
        };
        
        // Categorize based on proficiency thresholds
        if (proficiency >= strengthThreshold) {
            strengthsUpdates.push(entry);
        } else if (proficiency <= weaknessThreshold) { // Use <= instead of < for consistency
            weaknessesUpdates.push({
                ...entry,
                recommendedPractice: `Focus on ${subskill} fundamentals`,
                priority: proficiency < 0.2 ? 'high' : 'medium'
            });
        }
    }
    
    return { strengthsUpdates, weaknessesUpdates };
};

/**
 * Get today's date string in YYYY-MM-DD format
 * @returns {string} Today's date
 */
const getTodayDateString = () => {
    return new Date().toISOString().split('T')[0];
};

/**
 * Get start and end of day for date comparisons
 * @param {string} dateString - Date string in YYYY-MM-DD format
 * @returns {Object} Start and end Date objects
 */
const getDayBounds = (dateString) => {
    const startOfDay = new Date(dateString + 'T00:00:00.000Z');
    const endOfDay = new Date(dateString + 'T23:59:59.999Z');
    return { startOfDay, endOfDay };
};

/**
 * Create daily activity entry
 * @param {Object} attemptedTestRecord - Test attempt record
 * @param {Object} testMetrics - Test metrics
 * @param {number} consistencyScore - Consistency score
 * @returns {Object} Daily activity entry
 */
const createDailyActivityEntry = (attemptedTestRecord, testMetrics, consistencyScore) => {
    const totalTimeMinutes = Math.max(0, (attemptedTestRecord?.totalTimeTaken || 0) / 60);
    
    return {
        date: new Date(),
        minutesSpent: Math.round(totalTimeMinutes * 100) / 100,
        questionsAnswered: testMetrics.totalQuestions || 0,
        consistencyScore: Math.round((consistencyScore || 0) * 100) / 100,
        averageSessionLength: Math.round(totalTimeMinutes * 100) / 100,
        accuracy: Math.round((testMetrics.accuracyRate || 0) * 100) / 100
    };
};

/**
 * Initialize analytics structure for a subject
 * @returns {Object} Default analytics structure
 */
const createDefaultAnalytics = () => ({
    performanceMetrics: {
        averageTimePerQuestion: 0,
        averageScore: 0,
        currentProficiency: 0.5,
        improvementRate: 0,
        totalTestsTaken: 0,
        completionRate: 0
    },
    progressTracking: {
        dailyActivity: [],
        weeklyProgress: [],
        learningVelocity: 0
    },
    knowledgeAnalysis: {
        strengths: [],
        weaknesses: [],
        topicProficiencies: {}
    },
    learningBehavior: {
        errorPatterns: [],
        learningStyle: {
            speedVsAccuracy: 0.5,
            reflectionTime: 0,
            retryFrequency: 0
        }
    },
    personalRecommendations: {
        nextTopics: [],
        practiceSuggestions: [],
        estimatedMasteryTimeline: []
    },
    lastUpdated: new Date()
});

/**
 * Main function to update student analytics
 * @param {string} userId - Student ID
 * @param {Array} responses - Test responses
 * @param {Object} attemptedTestRecord - Test attempt record
 * @param {Object} subskillProficiencyMap - Subskill proficiency map
 * @param {string} subject - Subject name
 * @returns {Promise<Object>} Updated student document
 */
export const updateStudentAnalytics = async (userId, responses, attemptedTestRecord, subskillProficiencyMap, subject) => {
    // Input validation
    if (!userId || typeof userId !== 'string') {
        console.error('Valid userId is required');
    }
    
    if (!Array.isArray(responses)) {
        console.error('Responses must be an array');
    }
    
    if (!attemptedTestRecord || typeof attemptedTestRecord !== 'object') {
        console.error('Attempted test record is required');
    }
    
    if (!attemptedTestRecord.testId) {
        console.error('Test ID is required in attempted test record');
    }
    
    if (!subject || typeof subject !== 'string') {
        console.error('Subject name is required');
    }
    
    let session = null;
    
    try {
        // Get collection name for questions
        const collectionName = await getCollectionNameTest(attemptedTestRecord.testId, subject);
        if (!collectionName) {
            console.error('Could not determine collection name for test questions');
        }
        
        // Start MongoDB session for transaction consistency
        session = await Student.startSession();
        
        return await session.withTransaction(async () => {
            // Fetch current student data
            const student = await Student.findById(userId).session(session);
            if (!student) {
                console.error(`Student not found with ID: ${userId}`);
            }
            
            // Find or initialize subject data
            let subjectIndex = student.subjects?.findIndex(s => 
                new RegExp(`^${subject}$`, 'i').test(s.subjectName)
            );
            
            if (subjectIndex === -1) {
                // Initialize subject if it doesn't exist
                if (!student.subjects) student.subjects = [];
                student.subjects.push({
                    subjectName: subject,
                    analytics: createDefaultAnalytics()
                });
                subjectIndex = student.subjects.length - 1;
                await student.save({ session });
            }
            
            const subjectObj = student.subjects[subjectIndex];
            
            // Initialize analytics if missing
            if (!subjectObj.analytics) {
                subjectObj.analytics = createDefaultAnalytics();
            }
            
            // Calculate test metrics
            const testMetrics = calculateTestMetrics(responses, attemptedTestRecord);
            
            // Get historical data for consistency calculation
            const previousTests = Array.isArray(subjectObj.attemptedTests) ? subjectObj.attemptedTests : [];
            const previousScores = previousTests
                .filter(test => test && typeof test.totalScore === 'number' && !isNaN(test.totalScore))
                .map(test => test.totalScore);
            
            const consistencyScore = calculateConsistencyScore(previousScores, testMetrics.score);
            
            // Calculate proficiency changes
            const currentProficiency = subjectObj.analytics?.performanceMetrics?.currentProficiency ?? 0.5;
            const newProficiency = calculateProficiencyChange(currentProficiency, testMetrics.accuracyRate);
            
            // Fetch question data
            const questionData = await fetchQuestionData(responses, collectionName);
            
            // Process strengths and weaknesses
            const { strengthsUpdates, weaknessesUpdates } = await processStrengthsAndWeaknesses(
                subskillProficiencyMap,
                questionData
            );
            
            // Calculate updated metrics
            const totalTestsTaken = (subjectObj.analytics?.performanceMetrics?.totalTestsTaken || 0) + 1;
            const historicalAvgScore = subjectObj.analytics?.performanceMetrics?.averageScore || 0;
            const historicalAvgTime = subjectObj.analytics?.performanceMetrics?.averageTimePerQuestion || 0;
            
            // Use proper weighted average calculation
            const updatedAvgScore = totalTestsTaken === 1 ? 
                testMetrics.score : 
                ((historicalAvgScore * (totalTestsTaken - 1)) + testMetrics.score) / totalTestsTaken;
                
            const updatedAvgTime = totalTestsTaken === 1 ? 
                testMetrics.avgTimePerQuestion : 
                ((historicalAvgTime * (totalTestsTaken - 1)) + testMetrics.avgTimePerQuestion) / totalTestsTaken;
            
            // Create daily activity entry
            const dailyActivityEntry = createDailyActivityEntry(attemptedTestRecord, testMetrics, consistencyScore);
            const today = getTodayDateString();
            const { startOfDay, endOfDay } = getDayBounds(today);
            
            // Check if there's already an entry for today
            const hasActivityForToday = await Student.findOne({
                _id: userId,
                "subjects.subjectName": { $regex: `^${subject}$`, $options: 'i' },
                "subjects.analytics.progressTracking.dailyActivity": {
                    $elemMatch: {
                        date: {
                            $gte: startOfDay,
                            $lte: endOfDay
                        }
                    }
                }
            }).session(session);
            
            // Prepare topic proficiencies update
            const topicProficiencies = {};
            if (subskillProficiencyMap && typeof subskillProficiencyMap === 'object') {
                for (const [skill, prof] of Object.entries(subskillProficiencyMap)) {
                    if (typeof prof === 'number' && !isNaN(prof) && prof >= 0 && prof <= 1) {
                        topicProficiencies[skill] = Math.round(prof * 10000) / 10000;
                    }
                }
            }
            
            // Build comprehensive update operation
            const updateFields = {
                "subjects.$.analytics.performanceMetrics.averageTimePerQuestion": Math.round(updatedAvgTime * 100) / 100,
                "subjects.$.analytics.performanceMetrics.averageScore": Math.round(updatedAvgScore * 100) / 100,
                "subjects.$.analytics.performanceMetrics.currentProficiency": Math.round(newProficiency * 10000) / 10000,
                "subjects.$.analytics.performanceMetrics.improvementRate": Math.round((newProficiency - currentProficiency) * 10000) / 100,
                "subjects.$.analytics.performanceMetrics.totalTestsTaken": totalTestsTaken,
                "subjects.$.analytics.performanceMetrics.completionRate": Math.round((responses.filter(r => r.response !== null).length / Math.max(1, responses.length)) * 10000) / 10000,
                "subjects.$.analytics.knowledgeAnalysis.topicProficiencies": topicProficiencies,
                "subjects.$.analytics.lastUpdated": new Date()
            };
            
            const updateOperation = { $set: updateFields };
            const arrayFilters = [];
            
            // Handle daily activity updates
            if (hasActivityForToday) {
                // Update existing daily activity entry
                updateOperation.$inc = {
                    "subjects.$.analytics.progressTracking.dailyActivity.$[elem].minutesSpent": dailyActivityEntry.minutesSpent,
                    "subjects.$.analytics.progressTracking.dailyActivity.$[elem].questionsAnswered": dailyActivityEntry.questionsAnswered
                };
                
                // Update other fields for today's entry
                updateOperation.$set["subjects.$.analytics.progressTracking.dailyActivity.$[elem].accuracy"] = dailyActivityEntry.accuracy;
                updateOperation.$set["subjects.$.analytics.progressTracking.dailyActivity.$[elem].consistencyScore"] = dailyActivityEntry.consistencyScore;
                
                arrayFilters.push({
                    "elem.date": {
                        $gte: startOfDay,
                        $lte: endOfDay
                    }
                });
            } else {
                // Add new daily activity entry
                updateOperation.$push = {
                    "subjects.$.analytics.progressTracking.dailyActivity": {
                        $each: [dailyActivityEntry],
                        $sort: { date: -1 },
                        $slice: 30 // Keep last 30 days
                    }
                };
            }
            
            // Add strengths and weaknesses updates (replace existing entries to avoid duplicates)
            if (strengthsUpdates.length > 0) {
                updateOperation.$set["subjects.$.analytics.knowledgeAnalysis.strengths"] = strengthsUpdates
                    .sort((a, b) => b.proficiency - a.proficiency)
                    .slice(0, 15); // Keep top 15 strengths
            }
            
            if (weaknessesUpdates.length > 0) {
                updateOperation.$set["subjects.$.analytics.knowledgeAnalysis.weaknesses"] = weaknessesUpdates
                    .sort((a, b) => a.proficiency - b.proficiency)
                    .slice(0, 15); // Keep top 15 weaknesses
            }
            
            // Execute the update
            const updateOptions = {
                new: true,
                session,
                upsert: false
            };
            
            if (arrayFilters.length > 0) {
                updateOptions.arrayFilters = arrayFilters;
            }
            
            const updateResult = await Student.findOneAndUpdate(
                { 
                    _id: userId, 
                    "subjects.subjectName": { $regex: `^${subject}$`, $options: 'i' } 
                },
                updateOperation,
                updateOptions
            );
            
            if (!updateResult) {
                console.error('Failed to update student analytics: No document was updated');
            }
            
            return {
                success: true,
                message: 'Student analytics updated successfully',
                data: {
                    studentId: userId,
                    subject: subject,
                    newProficiency: Math.round(newProficiency * 10000) / 10000,
                    testScore: Math.round(testMetrics.score * 100) / 100,
                    totalTestsTaken: totalTestsTaken,
                    strengthsCount: strengthsUpdates.length,
                    weaknessesCount: weaknessesUpdates.length,
                    consistencyScore: Math.round(consistencyScore * 100) / 100
                }
            };
        });
        
    } catch (error) {
        console.error('Error updating student analytics:', {
            error: error.message,
            userId,
            subject,
            testId: attemptedTestRecord?.testId,
            stack: error.stack
        });
        
        // console.error(`Failed to update student analytics: ${error.message}`);
    } finally {
        // Ensure session is properly closed
        if (session) {
            await session.endSession();
        }
    }
};