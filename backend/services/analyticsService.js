import {
    UserSession,
    UserInteraction,
    PageAnalytics,
    FeatureUsage,
    UserBehavior
} from '../models/UserAnalytics.js';
import { UAParser } from 'ua-parser-js';

class AnalyticsService {
    
    // Create or update user session
    async createSession(sessionData) {
        try {
            console.log('Analytics Service: Creating session with data:', sessionData);

            const parser = new UAParser(sessionData.userAgent);
            const deviceInfo = {
                userAgent: sessionData.userAgent,
                browser: `${parser.getBrowser().name} ${parser.getBrowser().version}`,
                os: `${parser.getOS().name} ${parser.getOS().version}`,
                device: parser.getDevice().type || 'desktop',
                screenResolution: sessionData.screenResolution,
                timezone: sessionData.timezone
            };

            console.log('Analytics Service: Parsed device info:', deviceInfo);

            const session = new UserSession({
                userId: sessionData.userId,
                userType: sessionData.userType,
                sessionId: sessionData.sessionId,
                deviceInfo,
                ipAddress: sessionData.ipAddress,
                location: sessionData.location
            });

            console.log('Analytics Service: Saving session to database...');
            await session.save();
            console.log('Analytics Service: Session saved successfully:', session._id);
            return session;
        } catch (error) {
            console.error('Analytics Service: Error creating session:', error);
            throw error;
        }
    }

    // Update session end time and duration
    async endSession(sessionId) {
        try {
            const session = await UserSession.findOne({ sessionId, isActive: true });
            if (session) {
                const endTime = new Date();
                const duration = Math.floor((endTime - session.startTime) / 1000);
                
                session.endTime = endTime;
                session.duration = duration;
                session.isActive = false;
                
                await session.save();
                
                // Update user behavior patterns
                await this.updateUserBehavior(session.userId, session.userType, duration);
                
                return session;
            }
        } catch (error) {
            console.error('Error ending session:', error);
            throw error;
        }
    }

    // Track user interaction
    async trackInteraction(interactionData) {
        try {
            const interaction = new UserInteraction(interactionData);
            await interaction.save();
            
            // Update session last activity
            await UserSession.updateOne(
                { sessionId: interactionData.sessionId, isActive: true },
                { lastActivity: new Date() }
            );
            
            // Update page analytics
            await this.updatePageAnalytics(interactionData);
            
            // Update feature usage analytics
            if (interactionData.feature) {
                await this.updateFeatureUsage(interactionData);
            }
            
            return interaction;
        } catch (error) {
            console.error('Error tracking interaction:', error);
            throw error;
        }
    }

    // Update page analytics (aggregated daily data)
    async updatePageAnalytics(interactionData) {
        try {
            if (interactionData.interactionType !== 'page_view') return;
            
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            const pageAnalytics = await PageAnalytics.findOneAndUpdate(
                { page: interactionData.page, date: today },
                {
                    $inc: { 
                        totalViews: 1,
                        [`userTypeBreakdown.${interactionData.userType.toLowerCase()}s`]: 1
                    }
                },
                { upsert: true, new: true }
            );
            
            // Update unique visitors count
            const uniqueVisitorsToday = await UserInteraction.distinct('userId', {
                page: interactionData.page,
                interactionType: 'page_view',
                timestamp: { $gte: today }
            });
            
            pageAnalytics.uniqueVisitors = uniqueVisitorsToday.length;
            await pageAnalytics.save();
            
        } catch (error) {
            console.error('Error updating page analytics:', error);
        }
    }

    // Update feature usage analytics
    async updateFeatureUsage(interactionData) {
        try {
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            
            await FeatureUsage.findOneAndUpdate(
                { feature: interactionData.feature, date: today },
                {
                    $inc: { 
                        totalUsage: 1,
                        [`userTypeUsage.${interactionData.userType.toLowerCase()}s`]: 1
                    }
                },
                { upsert: true, new: true }
            );
            
            // Update unique users count for this feature today
            const uniqueUsersToday = await UserInteraction.distinct('userId', {
                feature: interactionData.feature,
                timestamp: { $gte: today }
            });
            
            await FeatureUsage.updateOne(
                { feature: interactionData.feature, date: today },
                { uniqueUsers: uniqueUsersToday.length }
            );
            
        } catch (error) {
            console.error('Error updating feature usage:', error);
        }
    }

    // Update user behavior patterns
    async updateUserBehavior(userId, userType, sessionDuration) {
        try {
            let userBehavior = await UserBehavior.findOne({ userId });
            
            if (!userBehavior) {
                userBehavior = new UserBehavior({
                    userId,
                    userType,
                    retentionMetrics: {
                        firstVisit: new Date(),
                        lastVisit: new Date(),
                        totalSessions: 1,
                        totalTimeSpent: sessionDuration
                    }
                });
            } else {
                userBehavior.retentionMetrics.lastVisit = new Date();
                userBehavior.retentionMetrics.totalSessions += 1;
                userBehavior.retentionMetrics.totalTimeSpent += sessionDuration;
            }
            
            // Calculate engagement score (simple algorithm)
            const avgSessionDuration = userBehavior.retentionMetrics.totalTimeSpent / userBehavior.retentionMetrics.totalSessions;
            const sessionFrequency = userBehavior.retentionMetrics.totalSessions;
            userBehavior.engagementScore = Math.min(100, (avgSessionDuration / 60) * 10 + sessionFrequency * 2);
            
            userBehavior.lastUpdated = new Date();
            await userBehavior.save();
            
        } catch (error) {
            console.error('Error updating user behavior:', error);
        }
    }

    // Get analytics dashboard data
    async getDashboardData(dateRange = 30) {
        try {
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - dateRange);
            
            // Get overall metrics
            const totalUsers = await UserSession.distinct('userId', {
                startTime: { $gte: startDate }
            });
            
            const totalSessions = await UserSession.countDocuments({
                startTime: { $gte: startDate }
            });
            
            const avgSessionDuration = await UserSession.aggregate([
                { $match: { startTime: { $gte: startDate }, duration: { $exists: true } } },
                { $group: { _id: null, avgDuration: { $avg: '$duration' } } }
            ]);
            
            // Get user type breakdown
            const userTypeBreakdown = await UserSession.aggregate([
                { $match: { startTime: { $gte: startDate } } },
                { $group: { _id: '$userType', count: { $sum: 1 } } }
            ]);
            
            // Get top pages
            const topPages = await PageAnalytics.aggregate([
                { $match: { date: { $gte: startDate } } },
                { $group: { _id: '$page', totalViews: { $sum: '$totalViews' } } },
                { $sort: { totalViews: -1 } },
                { $limit: 10 }
            ]);
            
            // Get feature usage
            const featureUsage = await FeatureUsage.aggregate([
                { $match: { date: { $gte: startDate } } },
                { $group: { _id: '$feature', totalUsage: { $sum: '$totalUsage' } } },
                { $sort: { totalUsage: -1 } }
            ]);
            
            // Get daily activity trend
            const dailyActivity = await UserSession.aggregate([
                { $match: { startTime: { $gte: startDate } } },
                {
                    $group: {
                        _id: { $dateToString: { format: '%Y-%m-%d', date: '$startTime' } },
                        sessions: { $sum: 1 },
                        uniqueUsers: { $addToSet: '$userId' }
                    }
                },
                { $addFields: { uniqueUsers: { $size: '$uniqueUsers' } } },
                { $sort: { _id: 1 } }
            ]);
            
            return {
                overview: {
                    totalUsers: totalUsers.length,
                    totalSessions,
                    avgSessionDuration: avgSessionDuration[0]?.avgDuration || 0,
                    dateRange
                },
                userTypeBreakdown,
                topPages,
                featureUsage,
                dailyActivity
            };
            
        } catch (error) {
            console.error('Error getting dashboard data:', error);
            throw error;
        }
    }

    // Get user-specific analytics
    async getUserAnalytics(userId, dateRange = 30) {
        try {
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - dateRange);
            
            const userSessions = await UserSession.find({
                userId,
                startTime: { $gte: startDate }
            }).sort({ startTime: -1 });
            
            const userInteractions = await UserInteraction.find({
                userId,
                timestamp: { $gte: startDate }
            }).sort({ timestamp: -1 });
            
            const userBehavior = await UserBehavior.findOne({ userId });
            
            return {
                sessions: userSessions,
                interactions: userInteractions,
                behavior: userBehavior
            };
            
        } catch (error) {
            console.error('Error getting user analytics:', error);
            throw error;
        }
    }

    // Clean up old data (for data retention compliance)
    async cleanupOldData(retentionDays = 365) {
        try {
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - retentionDays);
            
            await UserSession.deleteMany({ startTime: { $lt: cutoffDate } });
            await UserInteraction.deleteMany({ timestamp: { $lt: cutoffDate } });
            await PageAnalytics.deleteMany({ date: { $lt: cutoffDate } });
            await FeatureUsage.deleteMany({ date: { $lt: cutoffDate } });
            
            console.log(`Cleaned up analytics data older than ${retentionDays} days`);
        } catch (error) {
            console.error('Error cleaning up old data:', error);
        }
    }
}

export default new AnalyticsService();
