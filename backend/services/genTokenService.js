import jwt from 'jsonwebtoken';


export const genTokens = (user) => {
  const accessToken = jwt.sign(
    {"id": user._id},
    process.env.ACCESS_TOKEN_SECRET,
    { expiresIn: process.env.ACCESS_EXPIRES_IN }
  );
  const refreshToken = jwt.sign(
    {"id": user._id},
    process.env.REFRESH_TOKEN_SECRET,
    { expiresIn: process.env.REFRESH_EXPIRES_IN }
  );

  return [accessToken, refreshToken];
}
