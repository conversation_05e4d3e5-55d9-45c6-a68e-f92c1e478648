// forgotPasswordService.js
import jwt from 'jsonwebtoken';
import nodemailer from 'nodemailer';
import Student from '../models/Student.js';
import Teacher from '../models/Teacher.js';

const JWT_SECRET = "eFWU3gMvhQ56Bx8HHBMp";
const JWT_RESET_EXPIRATION = "1h";
const EMAIL_FROM = "<EMAIL>";

/**
 * Send password reset email
 * @param {string} email - User email address
 * @param {string} userType - Either 'Student' or 'Teacher'
 * @returns {Promise<Object>} Status message
 */
export const sendPasswordResetEmail = async (email, userType, origin) => {
    try {
        // Find user by email
        let user;
        if (userType === 'Student') {
            user = await Student.findOne({ email });
        } else if (userType === 'Teacher') {
            user = await Teacher.findOne({ email });
        } else {
            console.error('Invalid user type');
        }

        if (!user) {
            console.error('No account found with this email');
        }

        // Generate reset token
        const resetToken = jwt.sign(
            { userId: user._id, userType, email },
            JWT_SECRET,
            { expiresIn: JWT_RESET_EXPIRATION }
        );

        // Create reset link
        const resetLink = `${origin}/reset-password?token=${resetToken}`;

        // Send email
        const transporter = nodemailer.createTransport({
            host: "smtp.gmail.com",
            port: 587,
            secure: false,
            auth: {
                user: process.env.EMAIL_USERNAME || "<EMAIL>",
                pass: process.env.EMAIL_PASSWORD || "egeaaomtiqgothho"
            },
        });

        await transporter.sendMail({
            from: EMAIL_FROM,
            to: email,
            subject: 'Password Reset Request',
            html: `
        <h1>Password Reset</h1>
        <p>You requested a password reset for your account.</p>
        <p>Click the link below to reset your password. This link is valid for 1 hour.</p>
        <a href="${resetLink}" style="display: inline-block; padding: 10px 20px; background-color: #4CAF50; color: white; text-decoration: none; border-radius: 5px;">Reset Password</a>
        <p>If you didn't request this, please ignore this email.</p>
      `,
        });

        return { message: 'Password reset email sent successfully' };
    } catch (error) {
        console.error('Send password reset email error:', error);
        
    }
};

/**
 * Reset user password with token
 * @param {string} token - Reset password token
 * @param {string} newPassword - New password
 * @returns {Promise<Object>} Status message
 */
export const resetPassword = async (token, newPassword) => {
    try {
        const { userId, userType } = verifyAndDecodeToken(token);
        const user = await findUserByTypeAndId(userType, userId);
        
        // Update password
        user.password = newPassword; // Assuming you have a pre-save hook that hashes the password
        await user.save();

        return { message: 'Password reset successful' };
    } catch (error) {
        console.error('Reset password error:', error);
        
    }
};

/**
 * Validate a reset password token without resetting the password
 * @param {string} token - Reset password token
 * @returns {Promise<Object>} The decoded token payload or error
 */
export const validateResetToken = async (token) => {
    try {
        const decoded = verifyAndDecodeToken(token);
        await findUserByTypeAndId(decoded.userType, decoded.userId);
        
        return { valid: true, decoded };
    } catch (error) {
        console.error('Token validation error:', error);
        return { 
            valid: false, 
            error: error.name === 'TokenExpiredError' ? 'Token expired' : 'Invalid token' 
        };
    }
};

/**
 * Helper function to verify and decode JWT token
 * @param {string} token - JWT token to verify
 * @returns {Object} Decoded token payload
 */
const verifyAndDecodeToken = (token) => {
    try {
        return jwt.verify(token, JWT_SECRET);
    } catch (error) {
        if (error.name === 'TokenExpiredError') {
            console.error('Password reset link expired');
        }
        console.error('Invalid or expired reset token');
    }
};

/**
 * Helper function to find user by type and ID
 * @param {string} userType - Either 'Student' or 'Teacher'
 * @param {string} userId - User's ID
 * @returns {Promise<Object>} User document
 */
const findUserByTypeAndId = async (userType, userId) => {
    let user;
    
    if (userType === 'Student') {
        user = await Student.findById(userId);
    } else if (userType === 'Teacher') {
        user = await Teacher.findById(userId);
    } else {
        console.error('Invalid user type in token');
    }

    if (!user) {
        console.error('User not found');
    }
    
    return user;
};