import { createKnowledgeGraphModel } from '../models/knowledgeGraphModel.js';

class CurriculumService {
  async createCurriculumGraph(curriculumData, relatedSubtopics, subject) {
    try {
      // Get the subject-specific model
      const CurriculumNode = createKnowledgeGraphModel(subject);
      
      // Clear existing nodes before creating new graph
      await CurriculumNode.deleteMany({});
      
      // Build the curriculum graph
      const chapterNodes = await CurriculumNode.buildCurriculumGraph(curriculumData, relatedSubtopics);
      
      return chapterNodes;
    } catch (error) {
      console.error(`Error creating curriculum graph for ${subject}:`, error);
      
    }
  }

  async getCurriculumStructure(subject) {
    try {
      const CurriculumNode = createKnowledgeGraphModel(subject);
      return await CurriculumNode.visualizeCurriculum();
    } catch (error) {
      console.error(`Error retrieving curriculum structure for ${subject}:`, error);
      
    }
  }

  async getCurriculumStructureClassWise(subject, classDetail) {
    try {
      const CurriculumNode = createKnowledgeGraphModel(subject);
      return await CurriculumNode.visualizeCurriculumClassWise(classDetail);
    } catch (error) {
      console.error(`[ERROR] Retrieving curriculum structure for ${subject} and class ${classDetail} failed:`, error);
    }
  }

  async getChapterSubtopics(chapterName, subject) {
    try {
      const CurriculumNode = createKnowledgeGraphModel(subject);
      const chapter = await CurriculumNode.findOne({ 
        name: chapterName, 
        type: 'Chapter' 
      }).populate('children');

      return chapter ? chapter.children : [];
    } catch (error) {
      console.error(`Error retrieving subtopics for chapter ${chapterName} in ${subject}:`, error);
      
    }
  }
}

export default new CurriculumService();
