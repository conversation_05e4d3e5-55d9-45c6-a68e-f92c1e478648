import { StudentCurriculumModel } from '../models/StudentKnowledgeGraphModel.js';
import Student from '../models/Student.js';

/**
 * Calculate weighted average proficiency from curriculum progress
 * @param {Array} curriculumProgress - Array of curriculum progress items
 * @param {Object} options - Calculation options
 * @returns {Number} Calculated overall proficiency (0-100)
 */
export const calculateOverallProficiencyFromCurriculum = (curriculumProgress, options = {}) => {
    try {
        // Handle edge cases
        if (!curriculumProgress || !Array.isArray(curriculumProgress) || curriculumProgress.length === 0) {
            return 0;
        }

        // Filter out invalid progress items
        const validProgress = curriculumProgress.filter(progress => 
            progress && 
            progress.nodeId && 
            typeof progress.proficiency === 'number' && 
            !isNaN(progress.proficiency)
        );

        if (validProgress.length === 0) {
            return 0;
        }

        // Default weighting strategy: equal weights
        const { 
            weightingStrategy = 'equal',
            topicWeight = 1.0,
            subtopicWeight = 1.0 
        } = options;

        let totalWeightedScore = 0;
        let totalWeight = 0;

        validProgress.forEach(progress => {
            const proficiency = Math.max(0, Math.min(100, progress.proficiency)); // Clamp between 0-100
            let weight = 1; // Default weight

            // Apply weighting strategy
            if (weightingStrategy === 'nodeType' && progress.nodeId.type) {
                switch (progress.nodeId.type) {
                    case 'Chapter':
                        weight = topicWeight;
                        break;
                    case 'Subtopic':
                        weight = subtopicWeight;
                        break;
                    default:
                        weight = 1;
                }
            } else if (weightingStrategy === 'order' && progress.nodeId.order !== undefined) {
                // Higher order = more important (later topics might be more complex)
                weight = 1 + (progress.nodeId.order * 0.1);
            }
            // For 'equal' strategy, weight remains 1

            totalWeightedScore += proficiency * weight;
            totalWeight += weight;
        });

        // Calculate weighted average
        const overallProficiency = totalWeight > 0 ? totalWeightedScore / totalWeight : 0;
        
        // Round to 2 decimal places and ensure it's between 0-100
        return Math.max(0, Math.min(100, Math.round(overallProficiency * 100) / 100));

    } catch (error) {
        console.error('Error calculating overall proficiency:', error);
        return 0;
    }
};

/**
 * Update overall proficiency for a specific subject
 * @param {String} studentId - Student ID
 * @param {String} subject - Subject name
 * @param {Object} options - Calculation options
 * @returns {Number} Updated overall proficiency
 */
export const updateSubjectOverallProficiency = async (studentId, subject, options = {}) => {
    try {
        // Get student's curriculum progress for the subject
        const studentCurriculum = await StudentCurriculumModel.findOne({
            studentId,
            subject
        }).populate('curriculumProgress.nodeId');

        if (!studentCurriculum) {
            console.warn(`No curriculum found for student ${studentId} in subject ${subject}`);
            return 0;
        }

        // Calculate overall proficiency
        const overallProficiency = calculateOverallProficiencyFromCurriculum(
            studentCurriculum.curriculumProgress,
            options
        );

        // Update the student's subject overall proficiency
        const updateResult = await Student.findOneAndUpdate(
            { 
                _id: studentId,
                'subjects.subjectName': subject
            },
            {
                $set: {
                    'subjects.$.overallProficiency': overallProficiency / 100 // Store as 0-1 range
                }
            },
            { new: true }
        );

        if (!updateResult) {
            console.warn(`Failed to update overall proficiency for student ${studentId} in subject ${subject}`);
            return overallProficiency;
        }

        // Also update the totalProficiency in StudentCurriculum for consistency
        await StudentCurriculumModel.findByIdAndUpdate(
            studentCurriculum._id,
            {
                $set: {
                    totalProficiency: overallProficiency
                }
            }
        );

        console.log(`Updated overall proficiency for student ${studentId} in ${subject}: ${overallProficiency}%`);
        return overallProficiency;

    } catch (error) {
        console.error(`Error updating overall proficiency for student ${studentId} in subject ${subject}:`, error);
        return 0;
    }
};

/**
 * Batch update overall proficiency for multiple students
 * @param {Array} updates - Array of {studentId, subject} objects
 * @param {Object} options - Calculation options
 * @returns {Array} Array of results
 */
export const batchUpdateOverallProficiency = async (updates, options = {}) => {
    const results = [];
    
    for (const { studentId, subject } of updates) {
        try {
            const proficiency = await updateSubjectOverallProficiency(studentId, subject, options);
            results.push({ studentId, subject, proficiency, success: true });
        } catch (error) {
            console.error(`Failed to update proficiency for ${studentId} in ${subject}:`, error);
            results.push({ studentId, subject, proficiency: 0, success: false, error: error.message });
        }
    }
    
    return results;
};

/**
 * Migrate existing records from hardcoded 0.5 to calculated values
 * @param {Object} options - Migration options
 * @returns {Object} Migration results
 */
export const migrateHardcodedProficiencies = async (options = {}) => {
    try {
        const { dryRun = false, batchSize = 100 } = options;
        
        // Find all students with hardcoded 0.5 proficiency
        const studentsWithHardcodedProficiency = await Student.find({
            'subjects.overallProficiency': 0.5
        }).select('_id subjects');

        console.log(`Found ${studentsWithHardcodedProficiency.length} students with hardcoded proficiency`);

        if (dryRun) {
            return {
                totalStudents: studentsWithHardcodedProficiency.length,
                dryRun: true,
                message: 'Dry run completed - no changes made'
            };
        }

        let updated = 0;
        let failed = 0;

        // Process in batches
        for (let i = 0; i < studentsWithHardcodedProficiency.length; i += batchSize) {
            const batch = studentsWithHardcodedProficiency.slice(i, i + batchSize);
            
            for (const student of batch) {
                for (const subject of student.subjects) {
                    if (subject.overallProficiency === 0.5) {
                        try {
                            await updateSubjectOverallProficiency(
                                student._id.toString(),
                                subject.subjectName,
                                options
                            );
                            updated++;
                        } catch (error) {
                            console.error(`Failed to migrate ${student._id} - ${subject.subjectName}:`, error);
                            failed++;
                        }
                    }
                }
            }
        }

        return {
            totalStudents: studentsWithHardcodedProficiency.length,
            updated,
            failed,
            message: `Migration completed: ${updated} updated, ${failed} failed`
        };

    } catch (error) {
        console.error('Error during migration:', error);
        throw error;
    }
};
