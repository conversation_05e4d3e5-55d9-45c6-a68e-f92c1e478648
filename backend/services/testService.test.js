import { calculateSimilarity } from './testService.js';

describe('calculateSimilarity', () => {
    test('identical strings should have similarity of 1', () => {
        expect(calculateSimilarity('hello', 'hello')).toBe(1);
        expect(calculateSimilarity('test', 'test')).toBe(1);
    });

    test('completely different strings should have low similarity', () => {
        expect(calculateSimilarity('hello', 'world')).toBeCloseTo(0.080, 2);
        expect(calculateSimilarity('test', 'xyz')).toBe(0);
    });

    test('strings with minor differences should have moderate similarity', () => {
        expect(calculateSimilarity('hello', 'helo')).toBeCloseTo(0.320, 2);
        expect(calculateSimilarity('test', 'tst')).toBeCloseTo(0.300, 2);
    });

    test('case sensitivity should not affect similarity', () => {
        expect(calculateSimilarity('Hello', 'hello')).toBe(1);
        expect(calculateSimilarity('TEST', 'test')).toBe(1);
    });

    test('strings with extra spaces should have moderate similarity', () => {
        expect(calculateSimilarity('hello world', 'helloworld')).toBeCloseTo(0.364, 2);
        expect(calculateSimilarity('test case', 'testcase')).toBeCloseTo(0.356, 2);
    });

    test('strings with different lengths should have appropriate similarity', () => {
        expect(calculateSimilarity('hello', 'hell')).toBeCloseTo(0.320, 2);
        expect(calculateSimilarity('test', 'testing')).toBeCloseTo(0.229, 2);
    });

    test('empty strings should be handled correctly', () => {
        expect(calculateSimilarity('', '')).toBe(1);
        expect(calculateSimilarity('hello', '')).toBe(0);
        expect(calculateSimilarity('', 'hello')).toBe(0);
    });

    test('strings with special characters should be handled correctly', () => {
        expect(calculateSimilarity('hello!', 'hello')).toBeCloseTo(0.533, 2);
        expect(calculateSimilarity('test@123', 'test123')).toBeCloseTo(0.550, 2);
    });

    test('strings with numbers should be handled correctly', () => {
        expect(calculateSimilarity('123', '123')).toBe(1);
        expect(calculateSimilarity('123', '124')).toBeCloseTo(0.267, 2);
    });

    test('strings with mixed content should be handled correctly', () => {
        expect(calculateSimilarity('test123!', 'test123')).toBeCloseTo(0.550, 2);
        expect(calculateSimilarity('hello123', 'hello124')).toBeCloseTo(0.350, 2);
    });

    test('answer formatting variations should have high similarity', () => {
        // Note: Our prefix stripping now works better for proper prefixes
        expect(calculateSimilarity('answer', 'a). answer')).toBeCloseTo(0.573, 2);
        expect(calculateSimilarity('answer', 'a) answer')).toBe(1); // Perfect match after prefix removal
        expect(calculateSimilarity('answer', 'A. answer')).toBe(1); // Perfect match after prefix removal
        expect(calculateSimilarity('answer', '1. answer')).toBe(1); // Perfect match after prefix removal
        expect(calculateSimilarity('answer', 'answer.')).toBeCloseTo(0.543, 2);
        expect(calculateSimilarity('answer', 'answer,')).toBeCloseTo(0.543, 2);
    });

    test('mathematical expressions should have appropriate similarity', () => {
        // These have high character overlap but different meanings
        expect(calculateSimilarity('number > 3', 'number < 3')).toBeCloseTo(0.760, 2);
        expect(calculateSimilarity('x + 5', 'x - 5')).toBeCloseTo(0.600, 2);
        expect(calculateSimilarity('y = 2x', 'y = 3x')).toBeCloseTo(0.633, 2);
        expect(calculateSimilarity('sqrt(16)', 'sqrt(25)')).toBeCloseTo(0.300, 2);
        expect(calculateSimilarity('2πr', '2πd')).toBeCloseTo(0.267, 2);
    });

    test('answers with small but significant differences', () => {
        expect(calculateSimilarity('the cat', 'the hat')).toBeCloseTo(0.476, 2);
        expect(calculateSimilarity('blue sky', 'blue sea')).toBeCloseTo(0.533, 2);
        expect(calculateSimilarity('right angle', 'right triangle')).toBeCloseTo(0.548, 2);
        expect(calculateSimilarity('prime number', 'prime factor')).toBeCloseTo(0.467, 2);
        expect(calculateSimilarity('parallel lines', 'perpendicular lines')).toBeCloseTo(0.423, 2);
    });

    // Additional tests for edge cases specific to our multi-metric approach
    test('multi-word answers with good keyword overlap should score well', () => {
        const longCorrect = "Object detection (to find the license plate region), followed by Optical Character Recognition (OCR) on the detected plate region.";
        const longWithoutPrefix = "Object detection (to find the license plate region), followed by Optical Character Recognition (OCR) on the detected plate region.";
        
        expect(calculateSimilarity(longCorrect, longWithoutPrefix)).toBeGreaterThan(0.95);
    });

    test('answers with shared keywords but different structure should have moderate similarity', () => {
        expect(calculateSimilarity('machine learning algorithm', 'algorithm for machine learning')).toBeGreaterThan(0.6);
        expect(calculateSimilarity('deep neural network', 'neural network with deep layers')).toBeGreaterThan(0.5);
    });

    test('technical terms should be handled appropriately', () => {
        expect(calculateSimilarity('HTTP protocol', 'HTTPS protocol')).toBeGreaterThan(0.7);
        expect(calculateSimilarity('TCP/IP', 'UDP/IP')).toBeGreaterThan(0.6);
        expect(calculateSimilarity('JavaScript', 'TypeScript')).toBeGreaterThan(0.4);
    });
});