import { GoogleGenerativeAI } from '@google/generative-ai';
import { StudentCurriculumModel } from '../models/StudentKnowledgeGraphModel.js';
import mongoose from 'mongoose';
import { createKnowledgeGraphModel } from '../models/knowledgeGraphModel.js';
import LLMCache from '../models/LLMCacheModel.js';
import ChatConversation from '../models/ChatConversation.js';
import Teacher from '../models/Teacher.js';
import mongoMcpService from './mongoMcpService.js';
import { config } from 'dotenv';
import crypto from 'crypto';

config();

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);
const defaultGeminiModel = "gemini-2.0-flash";

// Helper function to hash messages for caching
const hashMessage = (message) => {
  return crypto.createHash('sha256').update(message).digest('hex').substring(0, 16);
};

// Validate teacher access to a specific class
async function validateTeacherClassAccess(teacherId, classId) {
  try {
    // Check if teacher exists and has access to the class
    const teacher = await Teacher.findById(teacherId).populate('classes');
    if (!teacher) {
      console.log(`[ACCESS] Teacher not found: ${teacherId}`);
      return false;
    }

    // Check if the class is in teacher's assigned classes
    const hasAccess = teacher.classes?.some(cls => cls._id.toString() === classId);
    console.log(`[ACCESS] Teacher ${teacherId} access to class ${classId}: ${hasAccess}`);
    return hasAccess;
  } catch (error) {
    console.error('Error validating teacher class access:', error);
    return false;
  }
}



const fetchStudentKnowledgeGraph = async (studentId, subject) => {
  try {
    // Define the correct model name
    const correctModelName = `CurriculumNode_${subject}`;

    // Ensure the dynamic model exists for the subject
    if (!mongoose.models[correctModelName]) {
      console.error(`Creating model for subject: ${subject}`);
      createKnowledgeGraphModel(subject);
    }

    // Find and update the student curriculum in one operation if needed
    const studentCurriculum = await StudentCurriculumModel.findOne({ studentId, 'curriculumProgress.subject': subject });
    if (!studentCurriculum) {
      console.error('Student curriculum not found');
    }

    // Check and update node models in one pass
    let needsSave = false;
    for (const progress of studentCurriculum.curriculumProgress) {
      if (progress.nodeModel !== correctModelName) {
        studentCurriculum.setNodeModelForSubject(progress, subject);
        needsSave = true;
      }
    }

    if (needsSave) {
      await studentCurriculum.save();
    }

    // Get populated data in a single query with efficient projection
    const populatedCurriculum = await StudentCurriculumModel.findOne({ studentId, 'curriculumProgress.subject': subject })
      .populate({
        path: "curriculumProgress.nodeId",
        model: correctModelName,
        select: "_id name type description parents children _parentModel _childModel"
      });

    // Filter valid progress items for the subject
    const populatedProgress = populatedCurriculum.curriculumProgress.filter(
      progress => progress.subject === subject && progress.nodeId
    );

    // Batch populate parents and children
    const parentModelMap = new Map();
    const childModelMap = new Map();

    // Prepare batch queries by grouping by model
    for (const progress of populatedProgress) {
      const node = progress.nodeId;
      if (typeof node !== 'object') continue;

      // Prepare parent queries
      if (node.parents?.length && node._parentModel) {
        if (!parentModelMap.has(node._parentModel)) {
          parentModelMap.set(node._parentModel, []);
        }
        const parentIds = node.parents.map(p => (typeof p === 'object' ? p._id : p));
        parentModelMap.get(node._parentModel).push({
          node,
          ids: parentIds
        });
      }

      // Prepare child queries
      if (node.children?.length && node._childModel) {
        if (!childModelMap.has(node._childModel)) {
          childModelMap.set(node._childModel, []);
        }
        const childIds = node.children.map(c => (typeof c === 'object' ? c._id : c));
        childModelMap.get(node._childModel).push({
          node,
          ids: childIds
        });
      }
    }

    // Execute batch parent queries
    for (const [modelName, items] of parentModelMap.entries()) {
      const parentModel = mongoose.model(modelName);
      const allParentIds = items.flatMap(item => item.ids);
      const parents = await parentModel.find({
        _id: { $in: allParentIds }
      }).select("_id name type description");

      const parentsById = new Map(parents.map(p => [p._id.toString(), p]));

      // Assign parents to their respective nodes
      for (const item of items) {
        item.node.parents = item.ids
          .map(id => parentsById.get(id?.toString()))
          .filter(Boolean);
      }
    }

    // Execute batch child queries
    for (const [modelName, items] of childModelMap.entries()) {
      const childModel = mongoose.model(modelName);
      const allChildIds = items.flatMap(item => item.ids);
      const children = await childModel.find({
        _id: { $in: allChildIds }
      }).select("_id name type description");

      const childrenById = new Map(children.map(c => [c._id.toString(), c]));

      // Assign children to their respective nodes
      for (const item of items) {
        item.node.children = item.ids
          .map(id => childrenById.get(id?.toString()))
          .filter(Boolean);
      }
    }

    return {
      studentId: studentCurriculum.studentId,
      subject,
      curriculumProgress: populatedProgress,
      totalSubjectNodes: populatedProgress.length
    };
  } catch (error) {
    console.error('Error fetching student knowledge graph:', error);
    
  }
};



const prepareStudentProfileForPrompt = async (studentProfile) => {
  const { studentId, subject, knowledgeGraph } = studentProfile;
  let graph = knowledgeGraph;
  if (!graph) {
    graph = await fetchStudentKnowledgeGraph(studentId, subject);
  }
  return {
    ...studentProfile,
    progressData: graph.curriculumProgress.map(progress => ({
      nodeName: progress.nodeId.name,
      nodeType: progress.nodeId.type,
      status: progress.status,
      proficiency: progress.proficiency,
      parents: progress.nodeId.parents?.map(parent => ({
        name: parent.name,
        type: parent.type
      })) || [],
      children: progress.nodeId.children?.map(child => ({
        name: child.name,
        type: child.type
      })) || []
    })),
    knowledgeGraph: graph // Attach for reuse
  };
};



// New chat-specific caching functions
const checkChatCache = async (studentId, subject, conversationId, messageContent) => {
  const cacheKey = LLMCache.generateCacheKey(studentId, subject, 'chat', conversationId, messageContent);

  const cacheEntry = await LLMCache.findOne({ cacheKey });

  if (cacheEntry) {
    // Increment hit count and update last accessed
    await cacheEntry.incrementHitCount();
    console.log(`[CACHE HIT] Chat response found for user ${studentId}, conversation ${conversationId}`);
  }

  return cacheEntry;
};

const updateChatCache = async (cacheKey, response, requestType = 'chat', metadata = {}) => {
  try {
    await LLMCache.findOneAndUpdate(
      { cacheKey },
      {
        cacheKey,
        response,
        requestType,
        metadata: {
          ...metadata,
          hitCount: 1
        },
        createdAt: new Date(),
        lastAccessed: new Date()
      },
      { upsert: true }
    );
    console.log(`[CACHE SAVE] Response cached with key: ${cacheKey}`);
  } catch (error) {
    console.error('Error saving cache:', error);
  }
};

// Helper function to check teacher chat cache
const checkTeacherChatCache = async (cacheKey) => {
  try {
    const cachedResponse = await LLMCache.findOne({ cacheKey });
    if (cachedResponse) {
      // Update last accessed time
      cachedResponse.lastAccessed = new Date();
      cachedResponse.metadata.hitCount = (cachedResponse.metadata.hitCount || 0) + 1;
      await cachedResponse.save();

      console.log(`[TEACHER CACHE] Cache hit for key: ${cacheKey}`);
      return cachedResponse;
    }
    return null;
  } catch (error) {
    console.error('Error checking teacher chat cache:', error);
    return null;
  }
};

// New function specifically for chatbot that returns plain text
const callGeminiAPIForChat = async (prompt, modelName = defaultGeminiModel) => {
  try {
    const model = genAI.getGenerativeModel({ model: modelName });
    const result = await model.generateContent(prompt);
    const response = await result.response;
    let text = response.text();

    // Clean up the response text
    text = text.trim();

    // Return the plain text response for chatbot
    return text;
  } catch (error) {
    console.error(`Error calling Gemini API for chat with model ${modelName}:`, error);
    throw error;
  }
};

// Enhanced chatbot response with MongoDB MCP integration
export const getChatbotResponseWithMCP = async (req, res) => {
  const startTime = Date.now();

  try {
    const { studentId, subject, message, conversationHistory = [], conversationId } = req.body;

    // Detect if this is a teacher request based on studentId pattern
    const isTeacherRequest = studentId.startsWith('teacher_');

    if (isTeacherRequest) {
      console.log(`[MCP] Processing teacher chat request for studentId: ${studentId}, subject: ${subject}`);

      // Extract teacher ID and class ID from the composite studentId
      const parts = studentId.split('_');
      if (parts.length >= 4) { // teacher_<teacherId>_class_<classId>
        const teacherId = parts[1];
        const classId = parts[3];

        console.log(`[MCP] Extracted teacherId: ${teacherId}, classId: ${classId}`);

        // Validate teacher access to the class
        const teacherHasAccess = await validateTeacherClassAccess(teacherId, classId);
        if (!teacherHasAccess) {
          return res.status(403).json({
            message: "Access denied. You don't have permission to access this class.",
            error: "Unauthorized access"
          });
        }

        // Check teacher-specific cache first
        const teacherCacheKey = `teacher_${teacherId}_${classId}_${subject}_${hashMessage(message)}`;
        const cachedTeacherResponse = await checkTeacherChatCache(teacherCacheKey);
        if (cachedTeacherResponse) {
          console.log(`[TEACHER CACHE HIT] Returning cached response for teacher ${teacherId}`);
          return res.json(cachedTeacherResponse.response);
        }

        // Generate teacher-specific enhanced prompt with multi-student context
        const enhancedPrompt = await mongoMcpService.generateTeacherEnhancedPrompt(
          teacherId,
          classId,
          subject,
          message,
          conversationHistory
        );

        console.log(`[MCP] Generated teacher enhanced prompt with multi-student context`);

        let responseText;
        try {
          responseText = await callGeminiAPIForChat(enhancedPrompt);
        } catch (apiError) {
          console.error('[MCP] Gemini API error, using teacher fallback response:', apiError.message);
          responseText = `I'm currently experiencing high demand and cannot process your request at the moment. However, I can help you with:

• Analyzing individual student performance in ${subject}
• Identifying students who need additional support
• Providing class-wide performance insights
• Suggesting differentiated instruction strategies
• Tracking student progress over time

Please try your question again in a few moments, or ask me about specific students or topics you'd like to focus on.`;
        }

        const responseTime = Date.now() - startTime;

        // Format the response for the teacher chatbot (same format as student response)
        const chatbotResponse = {
          message: responseText || "I'm here to help you analyze your students' performance and provide insights about your class.",
          studentContext: {
            knowledgeLevel: 'teacher_multi_student',
            dataSource: 'multi_student_analysis',
            responseTime: responseTime,
            classId: classId,
            teacherId: teacherId
          },
          suggestions: [],
          relatedTopics: []
        };

        // Cache the response for future use with teacher-specific key
        await updateChatCache(teacherCacheKey, chatbotResponse, 'teacher_chat', {
          teacherId,
          classId,
          subject,
          responseTime
        });

        console.log(`[MCP] Teacher chat response generated successfully in ${responseTime}ms`);
        res.json(chatbotResponse);
        return;
      } else {
        return res.status(400).json({
          message: "Invalid teacher request format",
          error: "Invalid studentId format for teacher request"
        });
      }
    }

    // Regular student request processing
    console.log(`[MCP] Processing student chat request for student: ${studentId}, subject: ${subject}`);

    // Check cache first
    const cachedResponse = await checkChatCache(studentId, subject, conversationId, message);
    if (cachedResponse) {
      console.log(`[CACHE HIT] Returning cached response for user ${studentId}`);
      return res.json(cachedResponse.response);
    }

    // Initialize MCP service if not ready
    if (!mongoMcpService.isReady()) {
      console.log('[MCP] Initializing MongoDB MCP service...');
      const initialized = await mongoMcpService.initialize();
      if (!initialized) {
        console.warn('[MCP] Failed to initialize, falling back to traditional method');
        return getChatbotResponse(req, res);
      }
    }

    // Generate enhanced prompt with direct database access
    const enhancedPrompt = await mongoMcpService.generateEnhancedPrompt(
      studentId,
      subject,
      message,
      conversationHistory
    );

    console.log(`[MCP] Generated enhanced prompt with direct DB context`);

    let responseText;
    try {
      responseText = await callGeminiAPIForChat(enhancedPrompt);
    } catch (apiError) {
      console.error('[MCP] Gemini API error, using fallback response:', apiError.message);
      responseText = "I'm currently experiencing high demand. Please try your question again in a moment, and I'll provide you with personalized insights about your learning progress.";
    }
    const responseTime = Date.now() - startTime;

    // Format the response for the chatbot
    const chatbotResponse = {
      message: responseText || "I understand you're asking about this topic. Let me help you explore it further!",
      studentContext: {
        knowledgeLevel: 'personalized', // MCP provides complete context
        dataSource: 'direct_database_access',
        responseTime: responseTime
      },
      suggestions: [],
      relatedTopics: []
    };

    // Cache the response with metadata
    const metadata = {
      responseTime,
      modelUsed: defaultGeminiModel,
      promptTokens: enhancedPrompt.length / 4, // Rough estimate
      responseTokens: responseText.length / 4, // Rough estimate
      mcpEnabled: true
    };

    await updateChatCache(studentId, subject, conversationId, message, chatbotResponse, metadata);

    console.log(`[MCP] Enhanced chatbot response completed in ${responseTime}ms`);
    res.json(chatbotResponse);
  } catch (error) {
    console.error('Error in getChatbotResponseWithMCP:', error);
    console.log('[MCP] Falling back to traditional chatbot response');
    // Fallback to original method if MCP fails
    return getChatbotResponse(req, res);
  }
};

export const getChatbotResponse = async (req, res) => {
  const startTime = Date.now();

  try {
    const { studentId, subject, message, conversationHistory = [], conversationId } = req.body;

    // Check if this is a teacher request (composite ID format)
    if (studentId && studentId.startsWith('teacher_')) {
      console.log(`[FALLBACK] Teacher request detected in fallback function: ${studentId}`);

      // Extract teacher ID and class ID from the composite studentId
      const parts = studentId.split('_');
      if (parts.length >= 4) { // teacher_<teacherId>_class_<classId>
        const teacherId = parts[1];
        const classId = parts[3];

        // Validate teacher access to the class
        const teacherHasAccess = await validateTeacherClassAccess(teacherId, classId);
        if (!teacherHasAccess) {
          return res.status(403).json({
            message: "Access denied. You don't have permission to access this class.",
            error: "Unauthorized access"
          });
        }

        // Provide a simplified teacher response when MCP fails
        const teacherFallbackResponse = {
          message: `I'm currently experiencing high demand and cannot access detailed class analytics at the moment. However, I can help you with:

• General teaching strategies for ${subject}
• Assessment planning and feedback techniques
• Student engagement methods
• Curriculum planning assistance

Please try your question again in a moment for detailed class insights.`,
          teacherContext: {
            classId,
            subject,
            dataSource: 'fallback_mode',
            responseTime: Date.now() - startTime
          },
          suggestions: [
            "How can I improve student engagement?",
            "What are effective assessment strategies?",
            "How do I differentiate instruction?"
          ],
          relatedTopics: []
        };

        return res.json(teacherFallbackResponse);
      } else {
        return res.status(400).json({
          message: "Invalid teacher request format",
          error: "Invalid studentId format for teacher request"
        });
      }
    }

    // Regular student request processing
    console.log(`[FALLBACK] Processing student request: ${studentId}`);

    // Check cache first
    const cachedResponse = await checkChatCache(studentId, subject, conversationId, message);
    if (cachedResponse) {
      console.log(`[CACHE HIT] Returning cached response for user ${studentId}`);
      return res.json(cachedResponse.response);
    }

    // Prepare student profile with knowledge graph data
    const studentProfile = await prepareStudentProfileForPrompt({ studentId, subject });

    // Create a conversational prompt that includes context
    const conversationContext = conversationHistory.length > 0
      ? `Previous conversation:\n${conversationHistory.map(msg => `${msg.type}: ${msg.content}`).join('\n')}\n\n`
      : '';

    // Optimized prompt for concise responses (70-80% shorter)
    const prompt = `You are AegisAI, a personalized learning assistant for ${subject}. Provide CONCISE, actionable responses (max 150 words).

${conversationContext}Student Context:
- Proficiency: ${studentProfile.overallProficiency || 'intermediate'}
- Recent Topics: ${studentProfile.recentTopics?.slice(0, 3).join(', ') || 'None'}
- Strengths: ${studentProfile.strengths?.slice(0, 2).join(', ') || 'None'}
- Weaknesses: ${studentProfile.weaknesses?.slice(0, 2).join(', ') || 'None'}

Question: ${message}

Respond with:
1. Direct answer (2-3 sentences max)
2. One specific next step
3. One related concept (if relevant)

Keep it conversational but brief. Focus on actionable insights.`;

    let responseText;
    try {
      responseText = await callGeminiAPIForChat(prompt);
    } catch (apiError) {
      console.error('[CHAT] Gemini API error, using fallback response:', apiError.message);
      responseText = "I'm currently experiencing high demand. Please try your question again in a moment, and I'll provide you with personalized insights about your learning progress.";
    }
    const responseTime = Date.now() - startTime;

    // Format the response for the chatbot
    const chatbotResponse = {
      message: responseText || "I understand you're asking about this topic. Let me help you explore it further!",
      studentContext: {
        knowledgeLevel: studentProfile.overallProficiency || 'intermediate',
        recentTopics: studentProfile.recentTopics?.slice(0, 3) || [],
        strengths: studentProfile.strengths?.slice(0, 2) || [],
        weaknesses: studentProfile.weaknesses?.slice(0, 2) || []
      },
      suggestions: [],
      relatedTopics: []
    };

    // Cache the response with metadata
    const metadata = {
      responseTime,
      modelUsed: defaultGeminiModel,
      promptTokens: prompt.length / 4, // Rough estimate
      responseTokens: responseText.length / 4 // Rough estimate
    };

    await updateChatCache(studentId, subject, conversationId, message, chatbotResponse, metadata);

    // Save conversation to database if conversationId is provided
    if (conversationId) {
      try {
        // Create the updated messages array with the new user message and assistant response
        const userMessage = {
          id: `user-${Date.now()}`,
          type: 'user',
          content: message,
          timestamp: new Date()
        };

        const assistantMessage = {
          id: `assistant-${Date.now()}`,
          type: 'assistant',
          content: responseText,
          timestamp: new Date()
        };

        // Combine conversation history with new messages
        const allMessages = [
          ...conversationHistory.map(msg => ({
            id: msg.id || `${msg.type}-${Date.now()}-${Math.random()}`,
            type: msg.type,
            content: msg.content,
            timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date()
          })),
          userMessage,
          assistantMessage
        ];

        // Try to update existing conversation or create new one
        let conversation = await ChatConversation.findOne({
          id: conversationId,
          userId: studentId
        });

        if (conversation) {
          conversation.messages = allMessages;
          conversation.updatedAt = new Date();
          await conversation.save();
        } else {
          // Create new conversation
          conversation = new ChatConversation({
            id: conversationId,
            userId: studentId,
            title: `${subject} Chat`,
            subject,
            messages: allMessages
          });
          await conversation.save();
        }
      } catch (dbError) {
        console.error('Error saving conversation to database:', dbError);
        // Don't fail the response if database save fails
      }
    }

    res.json(chatbotResponse);
  } catch (error) {
    console.error('Error in getChatbotResponse controller:', error);
    res.status(500).json({
      message: "I'm having trouble processing your request right now. Please try again in a moment.",
      error: error.message
    });
  }
};

// Note: Teacher-specific functions have been removed as we now use the unified
// getChatbotResponseWithMCP function that automatically detects teacher vs student requests



export const teachingAssistantService = {
  getChatbotResponse,
  getChatbotResponseWithMCP,
};