import Class from '../models/Class.js';
import Student from '../models/Student.js';
import { getCollectionNameClass, getQuestion } from '../services/testService.js';

/**
 * Updates class analytics based on student test performance
 * @param {string} classId - The class ID
 * @param {Object} testData - Test response data
 * @param {string} studentId - The student ID
 * @param {Object} subskillProficiencyMap - Map of subskill to proficiency scores
 * @returns {Promise<Object>} Success/failure response
 */
export const updateClassAnalytics = async (classId, testData, studentId, subskillProficiencyMap) => {
    // Input validation
    if (!classId || !testData || !studentId || !subskillProficiencyMap) {
        console.error('Missing required parameters');
    }

    const { responses = [], testId, totalScore = 0, totalTimeTaken = 0 } = testData;

    if (!testId) {
        console.error('Test ID is required');
    }

    let session;
    try {
        // Use MongoDB session for transaction consistency
        session = await Class.startSession();
        
        return await session.withTransaction(async () => {
            // Step 1: Get and validate class data with populated students
            const classRecord = await Class.findById(classId)
                .populate('students')
                .session(session);
                
            if (!classRecord) {
                console.error(`Class not found with ID: ${classId}`);
            }

            // Step 2: Initialize analytics structure if needed
            await initializeClassAnalytics(classRecord, session);

            // Step 3: Validate student and get subject data
            const student = await Student.findById(studentId).session(session);
            if (!student) {
                console.error(`Student not found with ID: ${studentId}`);
            }

            const subjectData = student.subjects?.find(s => 
                s.subjectClassId?.toString() === classId.toString()
            );
            
            if (!subjectData) {
                console.error('Student is not enrolled in this class subject');
            }

            // Step 4: Get collection name for questions
            const collectionName = await getCollectionNameClass(classId, classRecord.subject);
            if (!collectionName) {
                console.error('Could not determine collection name for class');
            }

            // Step 5: Fetch question data first (needed for other calculations)
            const questionData = await fetchQuestionData(responses, collectionName);

            // Step 6: Calculate all analytics components in parallel
            const [
                proficiencyData,
                testMetrics,
                topicInsights
            ] = await Promise.all([
                calculateClassProficiencyData(classId, classRecord.students, session),
                calculateTestMetrics(testId, classRecord.students, totalScore, totalTimeTaken, session),
                calculateTopicInsights(responses, subskillProficiencyMap, questionData)
            ]);

            // Step 7: Determine student grouping
            const studentGroup = determineStudentGroup(
                studentId, 
                subjectData,
                responses,
                questionData
            );

            // Step 8: Build update operations
            const updateOps = buildUpdateOperations({
                proficiencyData,
                testMetrics,
                topicInsights,
                studentGroup,
                testId,
                questionData
            });

            // Step 9: Execute updates in sequence to avoid MongoDB conflicts
            // First, remove student from all groups
            await Class.findByIdAndUpdate(
                classId,
                {
                    $pull: {
                        'analytics.studentGroups.needsIntervention': { studentId: studentGroup.data.studentId },
                        'analytics.studentGroups.onTrack': { studentId: studentGroup.data.studentId },
                        'analytics.studentGroups.accelerated': { studentId: studentGroup.data.studentId }
                    }
                },
                { session }
            );

            // Then, execute the main update with the student group addition
            const updatedClass = await Class.findByIdAndUpdate(
                classId, 
                updateOps, 
                { session, new: true }
            );

            if (!updatedClass) {
                console.error('Failed to update class analytics');
            }

            return {
                success: true,
                message: 'Class analytics updated successfully',
                data: {
                    averageProficiency: proficiencyData.averageProficiency,
                    testCompletionRate: testMetrics.completionRate,
                    topicCount: topicInsights.length,
                    studentGroup: studentGroup.category
                }
            };
        });

    } catch (error) {
        console.error('Error updating class analytics:', {
            error: error.message,
            classId,
            studentId,
            testId: testData.testId,
            stack: error.stack
        });
        
        // console.error(`Failed to update class analytics: ${error.message}`);
    } finally {
        // Ensure session is properly closed
        if (session) {
            await session.endSession();
        }
    }
};

/**
 * Initialize analytics structure for a class if not present
 */
async function initializeClassAnalytics(classRecord, session) {
    if (classRecord.analytics) return;

    const defaultAnalytics = {
        classPerformance: {
            averageProficiency: 0,
            proficiencyDistribution: {
                advanced: [],
                proficient: [],
                developing: [],
                beginning: []
            },
            testCompletionRate: 0,
            averageTestScore: 0,
            proficiencyTrend: []
        },
        topicInsights: [],
        studentGroups: {
            needsIntervention: [],
            onTrack: [],
            accelerated: []
        },
        testAnalytics: [],
        curriculumProgress: { 
            topicsCovered: [] 
        },
        lastUpdated: new Date()
    };

    await Class.findByIdAndUpdate(
        classRecord._id,
        { $set: { analytics: defaultAnalytics } },
        { session }
    );
}

/**
 * Calculate class-wide proficiency data
 */
async function calculateClassProficiencyData(classId, studentIds, session) {
    // Ensure we have valid student IDs array
    if (!Array.isArray(studentIds) || studentIds.length === 0) {
        return {
            averageProficiency: 0,
            proficiencyDistribution: {
                advanced: [],
                proficient: [],
                developing: [],
                beginning: []
            },
            topicProficiencies: new Map(),
            validStudentCount: 0
        };
    }

    const studentsWithSubjects = await Student.find({
        _id: { $in: studentIds },
        'subjects.subjectClassId': classId
    }).session(session);

    const proficiencyDistribution = {
        advanced: [],
        proficient: [],
        developing: [],
        beginning: []
    };

    const topicProficiencies = new Map();
    let totalProficiency = 0;
    let validStudentCount = 0;

    for (const student of studentsWithSubjects) {
        const relevantSubject = student.subjects?.find(s =>
            s.subjectClassId?.toString() === classId.toString()
        );

        if (!relevantSubject?.analytics?.performanceMetrics) continue;

        const proficiency = relevantSubject.analytics.performanceMetrics.currentProficiency;
        if (typeof proficiency !== 'number' || proficiency < 0 || proficiency > 1) continue;

        totalProficiency += proficiency;
        validStudentCount++;

        // Categorize student
        const studentIdStr = student._id.toString();
        if (proficiency >= 0.85) proficiencyDistribution.advanced.push(studentIdStr);
        else if (proficiency >= 0.7) proficiencyDistribution.proficient.push(studentIdStr);
        else if (proficiency >= 0.5) proficiencyDistribution.developing.push(studentIdStr);
        else proficiencyDistribution.beginning.push(studentIdStr);

        // Collect topic proficiencies
        const topicProfs = relevantSubject.analytics.topicProficiencies;
        if (topicProfs && typeof topicProfs === 'object') {
            for (const [topicId, prof] of Object.entries(topicProfs)) {
                if (typeof prof === 'number' && prof >= 0 && prof <= 1) {
                    if (!topicProficiencies.has(topicId)) {
                        topicProficiencies.set(topicId, { sum: 0, count: 0 });
                    }
                    const data = topicProficiencies.get(topicId);
                    data.sum += prof;
                    data.count++;
                }
            }
        }
    }

    return {
        averageProficiency: validStudentCount > 0 ? totalProficiency / validStudentCount : 0,
        proficiencyDistribution,
        topicProficiencies,
        validStudentCount
    };
}

/**
 * Calculate test completion and performance metrics
 */
async function calculateTestMetrics(testId, studentIds, totalScore, totalTimeTaken, session) {
    // Ensure we have valid student IDs array
    if (!Array.isArray(studentIds)) {
        return {
            completionRate: 0,
            averageScore: totalScore || 0,
            averageTime: totalTimeTaken || 0,
            participantCount: 0
        };
    }

    const totalStudents = studentIds.length;
    
    if (totalStudents === 0) {
        return {
            completionRate: 0,
            averageScore: totalScore || 0,
            averageTime: totalTimeTaken || 0,
            participantCount: 0
        };
    }

    // Count students who took this test
    const participantCount = await Student.countDocuments({
        _id: { $in: studentIds },
        'subjects.attemptedTests.testId': testId
    }).session(session);

    // Get aggregated test statistics
    const testStats = await Student.aggregate([
        { 
            $match: { 
                _id: { $in: studentIds },
                'subjects.attemptedTests.testId': testId 
            } 
        },
        { $unwind: '$subjects' },
        { $unwind: '$subjects.attemptedTests' },
        { $match: { 'subjects.attemptedTests.testId': testId } },
        {
            $group: {
                _id: null,
                avgScore: { $avg: '$subjects.attemptedTests.totalScore' },
                avgTime: { $avg: '$subjects.attemptedTests.totalTimeTaken' },
                count: { $sum: 1 }
            }
        }
    ]).session(session);

    const stats = testStats[0] || {
        avgScore: totalScore || 0,
        avgTime: totalTimeTaken || 0,
        count: participantCount > 0 ? participantCount : 1
    };

    return {
        completionRate: (participantCount / totalStudents) * 100,
        averageScore: stats.avgScore || 0,
        averageTime: stats.avgTime || 0,
        participantCount: stats.count
    };
}

/**
 * Fetch question data for responses
 */
async function fetchQuestionData(responses, collectionName) {
    const questionData = new Map();
    
    if (!Array.isArray(responses) || responses.length === 0) {
        return questionData;
    }
    
    // Process questions in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < responses.length; i += batchSize) {
        const batch = responses.slice(i, i + batchSize);
        const promises = batch.map(async (response) => {
            try {
                if (!response.questionId) {
                    console.warn('Response missing questionId:', response);
                    return null;
                }

                const question = await getQuestion(response.questionId, collectionName);
                if (question && question.subtopic) {
                    const subtopics = question.subtopic
                        .split(',')
                        .map(s => s.trim())
                        .filter(s => s.length > 0);
                    
                    return {
                        questionId: response.questionId,
                        subtopics,
                        response: response
                    };
                }
            } catch (error) {
                console.warn(`Failed to fetch question ${response.questionId}:`, error.message);
            }
            return null;
        });
        
        const results = await Promise.all(promises);
        results.forEach(result => {
            if (result) {
                questionData.set(result.questionId, result);
            }
        });
    }
    
    return questionData;
}

/**
 * Calculate topic insights based on responses and proficiency data
 */
async function calculateTopicInsights(responses, subskillProficiencyMap, questionData) {
    const topicInsights = [];

    if (!subskillProficiencyMap || typeof subskillProficiencyMap !== 'object') {
        return topicInsights;
    }

    for (const [subskill, proficiency] of Object.entries(subskillProficiencyMap)) {
        if (typeof proficiency !== 'number') continue;

        // Find responses related to this subskill
        const relevantResponses = [];
        for (const [questionId, qData] of questionData) {
            if (qData.subtopics && qData.subtopics.includes(subskill)) {
                relevantResponses.push(qData.response);
            }
        }

        const responseCount = relevantResponses.length;
        
        // Calculate metrics only if we have responses
        let struggleRate = 0;
        let masteryRate = 0;
        let timeSpentAverage = 0;

        if (responseCount > 0) {
            const correctCount = relevantResponses.filter(r => r.response === true).length;
            const incorrectCount = responseCount - correctCount;
            
            struggleRate = incorrectCount / responseCount;
            masteryRate = correctCount / responseCount;
            
            const totalTime = relevantResponses.reduce((sum, r) => 
                sum + (typeof r.timeSpent === 'number' ? r.timeSpent : 0), 0
            );
            timeSpentAverage = responseCount > 0 ? totalTime / responseCount : 0;
        }

        topicInsights.push({
            topicId: subskill,
            averageProficiency: Math.max(0, Math.min(1, proficiency)), // Clamp between 0-1
            struggleRate,
            masteryRate,
            timeSpentAverage,
            responseCount
        });
    }

    return topicInsights;
}

/**
 * Determine which group a student belongs to
 */
function determineStudentGroup(studentId, subjectData, responses, questionData) {
    const proficiency = subjectData.analytics?.performanceMetrics?.currentProficiency || 0;
    
    // Ensure responses is an array
    const validResponses = Array.isArray(responses) ? responses : [];
    
    const incorrectQuestions = validResponses
        .filter(r => r.response === false) // Use strict equality for boolean
        .map(r => r.questionId)
        .filter(Boolean); // Remove null/undefined questionIds
    
    const correctQuestions = validResponses
        .filter(r => r.response === true) // Use strict equality for boolean
        .map(r => r.questionId)
        .filter(Boolean); // Remove null/undefined questionIds

    const weaknesses = subjectData.analytics?.knowledgeAnalysis?.weaknesses || [];
    const strengths = subjectData.analytics?.knowledgeAnalysis?.strengths || [];

    if (proficiency < 0.5) {
        return {
            category: 'needsIntervention',
            data: {
                studentId,
                keyGaps: incorrectQuestions.slice(0, 5), // Limit to prevent bloat
                bloomTaxonomyWeakness: weaknesses
                    .slice(0, 3)
                    .map(w => w.subtopicId)
                    .filter(Boolean)
            }
        };
    } else if (proficiency > 0.85) {
        return {
            category: 'accelerated',
            data: {
                studentId,
                readyForTopics: correctQuestions.slice(0, 5), // Limit to prevent bloat
                bloomTaxonomyStrengths: strengths
                    .slice(0, 3)
                    .map(s => s.subtopicId)
                    .filter(Boolean)
            }
        };
    } else {
        return {
            category: 'onTrack',
            data: { studentId } // Consistent object structure
        };
    }
}

/**
 * Build MongoDB update operations
 */
function buildUpdateOperations({
    proficiencyData,
    testMetrics,
    topicInsights,
    studentGroup,
    testId,
    questionData
}) {
    const now = new Date();
    
    // Calculate difficulty and question effectiveness
    const questionDataArray = Array.from(questionData.values());
    const totalResponses = questionDataArray.length;
    const incorrectResponses = questionDataArray
        .filter(q => q.response.response === false).length; // Use strict equality
    
    const difficultyRating = totalResponses > 0 ? incorrectResponses / totalResponses : 0;
    
    const questionEffectiveness = questionDataArray.map(qData => ({
        questionId: qData.questionId,
        discriminationIndex: 0, // Would need historical data for proper calculation
        difficultyIndex: qData.response.response === true ? 1 : 0 // Use strict equality
    }));

    // Get existing proficiency trend or initialize empty array
    const proficiencyTrendEntry = {
        date: now,
        averageProficiency: proficiencyData.averageProficiency
    };

    const updateOps = {
        $set: {
            'analytics.classPerformance': {
                averageProficiency: proficiencyData.averageProficiency,
                proficiencyDistribution: proficiencyData.proficiencyDistribution,
                testCompletionRate: testMetrics.completionRate,
                averageTestScore: testMetrics.averageScore,
                proficiencyTrend: [proficiencyTrendEntry] // Start fresh or append logic needed
            },
            'analytics.topicInsights': topicInsights,
            'analytics.curriculumProgress': {
                topicsCovered: [] // Simplified - can be enhanced based on requirements
            },
            'analytics.lastUpdated': now
        },
        $push: {
            'analytics.testAnalytics': {
                $each: [{
                    testId,
                    participationRate: testMetrics.completionRate,
                    averageScore: testMetrics.averageScore,
                    averageCompletionTime: testMetrics.averageTime,
                    difficultyRating,
                    questionEffectiveness,
                    participantCount: testMetrics.participantCount,
                    dateAnalyzed: now
                }],
                $slice: -50 // Keep only last 50 test analytics to prevent unbounded growth
            }
        }
    };

    // Add student to appropriate group - use $addToSet to prevent duplicates
    const groupPath = `analytics.studentGroups.${studentGroup.category}`;
    updateOps.$addToSet = {
        [groupPath]: studentGroup.data
    };

    return updateOps;
}