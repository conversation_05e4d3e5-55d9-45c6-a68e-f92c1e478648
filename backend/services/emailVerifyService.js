import nodemailer from 'nodemailer';
import jwt from 'jsonwebtoken';
import Student from '../models/Student.js';
import Teacher from '../models/Teacher.js';
import { RateLimiterMemory } from 'rate-limiter-flexible';

const emailSendLimiter = new RateLimiterMemory({
    points: 5,
    duration: 3600,
    blockDuration: 7200,
});

const emailVerifyLimiter = new RateLimiterMemory({
    points: 10,
    duration: 3600,
    blockDuration: 3600,
});

const transporter = nodemailer.createTransport({
    host: "smtp.gmail.com",
    port: 587,
    secure: false,
    auth: {
        user: process.env.EMAIL_USERNAME || "<EMAIL>",
        pass: process.env.EMAIL_PASSWORD || "egeaaomtiqgothho"
    },
});

const generateVerificationToken = (user) => {
    return jwt.sign(
        { userId: user._id, userType: user.role },
        "eFWU3gMvhQ56Bx8HHBMp",
        { expiresIn: '24h' }
    );
};

const sendVerificationEmail = async (user, origin) => {
    try {
        await emailSendLimiter.consume(user.email);

        const token = generateVerificationToken(user);
        const verificationUrl = `${origin}/verify-email?token=${token}`;

        const message = {
            from: process.env.EMAIL_FROM || "AegisScholar <<EMAIL>>",
            to: user.email,
            subject: 'Verify Your AegisScholar Account',
            html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #333; font-size: 24px; margin-bottom: 20px;">Welcome to AegisScholar</h2>
              <div style="background-color: #f9f9f9; border-radius: 10px; padding: 25px; margin-bottom: 20px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
                <p style="margin-top: 0; color: #333; font-size: 16px;">Hello ${user.username || ''},</p>
                <p style="color: #333; font-size: 16px;">Thank you for creating an account with AegisScholar. To complete your registration, please verify your email address by clicking the button below:</p>
                <div style="text-align: center; margin: 30px 0;">
                  <a href="${verificationUrl}" 
                     style="background-color: #4285f4; color: white; padding: 12px 30px; text-decoration: none; 
                     border-radius: 5px; font-weight: bold; display: inline-block; font-size: 16px;
                     box-shadow: 0 2px 5px rgba(0,0,0,0.1);">Verify Email Address</a>
                </div>
                <p style="color: #333; font-size: 16px;">This verification link will expire in 24 hours.</p>
                <p style="color: #333; font-size: 16px; margin-bottom: 0;">If you did not create this account, please ignore this email.</p>
              </div>
              <div style="font-size: 14px; color: #777; text-align: center; padding-top: 15px; border-top: 1px solid #eee;">
                <p style="margin: 5px 0;">© ${new Date().getFullYear()} AegisScholar. All rights reserved.</p>
                <p style="margin: 5px 0;">
                  <a href="https://aegisscholar.com/terms" style="color: #4285f4; text-decoration: none; margin: 0 10px;">Terms of Service</a>
                  <a href="https://aegisscholar.com/privacy" style="color: #4285f4; text-decoration: none; margin: 0 10px;">Privacy Policy</a>
                </p>
              </div>
            </div>
            `
        };
        await transporter.sendMail(message);

        return { success: true, token };
    } catch (error) {
        if (error instanceof Error) {
            console.error('Email send error:', error.message);
            
        } else {
            const timeLeft = Math.floor(error.msBeforeNext / 1000) || 1;
            console.error(`Too many requests. Please try again after ${timeLeft} seconds.`);
        }
    }
};

const verifyEmail = async (token) => {
    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET || "eFWU3gMvhQ56Bx8HHBMp");

        await emailVerifyLimiter.consume(decoded.userId);

        let user;
        if (decoded.userType === 'Student') {
            user = await Student.findById(decoded.userId);
        } else if (decoded.userType === 'Teacher') {
            user = await Teacher.findById(decoded.userId);
        }

        if (!user) {
            console.error('User not found');
        }

        if (user.isEmailVerified === true) {
            return { message: 'Email already verified' };
        }

        user.isEmailVerified = true;
        await user.save();

        return { message: 'Email verified successfully' };
    } catch (error) {
        console.error("Verification error:", error);

        if (error.name === 'TokenExpiredError') {
            console.error('Verification link expired');
        } else if (error instanceof Error) {
            console.error('Verification failed:', error.message);
            
        } else {
            const timeLeft = Math.floor(error.msBeforeNext / 1000) || 1;
            console.error(`Too many verification attempts. Please try again after ${timeLeft} seconds.`);
        }
    }
};

export { sendVerificationEmail, verifyEmail };