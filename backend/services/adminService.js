import mongoose from 'mongoose';
import School from '../models/School.js'
import crypto from 'crypto';
import Class from '../models/Class.js';
import Teacher from '../models/Teacher.js';
import Student from '../models/Student.js';
import { generateUniqueClassCode } from '../services/classServices.js';
import StudentCurriculumService from '../services/studentCurriculumService.js';

export const createSchool = async (schoolName, schoolAddress, session = null) => {
    try {
        if (!schoolName || !schoolAddress) {
            console.error('School name and location are required');
        }
        const fullAddress = schoolAddress.split(',').map(s => s.trim());
        if (fullAddress.length < 4) {
            console.error('Address format is invalid');
        }
        const phone = parseInt(fullAddress.pop(), 10);
        const pincode = parseInt(fullAddress.pop(), 10);
        const state = fullAddress.pop();
        const city = fullAddress.pop();
        const address = fullAddress.join(', ');

        const school = await School.findOne({ name: schoolName, pincode, phone }).session(session);
        if (school) {
            console.error('School already exists:', school);
            return school;
        }

        const schoolCode = generateSchoolCode(schoolName, pincode, phone);
        const newSchool = await School.create([
            {
                schoolCode,
                name: schoolName,
                address,
                city,
                state,
                pincode,
                phone,
                students: [],
                classes: [],
                teachers: []
            }
        ], { session });

        console.error('New school created:', newSchool[0]);
        return newSchool[0];
    } catch (error) {
        console.error('Error creating school:', error);
        
    }
}

export const createClass = async (schoolCode, className, subject, classStd, teacherId, session = null) => {
    try {
        if (!schoolCode || !className) {
            console.error('School code and class name are required');
        }
        const school = await School.findOne({ schoolCode }).session(session);
        if (!school) {
            console.error('School not found');
        }

        // If school.classes is array of ObjectIds, need to populate to check name
        let existingClass = null;
        if (school.classes && school.classes.length > 0) {
            await school.populate({ path: 'classes', select: 'name', options: { session } });
            existingClass = school.classes.find(c => c.name === className);
        }
        if (existingClass) {
            console.error('Class already exists:', existingClass);
            return existingClass;
        }

        const classCode = await generateUniqueClassCode();

        const newClass = await Class.create([
            {
                classStd,
                classCode,
                schoolCode,
                teacherId,
                subject,
                className,
            }
        ], { session });

        const teacher = await Teacher.findById(teacherId).session(session);
        if (!teacher) {
            console.error('Teacher not found');
        }
        teacher.classes.push(newClass[0]._id);
        await teacher.save({ session });

        school.classes.push(newClass[0]._id);
        await school.save({ session });
        console.error('New class created:', newClass[0]);
        return newClass[0];
    } catch (error) {
        console.error('Error creating class:', error);
        
    }
}

export const createStudent = async (firstName, lastName, admissionNumber, classCode, schoolCode, session = null) => {
    try {
        if (!firstName || !lastName || !admissionNumber || !classCode || !schoolCode) {
            console.error('First name, last name, admission number, class code, and school code are required');
        }
        const existingStudent = await Student.findOne({ admissionNumber }).session(session);
        if (existingStudent) {
            // Return credentials for existing student
            return {
                username: existingStudent.username,
                firstName: existingStudent.firstName,
                lastName: existingStudent.lastName,
                admissionNumber: existingStudent.admissionNumber,
                password: '[already set]',
            };
        }

        const password = admissionNumber.replace(/[^a-zA-Z0-9]/g, '');
        const username = `${firstName.toLowerCase()}.${lastName.toLowerCase()}`.replace(/[^a-zA-Z0-9.]/g, '');

        const newStudent = await Student.create([
            {
            username: username,
            firstName,
            lastName,
            admissionNumber,
            password,
            isEmailVerified: true,
            schoolCode
            }
        ], { session });

        const school = await School.findOne({ schoolCode }).session(session);
        if (!school) {
            await Student.findByIdAndDelete(newStudent[0]._id).session(session);
            console.error('Invalid school');
        }
        school.students.push(newStudent[0]._id);
        await school.save({ session });

        const classObj = await Class.findOne({ classCode }).session(session);
        if (!classObj) {
            console.error('Invalid class');
        }
        const usernameExists = await Student.findOne({ username: username, _id: { $in: classObj.students } }).session(session);
        if (usernameExists) {
            console.error('Student username already exists in this class');
        }
        classObj.students.push(newStudent[0]._id);
        await classObj.save({ session });
        newStudent[0].classes.push(classObj._id);
        await newStudent[0].save({ session });
        
        // Initialize curriculum for the new student and add subject
        const studentKnowledgeGraph = await StudentCurriculumService.initializeStudentCurriculum(
            newStudent[0]._id,
            `Class ${classObj.classStd}`,
            classObj.subject
        );
        const newSubject = {
            subjectClassId: classObj._id,
            subjectName: classObj.subject,
            overallProficiency: 0.5,
            knowledgeGraphId: studentKnowledgeGraph._id,
            testHistory: []
        };
        newStudent[0].subjects.push(newSubject);
        await newStudent[0].save({ session });
        
        return {
            username: newStudent[0].username,
            firstName: newStudent[0].firstName,
            lastName: newStudent[0].lastName,
            admissionNumber: newStudent[0].admissionNumber,
            password,
        };
    } catch (error) {
        console.error('Error creating student:', error);
        
    }
}

export const createTeacher = async (teacherName, teacherEmail, schoolCode, session = null) => {
    try {
        if (!teacherName || !schoolCode) {
            console.error('Teacher name and school code are required');
        }
        if (teacherEmail) {
            const emailExists = await Teacher.findOne({ email: teacherEmail }).session(session);
            if (emailExists) {
                console.error('Teacher email already exists');
            }
        }
        const existingTeacher = await Teacher.findOne({ name: teacherName, schoolCode }).session(session);
        if (existingTeacher) {
            // Return credentials for existing teacher
            return {
                username: existingTeacher.username,
                email: existingTeacher.email || '',
                password: '[already set]',
            };
        }

        const newPassword = createUniqueTeacherPassword(teacherName, schoolCode);
        const newTeacher = await Teacher.create([
            {
                username: teacherName.toLowerCase(),
                email: teacherEmail,
                password: newPassword,
                schoolCode,
                isEmailVerified: true,
            }
        ], { session });

        const school = await School.findOne({ schoolCode }).session(session);
        if (!school) {
            await Teacher.findByIdAndDelete(newTeacher[0]._id).session(session);
            console.error('Invalid school');
        }
        school.teachers.push(newTeacher[0]._id);
        await school.save({ session });

        console.error('New teacher created:', newTeacher[0]);
        return {
            id: newTeacher[0]._id,
            username: newTeacher[0].username,
            email: newTeacher[0].email || '',
            password: newPassword,
        };
    } catch (error) {
        console.error('Error creating teacher:', error);
        
    }
}

function createUniqueTeacherPassword(teacherName, schoolCode) {
    // Normalize input by trimming and converting to uppercase for consistency
    const normalizedName = String(teacherName).trim().toUpperCase();
    const normalizedSchoolId = String(schoolCode).trim();
    // Combine the inputs into a single string using a delimiter
    const combinedString = `${normalizedName}-${normalizedSchoolId}`;
    // Create a hash of the combined string
    const hash = crypto.createHash('sha256')
        .update(combinedString)
        .digest('hex');
    // Optionally, shorten the hash to a manageable length (e.g., 8 characters)
    const password = hash.substring(0, 8);
    console.error('Generated teacher password:', password);
    return password;
}

function generateSchoolCode(name, pincode, phoneNumber) {
    // Normalize input by trimming and converting to uppercase for consistency
    const normalizedName = String(name).trim().toUpperCase();
    const normalizedPincode = String(pincode).trim();
    const normalizedPhone = String(phoneNumber).trim();

    // Combine the inputs into a single string using a delimiter
    const combinedString = `${normalizedName}-${normalizedPincode}-${normalizedPhone}`;

    // Create a hash of the combined string
    const hash = crypto.createHash('sha256')
        .update(combinedString)
        .digest('hex');

    // Optionally, shorten the hash to a manageable length (e.g., 8 characters)
    const schoolCode = hash.substring(0, 8).toUpperCase();
    return schoolCode;
}