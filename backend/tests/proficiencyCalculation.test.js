import { describe, it, expect, beforeEach } from 'vitest';
import { calculateOverallProficiencyFromCurriculum } from '../services/proficiencyCalculationService.js';

describe('Proficiency Calculation Service', () => {
    let mockCurriculumProgress;

    beforeEach(() => {
        mockCurriculumProgress = [
            {
                nodeId: {
                    _id: '1',
                    name: 'Chapter 1',
                    type: 'Chapter',
                    order: 0
                },
                proficiency: 80,
                status: 'In Progress'
            },
            {
                nodeId: {
                    _id: '2',
                    name: 'Subtopic 1.1',
                    type: 'Subtopic',
                    order: 0
                },
                proficiency: 90,
                status: 'Completed'
            },
            {
                nodeId: {
                    _id: '3',
                    name: 'Subtopic 1.2',
                    type: 'Subtopic',
                    order: 1
                },
                proficiency: 70,
                status: 'In Progress'
            }
        ];
    });

    describe('calculateOverallProficiencyFromCurriculum', () => {
        it('should calculate equal weighted average correctly', () => {
            const result = calculateOverallProficiencyFromCurriculum(mockCurriculumProgress, {
                weightingStrategy: 'equal'
            });
            
            // (80 + 90 + 70) / 3 = 80
            expect(result).toBe(80);
        });

        it('should calculate node type weighted average correctly', () => {
            const result = calculateOverallProficiencyFromCurriculum(mockCurriculumProgress, {
                weightingStrategy: 'nodeType',
                topicWeight: 1.2,
                subtopicWeight: 1.0
            });
            
            // (80 * 1.2 + 90 * 1.0 + 70 * 1.0) / (1.2 + 1.0 + 1.0) = 256 / 3.2 = 80
            expect(result).toBe(80);
        });

        it('should handle empty curriculum progress', () => {
            const result = calculateOverallProficiencyFromCurriculum([]);
            expect(result).toBe(0);
        });

        it('should handle null/undefined input', () => {
            expect(calculateOverallProficiencyFromCurriculum(null)).toBe(0);
            expect(calculateOverallProficiencyFromCurriculum(undefined)).toBe(0);
        });

        it('should filter out invalid progress items', () => {
            const invalidProgress = [
                {
                    nodeId: {
                        _id: '1',
                        name: 'Valid Chapter',
                        type: 'Chapter'
                    },
                    proficiency: 80,
                    status: 'In Progress'
                },
                {
                    nodeId: null, // Invalid
                    proficiency: 90,
                    status: 'Completed'
                },
                {
                    nodeId: {
                        _id: '3',
                        name: 'Valid Subtopic',
                        type: 'Subtopic'
                    },
                    proficiency: 'invalid', // Invalid proficiency
                    status: 'In Progress'
                }
            ];

            const result = calculateOverallProficiencyFromCurriculum(invalidProgress);
            expect(result).toBe(80); // Only the valid item should be counted
        });

        it('should clamp proficiency values to 0-100 range', () => {
            const extremeProgress = [
                {
                    nodeId: {
                        _id: '1',
                        name: 'Chapter 1',
                        type: 'Chapter'
                    },
                    proficiency: -10, // Should be clamped to 0
                    status: 'In Progress'
                },
                {
                    nodeId: {
                        _id: '2',
                        name: 'Chapter 2',
                        type: 'Chapter'
                    },
                    proficiency: 150, // Should be clamped to 100
                    status: 'Completed'
                }
            ];

            const result = calculateOverallProficiencyFromCurriculum(extremeProgress);
            expect(result).toBe(50); // (0 + 100) / 2 = 50
        });

        it('should handle order-based weighting', () => {
            const result = calculateOverallProficiencyFromCurriculum(mockCurriculumProgress, {
                weightingStrategy: 'order'
            });
            
            // Weights: 1.0, 1.0, 1.1 (based on order)
            // (80 * 1.0 + 90 * 1.0 + 70 * 1.1) / (1.0 + 1.0 + 1.1) = 247 / 3.1 ≈ 79.68
            expect(result).toBeCloseTo(79.68, 1);
        });

        it('should return rounded result to 2 decimal places', () => {
            const progress = [
                {
                    nodeId: { _id: '1', name: 'Test', type: 'Chapter' },
                    proficiency: 33.333,
                    status: 'In Progress'
                },
                {
                    nodeId: { _id: '2', name: 'Test', type: 'Chapter' },
                    proficiency: 66.666,
                    status: 'In Progress'
                }
            ];

            const result = calculateOverallProficiencyFromCurriculum(progress);
            expect(result).toBe(50); // Should be rounded properly
        });
    });
});
