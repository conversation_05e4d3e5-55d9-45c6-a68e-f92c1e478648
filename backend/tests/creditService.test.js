import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import mongoose from 'mongoose';
import CreditService from '../services/creditService.js';
import Student from '../models/Student.js';
import Teacher from '../models/Teacher.js';
import CreditTransaction, { TRANSACTION_TYPES, TRANSACTION_STATUS, USAGE_TYPES } from '../models/CreditTransaction.js';

// Mock the models
vi.mock('../models/Student.js');
vi.mock('../models/Teacher.js');
vi.mock('../models/CreditTransaction.js');

describe('CreditService', () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe('getCreditBalance', () => {
        it('should return credit balance for a student', async () => {
            const mockUser = {
                _id: 'user123',
                credits: { balance: 50 }
            };
            
            Student.findById.mockResolvedValue(mockUser);
            
            const balance = await CreditService.getCreditBalance('user123', 'Student');
            
            expect(balance).toBe(50);
            expect(Student.findById).toHaveBeenCalledWith('user123');
        });

        it('should return 0 if user has no credits field', async () => {
            const mockUser = { _id: 'user123' };
            
            Student.findById.mockResolvedValue(mockUser);
            
            const balance = await CreditService.getCreditBalance('user123', 'Student');
            
            expect(balance).toBe(0);
        });

        it('should throw error if user not found', async () => {
            Student.findById.mockResolvedValue(null);
            
            await expect(CreditService.getCreditBalance('user123', 'Student'))
                .rejects.toThrow('User not found');
        });
    });

    describe('hasSufficientCredits', () => {
        it('should return true if user has sufficient credits', async () => {
            const mockUser = {
                _id: 'user123',
                credits: { balance: 10 }
            };
            
            Student.findById.mockResolvedValue(mockUser);
            
            const result = await CreditService.hasSufficientCredits('user123', 'Student', 5);
            
            expect(result).toBe(true);
        });

        it('should return false if user has insufficient credits', async () => {
            const mockUser = {
                _id: 'user123',
                credits: { balance: 3 }
            };
            
            Student.findById.mockResolvedValue(mockUser);
            
            const result = await CreditService.hasSufficientCredits('user123', 'Student', 5);
            
            expect(result).toBe(false);
        });

        it('should return false on error', async () => {
            Student.findById.mockRejectedValue(new Error('Database error'));
            
            const result = await CreditService.hasSufficientCredits('user123', 'Student', 5);
            
            expect(result).toBe(false);
        });
    });

    describe('deductCredits', () => {
        it('should successfully deduct credits', async () => {
            const mockUser = {
                _id: 'user123',
                credits: { balance: 10, totalSpent: 5 }
            };
            
            const mockSession = {
                startTransaction: vi.fn(),
                commitTransaction: vi.fn(),
                abortTransaction: vi.fn(),
                endSession: vi.fn()
            };
            
            const mockTransaction = {
                save: vi.fn().mockResolvedValue({}),
                toObject: vi.fn().mockReturnValue({ id: 'trans123' })
            };

            Student.startSession.mockResolvedValue(mockSession);
            Student.findById.mockResolvedValue(mockUser);
            Student.findByIdAndUpdate.mockResolvedValue({});
            CreditTransaction.mockImplementation(() => mockTransaction);

            const result = await CreditService.deductCredits('user123', 'Student', 3, {
                feature: USAGE_TYPES.AEGIS_GRADER,
                description: 'Test usage'
            });

            expect(result.success).toBe(true);
            expect(result.newBalance).toBe(7);
            expect(mockSession.commitTransaction).toHaveBeenCalled();
        });

        it('should throw error for insufficient credits', async () => {
            const mockUser = {
                _id: 'user123',
                credits: { balance: 2 }
            };
            
            const mockSession = {
                startTransaction: vi.fn(),
                abortTransaction: vi.fn(),
                endSession: vi.fn()
            };

            Student.startSession.mockResolvedValue(mockSession);
            Student.findById.mockResolvedValue(mockUser);

            await expect(CreditService.deductCredits('user123', 'Student', 5, {}))
                .rejects.toThrow('Insufficient credits');
            
            expect(mockSession.abortTransaction).toHaveBeenCalled();
        });
    });

    describe('addCredits', () => {
        it('should successfully add credits', async () => {
            const mockUser = {
                _id: 'user123',
                credits: { balance: 10, totalEarned: 20 }
            };
            
            const mockSession = {
                startTransaction: vi.fn(),
                commitTransaction: vi.fn(),
                abortTransaction: vi.fn(),
                endSession: vi.fn()
            };
            
            const mockTransaction = {
                save: vi.fn().mockResolvedValue({}),
                toObject: vi.fn().mockReturnValue({ id: 'trans123' })
            };

            Student.startSession.mockResolvedValue(mockSession);
            Student.findById.mockResolvedValue(mockUser);
            Student.findByIdAndUpdate.mockResolvedValue({});
            CreditTransaction.mockImplementation(() => mockTransaction);

            const result = await CreditService.addCredits('user123', 'Student', 5, {
                type: TRANSACTION_TYPES.PURCHASE,
                description: 'Credit purchase'
            });

            expect(result.success).toBe(true);
            expect(result.newBalance).toBe(15);
            expect(mockSession.commitTransaction).toHaveBeenCalled();
        });
    });

    describe('grantInitialCredits', () => {
        it('should grant initial credits to new user', async () => {
            const mockAddCreditsResult = {
                success: true,
                newBalance: 10,
                transaction: { id: 'trans123' }
            };

            // Mock the addCredits method
            vi.spyOn(CreditService, 'addCredits').mockResolvedValue(mockAddCreditsResult);

            const result = await CreditService.grantInitialCredits('user123', 'Student', 10);

            expect(result).toEqual(mockAddCreditsResult);
            expect(CreditService.addCredits).toHaveBeenCalledWith(
                'user123',
                'Student',
                10,
                expect.objectContaining({
                    type: TRANSACTION_TYPES.INITIAL_GRANT,
                    description: 'Welcome bonus: 10 free credits'
                })
            );
        });
    });
});

// Integration test example
describe('CreditService Integration', () => {
    // These would be actual integration tests with a test database
    it.skip('should handle concurrent credit deductions correctly', async () => {
        // Test concurrent access to prevent race conditions
        // This would require actual database setup
    });

    it.skip('should maintain transaction consistency', async () => {
        // Test that failed transactions are properly rolled back
        // This would require actual database setup
    });
});
