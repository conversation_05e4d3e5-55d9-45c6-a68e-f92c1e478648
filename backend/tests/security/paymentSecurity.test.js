import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import request from 'supertest';
import app from '../../app.js';
import CreditTransaction from '../../models/CreditTransaction.js';
import Student from '../../models/Student.js';
import crypto from 'crypto';

/**
 * Comprehensive Security Test Suite for Payment System
 * Tests for vulnerabilities, race conditions, and edge cases
 */
describe('Payment Security Tests', () => {
    let testUser;
    let authToken;
    let testTransaction;

    beforeEach(async () => {
        // Setup test user and authentication
        testUser = await Student.create({
            username: 'testuser',
            email: '<EMAIL>',
            password: 'hashedpassword',
            credits: { balance: 0 }
        });

        // Mock JWT token (in real tests, generate proper token)
        authToken = 'mock-jwt-token';
    });

    afterEach(async () => {
        // Cleanup test data
        await Student.deleteMany({});
        await CreditTransaction.deleteMany({});
    });

    describe('Payment Verification Security', () => {
        it('should reject payment verification with invalid signature', async () => {
            const response = await request(app)
                .post('/api/credits/verify')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    razorpay_order_id: 'order_test123',
                    razorpay_payment_id: 'pay_test123',
                    razorpay_signature: 'invalid_signature'
                });

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
            expect(response.body.message).toContain('Invalid payment signature');
        });

        it('should reject payment verification for non-existent transaction', async () => {
            const validSignature = generateValidSignature('order_nonexistent', 'pay_test123');
            
            const response = await request(app)
                .post('/api/credits/verify')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    razorpay_order_id: 'order_nonexistent',
                    razorpay_payment_id: 'pay_test123',
                    razorpay_signature: validSignature
                });

            expect(response.status).toBe(404);
            expect(response.body.success).toBe(false);
        });

        it('should reject payment verification for transaction belonging to different user', async () => {
            // Create transaction for different user
            const otherUser = await Student.create({
                username: 'otheruser',
                email: '<EMAIL>',
                password: 'hashedpassword'
            });

            const transaction = await CreditTransaction.create({
                userId: otherUser._id,
                userType: 'Student',
                transactionId: 'test_transaction',
                type: 'PURCHASE',
                status: 'PENDING',
                creditAmount: 10,
                balanceBefore: 0,
                balanceAfter: 10,
                payment: {
                    razorpayOrderId: 'order_other_user',
                    amount: 2499,
                    packageType: 'BASIC_10'
                }
            });

            const validSignature = generateValidSignature('order_other_user', 'pay_test123');
            
            const response = await request(app)
                .post('/api/credits/verify')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    razorpay_order_id: 'order_other_user',
                    razorpay_payment_id: 'pay_test123',
                    razorpay_signature: validSignature
                });

            expect(response.status).toBe(404);
            expect(response.body.message).toContain('unauthorized');
        });

        it('should prevent double processing of same payment', async () => {
            // Create pending transaction
            const transaction = await CreditTransaction.create({
                userId: testUser._id,
                userType: 'Student',
                transactionId: 'test_transaction',
                type: 'PURCHASE',
                status: 'PENDING',
                creditAmount: 10,
                balanceBefore: 0,
                balanceAfter: 10,
                payment: {
                    razorpayOrderId: 'order_test123',
                    amount: 2499,
                    packageType: 'BASIC_10'
                }
            });

            const validSignature = generateValidSignature('order_test123', 'pay_test123');
            
            // First verification
            const response1 = await request(app)
                .post('/api/credits/verify')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    razorpay_order_id: 'order_test123',
                    razorpay_payment_id: 'pay_test123',
                    razorpay_signature: validSignature
                });

            expect(response1.status).toBe(200);

            // Second verification attempt (should fail)
            const response2 = await request(app)
                .post('/api/credits/verify')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    razorpay_order_id: 'order_test123',
                    razorpay_payment_id: 'pay_test123',
                    razorpay_signature: validSignature
                });

            expect(response2.status).toBe(409);
            expect(response2.body.message).toContain('already processed');
        });
    });

    describe('Race Condition Tests', () => {
        it('should handle concurrent payment verification attempts', async () => {
            // Create pending transaction
            const transaction = await CreditTransaction.create({
                userId: testUser._id,
                userType: 'Student',
                transactionId: 'test_transaction',
                type: 'PURCHASE',
                status: 'PENDING',
                creditAmount: 10,
                balanceBefore: 0,
                balanceAfter: 10,
                payment: {
                    razorpayOrderId: 'order_concurrent',
                    amount: 2499,
                    packageType: 'BASIC_10'
                }
            });

            const validSignature = generateValidSignature('order_concurrent', 'pay_test123');
            
            // Simulate concurrent requests
            const promises = Array(5).fill().map(() => 
                request(app)
                    .post('/api/credits/verify')
                    .set('Authorization', `Bearer ${authToken}`)
                    .send({
                        razorpay_order_id: 'order_concurrent',
                        razorpay_payment_id: 'pay_test123',
                        razorpay_signature: validSignature
                    })
            );

            const responses = await Promise.all(promises);
            
            // Only one should succeed
            const successfulResponses = responses.filter(r => r.status === 200);
            const conflictResponses = responses.filter(r => r.status === 409);
            
            expect(successfulResponses.length).toBe(1);
            expect(conflictResponses.length).toBe(4);
        });
    });

    describe('Input Validation Tests', () => {
        it('should reject malformed payment IDs', async () => {
            const response = await request(app)
                .post('/api/credits/verify')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    razorpay_order_id: 'x', // Too short
                    razorpay_payment_id: 'pay_test123',
                    razorpay_signature: 'valid_signature'
                });

            expect(response.status).toBe(400);
        });

        it('should reject missing required fields', async () => {
            const response = await request(app)
                .post('/api/credits/verify')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    razorpay_order_id: 'order_test123'
                    // Missing payment_id and signature
                });

            expect(response.status).toBe(400);
        });
    });

    describe('Amount Validation Tests', () => {
        it('should reject payment with mismatched amount', async () => {
            // Create transaction with one amount
            const transaction = await CreditTransaction.create({
                userId: testUser._id,
                userType: 'Student',
                transactionId: 'test_transaction',
                type: 'PURCHASE',
                status: 'PENDING',
                creditAmount: 10,
                balanceBefore: 0,
                balanceAfter: 10,
                payment: {
                    razorpayOrderId: 'order_amount_test',
                    amount: 9999, // Wrong amount
                    packageType: 'BASIC_10' // Should be 2499
                }
            });

            const validSignature = generateValidSignature('order_amount_test', 'pay_test123');
            
            const response = await request(app)
                .post('/api/credits/verify')
                .set('Authorization', `Bearer ${authToken}`)
                .send({
                    razorpay_order_id: 'order_amount_test',
                    razorpay_payment_id: 'pay_test123',
                    razorpay_signature: validSignature
                });

            expect(response.status).toBe(400);
            expect(response.body.message).toContain('amount validation failed');
        });
    });
});

// Helper function to generate valid Razorpay signature for testing
function generateValidSignature(orderId, paymentId) {
    const body = orderId + "|" + paymentId;
    return crypto
        .createHmac("sha256", process.env.RAZORPAY_KEY_SECRET || 'test_secret')
        .update(body.toString())
        .digest("hex");
}
