from concurrent.futures import Thread<PERSON>oolExecutor
from langchain_core.documents import Document
from PyPDF2 import Pdf<PERSON><PERSON><PERSON>, PdfWriter
from dotenv import load_dotenv
from google.genai import types
from google import genai
from typing import List
from tqdm import tqdm
import json
import os
import sys

load_dotenv()

API_KEY = "[your-api-key]"

if not API_KEY or API_KEY == "[your-api-key]":
    API_KEY = os.getenv('GEMINI_API_KEY')

client = genai.Client(api_key=API_KEY)

def generate(b64Page, prompt, metadata):

    document = types.Part.from_bytes(
        data=b64Page,
        mime_type="application/pdf",
    )

    # model = "gemini-2.0-flash-001"
    model = "gemini-2.0-flash"
    contents = [
    types.Content(
        role="user",
        parts=[
        document,
        types.Part.from_text(text=prompt)
        ]
    ),
    ]
    generate_content_config = types.GenerateContentConfig(
    temperature = 0,
    top_p = 0.95,
    max_output_tokens = 8192,
    response_modalities = ["TEXT"],
    safety_settings = [types.SafetySetting(
        category="HARM_CATEGORY_HATE_SPEECH",
        threshold="OFF"
    ),types.SafetySetting(
        category="HARM_CATEGORY_DANGEROUS_CONTENT",
        threshold="OFF"
    ),types.SafetySetting(
        category="HARM_CATEGORY_SEXUALLY_EXPLICIT",
        threshold="OFF"
    ),types.SafetySetting(
        category="HARM_CATEGORY_HARASSMENT",
        threshold="OFF"
    )],
    # Since the response is now in Markdown format with custom tags, not JSON
    response_mime_type = "text/plain",
    system_instruction=[types.Part.from_text(text=prompt)],
    )

    response = client.models.generate_content(
    model = model,
    contents = contents,
    config = generate_content_config,
    )
    
    # The response is now plain text (Markdown with custom tags)
    page_content = response.text

    # Create a Document object
    return Document(
        page_content=page_content,
        metadata=metadata
    )

def evaluate(answer_sheet_markdown, rubric_markdown, evaluation_prompt):
    # Instead of using string formatting which can conflict with LaTeX expressions,
    # we'll replace the placeholders manually
    formatted_prompt = evaluation_prompt.replace(
        "{rubric_json}", rubric_markdown).replace(
        "{answer_sheet_json}", answer_sheet_markdown
    )
    
    model = "gemini-2.0-flash"
    contents = [
        types.Content(
            role="user",
            parts=[types.Part.from_text(text=formatted_prompt)]
        ),
    ]
    
    generate_content_config = types.GenerateContentConfig(
        temperature=0,
        top_p=0.95,
        max_output_tokens=8192,
        response_modalities=["TEXT"],
        safety_settings=[
            types.SafetySetting(category="HARM_CATEGORY_HATE_SPEECH", threshold="OFF"),
            types.SafetySetting(category="HARM_CATEGORY_DANGEROUS_CONTENT", threshold="OFF"),
            types.SafetySetting(category="HARM_CATEGORY_SEXUALLY_EXPLICIT", threshold="OFF"),
            types.SafetySetting(category="HARM_CATEGORY_HARASSMENT", threshold="OFF")
        ],
        # Since the response is now in Markdown format with custom tags, not JSON
        response_mime_type="text/plain",
        system_instruction=[types.Part.from_text(text=formatted_prompt)],
    )
    
    response = client.models.generate_content(
        model=model,
        contents=contents,
        config=generate_content_config,
    )
    
    # The response is now plain text (Markdown with custom tags)
    evaluation_result = response.text
    
    # Create a Document object with evaluation results
    metadata = {
        "type": "evaluation",
        "answer_sheet": "processed answer sheet",
        "rubric": "processed rubric"
    }
    
    return Document(
        page_content=evaluation_result,
        metadata=metadata
    )