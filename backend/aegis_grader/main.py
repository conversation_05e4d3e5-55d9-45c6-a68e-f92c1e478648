import json
import base64
import os
import sys
import traceback
from contextlib import ExitStack
from tempfile import NamedTemporaryFile
from preProcessPDF import get_pdf_reader_and_metadata, extract_page
from model import generate
from processOCR import process_all_pages, combine_and_evaluate, create_rubric

INPUT_JSON_PATH = "./aegis_grader/tempFile.json"
PDF_BASE64_PREFIX = "data:application/pdf;base64,"

def decode_and_save_pdf(base64_data: str, suffix: str) -> NamedTemporaryFile:
    if not base64_data:
        raise ValueError(f"Missing base64 data for {suffix}.")
    if base64_data.startswith(PDF_BASE64_PREFIX):
        encoded_data = base64_data[len(PDF_BASE64_PREFIX):]
    else:
        encoded_data = base64_data
        print(f"Warning: PDF base64 prefix not found for {suffix}.", file=sys.stderr)
    try:
        pdf_bytes = base64.b64decode(encoded_data)
    except base64.binascii.Error as e:
        raise ValueError(f"Invalid base64 data for {suffix}: {e}") from e
    temp_pdf_file = NamedTemporaryFile(mode="wb", suffix=f"_{suffix}.pdf", delete=False)
    temp_pdf_file.write(pdf_bytes)
    temp_pdf_file.flush()
    return temp_pdf_file

def process_pdf_to_jsonl(pdf_path: str, file_type: str, output_suffix: str) -> NamedTemporaryFile:
    print(f"Processing {file_type} from {pdf_path}...", file=sys.stderr)
    processed_docs = process_all_pages(pdf_path, file_type)
    temp_jsonl_file = NamedTemporaryFile(mode="w", suffix=f"_{output_suffix}.jsonl", delete=False)
    for doc in processed_docs:
        try:
            if hasattr(doc, 'model_dump_json'):
                json_str = doc.model_dump_json()
            elif hasattr(doc, 'model_dump'):
                json_str = json.dumps(doc.model_dump())
            elif hasattr(doc, 'dict'):
                json_str = json.dumps(doc.dict())
            elif isinstance(doc, dict):
                json_str = json.dumps(doc)
            else:
                raise TypeError(f"Document object of type {type(doc)} cannot be easily serialized to JSON.")
            temp_jsonl_file.write(json_str + "\n")
        except AttributeError as e:
            raise TypeError(f"Document object {doc} does not have a standard serialization method: {e}") from e
        except Exception as e:
            raise RuntimeError(f"Error serializing document for {output_suffix}: {e}") from e
    temp_jsonl_file.flush()
    print(f"{file_type.capitalize()} processed and saved to temporary file {temp_jsonl_file.name}", file=sys.stderr)
    return temp_jsonl_file

def main():
    all_evaluation_contents = []
    created_md_files = []
    with ExitStack() as stack:
        try:
            with open(INPUT_JSON_PATH, "r") as file:
                data = json.load(file)
            if 'answerSheets' not in data or 'rubric' not in data:
                raise ValueError("Input JSON missing 'answerSheets' or 'rubric' key.")
            if not isinstance(data['answerSheets'], list) or not data['answerSheets']:
                raise ValueError("'answerSheets' must be a non-empty list.")
            rubric_data = data['rubric'].get('pdfData')
            if not rubric_data:
                raise ValueError("Missing 'pdfData' in rubric.")
            temp_rubric_pdf = stack.enter_context(decode_and_save_pdf(rubric_data, "rubric"))
            temp_rubric_jsonl = stack.enter_context(process_pdf_to_jsonl(temp_rubric_pdf.name, "rubric", "rubric_processed"))
            rubric_jsonl_path = temp_rubric_jsonl.name
            print(f"\nFound {len(data['answerSheets'])} answer sheets to process.", file=sys.stderr)
            for i, sheet_info in enumerate(data['answerSheets']):
                sheet_index = i + 1
                print(f"--- Processing Answer Sheet {sheet_index} ---", file=sys.stderr)
                try:
                    sheet_pdf_data = sheet_info.get('pdfData')
                    if not sheet_pdf_data:
                        print(f"Warning: Skipping answer sheet {sheet_index} due to missing 'pdfData'.", file=sys.stderr)
                        continue
                    temp_answer_sheet_pdf = stack.enter_context(decode_and_save_pdf(sheet_pdf_data, f"answers_{sheet_index}"))
                    temp_answer_sheet_jsonl = stack.enter_context(process_pdf_to_jsonl(temp_answer_sheet_pdf.name, "answer_sheet", f"answers_processed_{sheet_index}"))
                    print(f"Evaluating answer sheet {sheet_index} ({temp_answer_sheet_pdf.name}) against rubric...", file=sys.stderr)
                    evaluation_result_doc = combine_and_evaluate(temp_answer_sheet_jsonl.name, rubric_jsonl_path)
                    if not hasattr(evaluation_result_doc, 'page_content'):
                        print(f"Error: Evaluation result for sheet {sheet_index} lacks 'page_content'. Skipping.", file=sys.stderr)
                        continue
                    evaluation_content = evaluation_result_doc.page_content
                    all_evaluation_contents.append(evaluation_content)
                    result_filename = f"evaluation_result_sheet_{sheet_index}.md"
                    created_md_files.append(result_filename)
                    try:
                        with open(result_filename, "w") as md_file:
                            md_file.write(evaluation_content)
                        print(f"Detailed evaluation for sheet {sheet_index} saved to {result_filename}", file=sys.stderr)
                    except IOError as e:
                        print(f"Warning: Could not save detailed evaluation file {result_filename}: {e}", file=sys.stderr)
                    print(f"--- Finished Answer Sheet {sheet_index} ---", file=sys.stderr)
                except (ValueError, TypeError, RuntimeError, FileNotFoundError, json.JSONDecodeError) as e:
                    print(f"\n!! Error processing Answer Sheet {sheet_index}: {type(e).__name__} - {str(e)} !!", file=sys.stderr)
                    traceback.print_exc(file=sys.stderr)
                    print(f"Skipping evaluation for sheet {sheet_index} due to error.", file=sys.stderr)
                except Exception as e:
                    print(f"\n!! Unexpected System Error during Answer Sheet {sheet_index}: {type(e).__name__} - {str(e)} !!", file=sys.stderr)
                    traceback.print_exc(file=sys.stderr)
                    print(f"Skipping evaluation for sheet {sheet_index} due to unexpected error.", file=sys.stderr)
            print(f"\nCombining results from {len(all_evaluation_contents)} successfully evaluated sheets.", file=sys.stderr)
            final_output_string = "\n\n".join(all_evaluation_contents)
            sys.stdout.write(final_output_string)
            sys.stdout.flush()
        except (ValueError, TypeError, RuntimeError, FileNotFoundError, json.JSONDecodeError) as e:
            print(f"\n!! Critical Processing Error: {type(e).__name__} - {str(e)} !!", file=sys.stderr)
            traceback.print_exc(file=sys.stderr)
        except Exception as e:
            print(f"\n!! Critical Unexpected System Error: {type(e).__name__} - {str(e)} !!", file=sys.stderr)
            traceback.print_exc(file=sys.stderr)
        finally:
             print("\nTemporary file cleanup handled by ExitStack.", file=sys.stderr)
             if created_md_files:
                  print(f"Note: Detailed evaluation markdown files created: {', '.join(created_md_files)}", file=sys.stderr)
if __name__ == "__main__":
    main()