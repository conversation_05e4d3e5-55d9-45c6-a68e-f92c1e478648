# schemas.py
from google.genai import types

# --- Schema: Answer Sheet Extraction (from previous example) ---
# (Keep this if you need it alongside the others)

diagram_schema = types.Schema(
    type=types.Type.OBJECT,
    properties={
        'type': types.Schema(type=types.Type.STRING, description="Type of diagram"),
        'components': types.Schema(type=types.Type.ARRAY, items=types.Schema(type=types.Type.STRING)),
        'labels': types.Schema(type=types.Type.ARRAY, items=types.Schema(type=types.Type.STRING)),
        'relationships': types.Schema(type=types.Type.STRING),
        'details': types.Schema(type=types.Type.STRING),
    },
    required=['type', 'components', 'labels', 'relationships', 'details']
)

answer_question_schema = types.Schema(
    type=types.Type.OBJECT,
    properties={
        'number': types.Schema(type=types.Type.STRING),
        'content': types.Schema(type=types.Type.STRING, description="Answer text with LaTeX math"),
        'diagrams': types.Schema(type=types.Type.ARRAY, items=diagram_schema, nullable=True),
        'crossed_out': types.Schema(type=types.Type.ARRAY, items=types.Schema(type=types.Type.STRING), nullable=True),
        'unclear': types.Schema(type=types.Type.ARRAY, items=types.Schema(type=types.Type.STRING), nullable=True),
    },
    required=['number', 'content']
)

answer_section_schema = types.Schema(
    type=types.Type.OBJECT,
    properties={
        'name': types.Schema(type=types.Type.STRING),
        'questions': types.Schema(type=types.Type.ARRAY, items=answer_question_schema)
    },
    required=['name', 'questions']
)

answer_page_schema = types.Schema(
    type=types.Type.OBJECT,
    properties={
        'page_number': types.Schema(type=types.Type.INTEGER),
        'sections': types.Schema(type=types.Type.ARRAY, items=answer_section_schema, nullable=True),
        'questions': types.Schema(type=types.Type.ARRAY, items=answer_question_schema, nullable=True)
    },
    required=['page_number']
)

ANSWER_SHEET_SCHEMA = types.Schema(
    type=types.Type.OBJECT,
    properties={
        'metadata': types.Schema(
            type=types.Type.OBJECT,
            properties={
                'total_pages': types.Schema(type=types.Type.INTEGER),
                'subject': types.Schema(type=types.Type.STRING),
                'student_name': types.Schema(type=types.Type.STRING),
                'student_id': types.Schema(type=types.Type.STRING),
                'class_grade': types.Schema(type=types.Type.STRING),
                'date': types.Schema(type=types.Type.STRING, description="Date in DD-MM-YYYY format"),
                'language': types.Schema(type=types.Type.STRING),
            },
            required=['total_pages', 'subject', 'student_name', 'student_id', 'class_grade', 'date', 'language']
        ),
        'pages': types.Schema(type=types.Type.ARRAY, items=answer_page_schema)
    },
    required=['metadata', 'pages']
)


# --- Schema: Rubric Extraction / Creation ---
# Note: These two are very similar, differing mainly in metadata.
# We define shared sub-schemas first.

rubric_criteria_schema = types.Schema(
    type=types.Type.OBJECT,
    description="Specific criterion for marking",
    properties={
        'name': types.Schema(type=types.Type.STRING, description="Name or identifier of the criterion"),
        'marks': types.Schema(type=types.Type.INTEGER, description="Marks allocated for this criterion"),
        'description': types.Schema(type=types.Type.STRING, description="Description of the criterion"),
    },
    required=['name', 'marks', 'description']
)

rubric_alternative_approach_schema = types.Schema(
    type=types.Type.OBJECT,
    description="An alternative valid approach to solving the question",
    properties={
        'description': types.Schema(type=types.Type.STRING, description="Description of the alternative approach"),
        'criteria': types.Schema(
            type=types.Type.ARRAY,
            items=rubric_criteria_schema,
            description="Marking criteria specific to this alternative approach"
        ),
    },
    required=['description', 'criteria']
)

rubric_common_error_schema = types.Schema(
    type=types.Type.OBJECT,
    description="Common errors students make",
    properties={
        'description': types.Schema(type=types.Type.STRING, description="Description of the common error"),
        'deduction': types.Schema(type=types.Type.INTEGER, description="Marks to be deducted for this error"),
        'explanation': types.Schema(type=types.Type.STRING, description="Explanation of why it's an error or how to correct it"),
    },
    required=['description', 'deduction', 'explanation']
)

rubric_question_schema = types.Schema(
    type=types.Type.OBJECT,
    description="Details of a single question in the rubric",
    properties={
        'number': types.Schema(type=types.Type.STRING, description="Question number (e.g., '1a', '2')"),
        'marks': types.Schema(type=types.Type.INTEGER, description="Total marks for the question"),
        'question_text': types.Schema(type=types.Type.STRING, description="The text of the question (use LaTeX for math)"),
        'model_answer': types.Schema(type=types.Type.STRING, description="The ideal or expected answer (use LaTeX for math)"),
        'criteria': types.Schema(
            type=types.Type.ARRAY,
            items=rubric_criteria_schema,
            description="Primary marking criteria"
        ),
        'alternative_approaches': types.Schema(
            type=types.Type.ARRAY,
            items=rubric_alternative_approach_schema,
            nullable=True, # Alternatives might not always exist
            description="Alternative valid solution methods"
        ),
        'common_errors': types.Schema(
            type=types.Type.ARRAY,
            items=rubric_common_error_schema,
            nullable=True, # Common errors might not always be listed
            description="Common mistakes and deductions"
        ),
    },
    required=['number', 'marks', 'question_text', 'model_answer', 'criteria']
)

rubric_section_schema = types.Schema(
    type=types.Type.OBJECT,
    description="A section within the examination rubric",
    properties={
        'name': types.Schema(type=types.Type.STRING, description="Name of the section (e.g., 'Section A')"),
        'instructions': types.Schema(type=types.Type.STRING, description="Instructions specific to this section"),
        'marks': types.Schema(type=types.Type.INTEGER, description="Total marks for this section"),
        'questions': types.Schema(
            type=types.Type.ARRAY,
            items=rubric_question_schema,
            description="List of questions within this section"
        ),
    },
    required=['name', 'instructions', 'marks', 'questions']
)

# Schema specifically for RUBRIC EXTRACTION (includes total_pages)
RUBRIC_EXTRACTION_SCHEMA = types.Schema(
    type=types.Type.OBJECT,
    description="Structured representation of an examination rubric extracted from a document",
    properties={
        'metadata': types.Schema(
            type=types.Type.OBJECT,
            properties={
                'total_pages': types.Schema(type=types.Type.INTEGER, description="Total number of pages in the source document"),
                'subject': types.Schema(type=types.Type.STRING),
                'exam_name': types.Schema(type=types.Type.STRING),
                'total_marks': types.Schema(type=types.Type.INTEGER),
                'duration': types.Schema(type=types.Type.STRING, description="e.g., '2 hours', '90 minutes'"),
                'special_instructions': types.Schema(type=types.Type.STRING, description="Overall instructions for the exam"),
            },
            required=['total_pages', 'subject', 'exam_name', 'total_marks', 'duration', 'special_instructions']
        ),
        'sections': types.Schema(
            type=types.Type.ARRAY,
            items=rubric_section_schema
        )
    },
    required=['metadata', 'sections']
)

# Schema specifically for RUBRIC CREATION (excludes total_pages from metadata)
CREATE_RUBRIC_SCHEMA = types.Schema(
    type=types.Type.OBJECT,
    description="Structured representation of a newly created examination rubric",
    properties={
        'metadata': types.Schema(
            type=types.Type.OBJECT,
            properties={
                # 'total_pages' is omitted here compared to extraction
                'subject': types.Schema(type=types.Type.STRING),
                'exam_name': types.Schema(type=types.Type.STRING),
                'total_marks': types.Schema(type=types.Type.INTEGER),
                'duration': types.Schema(type=types.Type.STRING, description="e.g., '2 hours', '90 minutes'"),
                'special_instructions': types.Schema(type=types.Type.STRING, description="Overall instructions for the exam"),
            },
            required=['subject', 'exam_name', 'total_marks', 'duration', 'special_instructions']
        ),
        'sections': types.Schema(
            type=types.Type.ARRAY,
            items=rubric_section_schema
        )
    },
    required=['metadata', 'sections']
)


# --- Schema: Evaluation ---

evaluation_criteria_breakdown_schema = types.Schema(
    type=types.Type.OBJECT,
    description="Evaluation against a specific rubric criterion",
    properties={
        'criterion': types.Schema(type=types.Type.STRING, description="Name or description of the criterion being evaluated"),
        'marks_awarded': types.Schema(type=types.Type.INTEGER, description="Marks given for this criterion"),
        'marks_possible': types.Schema(type=types.Type.INTEGER, description="Maximum marks possible for this criterion"),
        'comments': types.Schema(type=types.Type.STRING, description="Specific feedback related to this criterion"),
    },
    required=['criterion', 'marks_awarded', 'marks_possible', 'comments']
)

evaluation_question_schema = types.Schema(
    type=types.Type.OBJECT,
    description="Evaluation details for a single student answer",
    properties={
        'number': types.Schema(type=types.Type.STRING, description="Question number (e.g., '1a', '2')"),
        'marks_awarded': types.Schema(type=types.Type.INTEGER, description="Total marks awarded for this question"),
        'marks_possible': types.Schema(type=types.Type.INTEGER, description="Maximum possible marks for this question"),
        'feedback': types.Schema(type=types.Type.STRING, description="Overall feedback for the student's answer to this question"),
        'criteria_breakdown': types.Schema(
            type=types.Type.ARRAY,
            items=evaluation_criteria_breakdown_schema,
            description="Detailed breakdown of marks based on rubric criteria"
        ),
    },
    required=['number', 'marks_awarded', 'marks_possible', 'feedback', 'criteria_breakdown']
)

evaluation_section_schema = types.Schema(
    type=types.Type.OBJECT,
    description="Evaluation summary for a section",
    properties={
        'name': types.Schema(type=types.Type.STRING, description="Name of the section evaluated"),
        'marks': types.Schema(type=types.Type.INTEGER, description="Total marks awarded in this section"),
        'possible_marks': types.Schema(type=types.Type.INTEGER, description="Maximum possible marks for this section"),
        'questions': types.Schema(
            type=types.Type.ARRAY,
            items=evaluation_question_schema,
            description="List of evaluated questions within this section"
        ),
    },
    required=['name', 'marks', 'possible_marks', 'questions']
)

EVALUATION_SCHEMA = types.Schema(
    type=types.Type.OBJECT,
    description="Structured evaluation of a student's responses against a rubric",
    properties={
        'evaluation_summary': types.Schema(
            type=types.Type.OBJECT,
            description="Overall summary of the evaluation",
            properties={
                'total_marks': types.Schema(type=types.Type.INTEGER, description="Total marks obtained by the student"),
                'maximum_possible_marks': types.Schema(type=types.Type.INTEGER, description="Maximum marks possible for the entire assessment"),
                'percentage_score': types.Schema(type=types.Type.NUMBER, description="Overall percentage score (float)"),
            },
            required=['total_marks', 'maximum_possible_marks', 'percentage_score']
        ),
        'sections': types.Schema(
            type=types.Type.ARRAY,
            items=evaluation_section_schema,
            description="Evaluation details broken down by section"
        )
    },
    required=['evaluation_summary', 'sections']
)