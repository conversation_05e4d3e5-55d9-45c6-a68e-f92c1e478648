import express from 'express';
import analyticsController from '../controllers/analyticsController.js';
import { body, query, validationResult } from 'express-validator';

const router = express.Router();

// Test route to verify analytics routes are working
router.get('/test', analyticsController.test);

// Middleware to check validation results
const checkValidation = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }
    next();
};

// Create or update user session
router.post('/session/start', [
    body('userId').isMongoId().withMessage('Valid user ID required'),
    body('userType').isIn(['Student', 'Teacher', 'Admin']).withMessage('Valid user type required'),
    body('sessionId').isString().notEmpty().withMessage('Session ID required'),
    body('userAgent').isString().optional(),
    body('screenResolution').isString().optional(),
    body('timezone').isString().optional(),
    body('ipAddress').isIP().optional()
], checkValidation, analyticsController.startSession);

// End user session
router.post('/session/end', [
    body('sessionId').isString().notEmpty().withMessage('Session ID required')
], checkValidation, analyticsController.endSession);

// Track user interaction
router.post('/interaction', [
    body('userId').isMongoId().withMessage('Valid user ID required'),
    body('sessionId').isString().notEmpty().withMessage('Session ID required'),
    body('userType').isIn(['Student', 'Teacher', 'Admin']).withMessage('Valid user type required'),
    body('interactionType').isIn([
        'page_view', 'button_click', 'form_submit', 'navigation',
        'feature_usage', 'test_action', 'chat_interaction', 'file_upload',
        'search', 'filter', 'sort', 'export', 'download'
    ]).withMessage('Valid interaction type required'),
    body('page').isString().notEmpty().withMessage('Page required'),
    body('feature').isIn([
        'aegis_ai', 'test_taking', 'dashboard', 'analytics',
        'profile', 'class_management', 'grading', 'schedule_test',
        'practice_test', 'subject_details', 'feedback'
    ]).optional()
], checkValidation, analyticsController.trackInteraction);

// Get analytics dashboard data
router.get('/dashboard', [
    query('dateRange').isInt({ min: 1, max: 365 }).optional().withMessage('Date range must be between 1 and 365 days'),
    query('userType').isIn(['Student', 'Teacher', 'Admin', 'all']).optional()
], checkValidation, analyticsController.getDashboard);

// Get user-specific analytics
router.get('/user/:userId', [
    query('dateRange').isInt({ min: 1, max: 365 }).optional().withMessage('Date range must be between 1 and 365 days')
], checkValidation, analyticsController.getUserAnalytics);

// Get page analytics
router.get('/pages', [
    query('dateRange').isInt({ min: 1, max: 365 }).optional(),
    query('page').isString().optional()
], checkValidation, analyticsController.getPageAnalytics);

// Get feature usage analytics
router.get('/features', [
    query('dateRange').isInt({ min: 1, max: 365 }).optional(),
    query('feature').isString().optional()
], checkValidation, analyticsController.getFeatureAnalytics);

// Get real-time analytics (active sessions, current users)
router.get('/realtime', analyticsController.getRealTimeAnalytics);

// Export analytics data
router.get('/export', [
    query('type').isIn(['sessions', 'interactions', 'pages', 'features']).withMessage('Valid export type required'),
    query('dateRange').isInt({ min: 1, max: 365 }).optional(),
    query('format').isIn(['json', 'csv']).optional()
], checkValidation, analyticsController.exportData);

// Helper function to convert JSON to CSV
function convertToCSV(data) {
    if (!data.length) return '';
    
    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(',');
    
    const csvRows = data.map(row => {
        return headers.map(header => {
            const value = row[header];
            if (typeof value === 'object' && value !== null) {
                return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
            }
            return `"${String(value).replace(/"/g, '""')}"`;
        }).join(',');
    });
    
    return [csvHeaders, ...csvRows].join('\n');
}

// Data cleanup endpoint (admin only)
router.delete('/cleanup', [
    query('retentionDays').isInt({ min: 30, max: 1095 }).optional().withMessage('Retention days must be between 30 and 1095')
], checkValidation, async (req, res) => {
    try {
        // Note: Add admin authentication middleware here
        const retentionDays = parseInt(req.query.retentionDays) || 365;
        await analyticsService.cleanupOldData(retentionDays);
        res.json({ success: true, message: `Cleaned up data older than ${retentionDays} days` });
    } catch (error) {
        console.error('Error cleaning up data:', error);
        res.status(500).json({ error: 'Failed to cleanup data' });
    }
});

export default router;
