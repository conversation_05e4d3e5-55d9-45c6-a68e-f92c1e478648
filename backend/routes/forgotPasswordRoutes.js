// forgotPasswordRoutes.js
import express from 'express';
import { handleForgotPassword, handleResetPassword } from '../controllers/forgotPasswordController.js';

const router = express.Router();

// Route for requesting password reset
router.post('/forgot-password', handleForgotPassword);

// Route for resetting password with token
router.post('/reset-password', handleResetPassword);

export default router;