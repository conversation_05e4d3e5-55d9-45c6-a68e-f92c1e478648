import { Router } from 'express'
import { getStudentDetails, getTeacherDetails, editStudentDetails, editTeacherDetails, getSchoolDetailsByCode, uploadProfileImage } from '../controllers/getDetailsController.js'
import { body, param } from 'express-validator'

const router = Router();

router.post('/uploadProfileImage', uploadProfileImage);

router.post('/getDetailStudent', [
  body('email').isString().notEmpty()
], getStudentDetails);

router.post('/getDetailTeacher', [
  body('email').isString().notEmpty()
], getTeacherDetails);

router.post('/editDetailStudent', [
  body('o_uname').isString().notEmpty(),
  body('new_email').isEmail().notEmpty(),
  body('new_uname').isString().notEmpty()
], editStudentDetails);

router.post('/editDetailTeacher', [
  body('o_uname').isString().notEmpty(),
  body('new_email').isEmail().notEmpty(),
  body('new_uname').isString().notEmpty(),
], editTeacherDetails)

router.get('/getSchoolDetailsByCode/:schoolCode', [
  param('schoolCode').isString().notEmpty()
], getSchoolDetailsByCode);

export default router;
