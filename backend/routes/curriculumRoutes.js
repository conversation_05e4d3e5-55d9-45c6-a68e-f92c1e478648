import { Router } from 'express';
import curriculumController from '../controllers/curriculumController.js';

const router = Router();

// Initialize curriculum graph (DONT RUN without discussing)
router.post('/initialize', curriculumController.initializeCurriculum.bind(curriculumController));

// Get full curriculum structure
router.get('/structure', curriculumController.getCurriculumStructure.bind(curriculumController));

// Get subtopics for a specific chapter
router.get('/chapter/:chapterName/subtopics', curriculumController.getChapterSubtopics.bind(curriculumController));

export default router;
