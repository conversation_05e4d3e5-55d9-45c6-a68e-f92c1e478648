import { Router } from "express";
import { addQuestions } from "../controllers/addQuestionsControllers.js";
import { body } from 'express-validator';

const router = Router();

router.post('/addQuestions', [
  body('filepath').isString().notEmpty(),
  body('metadata').isString().notEmpty(),
  body('subject').isString().notEmpty(),
  body('classDetail').isString().notEmpty()
], addQuestions);

export default router;
