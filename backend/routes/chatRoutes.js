import express from 'express';
import {
  getUserConversations,
  getConversation,
  createConversation,
  updateConversation,
  deleteConversation,
  saveConversationMessage,
  createNewConversationOnLogin,
  createNewConversationExplicit,
  trackLoginSession,
  clearChatState,
  getConversationAnalytics,
  cleanupDuplicateEmptyConversations,
  debugListConversations
} from '../controllers/chatController.js';

const router = express.Router();

// Get all conversations for a user (optionally filtered by subject)
router.get('/conversations', getUserConversations);

// Get a specific conversation
router.get('/conversations/:id', getConversation);

// Create a new conversation
router.post('/conversations', createConversation);

// Update a conversation
router.put('/conversations/:id', updateConversation);

// Delete a conversation
router.delete('/conversations/:id', deleteConversation);

// Save or update conversation after chat response (used by chat system)
router.post('/save-message', saveConversationMessage);

// Auto-create new conversation on login
router.post('/create-on-login', createNewConversationOnLogin);

// Create new conversation explicitly (user-initiated)
router.post('/create-new', createNewConversationExplicit);

// Track login session for proper chat initialization
router.post('/track-login', trackLoginSession);

// Clear chat state (for frontend state reset)
router.post('/clear-state', clearChatState);

// Get conversation analytics
router.get('/analytics', getConversationAnalytics);

// Clean up duplicate empty conversations
router.post('/cleanup-duplicates', cleanupDuplicateEmptyConversations);

// Debug endpoint to list conversations
router.get('/debug/list', debugListConversations);

// Note: Teacher conversations now use the same endpoints as students
// The backend automatically detects teacher vs student based on userId pattern

export default router;
