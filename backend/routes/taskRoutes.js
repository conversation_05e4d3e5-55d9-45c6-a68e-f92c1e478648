import { createTeacherTask, createStudentTask, updateStudentTask, updateTeacherTask, deleteStudentTask, deleteTeacherTask } from "../controllers/taskController.js";
import express from "express";

const router = express.Router();

router.post("/createTeacherTask", createTeacherTask);
router.post("/createStudentTask", createStudentTask);
router.put("/updateTeacherTask", updateTeacherTask);
router.put("/updateStudentTask", updateStudentTask);
router.delete("/deleteTeacherTask", deleteTeacherTask);
router.delete("/deleteStudentTask", deleteStudentTask);

export default router;

