import { Router } from 'express';
import { body } from 'express-validator';
import { RecommendQuestions } from '../controllers/RecommendQuestionsController.js';

const router = Router();

router.post('/', [
  body('numQuestions').isInt(),
  body('topics').isArray(),
  body('classId').isString().notEmpty(),
  body('testType').isString().notEmpty(),
  body('subject').isString().notEmpty(),
  body('userid').isString()
], RecommendQuestions);

export default router;
