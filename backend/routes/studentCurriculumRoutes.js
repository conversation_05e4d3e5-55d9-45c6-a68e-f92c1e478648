import { Router } from 'express';
import StudentCurriculumController from '../controllers/studentCurriculumController.js';

const router = Router();

// Initialize curriculum for a student (DONT RUN FOR EXISTING STUDENTS)
router.post('/initialize/:studentId', StudentCurriculumController.initialize.bind(StudentCurriculumController));

// Update progress for a specific curriculum node
router.put('/update-progress', StudentCurriculumController.updateProgress.bind(StudentCurriculumController));

// Get curriculum progress for a student
router.get('/progress/:studentId', StudentCurriculumController.getProgress.bind(StudentCurriculumController));

export default router;
