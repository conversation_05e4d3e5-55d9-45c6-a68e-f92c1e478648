import { Router } from 'express';
import {
    recalculateStudentProficiency,
    batchRecalculateProficiencies,
    migrateProficiencies,
    getStudentProficiencyStats
} from '../controllers/proficiencyController.js';

const router = Router();

/**
 * @route PUT /api/proficiency/recalculate/:studentId/:subject
 * @desc Recalculate overall proficiency for a specific student and subject
 * @access Private
 */
router.put('/recalculate/:studentId/:subject', recalculateStudentProficiency);

/**
 * @route POST /api/proficiency/batch-recalculate
 * @desc Batch recalculate proficiencies for multiple students
 * @access Private
 */
router.post('/batch-recalculate', batchRecalculateProficiencies);

/**
 * @route POST /api/proficiency/migrate
 * @desc Migrate all hardcoded proficiency values to calculated ones
 * @access Private (Admin only)
 */
router.post('/migrate', migrateProficiencies);

/**
 * @route GET /api/proficiency/stats/:studentId/:subject
 * @desc Get proficiency statistics for a student
 * @access Private
 */
router.get('/stats/:studentId/:subject', getStudentProficiencyStats);

export default router;
