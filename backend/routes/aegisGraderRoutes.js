import { getAllSubmissions, getPresignedUrl, getCurrentProgress, handleProcessingResults, processRefunds, processLambdaRefunds } from "../controllers/aegisGraderController.js";
import { verifyJWT } from "../middleware/verifyJWT.js";
import { checkAegisGraderCredits } from "../middleware/creditCheck.js";

import express from "express";

const router = express.Router();

// Legacy route (commented out since submitForGrading is no longer used)
// router.post("/submit", checkAegisGraderCredits, submitForGrading);
router.get("/submissions/:userId", getAllSubmissions);

// Apply credit check to presigned URL generation (new SQS/Lambda workflow)
router.post("/getPresigned", checkAegisGraderCredits, getPresignedUrl);

// Handle processing results from Lambda function (no JWT required for Lambda callbacks)
router.post("/processing-results", handleProcessingResults);

// Simple refund endpoint for Lambda (no JWT required for Lambda callbacks)
router.post("/process-lambda-refunds", processLambdaRefunds);

// Process refunds for failed evaluations (requires authentication)
router.post("/refunds/:submissionId?", processRefunds);

router.get("/getCurrentProgress/:jobId", getCurrentProgress); // for polling the current progress for a particular jobid

export default router;
