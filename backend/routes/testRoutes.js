import { Router } from 'express';
import { submitTest } from '../controllers/submitTestController.js';
import { getTests, createTest, updateTest, deleteTest, getTestById, getTestsByIds, createPersonalizedTest, createPersonalizedTestIndividual } from '../controllers/testHistoryController.js';
import { getTestSubmissions } from '../controllers/testSubmissionsController.js';

const router = Router();
// Route to handle real-time or batch processing for submitting answers or tests
router.post('/submit', submitTest);
router.get('/:testId/submissions', getTestSubmissions);
router.post('/bulk', getTestsByIds); // New bulk endpoint for fetching multiple tests
router.get('/:testId', getTestById)
router.get('/get', getTests);
router.post('/create', createTest);
router.put('/update', updateTest);
router.delete('/delete', deleteTest);

router.post('/createPersonalizedTest', createPersonalizedTest);
router.post('/createPersonalizedTestIndividual', createPersonalizedTestIndividual);

export default router;
