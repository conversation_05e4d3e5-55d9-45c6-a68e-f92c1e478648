import { Router } from 'express';
import { registerStudent, loginStudent, loginTeacher, registerTeacher, verifyUserEmail, resendVerificationEmail, googleAuth } from '../controllers/authController.js';
import { body } from 'express-validator';

const router = Router();

// Register student
router.post('/registerStudent', [
    body('username').isString().notEmpty(),
    // body('email').isEmail(),
    body('password').isLength({ min: 6 })
], registerStudent);

// Login student
router.post('/loginStudent', [
    body('email').isString().notEmpty(),
    body('password').isLength({ min: 6 })
], loginStudent);

// Register Teacher
router.post('/registerTeacher', [
    body('username').isString().notEmpty(),
    body('email').isEmail(),
    body('password').isLength({ min: 6 }),
    body('schoolId').notEmpty(),
    // body('subjects').notEmpty()
], registerTeacher);

// Login student
router.post('/loginTeacher', [
    body('email').isString().notEmpty(),
    body('password').isLength({ min: 6 })
], loginTeacher);

// Google registration/login
router.post('/google-auth', googleAuth);

router.get('/verify-email', verifyUserEmail);

router.post('/resend-verification-email', resendVerificationEmail);

export default router;
