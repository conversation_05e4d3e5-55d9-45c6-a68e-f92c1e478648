import { uploadImage, uploadMultipleImages, uploadProfilePhoto, getProfilePhotoPresignedUrl } from "../controllers/imageUploadController.js";
import express from 'express';

const router = express.Router();

router.post('/uploadProfilePhoto', uploadProfilePhoto);
router.post('/uploadSingleImage', uploadImage);
router.post('/uploadMultipleImages', uploadMultipleImages);
router.post('/getProfilePhotoPresignedUrl', getProfilePhotoPresignedUrl);

export default router;