import express from 'express';
import {
  getChatbotResponse,
  getChatbotResponseWithMCP,
} from '../controllers/teachingAssistantController.js';

const router = express.Router();

// Get chatbot response (traditional endpoint for conversational AI)
router.post('/chat', getChatbotResponse);

// Get enhanced chatbot response with MongoDB MCP integration
// Now handles both student and teacher requests automatically
router.post('/chat-mcp', getChatbotResponseWithMCP);

export default router;