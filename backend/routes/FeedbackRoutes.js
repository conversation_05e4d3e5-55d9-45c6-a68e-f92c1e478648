import { Router } from 'express';
import { body } from 'express-validator';

import { feedbackController, loadFeed } from '../controllers/feedbackController.js';

const router = Router();

router.post("/feed", [
  // body('comment').isString().notEmpty(),
  // body('type').isString().notEmpty().isIn(['like', 'dislike', 'bug']),
  // body('id').isString().notEmpty(),
  // body('role').isString().notEmpty().isIn(['Student', 'Teacher']),
  // body('images').notEmpty()
], feedbackController);

router.get("/feedbacks", loadFeed);


export default router;
