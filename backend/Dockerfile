# First stage: Node application build
FROM node:current-alpine3.20 AS app-build

# Create app directory with the right permissions
RUN mkdir -p /home/<USER>/app/node_modules && \
    chown -R node:node /home/<USER>/app

WORKDIR /home/<USER>/app

# Copy only package.json and package-lock.json to install dependencies
COPY --chown=node:node package*.json ./

# Switch to the non-root user
USER node

# Install dependencies
RUN npm install --no-package-lock

COPY --chown=node:node . .

# Second stage: Nginx
FROM nginx:latest

# Install Node.js, Python and pip in the Nginx image
RUN apt-get update && apt-get install -y \
    nodejs \
    npm \
    python3 \
    python3-pip \
    python3-venv
    
RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
# Install Python packages
COPY requirements.txt /requirements.txt
RUN pip install -r /requirements.txt

# Create necessary directories
RUN mkdir -p /home/<USER>/app/certs /home/<USER>/app/private

# Copy application code from the first stage
COPY --from=app-build /home/<USER>/app /home/<USER>/app

# Copy Nginx configuration and certificates
COPY ./nginx/nginx.conf /etc/nginx/conf.d/default.conf
COPY ./nginx/certs/nginx-selfsigned.pem /home/<USER>/app/certs/nginx-selfsigned.pem
COPY ./nginx/private/nginx-selfsigned.key /home/<USER>/app/private/nginx-selfsigned.key

# Copy and set permissions for startup script
COPY --chown=node:node start.sh /start.sh
RUN chmod +x /start.sh

EXPOSE 443

CMD ["/start.sh"]