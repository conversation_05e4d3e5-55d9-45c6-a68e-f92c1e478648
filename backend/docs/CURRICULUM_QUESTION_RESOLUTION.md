# Enhanced Curriculum and Question Resolution System

## Overview

The AegisAI chat system has been significantly enhanced to resolve raw nodeids and question IDs to meaningful content, providing students and teachers with contextual curriculum information instead of just displaying database identifiers. The system now includes intelligent question collection prioritization and dynamic model handling.

## Key Features

### 1. Curriculum Node Resolution
- **Before**: Raw nodeids like `ObjectId("507f1f77bcf86cd799439011")`
- **After**: Meaningful content with topic names, descriptions, and hierarchical relationships

### 2. Enhanced Question Resolution with Collection Prioritization
- **Before**: Raw question IDs with no context
- **After**: Complete question content including text, options, answers, and explanations
- **Smart Collection Selection**: Prioritizes subject/class-specific question collections
- **Dynamic Model Handling**: Uses appropriate Question model collections based on context

### 3. Question Source Prioritization
- **Priority 1**: Dynamic Question model collections (e.g., `questionBank_class10_mathematics`)
- **Priority 2**: Subject variations and fallback collections
- **Excluded**: AIQuestion and QuestionTemp models (used for different purposes)

### 4. Privacy Protection Maintained
- Teacher access restrictions to student chat conversations remain intact
- All existing privacy protections are preserved

## Technical Implementation

### Enhanced Collections Support

The `mongoMcpService.js` now supports additional collections:

```javascript
// New collections added
case 'curriculumNodes':
  // Dynamic curriculum node model based on subject
  model = createKnowledgeGraphModel(subject);
  break;
case 'questions':
  // Dynamic question model based on subject/class
  model = createQuestionModel(collectionName);
  break;
case 'aiQuestions':
  model = AIQuestion;
  break;
case 'questionTemp':
  model = QuestionTemp;
  break;
```

### New Methods Added

#### `resolveCurriculumNodes(curriculumProgress, subject)`
Resolves curriculum nodeids to meaningful content:
- Topic names and descriptions
- Hierarchical relationships (Chapter > Subtopic)
- Learning status and proficiency
- Parent-child relationships in curriculum

#### `resolveQuestions(questionIds, subject, studentClass, specificCollection)`
Resolves question IDs to complete question content with intelligent collection prioritization:
- **Smart Collection Selection**: Automatically determines the best question collections to search
- **Class-Aware Resolution**: Uses student's class level to prioritize appropriate collections
- **Subject Variations**: Handles different subject naming conventions (math/mathematics/maths)
- **Priority-Based Search**: Searches collections in order of relevance
- **Complete Question Data**: Returns question text, options, answers, explanations, topics, difficulty, etc.

#### `generateQuestionCollectionNames(subject, studentClass, specificCollection)`
Generates prioritized list of question collections to search:
- **Priority 1**: Specific collection if provided
- **Priority 2**: Exact class and subject match (e.g., `questionBank_class10_mathematics`)
- **Priority 3**: Subject variations for the same class
- **Priority 4**: Default class collections with subject variations
- **Deduplication**: Removes duplicate collections while preserving priority order

#### `extractQuestionIdsFromTestHistory(detailedTestHistory, gradedResults)`
Extracts question IDs from various test history sources:
- Test questions arrays
- Response objects with question references
- Answer sheets from graded results
- Question bank references

#### `buildHierarchicalPath(node, nodeMap)`
Creates readable hierarchical paths:
- `"Chapter: Algebra > Subtopic: Linear Equations"`
- `"Chapter: Geometry"`

## Enhanced User Context

### Student Context Enhancement
```javascript
// New fields added to user context
userContext.resolvedCurriculumNodes = [
  {
    nodeId: "507f1f77bcf86cd799439011",
    status: "In Progress",
    proficiency: 75,
    resolvedContent: {
      name: "Linear Equations",
      description: "Solving equations with one variable",
      type: "Subtopic",
      hierarchicalPath: "Chapter: Algebra > Subtopic: Linear Equations"
    }
  }
];

userContext.resolvedQuestions = [
  {
    questionId: "507f1f77bcf86cd799439012",
    source: "questionBank_class10_mathematics",
    priority: 2,
    resolvedContent: {
      question: "Solve for x: 2x + 5 = 13",
      options: ["x = 4", "x = 6", "x = 8", "x = 9"],
      answer: "x = 4",
      solution: "Subtract 5 from both sides: 2x = 8, then divide by 2: x = 4",
      topic: "Linear Equations",
      metadata: {
        collectionSource: "questionBank_class10_mathematics",
        difficulty: 0.6,
        bloomTaxonomy: "Application"
      }
    }
  }
];
```

### Teacher Context Enhancement
```javascript
// Teachers get resolved content for class-wide analysis
teacherContext.resolvedCurriculumNodes = [...];
teacherContext.resolvedQuestions = [...];
```

## AI Prompt Enhancement with Privacy Guidelines

### Privacy and Response Guidelines (CRITICAL)
All AI responses now include comprehensive privacy protection:

**Privacy Protection:**
- NEVER reveal private information: email addresses, phone numbers, real names, addresses, personal identifiers
- NEVER display raw database IDs, ObjectIds, or internal system identifiers
- NEVER expose technical metadata: device info, browser details, IP addresses, session tokens
- Always use resolved meaningful content (topic names, question text) instead of raw nodeids or question IDs

**Response Style:**
- Provide concise, focused answers that directly address the user's question
- Use clear, educational language appropriate for the user's level
- Avoid lengthy explanations unless specifically requested

**Interaction Boundaries:**
- NEVER provide unsolicited advice unless explicitly asked for recommendations
- NEVER be pushy about study methods, learning paths, or academic choices
- Respond to what is asked and let the user guide the conversation
- Frame help as optional: "Would you like me to..." rather than "You should..."

### Updated Instructions for Students
```
PRIVACY AND RESPONSE GUIDELINES (CRITICAL):
- NEVER reveal private information or display raw database IDs/ObjectIds
- Always use resolved meaningful content (topic names, question text)
- Provide concise, focused answers
- NEVER be pushy; frame help as optional

INSTRUCTIONS:
- Reference specific topics using resolved curriculum node names and hierarchical paths
- When discussing questions, use actual question content from resolvedQuestions, never raw IDs
- Keep responses conversational, educational, and privacy-compliant
```

### Updated Instructions for Teachers
```
PRIVACY AND RESPONSE GUIDELINES (CRITICAL):
- NEVER reveal private student information or display raw database IDs
- Always use resolved meaningful content instead of raw nodeids or question IDs
- Focus on educational insights, not technical system details

AVAILABLE DATA INCLUDES:
- Detailed test history with resolved question content and meaningful topic names
- Resolved curriculum nodes with educational topic names and descriptions
- Actual question text, options, answers, and explanations from resolvedQuestions
- Privacy-compliant educational insights focused on learning outcomes
```

## Usage Examples (Privacy-Compliant)

### Student Query: "What topics am I struggling with?"
**Before Enhancement**: "You're struggling with nodeids 507f1f77bcf86cd799439011 and 507f1f77bcf86cd799439012"

**After Enhancement (Privacy-Compliant)**: "You're struggling with Linear Equations (Chapter: Algebra) and Quadratic Functions (Chapter: Advanced Algebra). Would you like me to help you focus on the fundamentals of solving linear equations first?"

### Student Query: "How did I do on my recent test?"
**Before Enhancement**: "You answered question ObjectId('507f1f77bcf86cd799439011') incorrectly."

**After Enhancement (Privacy-Compliant)**: "In your recent test, you correctly answered the question about monetary policy tools but had difficulty with the question: 'Which of the following is the primary tool used by the Reserve Bank of India for monetary policy implementation?' Would you like me to explain the different monetary policy instruments?"

### Teacher Query: "Which students need help with algebra?"
**Before Enhancement**: "Students with low proficiency in nodeid 507f1f77bcf86cd799439011"

**After Enhancement (Privacy-Compliant)**: "3 students are struggling with Linear Equations (Chapter: Algebra). Recent test performance shows difficulty with questions like 'Solve for x: 2x + 5 = 13' and word problems involving linear relationships. Would you like specific recommendations for differentiated instruction?"

### Privacy Protection Examples:
**❌ NEVER Display**:
- Raw IDs: "ObjectId('507f1f77bcf86cd799439011')"
- Personal info: "<EMAIL> scored 75%"
- Technical metadata: "Device: iPhone, Browser: Safari"

**✅ ALWAYS Display**:
- Meaningful content: "Linear Equations (Chapter: Algebra)"
- Educational insights: "Recent test performance in monetary policy"
- Privacy-safe references: "In your recent test" or "3 students are struggling"

## Testing

Run the curriculum resolution test:
```bash
node test/curriculumResolutionTest.js
```

This test verifies:
- Curriculum node resolution functionality
- Question resolution across multiple collections
- Question ID extraction from test history
- Enhanced user knowledge graph generation
- Collection support for new data models

## Question Collection Prioritization Strategy

The system uses an intelligent prioritization strategy to find questions in the most relevant collections:

### Priority Levels

1. **Priority 1 (Highest)**: Specific collection if provided
   - Direct collection name specified by the caller

2. **Priority 2**: Exact class and subject match
   - `questionBank_class10_mathematics` for Class 10 Mathematics

3. **Priority 3**: Subject variations for the same class
   - `questionBank_class10_math`
   - `questionBank_class10_maths`

4. **Priority 4**: Default class collections
   - Tries common classes (10, 12, 11, 9) with subject variations
   - `questionBank_class12_mathematics`, `questionBank_class11_math`, etc.

### Subject Variation Mapping

The system handles common subject naming variations:
- `mathematics` ↔ `math` ↔ `maths`
- `physics` ↔ `phy`
- `chemistry` ↔ `chem`
- `computer` ↔ `cs` ↔ `computerscience`
- `social` ↔ `socialscience` ↔ `sst`

### Collection Exclusions

- **AIQuestion**: Used for AI-generated questions, not for test history resolution
- **QuestionTemp**: Used for temporary question storage, not for recommendations

## Benefits

1. **Better User Experience**: Students see meaningful topic names instead of database IDs
2. **Contextual Learning**: AI can reference actual question content and curriculum structure
3. **Improved Analytics**: Teachers get insights with resolved content for better decision-making
4. **Maintained Privacy**: All existing privacy protections remain intact
5. **Scalable Architecture**: Dynamic model support for different subjects and question collections
6. **Intelligent Question Resolution**: Prioritizes the most relevant question collections
7. **Class-Aware Context**: Uses student's class level for better question matching

## Future Enhancements

- Add caching for frequently resolved nodes and questions
- Implement batch resolution for better performance
- Add support for multimedia content resolution
- Enhance hierarchical path building with more complex relationships
