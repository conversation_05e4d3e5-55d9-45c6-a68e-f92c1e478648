import mongoose from 'mongoose';
import { createSchool, createClass, createStudent, createTeacher } from '../services/adminService.js';

const FIELD_MAP = {
    admissionNumber: [
        'ADMISSION NO.', 'ADMISSION NUMBER', 'ADMISSION_NO', 'ADMISSION_NO.', 'ADMISS<PERSON>', 'ADMI<PERSON><PERSON>N<PERSON>', 'ADMISSIONNO.'
    ],
    firstName: [
        'FIRST NAME', 'FIRSTNAME', 'FIRST_NAME', 'FNAME', 'F_NAME'
    ],
    lastName: [
        'LAST NAME', 'LASTNAME', 'LAST_NAME', 'LNAME', 'L_NAME', 'SURNAME'
    ],
    studentName: [
        "STUDENT'S NAME", 'STUDENT NAME', 'NAME', 'STUDENT', 'STUDENTNAME', 'STUDENT_NAME'
    ],
    schoolName: [
        'SCHOOL NAME', 'SCHOOL', 'SCHO<PERSON><PERSON><PERSON>', 'SCHOOL_NAME'
    ],
    schoolAddress: [
        'SCHOOL ADDRESS', 'ADDRESS', 'SCHOOLADDRESS', 'SCHOOL_ADDRESS'
    ],
    className: [
        'CLASS', 'CLASS NAME', 'CLASSNAME', 'CLASS_NAME'
    ],
    academicSession: [
        'ACADEMIC SESSION', 'SESSION', 'ACADEMICSESSION', 'ACADEMIC_SESSION'
    ],
    classEducator: [
        'CLASS EDUCATOR', 'EDUCATOR', 'TEACHER', 'CLASS TEACHER', 'CLASSTEACHER', 'CLASS_EDUCATOR'
    ],
    subject: [
        'SUBJECT', 'SUBJECTS', 'SUBJECT NAME', 'SUBJECTNAME', 'SUBJECT_NAME'
    ]
};

function mapUserFields(user) {
    const mapped = {};
    const foundKeys = new Set();
    for (const [internal, possibleNames] of Object.entries(FIELD_MAP)) {
        for (const name of possibleNames) {
            // Case-insensitive, ignore punctuation
            const foundKey = Object.keys(user).find(
                k => k.replace(/[^a-zA-Z0-9]/g, '').toLowerCase() === name.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()
            );
            if (foundKey) {
                if (foundKeys.has(foundKey)) {
                    // Duplicate field detected
                    mapped[`DUPLICATE_${internal}`] = user[foundKey];
                } else {
                    mapped[internal] = user[foundKey];
                    foundKeys.add(foundKey);
                }
                break;
            }
        }
    }
    return mapped;
}

function validateUser(mappedUser) {
    // Check if we have either firstName/lastName OR studentName
    const hasFirstLastName = mappedUser.firstName && mappedUser.lastName;
    const hasStudentName = mappedUser.studentName;

    if (!hasFirstLastName && !hasStudentName) {
        return ['firstName and lastName OR studentName'];
    }

    const required = ['admissionNumber', 'schoolName', 'schoolAddress', 'className'];
    const missing = required.filter(f => mappedUser[f] == null || mappedUser[f].toString().trim() === '');
    return missing;
}

export const createUsers = async (req, res) => {
    const session = await mongoose.startSession();
    try {
        const { users, classStd, subject, subjectTeacher, subjectTeacherEmail } = req.body;
        if (!Array.isArray(users) || users.length === 0) {
            return res.status(400).json({ message: 'Invalid input' });
        }

        const mappedUsers = users.map(mapUserFields);
        const errors = [];
        mappedUsers.forEach((u, idx) => {
            const missing = validateUser(u);
            if (missing.length) {
                errors.push({ row: idx + 1, missingFields: missing });
            }
        });
        if (errors.length) {
            return res.status(400).json({ message: 'Validation failed', errors });
        }

        let result;
        await session.withTransaction(async () => {
            // Create school (assume all users are from the same school for now)
            const school = await createSchool(
                mappedUsers[0].schoolName,
                mappedUsers[0].schoolAddress,
                session
            );
            if (!school) {
                console.error('School creation failed');
            }

            // Create teacher (assume one teacher for all classes in this upload)
            const teacher = await createTeacher(
                subjectTeacher,
                subjectTeacherEmail,
                school.schoolCode,
                session
            );
            if (!teacher) {
                console.error('Teacher creation failed');
            }

            // Group users by className
            const classGroups = {};
            mappedUsers.forEach(user => {
                const key = user.className;
                if (!classGroups[key]) classGroups[key] = [];
                classGroups[key].push(user);
            });

            const createdClasses = [];
            const allStudents = [];

            for (const [className, usersInClass] of Object.entries(classGroups)) {
                // Create class for each unique className
                const newClass = await createClass(
                    school.schoolCode,
                    className,
                    subject,
                    classStd,
                    teacher.id,
                    session
                );
                if (!newClass) {
                    console.error(`Class creation failed for class: ${className}`);
                }
                createdClasses.push(newClass);

                // Create students for this class
                const students = await Promise.all(usersInClass.map(async user => {
                    // Use firstName/lastName if available, otherwise fall back to studentName
                    let firstName, lastName;
                    if (user.firstName && user.lastName) {
                        firstName = user.firstName;
                        lastName = user.lastName;
                    } else if (user.studentName) {
                        // Split studentName into firstName and lastName
                        const nameParts = user.studentName.trim().split(/\s+/);
                        firstName = nameParts[0];
                        lastName = nameParts.slice(1).join(' ') || nameParts[0]; // Use first name as last name if only one word
                    }

                    return await createStudent(
                        firstName,
                        lastName,
                        user.admissionNumber,
                        newClass.classCode,
                        school.schoolCode,
                        session
                    );
                }));
                if (!students) {
                    console.error(`Student creation failed for class: ${className}`);
                }
                allStudents.push(...students);
            }

            result = {
                message: 'Users created successfully',
                credentials: {
                    teacher,
                    students: allStudents,
                    classes: createdClasses
                }
            };
        });
        session.endSession();
        return res.status(200).json(result);
    } catch (error) {
        session.endSession();
        console.error('Error creating users:', error);
        return res.status(500).json({ message: error.message || 'Internal server error' });
    }
}
