import StudentCurriculumService from '../services/studentCurriculumService.js';

class StudentCurriculumController {
  /**
   * Initialize curriculum progress for a student
   */
  async initialize(req, res) {
    try {
      const { studentId, className, subject } = req.params; // Extract student ID from request params
      const studentCurriculum = await StudentCurriculumService.initializeStudentCurriculum(studentId, className, subject);
      
      res.status(201).json({
        message: 'Curriculum initialized successfully',
        data: studentCurriculum,
      });
    } catch (error) {
      res.status(500).json({
        message: 'Error initializing curriculum',
        error: error.message,
      });
    }
  }

  /**
   * Update progress for a specific curriculum node
   */
  async updateProgress(req, res) {
    try {
      const { studentId, nodeId } = req.body; // Extract student ID and node ID from request body
      const updatedProgress = await StudentCurriculumService.updateNodeProgress(studentId, nodeId);

      res.status(200).json({
        message: 'Curriculum node progress updated successfully',
        data: updatedProgress,
      });
    } catch (error) {
      res.status(500).json({
        message: 'Error updating curriculum progress',
        error: error.message,
      });
    }
  }

  /**
   * Retrieve curriculum progress for a student
   */
  async getProgress(req, res) {
    try {
      const { studentId } = req.params; // Extract student ID from request params
      const progress = await StudentCurriculumService.getStudentProgress(studentId);

      res.status(200).json({
        message: 'Student curriculum progress retrieved successfully',
        data: progress,
      });
    } catch (error) {
      res.status(500).json({
        message: 'Error retrieving curriculum progress',
        error: error.message,
      });
    }
  }
}

export default new StudentCurriculumController();
