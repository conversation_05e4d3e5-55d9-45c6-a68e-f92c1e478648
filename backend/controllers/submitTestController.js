import {
    getUserSubject,
    getQuestion,
    updateUserProficiency,
    getTestConfig,
    getActiveUserCount,
    getNextQuestion,
    processBatchTest,
    calculateOverallProficiency,
    updateSubskillProficiencies,
    evaluateResponses
} from '../services/testService.js';
import { updateSubjectOverallProficiency } from '../services/proficiencyCalculationService.js';
import { UAParser } from 'ua-parser-js';
import { getCollectionNameTest } from '../services/testService.js';
import Student from '../models/Student.js';
import TestHistory from '../models/TestHistory.js';
import { updateClassAnalytics } from '../services/classAnalyticsService.js';
import { updateStudentAnalytics } from '../services/studentAnalyticsService.js';

// Controller function to handle test submission (real-time or batch processing)
export const submitTest = async (req, res) => {
    // const { userId: userId, testId: testId, responses: oldResponses } = req.body;   //response = if response is correct or not
    const { userId, testId, responses: oldResponses, startTime, endTime, subject } = req.body;

    const collectionName = await getCollectionNameTest(testId, subject);
    // console.error("[INFO] Collection name for test:", collectionName);
    const responses = await evaluateResponses(collectionName, oldResponses);
    // console.error("Evaluated responses:", responses);
    try {
        const testConfig = await getTestConfig(testId);
        // console.error("Got testConfig: ", JSON.stringify(testConfig));
        const activeUsers = await getActiveUserCount(testId);


        if (testConfig.isAdaptive && activeUsers <= testConfig.maxUsersForRealTime) {
            let allQuestionSubskills = []; // To track all subskills processed
            let subskillProficiencyMap = {}; // To map subskills to their proficiencies

            // Process each response and update subskill proficiencies
            for (const { questionId, response } of responses) {
                let currentSubskillsProficiencies = {};
                const question = await getQuestion(questionId, collectionName);
                // console.error("[INFO] Processing question:", question);
                const currentQuestionSubskills = question.subtopic?.split(',').map(s => s.trim());
                const userSubjectData = await getUserSubject(userId, subject);

                // console.error("userSubjectData:", userSubjectData);
                if (question) {
                    allQuestionSubskills = allQuestionSubskills.concat(currentQuestionSubskills);
                    // console.error("Curr question subskills:", currentQuestionSubskills);
                }

                // console.error("currentQuestionSubskills:", currentQuestionSubskills);
                // Find proficiencies for the current question's subskills
                currentQuestionSubskills.forEach((subskill) => {
                    const matchingProgress = userSubjectData?.knowledgeGraphId?.curriculumProgress?.find((progress) => {
                        const sortedNodeSkills = progress.nodeId.name.split(',').map(s => s.trim()).sort();
                        return JSON.stringify(sortedNodeSkills) === JSON.stringify([subskill].sort());
                    });

                    // Store the proficiency in a map
                    subskillProficiencyMap[subskill] = matchingProgress ? matchingProgress.proficiency : 0;
                    currentSubskillsProficiencies[subskill] = matchingProgress ? matchingProgress.proficiency : 0;
                    // console.error(`Proficiency for ${subskill}:`, subskillProficiencyMap[subskill]);
                });
                // Update proficiencies for the current question
                let updatedProficiencies = {};
                if (!testConfig.diagnosticTest) {
                    updatedProficiencies = updateSubskillProficiencies(
                        { ...currentSubskillsProficiencies }, // Pass a copy of the map
                        question,
                        response
                    );
                } else {
                    updatedProficiencies = updateSubskillProficiencies(
                        { ...currentSubskillsProficiencies },
                        question,
                        response,
                        2
                    );
                }

                // Update the map with new proficiencies
                currentQuestionSubskills.forEach((subskill) => {
                    subskillProficiencyMap[subskill] = updatedProficiencies?.currentProficiencies[subskill] || subskillProficiencyMap[subskill];
                });

                // console.error("Updated subskill proficiencies:", subskillProficiencyMap);
            }

            // Parse device and browser from user-agent
            const ua = UAParser(req.headers['user-agent']);

            const calculatedScore = responses.reduce((sum, r) => sum + (r.response ? 1 : 0), 0);
            const proficiencyBefore = 0.5;
            const proficiencyAfter = 0.6;  // Example value

            // Build the attempted test record
            const attemptedTestRecord = {
                testId,
                startTime: startTime,
                endTime: endTime,
                totalTimeTaken: Math.floor((new Date(endTime) - new Date(startTime)) / 1000),
                responses: responses.map((res, index) => ({
                    questionId: res.questionId,
                    selectedAnswer: oldResponses[index].selectedAnswer,
                    isCorrect: res.response,
                    intuition: oldResponses[index].intuition || '',
                    timeSpent: oldResponses[index].timeSpent
                })),
                totalScore: calculatedScore,
                proficiencyBefore,
                proficiencyAfter,
                metadata: {
                    device: ua.device.type ? `${ua.device.vendor} ${ua.device.model}` : 'Desktop',
                    browser: `${ua.browser.name} ${ua.browser.version}`,
                    ipAddress: req.ip || req.connection.remoteAddress
                },
                flaggedForReview: false
            };

            // console.error("Attempted test record:", attemptedTestRecord);

            // Again, assume the test corresponds to a subject identified by its name.
            const subjectIdentifier = testConfig.subjectName;  // e.g., "Mathematics"

            // Use an atomic update to push the record into the correct subject's attemptedTests array.
            const result = await Student.findOneAndUpdate(
                {
                    _id: userId,
                    "subjects.subjectName": { $regex: `^${subject}$`, $options: 'i' }
                },
                { $push: { "subjects.$.attemptedTests": attemptedTestRecord } },
                { new: true }
            );
            // console.error("Attempted test record added to student:", result);

            if (!result) {
                return res.status(404).json({ success: false, message: 'Student or subject not found' });
            }

            // Update student analytics with test data
            const studentAnalyticsUpdate = updateStudentAnalytics(userId, responses, attemptedTestRecord, subskillProficiencyMap, subject);
            // console.error("Student analytics update:", studentAnalyticsUpdate);

            // Update class analytics with test data
            const testData = {
                responses: responses,
                testId: testId,
                totalScore: calculatedScore,
                totalTimeTaken: attemptedTestRecord.totalTimeTaken
            };
            const classId = await TestHistory.findById(testId).select('class');
            // console.error("Class ID:", classId);
            const classAnalyticsUpdate = updateClassAnalytics(classId.class, testData, userId, subskillProficiencyMap);
            // console.error("Class analytics update:", classAnalyticsUpdate); 

            // Calculate overall proficiency using the final proficiencies
            const overallProficiency = calculateOverallProficiency(
                { ...subskillProficiencyMap },
                allQuestionSubskills
            );
            // console.error("Overall proficiency:", overallProficiency);

            // Update both subskill and overall proficiencies
            // console.error("subject", subject)
            await updateUserProficiency(userId, {
                subskills: { ...subskillProficiencyMap },
                // overall: overallProficiency
            }, subject);

            // Ensure overall proficiency is recalculated after test submission
            await updateSubjectOverallProficiency(userId, subject, {
                weightingStrategy: 'nodeType',
                topicWeight: 1.2,
                subtopicWeight: 1.0
            });

            // Get next question based on updated proficiencies
            // const nextQuestion = await getNextQuestion({
            //     overall: overallProficiency,
            //     subskills: currentProficiencies
            // });

            return res.json({
                success: true,
                message: "Adaptive test submitted successfully.",
                newProficiency: {
                    // overall: overallProficiency,
                    subskills: subskillProficiencyMap
                },

                // nextQuestion
            });

        } else if (testConfig.allowBatchProcessing) {
            // Batch processing
            const newProficiency = await processBatchTest(userId, responses, subject);

            return res.json({
                success: true,
                message: "Batch test submitted successfully.",
                newProficiency
            });

        } else {
            return res.status(400).json({
                success: false,
                message: 'Invalid test configuration or too many active users.'
            });
        }
    } catch (error) {
        console.error("Error processing test submission:", error);
        if (!res.headersSent) {
            return res.status(500).json({
                success: false,
                message: 'Server error during test submission'
            });
        }
    }
};
