import { jest } from '@jest/globals';

// Test for null reference handling in student subjects
describe('Null Reference Handling Tests', () => {
    test('should handle null student subjects gracefully', () => {
        // Simulate the scenario that was causing the error
        const student = null;

        // This is what was happening before the fix
        expect(() => {
            if (student && student.subjects) {
                const matchingSubject = student.subjects.find(
                    subject => subject.subjectName.toLowerCase() === 'mathematics'
                );
            }
        }).not.toThrow();
    });

    test('should handle student with null subjects array', () => {
        const student = {
            _id: 'studentId1',
            subjects: null
        };

        // This should not throw an error with our fix
        expect(() => {
            if (student && student.subjects && Array.isArray(student.subjects)) {
                const matchingSubject = student.subjects.find(
                    subject => subject.subjectName.toLowerCase() === 'mathematics'
                );
            }
        }).not.toThrow();
    });

    test('should handle student with undefined subjects', () => {
        const student = {
            _id: 'studentId1'
            // subjects is undefined
        };

        // This should not throw an error with our fix
        expect(() => {
            if (student && student.subjects && Array.isArray(student.subjects)) {
                const matchingSubject = student.subjects.find(
                    subject => subject.subjectName.toLowerCase() === 'mathematics'
                );
            }
        }).not.toThrow();
    });

    test('should handle valid student with subjects correctly', () => {
        const student = {
            _id: 'studentId1',
            subjects: [
                { subjectName: 'Mathematics', testHistory: [] },
                { subjectName: 'Science', testHistory: [] }
            ]
        };

        let matchingSubject = null;
        expect(() => {
            if (student && student.subjects && Array.isArray(student.subjects)) {
                matchingSubject = student.subjects.find(
                    subject => subject.subjectName.toLowerCase() === 'mathematics'
                );
            }
        }).not.toThrow();

        expect(matchingSubject).toBeTruthy();
        expect(matchingSubject.subjectName).toBe('Mathematics');
    });
});

// Test the test activation logic
describe('Test Activation Logic', () => {
    beforeEach(() => {
        jest.clearAllMocks();
        // Mock current time to a fixed date for consistent testing
        jest.useFakeTimers();
        jest.setSystemTime(new Date('2024-01-15T10:00:00Z')); // 10 AM UTC
    });

    afterEach(() => {
        jest.useRealTimers();
    });

    describe('Test should be active based on date and start time', () => {
        test('Test scheduled for future date should be active', () => {
            const testDate = new Date('2024-01-16'); // Tomorrow
            const startTime = '09:00'; // 9 AM
            const currentDate = new Date(); // 2024-01-15T10:00:00Z

            // Combine test date with start time
            let testStartDateTime = new Date(testDate);
            if (startTime) {
                const [hours, minutes] = startTime.split(':');
                testStartDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
            }

            const shouldBeActive = testStartDateTime > currentDate;
            expect(shouldBeActive).toBe(true);
        });

        test('Test scheduled for today but later time should be active', () => {
            const testDate = new Date('2024-01-15T00:00:00Z'); // Today at midnight UTC
            const startTime = '15:00'; // 3 PM (5 hours from now)
            const currentDate = new Date(); // 2024-01-15T10:00:00Z

            // Combine test date with start time
            let testStartDateTime = new Date(testDate);
            if (startTime) {
                const [hours, minutes] = startTime.split(':');
                testStartDateTime.setUTCHours(parseInt(hours), parseInt(minutes), 0, 0);
            }

            const shouldBeActive = testStartDateTime > currentDate;
            expect(shouldBeActive).toBe(true);
        });

        test('Test scheduled for today but earlier time should be inactive', () => {
            const testDate = new Date('2024-01-15T00:00:00Z'); // Today at midnight UTC
            const startTime = '08:00'; // 8 AM (2 hours ago)
            const currentDate = new Date(); // 2024-01-15T10:00:00Z

            // Combine test date with start time
            let testStartDateTime = new Date(testDate);
            if (startTime) {
                const [hours, minutes] = startTime.split(':');
                testStartDateTime.setUTCHours(parseInt(hours), parseInt(minutes), 0, 0);
            }

            const shouldBeActive = testStartDateTime > currentDate;
            expect(shouldBeActive).toBe(false);
        });

        test('Test scheduled for past date should be inactive', () => {
            const testDate = new Date('2024-01-14'); // Yesterday
            const startTime = '15:00'; // 3 PM
            const currentDate = new Date(); // 2024-01-15T10:00:00Z

            // Combine test date with start time
            let testStartDateTime = new Date(testDate);
            if (startTime) {
                const [hours, minutes] = startTime.split(':');
                testStartDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
            }

            const shouldBeActive = testStartDateTime > currentDate;
            expect(shouldBeActive).toBe(false);
        });

        test('Test without start time should use date only', () => {
            const testDate = new Date('2024-01-16'); // Tomorrow
            const startTime = null;
            const currentDate = new Date(); // 2024-01-15T10:00:00Z

            // Combine test date with start time
            let testStartDateTime = new Date(testDate);
            if (startTime) {
                const [hours, minutes] = startTime.split(':');
                testStartDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
            }

            const shouldBeActive = testStartDateTime > currentDate;
            expect(shouldBeActive).toBe(true);
        });

        test('Test at exact current time should be inactive', () => {
            const testDate = new Date('2024-01-15T00:00:00Z'); // Today at midnight UTC
            const startTime = '10:00'; // Exactly current time
            const currentDate = new Date(); // 2024-01-15T10:00:00Z

            // Combine test date with start time
            let testStartDateTime = new Date(testDate);
            if (startTime) {
                const [hours, minutes] = startTime.split(':');
                testStartDateTime.setUTCHours(parseInt(hours), parseInt(minutes), 0, 0);
            }

            const shouldBeActive = testStartDateTime > currentDate;
            expect(shouldBeActive).toBe(false); // Should be false because it's not > current time
        });
    });

    describe('Edge cases for time handling', () => {
        test('Test with start time just 1 minute in future should be inactive (due to 2-minute buffer)', () => {
            const testDate = new Date('2024-01-15T00:00:00Z'); // Today at midnight UTC
            const startTime = '10:01'; // 1 minute from now
            const currentDate = new Date(); // 2024-01-15T10:00:00Z

            let testStartDateTime = new Date(testDate);
            if (startTime) {
                const [hours, minutes] = startTime.split(':');
                testStartDateTime.setUTCHours(parseInt(hours), parseInt(minutes), 0, 0);
            }

            // Apply the same 2-minute buffer logic as in the backend
            const bufferTime = 2 * 60 * 1000; // 2 minutes in milliseconds
            const currentDateWithBuffer = new Date(currentDate.getTime() + bufferTime);
            const shouldBeActive = testStartDateTime > currentDateWithBuffer;

            expect(shouldBeActive).toBe(false); // Should be false due to 2-minute buffer
        });

        test('Test with start time 5 minutes in future should be active', () => {
            const testDate = new Date('2024-01-15T00:00:00Z'); // Today at midnight UTC
            const startTime = '10:05'; // 5 minutes from now
            const currentDate = new Date(); // 2024-01-15T10:00:00Z

            let testStartDateTime = new Date(testDate);
            if (startTime) {
                const [hours, minutes] = startTime.split(':');
                testStartDateTime.setUTCHours(parseInt(hours), parseInt(minutes), 0, 0);
            }

            // Apply the same 2-minute buffer logic as in the backend
            const bufferTime = 2 * 60 * 1000; // 2 minutes in milliseconds
            const currentDateWithBuffer = new Date(currentDate.getTime() + bufferTime);
            const shouldBeActive = testStartDateTime > currentDateWithBuffer;

            expect(shouldBeActive).toBe(true); // Should be true as 5 minutes > 2 minute buffer
        });

        test('Test with start time just 1 minute in past should be inactive', () => {
            const testDate = new Date('2024-01-15T00:00:00Z'); // Today at midnight UTC
            const startTime = '09:59'; // 1 minute ago
            const currentDate = new Date(); // 2024-01-15T10:00:00Z

            let testStartDateTime = new Date(testDate);
            if (startTime) {
                const [hours, minutes] = startTime.split(':');
                testStartDateTime.setUTCHours(parseInt(hours), parseInt(minutes), 0, 0);
            }

            const shouldBeActive = testStartDateTime > currentDate;
            expect(shouldBeActive).toBe(false);
        });
    });
});
