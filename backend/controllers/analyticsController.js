import analyticsService from '../services/analyticsService.js';

class AnalyticsController {
    // Test endpoint to verify analytics system is working
    async test(req, res) {
        try {
            console.log('Analytics test endpoint hit');
            res.json({
                success: true,
                message: 'Analytics system is working!',
                timestamp: new Date()
            });
        } catch (error) {
            console.error('Analytics test error:', error);
            res.status(500).json({ error: 'Analytics test failed' });
        }
    }

    // Create or start a new user session
    async startSession(req, res) {
        try {
            console.log('Analytics Controller: Received session start request:', req.body);

            const sessionData = {
                userId: req.body.userId,
                userType: req.body.userType,
                sessionId: req.body.sessionId,
                userAgent: req.body.userAgent || req.get('User-Agent'),
                screenResolution: req.body.screenResolution,
                timezone: req.body.timezone,
                ipAddress: req.body.ipAddress || req.ip,
                location: req.body.location || {}
            };

            console.log('Analytics Controller: Processed session data:', sessionData);

            const session = await analyticsService.createSession(sessionData);
            console.log('Analytics Controller: Session created successfully');
            
            res.status(201).json({ 
                success: true, 
                sessionId: session.sessionId 
            });
        } catch (error) {
            console.error('Analytics Controller: Error creating session:', error);
            res.status(500).json({ 
                error: 'Failed to create session', 
                details: error.message 
            });
        }
    }

    // End a user session
    async endSession(req, res) {
        try {
            const session = await analyticsService.endSession(req.body.sessionId);
            res.json({ 
                success: true, 
                duration: session?.duration 
            });
        } catch (error) {
            console.error('Analytics Controller: Error ending session:', error);
            res.status(500).json({ error: 'Failed to end session' });
        }
    }

    // Track user interaction
    async trackInteraction(req, res) {
        try {
            const interactionData = {
                userId: req.body.userId,
                sessionId: req.body.sessionId,
                userType: req.body.userType,
                interactionType: req.body.interactionType,
                page: req.body.page,
                previousPage: req.body.previousPage,
                elementType: req.body.elementType,
                elementId: req.body.elementId,
                elementClass: req.body.elementClass,
                elementText: req.body.elementText,
                feature: req.body.feature,
                metadata: req.body.metadata || {},
                loadTime: req.body.loadTime,
                responseTime: req.body.responseTime
            };

            const interaction = await analyticsService.trackInteraction(interactionData);
            res.status(201).json({ 
                success: true, 
                interactionId: interaction._id 
            });
        } catch (error) {
            console.error('Analytics Controller: Error tracking interaction:', error);
            res.status(500).json({ error: 'Failed to track interaction' });
        }
    }

    // Get analytics dashboard data
    async getDashboard(req, res) {
        try {
            const dateRange = parseInt(req.query.dateRange) || 30;
            const dashboardData = await analyticsService.getDashboardData(dateRange);
            res.json(dashboardData);
        } catch (error) {
            console.error('Analytics Controller: Error getting dashboard data:', error);
            res.status(500).json({ error: 'Failed to get dashboard data' });
        }
    }

    // Get user-specific analytics
    async getUserAnalytics(req, res) {
        try {
            const userId = req.params.userId;
            const dateRange = parseInt(req.query.dateRange) || 30;
            const userAnalytics = await analyticsService.getUserAnalytics(userId, dateRange);
            res.json(userAnalytics);
        } catch (error) {
            console.error('Analytics Controller: Error getting user analytics:', error);
            res.status(500).json({ error: 'Failed to get user analytics' });
        }
    }

    // Get page analytics
    async getPageAnalytics(req, res) {
        try {
            const dateRange = parseInt(req.query.dateRange) || 30;
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - dateRange);
            
            const matchQuery = { date: { $gte: startDate } };
            if (req.query.page) {
                matchQuery.page = req.query.page;
            }
            
            const { PageAnalytics } = await import('../models/UserAnalytics.js');
            const pageAnalytics = await PageAnalytics.find(matchQuery)
                .sort({ date: -1 })
                .limit(100);
                
            res.json(pageAnalytics);
        } catch (error) {
            console.error('Analytics Controller: Error getting page analytics:', error);
            res.status(500).json({ error: 'Failed to get page analytics' });
        }
    }

    // Get feature usage analytics
    async getFeatureAnalytics(req, res) {
        try {
            const dateRange = parseInt(req.query.dateRange) || 30;
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - dateRange);
            
            const matchQuery = { date: { $gte: startDate } };
            if (req.query.feature) {
                matchQuery.feature = req.query.feature;
            }
            
            const { FeatureUsage } = await import('../models/UserAnalytics.js');
            const featureUsage = await FeatureUsage.find(matchQuery)
                .sort({ date: -1 })
                .limit(100);
                
            res.json(featureUsage);
        } catch (error) {
            console.error('Analytics Controller: Error getting feature usage:', error);
            res.status(500).json({ error: 'Failed to get feature usage' });
        }
    }

    // Get real-time analytics
    async getRealTimeAnalytics(req, res) {
        try {
            const { UserSession, UserInteraction } = await import('../models/UserAnalytics.js');
            
            // Get active sessions (last activity within 5 minutes)
            const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
            const activeSessions = await UserSession.find({
                isActive: true,
                lastActivity: { $gte: fiveMinutesAgo }
            });
            
            // Get current page views (last 1 minute)
            const oneMinuteAgo = new Date(Date.now() - 60 * 1000);
            const recentPageViews = await UserInteraction.aggregate([
                {
                    $match: {
                        interactionType: 'page_view',
                        timestamp: { $gte: oneMinuteAgo }
                    }
                },
                {
                    $group: {
                        _id: '$page',
                        count: { $sum: 1 },
                        users: { $addToSet: '$userId' }
                    }
                },
                {
                    $addFields: {
                        uniqueUsers: { $size: '$users' }
                    }
                },
                {
                    $project: {
                        page: '$_id',
                        views: '$count',
                        uniqueUsers: 1,
                        _id: 0
                    }
                },
                { $sort: { views: -1 } }
            ]);
            
            res.json({
                activeSessions: activeSessions.length,
                activeUsers: activeSessions.length,
                recentPageViews,
                timestamp: new Date()
            });
        } catch (error) {
            console.error('Analytics Controller: Error getting real-time analytics:', error);
            res.status(500).json({ error: 'Failed to get real-time analytics' });
        }
    }

    // Export analytics data
    async exportData(req, res) {
        try {
            const { type, dateRange = 30, format = 'json' } = req.query;
            const startDate = new Date();
            startDate.setDate(startDate.getDate() - parseInt(dateRange));
            
            let data = [];
            const { UserSession, UserInteraction, PageAnalytics, FeatureUsage } = await import('../models/UserAnalytics.js');
            
            switch (type) {
                case 'sessions':
                    data = await UserSession.find({ startTime: { $gte: startDate } }).lean();
                    break;
                case 'interactions':
                    data = await UserInteraction.find({ timestamp: { $gte: startDate } }).lean();
                    break;
                case 'pages':
                    data = await PageAnalytics.find({ date: { $gte: startDate } }).lean();
                    break;
                case 'features':
                    data = await FeatureUsage.find({ date: { $gte: startDate } }).lean();
                    break;
                default:
                    return res.status(400).json({ error: 'Invalid export type' });
            }
            
            if (format === 'csv') {
                // Convert to CSV format
                const csv = this.convertToCSV(data);
                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename=${type}_analytics_${dateRange}days.csv`);
                res.send(csv);
            } else {
                res.json(data);
            }
        } catch (error) {
            console.error('Analytics Controller: Error exporting analytics:', error);
            res.status(500).json({ error: 'Failed to export analytics' });
        }
    }

    // Clean up old analytics data
    async cleanupData(req, res) {
        try {
            // Note: Add admin authentication middleware here
            const retentionDays = parseInt(req.query.retentionDays) || 365;
            await analyticsService.cleanupOldData(retentionDays);
            res.json({ 
                success: true, 
                message: `Cleaned up data older than ${retentionDays} days` 
            });
        } catch (error) {
            console.error('Analytics Controller: Error cleaning up data:', error);
            res.status(500).json({ error: 'Failed to cleanup data' });
        }
    }

    // Helper method to convert JSON to CSV
    convertToCSV(data) {
        if (!data.length) return '';
        
        const headers = Object.keys(data[0]);
        const csvHeaders = headers.join(',');
        
        const csvRows = data.map(row => {
            return headers.map(header => {
                const value = row[header];
                if (typeof value === 'object' && value !== null) {
                    return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
                }
                return `"${String(value).replace(/"/g, '""')}"`;
            }).join(',');
        });
        
        return [csvHeaders, ...csvRows].join('\n');
    }
}

export default new AnalyticsController();
