// forgotPasswordController.js
import { sendPasswordResetEmail, resetPassword, validateResetToken } from '../services/forgotPasswordService.js';

/**
 * Handle forgot password request
 */
export const handleForgotPassword = async (req, res) => {
    try {
        const { email, usertype } = req.body;

        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }

        if (!usertype || !['Student', 'Teacher'].includes(usertype)) {
            return res.status(400).json({ message: 'Valid user type is required' });
        }

        const origin = req.get('origin') || req.headers.referer || `http://${req.headers.host}`;

        await sendPasswordResetEmail(email, usertype, origin);

        // Always return success even if email doesn't exist for security reasons
        return res.status(200).json({
            message: 'If an account exists with this email, a password reset link has been sent'
        });
    } catch (error) {
        console.error('Forgot password error:', error);
        // Don't expose internal errors to client
        return res.status(200).json({
            message: 'If an account exists with this email, a password reset link has been sent'
        });
    }
};

/**
 * Handle password reset
 */
export const handleResetPassword = async (req, res) => {
    try {
        const token = req.query.token;
        const { password: newPassword, validateOnly } = req.body;

        if (!token) {
            return res.status(400).json({ message: 'Reset token is required' });
        }

        // If validateOnly is true, only validate the token without resetting password
        if (validateOnly) {
            // Just validate the token without changing password
            // You'll need a function to validate the token
            const isValid = await validateResetToken(token);
            if (!isValid) {
                return res.status(400).json({ message: 'Invalid or expired token' });
            }
            return res.status(200).json({ message: 'Token is valid' });
        }

        // Otherwise, proceed with password reset
        if (!newPassword || newPassword.length < 8) {
            return res.status(400).json({ message: 'New password must be at least 8 characters' });
        }

        const result = await resetPassword(token, newPassword);

        return res.status(200).json(result);
    } catch (error) {
        console.error('Reset password error:', error);
        return res.status(400).json({ message: error.message || 'Password reset failed' });
    }
};