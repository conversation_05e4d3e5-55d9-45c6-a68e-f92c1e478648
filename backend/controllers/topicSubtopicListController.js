import { validationResult } from 'express-validator';
import { getStudentKnowledgeGraphService } from '../services/getKnowledgeGraphService.js';
import Class from '../models/Class.js';

const extractTopics = (curriculum) => {
  let topiclist = []
  for (const i of curriculum) {
    if (i['nodeId']['type'] === "Chapter") {
      topiclist.push(i['nodeId']['name']);
    }
  }
  return topiclist;
}


const extractSubTopics = (curriculum) => {
  let subtopiclist = []
  for (const i of curriculum) {
    if (i['nodeId']['type'] === "Subtopic") {
      subtopiclist.push(i['nodeId']['name']);
    }
  }
  return subtopiclist;
}

export const getTopics = async (req, res) => {
  try {
    const { classid, subject } = req.params;

    const classObj = await Class.findOne({"_id" : classid}).populate("students");
    if (!classObj) {
      console.error(`[ERROR] class not found: ${classid}`);
      return res.status(502);
    }

    const studentidArray = classObj.students.map(student => student._id);

    const curriculum = await getStudentKnowledgeGraphService(studentidArray[0], subject);

    return res.status(200).json(extractTopics(curriculum.curriculumProgress));

  } catch (error) {
    console.error(`[ERROR] during topic list returning: ${error}`);
    return res.status(500).json({error: error});
  }
}

export const getSubTopics = async (req, res) => {
  try {
    const {classid, subject} = req.params;
    const classObj = await Class.findOne({"_id" : classid}).populate("students");
    if (!classObj) {
      console.error(`[ERROR] class not found: ${classid}`);
      return res.status(502);
    }

    const studentidArray = classObj.students.map(student => student._id);

    const curriculum = await getStudentKnowledgeGraphService(studentidArray[0], subject);

    return res.status(200).json(extractSubTopics(curriculum.curriculumProgress));

  } catch (error) {
    console.error(`[ERROR] during topic list returning: ${error}`);
    return res.status(500).json({error: error});
  }
}
