import Class from "../models/Class.js";
import mongoose from "mongoose";
import Teacher from "../models/Teacher.js";
import Student from "../models/Student.js";
import School from "../models/School.js";
import StudentCurriculumService from '../services/studentCurriculumService.js';
import { generateUniqueClassCode } from "../services/classServices.js";

export async function getAllClasses(req, res) {
  try {
    const classes = await Class.find();
    return res.status(200).json(classes);

  } catch (error) {
    console.error("[ERROR] got error: ", error);
    return res.status(400).json({ errors: error.message });
  }
}

export async function createClass(req, res) {
  const session = await mongoose.startSession();
  try {
    const { classStd, schoolCode, teacherId, subject, className, students } = req.body;

    const classCode = await generateUniqueClassCode();
    const result = await session.withTransaction(async () => {
      // Check if the parameters are valid
      const newClass = await Class.create([{
        classStd,
        classCode,
        schoolCode,
        teacherId,
        subject,
        className,
        students,
      }], { session });

      const teacher = await Teacher.findById(teacherId).session(session);
      if (!teacher) {
        return res.status(400).json({ message: "Invalid teacher" });
      }
      teacher.classes.push(newClass[0]._id);
      await teacher.save({ session });

      // Add class to school
      const school = await School.findOne({ schoolCode: schoolCode }).session(session);
      if (!school) {
        return res.status(400).json({ message: 'Invalid school' });
      }
      school.classes.push(newClass[0]._id);
      await school.save({ session });

      let update = {};

      for (let userId of students) {
        console.error("[INFO] user id: ", userId);
        update = {
          $addToSet: { students: userId },
        };
        //   console.error("Student update object:", JSON.stringify(update));

        // Initialize curriculum
        const studentKnowledgeGraph = await StudentCurriculumService.initializeStudentCurriculum(
          userId,
          `Class ${newClass[0].classStd}`,
          newClass[0].subject
        );

        if (!studentKnowledgeGraph) {
          throw new Error(`Failed to initialize curriculum for student ${userId} in subject ${newClass[0].subject}`);
        }

        // Prepare new subject with knowledge graph ID
        const newSubject = {
          subjectClassId: newClass[0]._id,
          subjectName: newClass[0].subject,
          overallProficiency: 0.5,
          knowledgeGraphId: studentKnowledgeGraph._id,
          testHistory: []
        };

        // Update student by adding new subject while preserving existing ones
        await Student.findByIdAndUpdate(
          userId,
          {
            $addToSet: {
              subjects: newSubject,
              classes: newClass[0]._id
            }
          },
          { new: true, runValidators: true, session: session }
        );
      }

      const updatedClass = await Class.findOneAndUpdate({ classCode }, update, {
        new: true, session: session
      });
      return updatedClass;
    });
    session.endSession();
    return res.status(201).json({
      message: "Class created successfully",
      class: result,
    });
  } catch (error) {
    session.endSession();
    console.error("[ERROR] got error: ", error);
    return res.status(400).json({ errors: error.message });
  }
}

export async function joinClass(req, res) {
  const session = await mongoose.startSession();
  try {
    const { classCode, userid: userId, role } = req.body;
    const result = await session.withTransaction(async () => {
      const existingClass = await Class.findOne({ classCode }).session(session);
      if (!existingClass) {
        return res.status(404).json({ errors: "Class not found" });
      }
      let update = {};
      if (role === "Teacher") {
        update = {
          $addToSet: { teacherId: userId },
        };
        await Teacher.findByIdAndUpdate(
          userId,
          { $addToSet: { classes: existingClass._id } },
          { new: true, session }
        );
      } else if (role === "Student") {
        update = {
          $addToSet: { students: userId },
        };
        // Initialize curriculum with session
        const studentKnowledgeGraph = await StudentCurriculumService.initializeStudentCurriculum(
          userId,
          `Class ${existingClass.classStd}`,
          existingClass.subject,
          session
        );

        if (!studentKnowledgeGraph) {
          throw new Error(`Failed to initialize curriculum for student ${userId} in subject ${existingClass.subject}`);
        }

        const newSubject = {
          subjectClassId: existingClass._id,
          subjectName: existingClass.subject,
          overallProficiency: 0.5,
          knowledgeGraphId: studentKnowledgeGraph._id,
          testHistory: []
        };
        await Student.findByIdAndUpdate(
          userId,
          {
            $addToSet: {
              subjects: newSubject,
              classes: existingClass._id
            }
          },
          { new: true, runValidators: true, session }
        );
      }
      const updatedClass = await Class.findOneAndUpdate({ classCode }, update, {
        new: true, session
      });
      return updatedClass;
    });
    session.endSession();
    if (result && result.status && result.status !== 200) return result;
    return res.status(200).json(result);
  } catch (error) {
    session.endSession();
    console.error("Error in joinClass:", error);
    return res.status(400).json({ errors: error.message });
  }
}

export async function leaveClass(req, res) {
  const session = await mongoose.startSession();
  try {
    const { classCode, userid: userId, role } = req.body;
    const result = await session.withTransaction(async () => {
      const existingClass = await Class.findOne({ classCode }).session(session);
      if (!existingClass) {
        return res.status(404).json({ errors: "Class not found" });
      }
      let update = {};
      if (role === "Teacher") {
        update = {
          $pull: { teacherId: userId },
        };
        await Teacher.findByIdAndUpdate(
          userId,
          { $pull: { classes: existingClass._id } },
          { new: true, session }
        );
      } else if (role === "Student") {
        update = {
          $pull: { students: userId },
        };
        const student = await Student.findById(userId).session(session);
        const subjectToRemove = student.subjects.find(
          (subj) => subj.subjectClassId.toString() === existingClass._id.toString()
        );
        if (subjectToRemove && subjectToRemove.knowledgeGraphId) {
          await StudentCurriculumService.deleteStudentCurriculum(subjectToRemove.knowledgeGraphId, session);
        }
        await Student.findByIdAndUpdate(
          userId,
          { $pull: { classes: existingClass._id, subjects: { subjectClassId: existingClass._id } } },
          { new: true, session }
        );
      }
      const updatedClass = await Class.findOneAndUpdate({ classCode }, update, {
        new: true, session
      });
      return updatedClass;
    });
    session.endSession();
    if (result && result.status && result.status !== 200) return result;
    return res.status(200).json(result);
  } catch (error) {
    session.endSession();
    console.error("Error in leaveClass:", error);
    return res.status(400).json({ errors: error.message });
  }
}
