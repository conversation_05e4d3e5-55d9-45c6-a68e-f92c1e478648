import jwt from 'jsonwebtoken';
import  Student from '../models/Student.js';
import Teacher from '../models/Teacher.js';
import { validationResult } from 'express-validator';

export const handleRefresh = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    console.error("[ERROR] Validation errors in refresh:", errors.array());
    return res.status(401).json({ error: "Validation failed" });
  }

  const cookies = req.cookies;
  console.error("[DEBUG] Refresh request cookies:", cookies);
  console.error("[DEBUG] Refresh request body:", req.body);

  if (!cookies?.refresh) {
    console.error("[ERROR] No refresh cookie found");
    return res.status(401).json({ error: "No refresh token" });
  }

  const refreshToken = cookies.refresh;
  console.error("[DEBUG] Refresh token from cookie:", refreshToken);

  let user;
  if (req.body.role === 'Student') {
    user = await Student.findOne({securityToken: refreshToken});
  } else {
    user = await Teacher.findOne({securityToken: refreshToken});
  }

  console.error("[DEBUG] User found for refresh token:", !!user);

  if (!user) {
    console.error("[ERROR] user not found for refresh token secret\n");
    return res.status(403).json({ error: "Invalid refresh token" });
  }

  jwt.verify(
    refreshToken, process.env.REFRESH_TOKEN_SECRET, (err, decoded) => {
      if (err || user._id != decoded.id) {
        console.error("[ERROR] refresh token not valid or user.id id different from the decoded id\n");
        console.error("[ERROR] err: ", err);
        return res.sendStatus(403);
      }
      const accessToken = jwt.sign(
        {"id" : user._id},
        process.env.ACCESS_TOKEN_SECRET,
        {expiresIn: process.env.ACCESS_EXPIRES_IN}
      );
      res.json({ accessToken });
    }
  )
}
