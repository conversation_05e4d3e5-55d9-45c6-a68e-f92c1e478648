import { 
    updateSubjectOverallProficiency, 
    batchUpdateOverallProficiency,
    migrateHardcodedProficiencies,
    calculateOverallProficiencyFromCurriculum
} from '../services/proficiencyCalculationService.js';
import { StudentCurriculumModel } from '../models/StudentKnowledgeGraphModel.js';
import Student from '../models/Student.js';

/**
 * Recalculate overall proficiency for a specific student and subject
 */
export const recalculateStudentProficiency = async (req, res) => {
    try {
        const { studentId, subject } = req.params;
        const { weightingStrategy = 'equal', topicWeight = 1.0, subtopicWeight = 1.0 } = req.body;

        if (!studentId || !subject) {
            return res.status(400).json({
                success: false,
                message: 'Student ID and subject are required'
            });
        }

        const proficiency = await updateSubjectOverallProficiency(studentId, subject, {
            weightingStrategy,
            topicWeight,
            subtopicWeight
        });

        res.json({
            success: true,
            data: {
                studentId,
                subject,
                overallProficiency: proficiency,
                weightingStrategy
            },
            message: 'Proficiency recalculated successfully'
        });

    } catch (error) {
        console.error('Error recalculating student proficiency:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to recalculate proficiency',
            error: error.message
        });
    }
};

/**
 * Batch recalculate proficiencies for multiple students
 */
export const batchRecalculateProficiencies = async (req, res) => {
    try {
        const { updates, weightingStrategy = 'equal', topicWeight = 1.0, subtopicWeight = 1.0 } = req.body;

        if (!updates || !Array.isArray(updates)) {
            return res.status(400).json({
                success: false,
                message: 'Updates array is required'
            });
        }

        const results = await batchUpdateOverallProficiency(updates, {
            weightingStrategy,
            topicWeight,
            subtopicWeight
        });

        const successful = results.filter(r => r.success).length;
        const failed = results.filter(r => !r.success).length;

        res.json({
            success: true,
            data: {
                results,
                summary: {
                    total: results.length,
                    successful,
                    failed
                }
            },
            message: `Batch recalculation completed: ${successful} successful, ${failed} failed`
        });

    } catch (error) {
        console.error('Error in batch recalculation:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to perform batch recalculation',
            error: error.message
        });
    }
};

/**
 * Migrate all hardcoded proficiency values
 */
export const migrateProficiencies = async (req, res) => {
    try {
        const { 
            dryRun = false, 
            batchSize = 100,
            weightingStrategy = 'nodeType',
            topicWeight = 1.2,
            subtopicWeight = 1.0
        } = req.body;

        const result = await migrateHardcodedProficiencies({
            dryRun,
            batchSize,
            weightingStrategy,
            topicWeight,
            subtopicWeight
        });

        res.json({
            success: true,
            data: result,
            message: dryRun ? 'Dry run completed successfully' : 'Migration completed successfully'
        });

    } catch (error) {
        console.error('Error during migration:', error);
        res.status(500).json({
            success: false,
            message: 'Migration failed',
            error: error.message
        });
    }
};

/**
 * Get proficiency statistics for a student
 */
export const getStudentProficiencyStats = async (req, res) => {
    try {
        const { studentId, subject } = req.params;

        if (!studentId || !subject) {
            return res.status(400).json({
                success: false,
                message: 'Student ID and subject are required'
            });
        }

        // Get curriculum progress
        const studentCurriculum = await StudentCurriculumModel.findOne({
            studentId,
            subject
        }).populate('curriculumProgress.nodeId');

        if (!studentCurriculum) {
            return res.status(404).json({
                success: false,
                message: 'Student curriculum not found'
            });
        }

        // Get student's subject data
        const student = await Student.findById(studentId);
        const subjectData = student.subjects.find(s => s.subjectName.toLowerCase() === subject.toLowerCase());

        // Calculate current proficiency
        const calculatedProficiency = calculateOverallProficiencyFromCurriculum(
            studentCurriculum.curriculumProgress
        );

        // Generate statistics
        const validProgress = studentCurriculum.curriculumProgress.filter(p => 
            p.nodeId && typeof p.proficiency === 'number'
        );

        const stats = {
            totalNodes: validProgress.length,
            averageProficiency: calculatedProficiency,
            currentStoredProficiency: subjectData ? subjectData.overallProficiency * 100 : 0,
            nodeBreakdown: {
                chapters: validProgress.filter(p => p.nodeId.type === 'Chapter').length,
                subtopics: validProgress.filter(p => p.nodeId.type === 'Subtopic').length
            },
            proficiencyDistribution: {
                excellent: validProgress.filter(p => p.proficiency >= 90).length,
                good: validProgress.filter(p => p.proficiency >= 80 && p.proficiency < 90).length,
                average: validProgress.filter(p => p.proficiency >= 70 && p.proficiency < 80).length,
                needsImprovement: validProgress.filter(p => p.proficiency < 70).length
            }
        };

        res.json({
            success: true,
            data: {
                studentId,
                subject,
                stats,
                nodes: validProgress.map(p => ({
                    nodeId: p.nodeId._id,
                    name: p.nodeId.name,
                    type: p.nodeId.type,
                    proficiency: p.proficiency,
                    status: p.status
                }))
            }
        });

    } catch (error) {
        console.error('Error getting proficiency stats:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to get proficiency statistics',
            error: error.message
        });
    }
};
