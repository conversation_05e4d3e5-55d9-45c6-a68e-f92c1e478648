import { Upload } from "@aws-sdk/lib-storage";
import { S3, PutObjectCommand } from "@aws-sdk/client-s3"; // <-- Add PutObjectCommand import
import multer from 'multer';
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

// Multer configuration
const multerConfig = {
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 5 * 1024 * 1024, // 5MB limit
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg', 'image/webp'];
        if (!allowedTypes.includes(file.mimetype)) {
            cb(new Error('Invalid file type. Only JPEG, PNG and GIF are allowed.'));
            return;
        }
        cb(null, true);
    }
};

// Configure AWS S3
const s3 = new S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
});

// Helper function for S3 upload
const uploadToS3 = async (file) => {
    const filename = `uploads/images/questions/${file.originalname}`;
    const params = {
        Bucket: process.env.S3_BUCKET_NAME,
        Key: filename,
        Body: file.buffer,
        ContentType: file.mimetype,
    };

    const result = await new Upload({
        client: s3,
        params,
    }).done();
    return {
        url: result.Location,
        key: result.Key
    };
};

// Helper function for S3 upload (profile photos)
const uploadProfilePhotoToS3 = async (file, userId) => {
    // Use userId to uniquely identify the profile photo
    const extension = file.originalname.split('.').pop();
    const filename = `uploads/images/profilePhotos/${userId}.${extension}`;
    const params = {
        Bucket: process.env.S3_BUCKET_NAME,
        Key: filename,
        Body: file.buffer,
        ContentType: file.mimetype,
    };

    const result = await new Upload({
        client: s3,
        params,
    }).done();
    return {
        url: result.Location,
        key: result.Key
    };
};

// Single image upload endpoint
export const uploadImage = async (req, res) => {
    const upload = multer(multerConfig).single('image');

    try {
        await new Promise((resolve, reject) => {
            upload(req, res, (err) => err ? reject(err) : resolve());
        });

        if (!req.file) {
            return res.status(400).json({ message: "No file uploaded" });
        }

        const result = await uploadToS3(req.file);
        res.status(201).json({
            message: "Image uploaded successfully",
            ...result
        });

    } catch (error) {
        console.error("Upload Error:", error);
        res.status(400).json({
            message: "Error uploading file",
            error: error.message
        });
    }
};

// Multiple images upload endpoint
export const uploadMultipleImages = async (req, res) => {
    const upload = multer(multerConfig).array('images', 5);

    try {
        await new Promise((resolve, reject) => {
            upload(req, res, (err) => err ? reject(err) : resolve());
        });

        if (!req.files?.length) {
            return res.status(400).json({ message: "No files uploaded" });
        }

        const results = await Promise.all(req.files.map(uploadToS3));

        res.status(201).json({
            message: "Images uploaded successfully",
            images: results
        });

    } catch (error) {
        console.error("Upload Error:", error);
        res.status(400).json({
            message: "Error uploading files",
            error: error.message
        });
    }
};

// Profile photo upload endpoint
export const uploadProfilePhoto = async (req, res) => {
    // You may want to get userId from req.user or req.body
    const userId = req.user?.id || req.body.userId;
    if (!userId) {
        return res.status(400).json({ message: "User ID is required" });
    }
    const upload = multer(multerConfig).single('profilePhoto');

    try {
        await new Promise((resolve, reject) => {
            upload(req, res, (err) => err ? reject(err) : resolve());
        });

        if (!req.file) {
            return res.status(400).json({ message: "No file uploaded" });
        }

        const result = await uploadProfilePhotoToS3(req.file, userId);
        res.status(201).json({
            message: "Profile photo uploaded successfully",
            ...result
        });

    } catch (error) {
        console.error("Upload Error:", error);
        res.status(400).json({
            message: "Error uploading profile photo",
            error: error.message
        });
    }
};

// Generate presigned URL for profile photo upload
export const getProfilePhotoPresignedUrl = async (req, res) => {
    const userId = req.user?.id || req.body.userId;
    const { fileType } = req.body;
    if (!userId || !fileType) {
        return res.status(400).json({ message: "User ID and fileType are required" });
    }
    const extension = fileType.split('/').pop();
    const filename = `uploads/images/profilePhotos/${userId}.${extension}`;
    const params = {
        Bucket: process.env.S3_BUCKET_NAME,
        Key: filename,
        ContentType: fileType,
        ACL: "public-read" // Only include if you want public access
    };
    try {
        const command = new PutObjectCommand(params);
        const url = await getSignedUrl(s3, command, { expiresIn: 300 }); // 5 min expiry
        res.json({ url, key: filename });
    } catch (error) {
        console.error("Presign Error:", error);
        res.status(500).json({ message: "Error generating presigned URL", error: error.message });
    }
};