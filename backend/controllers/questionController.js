// controllers/questionController.js
import {createQuestionModel} from '../models/Question.js';

const getQuestions = async (req, res) => {
    try {
        const questions = await createQuestionModel.find().limit(10); // Exclude internal fields like __v if present
        res.json(questions);
    } catch (error) {
        res.status(500).json({ message: 'Error fetching questions from the database', error });
    }
};

export const createQuestion = async (req, res) => {
    try {
        const { questionData } = req.body;
        console.error(questionData);
        const question = await createQuestionModel().create({
            question: questionData.question,
            options: questionData.options,
            answer: questionData.answer,
            solution: questionData.solution,
            images: questionData.images,
            topic: questionData.topic,
            subtopic: questionData.subtopic,
            difficulty: questionData.difficulty,
            discrimination_parameter: 0.5,
        });
        
        res.status(201).json({ question, message: 'Question created successfully' });
    } catch (error) {
        res.status(400).json({ message: 'Error creating question', error });
    }
}

export { getQuestions };
