import ChatConversation from '../models/ChatConversation.js';
import crypto from 'crypto';

// Get all conversations for a user
export const getUserConversations = async (req, res) => {
  try {
    const { userId, subject } = req.query;
    
    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Build query - if subject is provided, filter by subject
    const query = { userId };
    if (subject) {
      query.subject = subject;
    }

    const conversations = await ChatConversation.find(query)
      .sort({ updatedAt: -1 })
      .lean();

    res.json(conversations);
  } catch (error) {
    console.error('Error fetching user conversations:', error);
    res.status(500).json({ error: 'Failed to fetch conversations' });
  }
};

// Get a specific conversation
export const getConversation = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.query;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const conversation = await ChatConversation.findOne({ 
      id, 
      userId 
    }).lean();

    if (!conversation) {
      return res.status(404).json({ error: 'Conversation not found' });
    }

    res.json(conversation);
  } catch (error) {
    console.error('Error fetching conversation:', error);
    res.status(500).json({ error: 'Failed to fetch conversation' });
  }
};

// Create a new conversation
export const createConversation = async (req, res) => {
  try {
    const { id, userId, title, subject, messages = [] } = req.body;

    if (!id || !userId || !title || !subject) {
      return res.status(400).json({ 
        error: 'ID, User ID, title, and subject are required' 
      });
    }

    // Check if conversation with this ID already exists
    const existingConversation = await ChatConversation.findOne({ id });
    if (existingConversation) {
      return res.status(409).json({ error: 'Conversation with this ID already exists' });
    }

    const conversation = new ChatConversation({
      id,
      userId,
      title,
      subject,
      messages
    });

    await conversation.save();
    res.status(201).json(conversation);
  } catch (error) {
    console.error('Error creating conversation:', error);
    res.status(500).json({ error: 'Failed to create conversation' });
  }
};

// Update a conversation (add messages, update title, etc.)
export const updateConversation = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId, messages, title } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const updateData = { updatedAt: new Date() };
    
    if (messages) {
      updateData.messages = messages;
    }
    
    if (title) {
      updateData.title = title;
    }

    const conversation = await ChatConversation.findOneAndUpdate(
      { id, userId },
      updateData,
      { new: true, runValidators: true }
    );

    if (!conversation) {
      return res.status(404).json({ error: 'Conversation not found' });
    }

    res.json(conversation);
  } catch (error) {
    console.error('Error updating conversation:', error);
    res.status(500).json({ error: 'Failed to update conversation' });
  }
};

// Delete a conversation
export const deleteConversation = async (req, res) => {
  try {
    const { id } = req.params;
    const { userId } = req.query;

    console.log(`[DELETE BACKEND] Received delete request for conversation: ${id}, userId: ${userId}`);
    console.log(`[DELETE BACKEND] Request headers:`, req.headers);
    console.log(`[DELETE BACKEND] Request params:`, req.params);
    console.log(`[DELETE BACKEND] Request query:`, req.query);

    if (!userId) {
      console.log(`[DELETE BACKEND] Error: User ID is required`);
      return res.status(400).json({ error: 'User ID is required' });
    }

    // First, check if conversation exists
    const existingConversation = await ChatConversation.findOne({ id, userId });
    console.log(`[DELETE BACKEND] Existing conversation found:`, existingConversation ? 'YES' : 'NO');

    if (existingConversation) {
      console.log(`[DELETE BACKEND] Conversation details:`, {
        id: existingConversation.id,
        userId: existingConversation.userId,
        title: existingConversation.title,
        subject: existingConversation.subject,
        messageCount: existingConversation.messages.length
      });
    }

    const conversation = await ChatConversation.findOneAndDelete({
      id,
      userId
    });

    if (!conversation) {
      console.log(`[DELETE BACKEND] Error: Conversation not found for deletion`);
      return res.status(404).json({ error: 'Conversation not found' });
    }

    console.log(`[DELETE BACKEND] Successfully deleted conversation: ${id}`);
    res.json({ message: 'Conversation deleted successfully', deletedId: id });
  } catch (error) {
    console.error('[DELETE BACKEND] Error deleting conversation:', error);
    res.status(500).json({ error: 'Failed to delete conversation' });
  }
};

// Save or update conversation after chat response
export const saveConversationMessage = async (req, res) => {
  try {
    const { conversationId, userId, subject, messages, title } = req.body;

    if (!conversationId || !userId || !subject || !messages) {
      return res.status(400).json({ 
        error: 'Conversation ID, User ID, subject, and messages are required' 
      });
    }

    // Try to find existing conversation
    let conversation = await ChatConversation.findOne({ 
      id: conversationId, 
      userId 
    });

    if (conversation) {
      // Update existing conversation
      conversation.messages = messages;
      if (title) {
        conversation.title = title;
      }
      conversation.updatedAt = new Date();
      await conversation.save();
    } else {
      // Create new conversation
      conversation = new ChatConversation({
        id: conversationId,
        userId,
        title: title || `New ${subject} Chat`,
        subject,
        messages
      });
      await conversation.save();
    }

    res.json(conversation);
  } catch (error) {
    console.error('Error saving conversation message:', error);
    res.status(500).json({ error: 'Failed to save conversation' });
  }
};

// Auto-create new conversation on login (only for genuine new sessions)
export const createNewConversationOnLogin = async (req, res) => {
  try {
    const { userId, subject, isNewLogin = false } = req.body;

    if (!userId || !subject) {
      return res.status(400).json({
        error: 'User ID and subject are required'
      });
    }

    console.log(`[CHAT LOGIN] Request for user ${userId}, subject ${subject}, isNewLogin: ${isNewLogin}`);

    // If this is not explicitly marked as a new login, just return existing conversations
    if (!isNewLogin) {
      const existingConversations = await ChatConversation.find({
        userId,
        subject
      }).sort({ updatedAt: -1 }).limit(1);

      if (existingConversations.length > 0) {
        console.log(`[CHAT LOGIN] Returning existing conversation for navigation/refresh`);
        return res.json({
          conversation: existingConversations[0],
          message: 'Existing conversation found',
          isExisting: true
        });
      }
    }

    // For genuine new logins, check if there's an existing empty conversation to reuse
    if (isNewLogin) {
      console.log(`[CHAT LOGIN] Checking for existing empty conversations to reuse`);

      // Find conversations with no messages (truly empty conversations)
      const emptyConversations = await ChatConversation.find({
        userId,
        subject,
        messages: { $size: 0 } // No messages at all
      }).sort({ updatedAt: -1 });

      // If we have empty conversations, reuse the most recent one
      if (emptyConversations.length > 0) {
        const conv = emptyConversations[0];
        console.log(`[CHAT LOGIN] Reusing existing empty conversation: ${conv.id}`);

        // Update the timestamp to make it the most recent
        conv.updatedAt = new Date();
        await conv.save();

        return res.json({
          conversation: conv,
          message: 'Reusing existing empty conversation',
          isReused: true
        });
      }
    }

    // Create a new conversation if no empty one exists or this is not a new login
    console.log(`[CHAT LOGIN] Creating new conversation`);

    // Generate unique conversation ID
    const conversationId = `conv_${userId}_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`;

    // Create new conversation without welcome message
    const conversation = new ChatConversation({
      id: conversationId,
      userId,
      title: `New ${subject} Chat`,
      subject,
      messages: [] // Start with empty conversation
    });

    await conversation.save();

    console.log(`[CHAT LOGIN] New conversation created: ${conversationId}`);

    res.json({
      conversation,
      message: 'New conversation created successfully',
      isNew: true
    });
  } catch (error) {
    console.error('Error creating new conversation on login:', error);
    res.status(500).json({ error: 'Failed to create new conversation' });
  }
};

// Create new conversation explicitly (user-initiated)
export const createNewConversationExplicit = async (req, res) => {
  try {
    const { userId, subject } = req.body;

    if (!userId || !subject) {
      return res.status(400).json({
        error: 'User ID and subject are required'
      });
    }

    // Generate unique conversation ID
    const conversationId = `conv_${userId}_${Date.now()}_${crypto.randomBytes(4).toString('hex')}`;

    // Create new conversation without welcome message
    const conversation = new ChatConversation({
      id: conversationId,
      userId,
      title: `New ${subject} Chat`,
      subject,
      messages: [] // Start with empty conversation
    });

    await conversation.save();

    res.json({
      conversation,
      message: 'New conversation created successfully'
    });
  } catch (error) {
    console.error('Error creating new conversation explicitly:', error);
    res.status(500).json({ error: 'Failed to create new conversation' });
  }
};

// Track user login session for chat initialization
export const trackLoginSession = async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // Store login timestamp in a simple in-memory store or database
    // For now, we'll use a simple approach with session tracking
    const loginTimestamp = Date.now();

    console.log(`[LOGIN TRACKER] User ${userId} logged in at ${new Date(loginTimestamp)}`);

    res.json({
      message: 'Login session tracked',
      userId,
      loginTimestamp,
      sessionId: `session_${userId}_${loginTimestamp}`
    });
  } catch (error) {
    console.error('Error tracking login session:', error);
    res.status(500).json({ error: 'Failed to track login session' });
  }
};

// Clear user's chat state (for frontend state reset)
export const clearChatState = async (req, res) => {
  try {
    const { userId } = req.body;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    // This endpoint is mainly for triggering frontend state clearing
    // The actual clearing happens on the frontend
    res.json({
      message: 'Chat state clear signal sent',
      userId,
      timestamp: new Date()
    });
  } catch (error) {
    console.error('Error clearing chat state:', error);
    res.status(500).json({ error: 'Failed to clear chat state' });
  }
};

// Get conversation analytics
export const getConversationAnalytics = async (req, res) => {
  try {
    const { userId, timeRange = 7 } = req.query;

    if (!userId) {
      return res.status(400).json({ error: 'User ID is required' });
    }

    const startDate = new Date(Date.now() - timeRange * 24 * 60 * 60 * 1000);

    const analytics = await ChatConversation.aggregate([
      {
        $match: {
          userId,
          createdAt: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$subject',
          totalConversations: { $sum: 1 },
          totalMessages: { $sum: { $size: '$messages' } },
          avgMessagesPerConversation: { $avg: { $size: '$messages' } },
          lastActivity: { $max: '$updatedAt' }
        }
      },
      {
        $sort: { totalMessages: -1 }
      }
    ]);

    res.json(analytics);
  } catch (error) {
    console.error('Error fetching conversation analytics:', error);
    res.status(500).json({ error: 'Failed to fetch analytics' });
  }
};

// Debug endpoint to list all conversations for a user
export const debugListConversations = async (req, res) => {
  try {
    const { userId, subject } = req.query;

    console.log(`[DEBUG] Listing conversations for userId: ${userId}, subject: ${subject}`);

    const query = { userId };
    if (subject) {
      query.subject = subject;
    }

    const conversations = await ChatConversation.find(query).lean();

    console.log(`[DEBUG] Found ${conversations.length} conversations`);
    conversations.forEach((conv, index) => {
      console.log(`[DEBUG] Conversation ${index + 1}:`, {
        id: conv.id,
        title: conv.title,
        subject: conv.subject,
        messageCount: conv.messages.length,
        createdAt: conv.createdAt,
        updatedAt: conv.updatedAt
      });
    });

    res.json({
      count: conversations.length,
      conversations: conversations.map(conv => ({
        id: conv.id,
        title: conv.title,
        subject: conv.subject,
        messageCount: conv.messages.length,
        createdAt: conv.createdAt,
        updatedAt: conv.updatedAt
      }))
    });
  } catch (error) {
    console.error('[DEBUG] Error listing conversations:', error);
    res.status(500).json({ error: 'Failed to list conversations' });
  }
};

// Clean up duplicate empty conversations for a user
export const cleanupDuplicateEmptyConversations = async (req, res) => {
  try {
    const { userId, subject } = req.body;

    if (!userId || !subject) {
      return res.status(400).json({ error: 'User ID and subject are required' });
    }

    console.log(`[CLEANUP] Starting cleanup for user ${userId}, subject ${subject}`);

    // Find all conversations with no messages (truly empty conversations)
    const emptyConversations = await ChatConversation.find({
      userId,
      subject,
      messages: { $size: 0 } // No messages at all
    }).sort({ updatedAt: -1 });

    if (emptyConversations.length <= 1) {
      console.log(`[CLEANUP] No duplicates found. Found ${emptyConversations.length} empty conversations.`);
      return res.json({
        message: 'No duplicate empty conversations found',
        removedCount: 0,
        remainingConversations: emptyConversations.length
      });
    }

    // Keep the most recent one, remove the rest
    const [keepConversation, ...conversationsToRemove] = emptyConversations;

    const idsToRemove = conversationsToRemove.map(conv => conv.id);

    // Remove duplicate conversations
    const deleteResult = await ChatConversation.deleteMany({
      id: { $in: idsToRemove }
    });

    console.log(`[CLEANUP] Removed ${deleteResult.deletedCount} duplicate empty conversations, kept: ${keepConversation.id}`);

    res.json({
      message: 'Duplicate empty conversations cleaned up',
      removedCount: deleteResult.deletedCount,
      keptConversation: keepConversation.id,
      remainingConversations: 1
    });
  } catch (error) {
    console.error('Error cleaning up duplicate conversations:', error);
    res.status(500).json({ error: 'Failed to cleanup conversations' });
  }
};

// Note: Teacher-specific chat functions have been removed as teachers now use
// the same chat endpoints as students. The backend automatically detects
// teacher vs student based on the userId pattern.
