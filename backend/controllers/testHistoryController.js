import { validationResult } from "express-validator";
import TestHistory from "../models/TestHistory.js";
import TestConfig from "../models/TestConfig.js";
import Teacher from "../models/Teacher.js";
import Student from "../models/Student.js";
import Class from "../models/Class.js";
import cron from "node-cron";
import { createQuestionModel } from "../models/Question.js";
import { getCollectionNameTest } from "../services/testService.js";

const updateTestStatus = async () => {
    const currentDate = new Date();
    const batchSize = 100; // Process in smaller batches to prevent memory issues

    try {
        // Use a single bulk operation for better performance
        const bulkOps = [];

        // Find tests that need status updates - get all tests to properly check start time
        const testsToUpdate = await TestHistory.find({}).select('_id testDate startTime active').lean();

        // Prepare bulk operations
        testsToUpdate.forEach(test => {
            // Combine test date with start time to get the actual test start datetime
            let testStartDateTime = new Date(test.testDate);
            if (test.startTime) {
                const [hours, minutes] = test.startTime.split(':');
                testStartDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
            }

            // Add a 2-minute buffer to account for processing time and small delays
            const bufferTime = 2 * 60 * 1000; // 2 minutes in milliseconds
            const currentDateWithBuffer = new Date(currentDate.getTime() + bufferTime);

            // Test should be active if the test start time is in the future (with buffer)
            const shouldBeActive = testStartDateTime > currentDateWithBuffer;

            if (test.active !== shouldBeActive) {
                bulkOps.push({
                    updateOne: {
                        filter: { _id: test._id },
                        update: {
                            $set: {
                                active: shouldBeActive,
                                lastStatusUpdate: currentDate
                            }
                        }
                    }
                });
            }
        });

        // Execute bulk operations in batches
        if (bulkOps.length > 0) {
            const results = {
                activated: 0,
                deactivated: 0
            };

            for (let i = 0; i < bulkOps.length; i += batchSize) {
                const batch = bulkOps.slice(i, i + batchSize);
                const result = await TestHistory.bulkWrite(batch, { ordered: false });
                
                // Count status changes
                batch.forEach(op => {
                    if (op.updateOne.update.$set.active) {
                        results.activated++;
                    } else {
                        results.deactivated++;
                    }
                });
            }

            console.error(`Test status update summary:
                - Deactivated tests: ${results.deactivated}
                - Activated tests: ${results.activated}
                - Total updates: ${bulkOps.length}
                - Timestamp: ${currentDate.toISOString()}`
            );
        } else {
            console.error('[INFO]No test status updates needed');
        }

        return true;
    } catch (error) {
        console.error('Error in updateTestStatus:', {
            timestamp: currentDate.toISOString(),
            error: error.message,
            stack: error.stack
        });
        
        // Optionally notify admin or monitoring service
        // await notifyAdminOfError(error);
        
        return false;
    }
};

cron.schedule('0 0 * * *', updateTestStatus);  // Run every day at midnight

export const getTestById = async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            message: "Validation failed",
            errors: errors.array()
        });
    }

    try {

        const { testId } = req.params;
        const test1 = await TestHistory.findById(testId);
        const collectionName = await getCollectionNameTest(testId, test1.subject);
        const QuestionModel = createQuestionModel(collectionName); // or dynamic based on test/class
        await QuestionModel.init();

        const test = await TestHistory.findById(testId)
        .populate({
            path: "questions",
            model: QuestionModel,
            select: "-answer -solution -difficulty -discrimination_parameter"
        })
        .populate({
            path: "class",
            model: "Class",
            select: "className classStd"
        });

        if (!test) {
            return res.status(404).json({
                message: "Test not found"
            });
        }

        return res.status(200).json({
            test,
            message: "Test fetched successfully"
        });
    } catch (error) {
        return res.status(400).json({
            message: "Error fetching test",
            error: error.message
        });
    }
};

// New bulk endpoint for fetching multiple test details efficiently
export const getTestsByIds = async (req, res) => {
    try {
        const { testIds } = req.body;

        if (!testIds || !Array.isArray(testIds) || testIds.length === 0) {
            return res.status(400).json({
                message: "testIds array is required and cannot be empty"
            });
        }

        // Limit the number of tests that can be fetched at once to prevent abuse
        if (testIds.length > 50) {
            return res.status(400).json({
                message: "Cannot fetch more than 50 tests at once"
            });
        }

        const tests = await TestHistory.find({ _id: { $in: testIds } })
        .populate({
            path: "class",
            model: "Class",
            select: "className classStd"
        })
        .select("_id subject topics testDate startTime duration numberOfQuestions totalMarks testInstructions testType class")
        .lean(); // Use lean() for better performance since we don't need full Mongoose documents

        // Create a map for easy lookup
        const testsMap = {};
        tests.forEach(test => {
            testsMap[test._id.toString()] = test;
        });

        return res.status(200).json({
            tests: testsMap,
            message: "Tests fetched successfully"
        });
    } catch (error) {
        return res.status(400).json({
            message: "Error fetching tests",
            error: error.message
        });
    }
};

export const getTests = async (req, res) => {
    try {
        await updateTestStatus();
        const tests = await TestHistory.find();
        return res.status(200).json({
            tests,
            message: "Tests fetched successfully"
        });
    } catch (error) {
        return res.status(400).json({
            message: "Error fetching tests",
            error: error.message
        });
    }
}

export const createPersonalizedTestIndividual = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      message: "Validation failed",
      errors: errors.array()
    });
  }

  try {
    const { testData } = req.body; // here testData is a test for a single student
    // console.log(`[INFO] got testData: ${JSON.stringify(testData)}`);
    console.log(`[INFO] got testData questions: ${JSON.stringify(testData.questions)}`);
    const currentDate = new Date();

    const testDate = new Date(testData.date);
    let testStartDateTime = new Date(testDate);
    if (testData.startTime) {
        const [hours, minutes] = testData.startTime.split(':');
        testStartDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
    }
    const initialActiveStatus = testStartDateTime > currentDate;

    const newTest = await TestHistory.create({
      class: testData.class,
      subject: testData.subject,
      testType: testData.testType,
      topics: testData.topics,
      questions: testData.questions[0].questions,
      testDate: testData.date,
      startTime: testData.startTime,
      duration: testData.duration,
      numberOfQuestions: testData.numberOfQuestions,
      totalMarks: testData.totalMarks,
      testInstructions: testData.instructions,
      createdBy: testData.teacherId, // in this case it would be the student itself
      active: initialActiveStatus, // Set initial active status
      lastStatusUpdate: currentDate
    });

    const student = await Student.findById(testData.teacherId);
    if (!student) {
      console.error("[ERROR] Teacher not found");
      return;
    }

    await TestConfig.create({
      testId: newTest._id,
      isAdaptive: true,
      allowBatchProcessing: false,
      maxUsersForRealTime: 100,
      diagnosticTest: false
    });
    // student.testHistory.push(newTest._id);
    // await student.save();
    const matchingSubject = student.subjects.find(
        subject => subject.subjectName.toLowerCase() === testData.subject.toLowerCase()
    );

    if (matchingSubject) {
        matchingSubject.testHistory.push(newTest._id);
        await student.save();
    }
    return res.status(201).json({
        message: "Test created successfully"
    });
  } catch (error) {
    console.error("[ERROR] creating personlized test", error);
    return res.status(400).json({
      message: "Error creating personalized test",
      error: error.message
    });
  }
}


export const createPersonalizedTest = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      message: "Validation failed",
      errors: errors.array()
    });
  }

  try {
    const { testData } = req.body;
    // testData is a list of multiple questions mapped to their test id per student
    for (const test of testData.questions) {
      const currentDate = new Date();

      // Calculate initial active status based on test date and start time
      const testDate = new Date(testData.date);
      let testStartDateTime = new Date(testDate);
      if (testData.startTime) {
          const [hours, minutes] = testData.startTime.split(':');
          testStartDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
      }
      const initialActiveStatus = testStartDateTime > currentDate;

      const newTest = await TestHistory.create({
        class: testData.class,
        subject: testData.subject,
        testType: testData.testType,
        topics: testData.topics,
        questions: test.questions,
        testDate: testData.date,
        startTime: testData.startTime,
        duration: testData.duration,
        numberOfQuestions: testData.numberOfQuestions,
        totalMarks: testData.totalMarks,
        testInstructions: testData.instructions,
        createdBy: testData.teacherId,
        active: initialActiveStatus, // Set initial active status based on start time
        lastStatusUpdate: currentDate
      });

      const teacher = await Teacher.findById(testData.teacherId);
      if (!teacher) {
        console.error("[ERROR] Teacher not found with ID:", testData.teacherId);
        return res.status(404).json({
          message: "Teacher not found",
          error: "Invalid teacher ID"
        });
      }

      await TestConfig.create({
        testId: newTest._id,
        isAdaptive: true,
        allowBatchProcessing: false,
        maxUsersForRealTime: 100,
        diagnosticTest: false
      });
      teacher.testHistory.push(newTest._id);
      await teacher.save();

      const student = await Student.findById(test.studentId);
      if (!student) {
        console.error("[ERROR] Student not found with ID:", test.studentId);
        return res.status(404).json({
          message: "Student not found",
          error: "Invalid student ID"
        });
      }

      if (!student.subjects || !Array.isArray(student.subjects)) {
        console.error("[ERROR] Student has no subjects array:", test.studentId);
        return res.status(400).json({
          message: "Student has no subjects configured",
          error: "Student subjects array is missing or invalid"
        });
      }

      const matchingSubject = student.subjects.find(
          subject => subject.subjectName.toLowerCase() === testData.subject.toLowerCase()
      );

      if (matchingSubject) {
          matchingSubject.testHistory.push(newTest._id);
          await student.save();
      } else {
          console.error("[WARNING] Student does not have subject:", testData.subject, "for student ID:", test.studentId);
      }
    }
    return res.status(201).json({
        message: "Test created successfully"
    });
  } catch (error) {
    console.error("[ERROR] creating personlized test", error);
    return res.status(400).json({
      message: "Error creating personalized test",
      error: error.message
    });
  }
}

export const createTest = async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            message: "Validation failed",
            errors: errors.array()
        });
    }

    try {
        const { testData } = req.body;
        const currentDate = new Date();

        // Calculate initial active status based on test date and start time
        const testDate = new Date(testData.date);
        let testStartDateTime = new Date(testDate);
        if (testData.startTime) {
            const [hours, minutes] = testData.startTime.split(':');
            testStartDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
        }

        // Add a 2-minute buffer to account for processing time and small delays
        const bufferTime = 2 * 60 * 1000; // 2 minutes in milliseconds
        const currentDateWithBuffer = new Date(currentDate.getTime() + bufferTime);
        const initialActiveStatus = testStartDateTime > currentDateWithBuffer;

        const test = await TestHistory.create({
            class: testData.class,
            subject: testData.subject,
            testType: testData.testType,
            topics: testData.topics,
            questions: testData.questions,
            testDate: testData.date,
            startTime: testData.startTime,
            duration: testData.duration,
            numberOfQuestions: testData.numberOfQuestions,
            totalMarks: testData.totalMarks,
            testInstructions: testData.instructions,
            createdBy: testData.teacherId,
            active: initialActiveStatus, // Set initial active status based on start time
            lastStatusUpdate: currentDate
        });

        await TestConfig.create({
            testId: test._id,
            isAdaptive: true,
            allowBatchProcessing: false,
            maxUsersForRealTime: 100,
            diagnosticTest: false
        })
        
        const user = await Student.findById(testData.teacherId);

        // If the user is a teacher, add the test to their test history
        if (!user) {
            const teacher = await Teacher.findById(testData.teacherId);
            if (!teacher) {
                console.error("[ERROR] Teacher not found with ID:", testData.teacherId);
                return res.status(404).json({
                    message: "Teacher not found",
                    error: "Invalid teacher ID"
                });
            }
            teacher.testHistory.push(test._id);
            await teacher.save();
        }

        const classObj = await Class.findById(testData.class);
        if (!classObj) {
            console.error("[ERROR] Class not found with ID:", testData.class);
            return res.status(404).json({
                message: "Class not found",
                error: "Invalid class ID"
            });
        }

        await Promise.all(classObj.students.map(async studentId => {
            try {
                const student = await Student.findById(studentId);

                if (!student) {
                    console.error("[WARNING] Student not found with ID:", studentId);
                    return; // Skip this student and continue with others
                }

                if (!student.subjects || !Array.isArray(student.subjects)) {
                    console.error("[WARNING] Student has no subjects array:", studentId);
                    return; // Skip this student and continue with others
                }

                const matchingSubject = student.subjects.find(
                    subject => subject.subjectName.toLowerCase() === testData.subject.toLowerCase()
                );

                if (matchingSubject) {
                    matchingSubject.testHistory.push(test._id);
                    await student.save();
                } else {
                    console.error("[WARNING] Student does not have subject:", testData.subject, "for student ID:", studentId);
                }
            } catch (studentError) {
                console.error("[ERROR] Error processing student:", studentId, studentError.message);
                // Continue with other students even if one fails
            }
        }));

        return res.status(201).json({
            test,
            message: "Test created successfully"
        });
    } catch (error) {
        return res.status(400).json({
            message: "Error creating test",
            error: error.message
        });
    }
};

export const updateTest = async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            message: "Validation failed",
            errors: errors.array()
        });
    }

    try {
        const { testID, testData } = req.body;
        const { date, startTime, duration, totalMarks, instructions } = testData;

        const currentDate = new Date();
        const newTestDate = new Date(testData.date);

        // Combine test date with start time to get the actual test start datetime
        let testStartDateTime = new Date(newTestDate);
        if (startTime) {
            const [hours, minutes] = startTime.split(':');
            testStartDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);
        }

        // Add a 2-minute buffer to account for processing time and small delays
        const bufferTime = 2 * 60 * 1000; // 2 minutes in milliseconds
        const currentDateWithBuffer = new Date(currentDate.getTime() + bufferTime);

        // Test should be active if the test start time is in the future (with buffer)
        const shouldBeActive = testStartDateTime > currentDateWithBuffer;

        // console.log("testID: ",testID, "testData: ",testData);
        // console.log("Current time:", currentDate.toISOString());
        // console.log("Test start time:", testStartDateTime.toISOString());
        // console.log("Should be active:", shouldBeActive);

        const updatedTest = await TestHistory.findByIdAndUpdate(
            testID,
            {
                testDate: date,
                startTime: startTime,
                duration: duration,
                totalMarks: totalMarks,
                testInstructions: instructions,
                active: shouldBeActive,
                lastStatusUpdate: currentDate
            },
            { new: true }
        );

        if (!updatedTest) {
            return res.status(404).json({
                message: "Test not found"
            });
        }

        return res.status(200).json({
            test: updatedTest,
            message: "Test updated successfully"
        });
    } catch (error) {
        return res.status(400).json({
            message: "Error updating test",
            error: error.message
        });
    }
};

export const deleteTest = async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({
            message: "Validation failed",
            errors: errors.array()
        });
    }

    try {
        const { testID: testId } = req.body;
        
        // First fetch the test to ensure it exists
        const test = await TestHistory.findById(testId);
        if (!test) {
            return res.status(404).json({
                message: "Test not found"
            });
        }

        // Use a transaction to ensure all operations succeed or fail together
        const session = await TestHistory.startSession();
        try {
            await session.withTransaction(async () => {
                // 1. Remove test reference from teacher
                await Teacher.findByIdAndUpdate(
                    test.createdBy,
                    { $pull: { testHistory: test._id } },
                    { session }
                );

                // 2. Remove test reference from students
                const classObj = await Class.findById(test.class).session(session);
                if (classObj) {
                    await Student.updateMany(
                        { _id: { $in: classObj.students } },
                        { 
                            $pull: { 
                                "subjects.$[subject].testHistory": test._id 
                            }
                        },
                        { 
                            arrayFilters: [{ "subject.subjectName": test.subject }],
                            session 
                        }
                    );
                }

                // 3. Delete the test config
                await TestConfig.findOneAndDelete(
                    { testId: test._id },
                    { session }
                );

                // 4. Finally delete the test itself
                await TestHistory.findByIdAndDelete(test._id, { session });
            });

            return res.status(200).json({
                message: "Test deleted successfully"
            });

        } catch (error) {
            throw error;
        } finally {
            session.endSession();
        }

    } catch (error) {
        console.error("Error deleting test:", error);
        return res.status(400).json({
            message: "Error deleting test",
            error: error.message
        });
    }
};
