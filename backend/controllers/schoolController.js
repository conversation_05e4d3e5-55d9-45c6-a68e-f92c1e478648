import School from '../models/School.js';
import crypto from 'crypto';

export async function getSchools(req, res) {
    try {
        const schools = await School.find();

        return res.status(200).json({
            schools,
        });
    } catch (error) {
        return res.status(400).json({ errors: error });
    }
}

function generateSchoolCode(name, pincode, phoneNumber) {
    // Normalize input by trimming and converting to uppercase for consistency
    const normalizedName = String(name).trim().toUpperCase();
    const normalizedPincode = String(pincode).trim();
    const normalizedPhone = String(phoneNumber).trim();
  
    // Combine the inputs into a single string using a delimiter
    const combinedString = `${normalizedName}-${normalizedPincode}-${normalizedPhone}`;
  
    // Create a hash of the combined string
    const hash = crypto.createHash('sha256')
                       .update(combinedString)
                       .digest('hex');
  
    // Optionally, shorten the hash to a manageable length (e.g., 8 characters)
    const schoolCode = hash.substring(0, 8).toUpperCase();
    return schoolCode;
  }
  

export async function createSchool(req, res) {
    try {
        const { name, pincode, phone} = req.body;

        const schoolCode = generateSchoolCode(name, pincode, phone);

        const school = await School.create({
            schoolCode: schoolCode,
            name,
            pincode,
            phone,
            students: [],
            classes: [],
            teachers: []
        });

        return res.status(201).json({
            message: 'School created successfully',
            school,
        });
    } catch (error) {
        return res.status(400).json({ errors: error });
    }
}

export async function updateSchool(req, res) {
    try {
        const { schoolId } = req.params;
        const { name, address, city, state, pincode, phone, email, website, students, teachers, classes } = req.body;

        const school = await School.findByIdAndUpdate(schoolId, {
            name,
            address,
            city,
            state,
            pincode,
            phone,
            email,
            website,
            students,
            teachers,
            classes
        }, { new: true });

        return res.status(200).json({
            message: 'School updated successfully',
            school,
        });
    } catch (error) {
        return res.status(400).json({ errors: error });
    }
}

export async function deleteSchool(req, res) {
    try {
        const { schoolId } = req.params;

        await School.findByIdAndDelete(schoolId);

        return res.status(200).json({ message: 'School deleted successfully' });
    } catch (error) {
        return res.status(400).json({ errors: error });
    }
}