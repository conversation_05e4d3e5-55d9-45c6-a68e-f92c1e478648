import Teacher from "../models/Teacher.js";
import Student from "../models/Student.js";


export async function createTeacherTask(req, res) {
    try {
        // console.error("teacher task body: ", req.body);
        const { userid: teacherID, task: task } = req.body;
        const teacher = await Teacher.findById(teacherID);
        if (!teacher) {
            return res.status(400).json({ message: "Invalid teacher" });
        }
        teacher.tasks.push(task);
        await teacher.save();
        // Get the newly created task
        const newTask = teacher.tasks[teacher.tasks.length - 1];

        // Respond with success message and the created task
        return res.status(201).json({
            message: "Task created successfully",
            task: newTask
        });
    } catch (error) {
        return res.status(400).json({ errors: error });
    }
}

export async function createStudentTask(req, res) {
    try {
        const { userid: studentID, task: task } = req.body;
        const student = await Student.findById(studentID);
        if (!student) {
            return res.status(400).json({ message: "Invalid student" });
        }
        student.tasks.push(task);
        await student.save();

        const newTask = student.tasks[student.tasks.length - 1];

        // Respond with success message and the created task
        return res.status(201).json({
            message: "Task created successfully",
            task: newTask
        });
    }
    catch (error) {
        return res.status(400).json({ errors: error });
    }
}

export async function updateTeacherTask(req, res) {
    try {
        const { userid: teacherID, taskid: taskID, task: updatedTask } = req.body;
        const teacher = await Teacher.findById(teacherID);
        if (!teacher) {
            return res.status(400).json({ message: "Invalid teacher" });
        }
        const task = teacher.tasks.id(taskID);
        if (!task) {
            return res.status(400).json({ message: "Invalid task" });
        }
        task.set(updatedTask);
        await teacher.save();
        return res.status(201).json({ message: "Task updated successfully" });
    }
    catch (error) {
        return res.status(400).json({ errors: error });
    }
}

export async function updateStudentTask(req, res) {
    try {
        const { userid: studentID, taskid: taskID, task: updatedTask } = req.body;
        const student = await Student.findById(studentID);
        if (!student) {
            return res.status(400).json({ message: "Invalid student" });
        }
        const task = student.tasks.id(taskID);
        if (!task) {
            return res.status(400).json({ message: "Invalid task" });
        }
        task.set(updatedTask);
        await student.save();
        return res.status(201).json({ message: "Task updated successfully" });
    }
    catch (error) {
        return res.status(400).json({ errors: error });
    }
}

export async function deleteTeacherTask(req, res) {
    try {
        const { userid: teacherID, taskid: taskID } = req.body;
        const teacher = await Teacher.findById(teacherID);
        if (!teacher) {
            return res.status(400).json({ message: "Invalid teacher" });
        }

        teacher.tasks.pull(taskID);
        await teacher.save();
        return res.status(200).json({ message: "Task deleted successfully" });
    }
    catch (error) {
        console.error(error);
        return res.status(400).json({ errors: error.message });
    }
}
export async function deleteStudentTask(req, res) {
    try {
        const { userid: studentID, taskid: taskID } = req.body;
        const student = await Student.findById(studentID);
        if (!student) {
            return res.status(400).json({ message: "Invalid student" });
        }
        student.tasks.pull(taskID);
        await student.save();
        return res.status(201).json({ message: "Task deleted successfully" });
    }
    catch (error) {
        return res.status(400).json({ errors: error });
    }
}
