import curriculumService from '../services/curriculumService.js';

class CurriculumController {
  async initializeCurriculum(req, res) {
    try {
      const curriculumData = req?.body?.curriculum;
      const relatedSubtopics = req?.body?.relatedSubtopics;
      const subject = req?.body?.subject;
      const result = await curriculumService.createCurriculumGraph(curriculumData, relatedSubtopics, subject);
      
      res.status(201).json({
        message: 'Curriculum graph created successfully',
        chapters: result.map(chapter => chapter.name),
      });
    } catch (error) {
      res.status(500).json({
        message: 'Error initializing curriculum',
        error: error.message,
      });
    }
  }

  async getCurriculumStructure(req, res) {
    try {
      const curriculum = await curriculumService.getCurriculumStructure();
      res.status(200).json(curriculum);
    } catch (error) {
      res.status(500).json({
        message: 'Error retrieving curriculum structure',
        error: error.message,
      });
    }
  }

  async getChapterSubtopics(req, res) {
    try {
      const { chapterName } = req.params;
      const subtopics = await curriculumService.getChapterSubtopics(chapterName);
      
      res.status(200).json(subtopics);
    } catch (error) {
      res.status(500).json({
        message: 'Error retrieving chapter subtopics',
        error: error.message,
      });
    }
  }
}

export default new CurriculumController();
