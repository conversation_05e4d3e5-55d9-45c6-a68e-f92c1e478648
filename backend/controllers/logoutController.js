import Student from "../models/Student.js";
import Teacher from "../models/Teacher.js";
import { validationResult } from "express-validator";

export const logoutStudent = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors });
  }
  try {
    // Accept either admissionNumber or email for flexibility
    const { admissionNumber, email } = req.body;
    let student;
    if (admissionNumber) {
      student = await Student.findOne({ admissionNumber });
    } else if (email) {
      student = await Student.findOne({ email });
    }
    if (!student) {
      return res.status(401).json({ message: "Invalid student" });
    }
    student.securityToken = undefined;
    await student.save();
    return res.status(201).json({ message: "logout successful" });
  } catch (error) {
    return res.status(500).json({ error: error });
  }
}

export const logoutTeacher = async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({errors: errors});
  }
  try {
    const email = req.body.email;
    const teacher = await Teacher.findOne({email});

    if (!teacher) {
      return res.status(401).json({message: "Invalid teacher"});
    }

    teacher.securityToken = undefined;
    await teacher.save();

    return res.status(201).json({message: "logout successful"});
  } catch (error) {
    return res.status(500).json({ error: error});
  }
}
