import { validationResult } from "express-validator";
import aiQuestion from "../models/AIQuestions.js";
import { createQuestionModel } from "../models/Question.js";
import QuestionTemp from "../models/QuestionTemp.js";
import { spawn} from "child_process";

export const mergeAIQ = async(req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({errors: errors.array()});
    }

    const collectionName = req.body.collection;
    const questions = await aiQuestion.find({metaDataCollection: collectionName});

    // const collectionObject = modelMap[collectionName];
    const collectionObject = createQuestionModel(collectionName);

    const alreadyPresentQuestions = await collectionObject.find();
    if (alreadyPresentQuestions !== undefined) {
      console.error(`[INFO] found apq length: ${alreadyPresentQuestions.length}`)
      console.error(`[INFO] found questions length: ${questions.length}`)
    }
    let uniqueQuestions = await pythonUniqueAdd(alreadyPresentQuestions, questions);
    if (!uniqueQuestions) {
      return res.status(500).json({message: "No unique questions found"});
    }
    uniqueQuestions = JSON.parse(uniqueQuestions);

    await collectionObject.insertMany(uniqueQuestions);
    // after merging, delete the questions from aiQuestions otherwise they might interfere with further merges
    await aiQuestion.deleteMany({metaDataCollection: collectionName});
    return res.status(201).json("Questions added successfully");
  } catch (error) {
    console.error("failed with error: ", error);
    return res.status(500).json({message: "Failure in question mergure"});
  }
}

const pythonUniqueAdd = async (collectionQuestions, addQuestions)=>{
  const pythonPath = process.env.NODE_ENV === "production" ? "/home/<USER>/app/python-backend/uniqueAIQuestions.py" : "./python-backend/uniqueAIQuestions.py";

  return new Promise((resolve, reject) => {
    // call the python program
    const pythonProcess = spawn("python3", [
      pythonPath,
    ], {shell: true});

    pythonProcess.stdin.write(JSON.stringify({"apq": collectionQuestions,"questions": addQuestions}));
    pythonProcess.stdin.end();

    let listOfQuestions = "";
    let errorOutput = "";

    pythonProcess.stdout.on("data", (data) => {
      // return the list of questions
      listOfQuestions += data.toString();
    });

    pythonProcess.stderr.on("data", (data) => {
      errorOutput += data.toString();
      console.error(`got stderr: ${data.toString()}`);
      // return res.status(501).json({ error: JSON.stringify(data) });
    });

    pythonProcess.on("error", (error) => {
      console.error(`got error: ${error.message}`);
      // return res.status(501).json({ error: JSON.stringify(error.message) });
      reject(new Error("failed to start python process: " + error.message));
    });

    pythonProcess.on("close", (code) => {
      if (code !== 0) {
        reject(new Error(`Python process exited with code ${code}: ${errorOutput}`));
      } else {
        resolve(listOfQuestions);
      }
    });
  });
}
