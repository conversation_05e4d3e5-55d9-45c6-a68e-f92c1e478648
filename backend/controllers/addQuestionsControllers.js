import { validationResult } from "express-validator";
import curriculumService from "../services/curriculumService.js";
import aiQuestion from "../models/AIQuestions.js";
import { spawn} from "child_process";

const extracttopiclist = async (curriculumStructure, topiclist, subtopiclist) => {
  for (let i = 0; i < curriculumStructure.length; i++) {
    topiclist.push(curriculumStructure[i].chapter.name);
    for (let j=0; j<curriculumStructure[i].subtopics.length; j++) {
      subtopiclist.push(curriculumStructure[i].subtopics[j].name);
    }
  }
}

export const addQuestions = async(req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({errors: errors.array()});
    }

    const fileName = req.body.filepath; // this is the pdf folder
    const metadata = req.body.metadata;
    const subject = req.body.subject;
    const classDetail = req.body.classDetail;
    // also need the topic and subtopic list
    let topiclist = [];
    let subtopiclist = []; 

    const curriculumStructure = await curriculumService.getCurriculumStructureClassWise(subject, classDetail);

    await extracttopiclist(curriculumStructure, topiclist, subtopiclist);

    const data = {
      filepath : fileName,
      topiclist : topiclist,
      subtopiclist : subtopiclist
    }

    // console.error(`[INFO] data: ${JSON.stringify(data)}`);

    const questions = await addDataPython(data); // a list of all the questions in the pdf
    if (!questions) {
      return res.status(402, {message: "[ERROR] questions not found"});
    }
    const JSONquestions = JSON.parse(questions);
    if (!JSONquestions) {
      return res.status(500).json({message: "Failure in question extraction"});
    }

    // add the question to the database
    console.error(`[INFO] length of questions: ${JSONquestions.length}`)
    for (let i = 0; i < JSONquestions.length; i++) {
      const dbQuestion = new aiQuestion({
        passage: JSONquestions[i].passage,
        question: JSONquestions[i].question,
        options: JSONquestions[i].options,
        answer: JSONquestions[i].answer,
        solution: JSONquestions[i].solution,
        topic: JSONquestions[i].topic,
        subtopic: JSONquestions[i].subtopic,
        difficulty: JSONquestions[i].difficulty,
        discriminatingParameter: JSONquestions[i].differentiatingParameter,
        bloomTaxonomy: JSONquestions[i].bloomTaxonomy,
        ansimage: JSONquestions[i].aimg,
        queimage: JSONquestions[i].qimg,
        metaDataCollection: metadata,
      });

      await dbQuestion.save();
    }
    console.error("done");
    return res.status(201).json({message: "[SUCCESS] Questions added successfully"});
  }
  catch (error) {
    console.error("[ERROR] ", error);
    return res.status(500).json({message: "[ERROR] Failure in question extraction"});
  }
}

function addDataPython(data) {
  const pythonPath = process.env.NODE_ENV === "production" ? "/home/<USER>/app/python-backend/visionApi.py" : "./python-backend/visionApi.py";

  return new Promise((resolve, reject) => {
    // call the python program
    const pythonProcess = spawn("python3", [
      pythonPath,
      JSON.stringify(data),
    ]);

    let listOfQuestions = "";
    let errorOutput = "";

    pythonProcess.stdout.on("data", (data) => {
      // return the list of questions
      listOfQuestions += data.toString();
    });

    pythonProcess.stderr.on("data", (data) => {
      errorOutput += data.toString();
      console.error(`[ERROR] stderr: ${data.toString()}`);
      // return res.status(501).json({ error: JSON.stringify(data) });
    });

    pythonProcess.on("error", (error) => {
      console.error(`[ERROR] ${error.message}`);
      // return res.status(501).json({ error: JSON.stringify(error.message) });
      reject(new Error("[ERROR] failed to start python process: " + error.message));
    });

    pythonProcess.on("close", (code) => {
      if (code !== 0) {
        reject(new Error(`[ERROR] Python process exited with code ${code}: ${errorOutput}`));
      } else {
        resolve(listOfQuestions);
      }
    });
  });
}
