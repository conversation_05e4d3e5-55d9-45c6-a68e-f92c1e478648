import { model } from "mongoose";
import Student from "../models/Student.js";
import Teacher from "../models/Teacher.js";
import School from "../models/School.js";
import { validationResult } from "express-validator";
import multer from "multer";

function validateRequest(req, res) {
  const errors = validationResult(req);

  if (!errors.isEmpty()) {
    console.error(`[ERROR]: ${JSON.stringify(errors)}`);
    res.status(400).json({ errors: errors.array() });
    return false;
  }
  return true;
}


// Multer configuration
const multerConfig = {
    storage: multer.memoryStorage(),
    limits: {
        fileSize: 10 * 1024 * 1024, 
    },
    fileFilter: (req, file, cb) => {
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg', 'image/webp'];
        if (!allowedTypes.includes(file.mimetype)) {
            cb(new Error('Invalid file type. Only JPEG, PNG and GIF are allowed.'));
            return;
        }
        cb(null, true);
    }
};


export async function uploadProfileImage(req, res) {
  try {
    if (!validateRequest(req, res)) return;
    const upload = multer(multerConfig).single('profileImage');
    
    await new Promise((resolve, reject) => {
      upload(req, res, (err) => err ? reject(err) : resolve());
    });

    if (!req.file) {
      return res.status(400).json({ message: 'No profile image uploaded.' });
    }

    const { userId } = req.body;
    console.error(`[DEBUG] Uploading profile image for userId: ${userId}`);

    // Find the student or teacher by userId
    let user;
    if (req.body.role === 'Student') {
      user = await Student.findById(userId);
    } else if (req.body.role === 'Teacher') {
      user = await Teacher.findById(userId);
    }

    if (!user) {
      return res.status(404).json({ message: 'User not found.' });
    }

    // Update the profile image
    user.profileImage = {
      filename: req.file.originalname,
      imageData: req.file.buffer,
      contentType: req.file.mimetype
    };

    await user.save();

    return res.status(200).json({ message: 'Profile image uploaded successfully.' });
  } catch (error) {
    console.error(`[ERROR] Failed to upload profile image: ${error}`);
    return res.status(500).json({ error: 'Failed to upload profile image.' });
  }
}

export async function getStudentDetails(req, res) {
  try {
    if (!validateRequest(req, res)) return;
    const email = req.body.email;
    console.error(`[DEBUG] getStudentDetails called with email: ${email}`);

    const student = await Student.findOne({$or: [{email: email}, {username: email}]})
      .populate({
        path: "classes",
        populate: {
          path: "teacherId",
          model: "Teacher",
          select: "username email"
        }
      })
      .populate({
        path: "subjects",
        populate: {
          path: "testHistory",
          model: "TestHistory",
        }
      });

    if (!student) {
      console.error(`[DEBUG] Student not found for email: ${email}`);
      return res.status(400).json({ message: "Invalid student" });
    }

    // Convert profile image buffer to data URL if present
    let profileImage = undefined;
    if (student.profileImage && student.profileImage.imageData) {
      profileImage = {
        filename: student.profileImage.filename,
        contentType: student.profileImage.contentType,
        imageData: `data:${student.profileImage.contentType};base64,${student.profileImage.imageData.toString('base64')}`
      };
    }

    const data = {
      id: student._id,
      username: student.username,
      firstName: student.firstName,
      lastName: student.lastName,
      email: student.email,
      classId: student.classId,
      schoolCode: student.schoolCode,
      profileImage: profileImage,
      subjects: student.subjects, // Now includes populated test history
      role: "Student",
      tasks: student.tasks,
      classes: student.classes,
    };

    return res.status(201).json(data);
  } catch (error) {
    return res.status(400).json({ errors: error });
  }
}

export async function getTeacherDetails(req, res) {
  try {
    if (!validateRequest(req, res)) return;
    const email = req.body.email;

    // Populate classes when querying the teacher
    const teacher = await Teacher.findOne({$or: [{ email }, {username: email}]})
      .populate([{
        path: "classes",
        populate: {
          path: "students",
          model: "Student",
          populate: {
            path: "subjects",
            populate: {
              path: "testHistory",
              model: "TestHistory"
            }
          }
        } }, {
        path: "testHistory",
      }]);

    if (!teacher) {
      return res.status(400).json({ message: "Invalid teacher email" });
    }

    // Convert profile image buffer to data URL if present
    let profileImage = undefined;
    if (teacher.profileImage && teacher.profileImage.imageData) {
      profileImage = {
        filename: teacher.profileImage.filename,
        contentType: teacher.profileImage.contentType,
        imageData: `data:${teacher.profileImage.contentType};base64,${teacher.profileImage.imageData.toString('base64')}`
      };
    }

    const data = {
      id: teacher._id,
      username: teacher.username,
      email: teacher.email,
      profileImage: profileImage,
      subjects: teacher.subjects,
      schoolCode: teacher.schoolCode,
      role: teacher.role,
      tasks: teacher.tasks,
      classes: teacher.classes, // Now classes are already populated
      testHistory: teacher.testHistory,
    };

    return res.status(200).json(data); // Changed to 200 since it's a successful GET
  } catch (error) {
    return res.status(400).json({ error: JSON.stringify(error.message) }); // Better error handling
  }
}

export async function editStudentDetails(req, res) {
  try {
    if (!validateRequest(req, res)) return;
    const o_uname = req.body.o_uname;
    const newemail = req.body.new_email;
    const newuname = req.body.new_uname;

    const newdoc = await Student.findOneAndUpdate(
      { username: o_uname },
      { email: newemail, username: newuname },
      { new: true }
    );
  } catch (e) {
    console.error(`[ERROR] Failed to edit student details: ${e}`);
    return res.status(400).json({ errors: e });
  }
}

export async function editTeacherDetails(req, res) {
  try {
    if (!validateRequest(req, res)) return;
    const o_uname = req.body.o_uname;
    const newemail = req.body.new_email;
    const newuname = req.body.new_uname;

    const newdoc = await Teacher.findOneAndUpdate(
      { username: o_uname },
      { email: newemail, username: newuname },
      { new: true }
    );
  } catch (e) {
    return res.status(400).json({ errors: e });
  }
}

export async function getSchoolDetailsByCode(req, res) {
  try {
    if (!validateRequest(req, res)) return;
    const schoolCode = req.params.schoolCode;

    const schoolInfo = await School.findOne({ schoolCode: schoolCode })

    return res.status(200).json({
      schoolCode: schoolInfo.schoolCode,
      name: schoolInfo.name,
      pincode: schoolInfo.pincode,
      phone: schoolInfo.phone
    });
  }
  catch (error) {
    return res.status(400).json({ errors: error });
  }
}