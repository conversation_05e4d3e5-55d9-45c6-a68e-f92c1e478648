import { validationResult } from "express-validator";
import aiQuestion from "../models/AIQuestions.js";
import { createQuestionModel } from "../models/Question.js";
import { spawn} from "child_process";

export const upscValidate = async (req, res) => {

  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({errors: errors.array()});
    }

    const tobemergedCollection = req.body.collection_name;
    const solution_file = req.body.solution_file;

    const questions = await aiQuestion.find({metaDataCollection: tobemergedCollection});

    console.log("got questions length: ", questions.length);

    await pythonValidator(solution_file, questions);
    res.status(201).json("Questions validated successfully");

  } catch (error) {
    console.log("failed with error: ", error);
    return res.status(500).json({message: "Failure in question validation"});
  }
}

const pythonValidator = async(solution_file, questions) => {
  const pythonPath = process.env.NODE_ENV === "production" ? "/home/<USER>/app/python-backend/upscQuestionSolutionValidator.py" : "./python-backend/upscQuestionSolutionValidator.py";

  return new Promise((resolve, reject) => {
    // call the python program
    const pythonProcess = spawn("python3", [
      pythonPath,
    ], {shell: true});

    pythonProcess.stdin.write(JSON.stringify({"questions": questions,"filename": solution_file})); // need to be modified a little bit
    pythonProcess.stdin.end();

    let errorOutput = "";


    pythonProcess.stderr.on("data", (data) => {
      errorOutput += data.toString();
      console.error(`got stderr: ${data.toString()}`);
      // return res.status(501).json({ error: JSON.stringify(data) });
    });

    pythonProcess.on("error", (error) => {
      console.error(`got error: ${error.message}`);
      // return res.status(501).json({ error: JSON.stringify(error.message) });
      reject(new Error("failed to start python process: " + error.message));
    });

    pythonProcess.on("close", (code) => {
      if (code !== 0) {
        reject(new Error(`Python process exited with code ${code}: ${errorOutput}`));
      } else {
        resolve("Python process completed successfully");
      }
    });
  });
}

