import { StudentCurriculumModel } from "../models/StudentKnowledgeGraphModel.js";
import mongoose from 'mongoose';
import { createKnowledgeGraphModel } from "../models/knowledgeGraphModel.js";

export const getStudentKnowledgeGraph = async (req, res) => {
  try {
    const { id: studentId, subject } = req.params;
    console.error(`Fetching ${subject} knowledge graph for student: ${studentId}`);

    // Define the correct model name once
    const correctModelName = `CurriculumNode_${subject}`;

    // Ensure the dynamic model exists for the subject
    if (!mongoose.models[correctModelName]) {
      console.error(`Creating model for subject: ${subject}`);
      createKnowledgeGraphModel(subject);
    }

    // Find and update the student curriculum in one operation if needed
    const studentCurriculum = await StudentCurriculumModel.findOne({ studentId, 'curriculumProgress.subject': subject });
    if (!studentCurriculum) {
      return res.status(404).json({ message: "Student curriculum not found" });
    }

    // Check and update node models in one pass
    let needsSave = false;
    for (const progress of studentCurriculum.curriculumProgress) {
      if (progress.nodeModel !== correctModelName) {
        studentCurriculum.setNodeModelForSubject(progress, subject);
        needsSave = true;
      }
    }

    if (needsSave) {
      await studentCurriculum.save();
    }

    // Get populated data in a single query with efficient projection
    const populatedCurriculum = await StudentCurriculumModel.findOne({ studentId, 'curriculumProgress.subject': subject })
      .populate({
        path: "curriculumProgress.nodeId",
        model: correctModelName,
        select: "_id name type description parents children _parentModel _childModel"
      });

    // Filter valid progress items for the subject
    const populatedProgress = populatedCurriculum.curriculumProgress.filter(
      progress => progress.subject === subject && progress.nodeId
    );

    // Batch populate parents and children
    const parentModelMap = new Map();
    const childModelMap = new Map();

    // Prepare batch queries by grouping by model
    for (const progress of populatedProgress) {
      const node = progress.nodeId;
      if (typeof node !== 'object') continue;

      // Prepare parent queries
      if (node.parents?.length && node._parentModel) {
        if (!parentModelMap.has(node._parentModel)) {
          parentModelMap.set(node._parentModel, []);
        }
        const parentIds = node.parents.map(p => (typeof p === 'object' ? p._id : p));
        parentModelMap.get(node._parentModel).push({
          node,
          ids: parentIds
        });
      }

      // Prepare child queries
      if (node.children?.length && node._childModel) {
        if (!childModelMap.has(node._childModel)) {
          childModelMap.set(node._childModel, []);
        }
        const childIds = node.children.map(c => (typeof c === 'object' ? c._id : c));
        childModelMap.get(node._childModel).push({
          node,
          ids: childIds
        });
      }
    }

    // Execute batch parent queries
    for (const [modelName, items] of parentModelMap.entries()) {
      // Ensure the model is registered before using it
      let parentModel;
      if (mongoose.models[modelName]) {
        parentModel = mongoose.models[modelName];
      } else {
        // Extract subject from model name and create the model
        const subjectFromModel = modelName.replace('CurriculumNode_', '');
        parentModel = createKnowledgeGraphModel(subjectFromModel);
      }

      const allParentIds = items.flatMap(item => item.ids);
      const parents = await parentModel.find({
        _id: { $in: allParentIds }
      }).select("_id name type description");

      const parentsById = new Map(parents.map(p => [p._id.toString(), p]));
      
      // Assign parents to their respective nodes
      for (const item of items) {
        item.node.parents = item.ids
          .map(id => parentsById.get(id?.toString()))
          .filter(Boolean);
      }
    }

    // Execute batch child queries
    for (const [modelName, items] of childModelMap.entries()) {
      // Ensure the model is registered before using it
      let childModel;
      if (mongoose.models[modelName]) {
        childModel = mongoose.models[modelName];
      } else {
        // Extract subject from model name and create the model
        const subjectFromModel = modelName.replace('CurriculumNode_', '');
        childModel = createKnowledgeGraphModel(subjectFromModel);
      }

      const allChildIds = items.flatMap(item => item.ids);
      const children = await childModel.find({
        _id: { $in: allChildIds }
      }).select("_id name type description");

      const childrenById = new Map(children.map(c => [c._id.toString(), c]));
      
      // Assign children to their respective nodes
      for (const item of items) {
        item.node.children = item.ids
          .map(id => childrenById.get(id?.toString()))
          .filter(Boolean);
      }
    }

    // Assemble and send the result
    const result = {
      studentId: studentCurriculum.studentId,
      subject,
      curriculumProgress: populatedProgress,
      totalSubjectNodes: populatedProgress.length
    };

    console.error(`Successfully fetched ${populatedProgress.length} nodes for ${subject}`);
    res.status(200).json(result);
  } catch (error) {
    console.error("Error details:", error);
    res.status(500).json({
      message: "Error fetching student knowledge graph",
      error: error.message
    });
  }
};