import Student from "../models/Student.js"
import {createQuestionModel} from "../models/Question.js";
import { getCollectionNameTest } from "../services/testService.js";
import TestHistory from "../models/TestHistory.js";

export const getResultById = async (req, res) => {
    try {
        const { studentId, testId } = req.params;
        // console.error("studentId:", studentId);
        // console.error("testId:", testId);
        const student = await Student.findById(studentId)
        .populate({
            path: 'subjects',
            populate: {
                path: 'attemptedTests'
            }
        });
        // console.error("Student:", student);
        if (!student) {
            return res.status(404).json({
                message: "Student not found"
            });
        }

        let test, sub;
        student.subjects.forEach(subject => {
            const foundTest = subject.attemptedTests.find(t => t.testId.toString() === testId);
            if (foundTest) {
                test = foundTest;
                sub = subject.subjectName;
            }
        });
        // Fetch questions for all responses concurrently using Promise.all
        const collectionName = await getCollectionNameTest(testId, sub);
        const questions = await Promise.all(
            test.responses.map(async response => {
                const question = await createQuestionModel(collectionName).findById(response.questionId);
                return question;
            })
        );

        // Fetch test details from TestHistory
        const testDetails = await TestHistory.findById(testId)
            .populate({
                path: "class",
                model: "Class",
                select: "className classStd"
            });

        // console.log("questions:", questions);

        if (!test) {
            return res.status(404).json({
                message: "Test result not found"
            });
        }

        return res.status(200).json({
            test,
            questions,
            testDetails,
            message: "Test result fetched successfully"
        });
    } catch (error) {
        return res.status(400).json({
            message: "Error fetching test result",
            error: error.message
        });
    }
};
