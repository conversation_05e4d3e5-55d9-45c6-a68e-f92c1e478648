import Student from "../models/Student.js";
import TestHistory from "../models/TestHistory.js";
import Class from "../models/Class.js";

/**
 * Get all submissions for a specific test
 * This endpoint efficiently fetches all student submissions for a test using MongoDB aggregation
 * to avoid the performance issue of iterating through all students individually
 */
export const getTestSubmissions = async (req, res) => {
    try {
        const { testId } = req.params;
        console.log('Fetching submissions for testId:', testId);

        // First, get the test details to find the associated class
        const testDetails = await TestHistory.findById(testId)
            .populate({
                path: "class",
                model: "Class",
                select: "className classStd students"
            });

        if (!testDetails) {
            return res.status(404).json({
                success: false,
                message: "Test not found"
            });
        }

        // Use a simpler approach: get all students in the class and then find their test attempts
        const classStudents = await Student.find(
            { _id: { $in: testDetails.class.students } },
            { username: 1, firstName: 1, lastName: 1, email: 1, profileImage: 1, subjects: 1 }
        );

        // Process each student to find their test attempt
        const submissions = classStudents.map(student => {
            let testAttempt = null;

            // Look through all subjects to find the test attempt
            for (const subject of student.subjects) {
                if (subject.attemptedTests && subject.attemptedTests.length > 0) {
                    const attempt = subject.attemptedTests.find(
                        test => test.testId && test.testId.toString() === testDetails._id.toString()
                    );
                    if (attempt) {
                        testAttempt = attempt;
                        break;
                    }
                }
            }

            return {
                studentId: student._id,
                studentName: (student.firstName && student.lastName)
                    ? `${student.firstName} ${student.lastName}`
                    : student.username,
                email: student.email,
                profileImage: student.profileImage,
                testAttempt: testAttempt,
                status: testAttempt ? 'completed' : 'not-attempted'
            };
        });

        // Transform the aggregation result to match the frontend interface
        const formattedSubmissions = submissions.map(submission => {
            if (submission.status === 'completed' && submission.testAttempt) {
                const attempt = submission.testAttempt;
                const timeSpentMinutes = attempt.totalTimeTaken ? Math.round(attempt.totalTimeTaken / 60) : 0;
                const percentage = Math.round((attempt.totalScore / testDetails.numberOfQuestions) * 100);

                return {
                    studentId: submission.studentId.toString(),
                    studentName: submission.studentName,
                    email: submission.email,
                    profileImage: submission.profileImage,
                    score: attempt.totalScore,
                    totalScore: testDetails.totalMarks,
                    percentage: percentage,
                    timeSpent: timeSpentMinutes,
                    submittedAt: attempt.endTime || attempt.startTime,
                    status: 'completed'
                };
            } else {
                return {
                    studentId: submission.studentId.toString(),
                    studentName: submission.studentName,
                    email: submission.email,
                    profileImage: submission.profileImage,
                    score: 0,
                    totalScore: testDetails.totalMarks,
                    percentage: 0,
                    timeSpent: 0,
                    submittedAt: null,
                    status: 'not-attempted'
                };
            }
        });

        // Calculate statistics
        const completedSubmissions = formattedSubmissions.filter(s => s.status === 'completed');
        const totalStudents = testDetails.class.students.length;
        
        const stats = {
            totalStudents: totalStudents,
            submittedCount: completedSubmissions.length,
            averageScore: completedSubmissions.length > 0 
                ? completedSubmissions.reduce((sum, s) => sum + s.score, 0) / completedSubmissions.length 
                : 0,
            highestScore: completedSubmissions.length > 0 
                ? Math.max(...completedSubmissions.map(s => s.score)) 
                : 0,
            lowestScore: completedSubmissions.length > 0 
                ? Math.min(...completedSubmissions.map(s => s.score)) 
                : 0,
            averageTime: completedSubmissions.length > 0 
                ? completedSubmissions.reduce((sum, s) => sum + s.timeSpent, 0) / completedSubmissions.length 
                : 0
        };

        return res.status(200).json({
            success: true,
            submissions: formattedSubmissions,
            stats: stats,
            testDetails: {
                id: testDetails._id,
                subject: testDetails.subject,
                topics: testDetails.topics,
                className: testDetails.class.className,
                classStd: testDetails.class.classStd,
                numberOfQuestions: testDetails.numberOfQuestions,
                totalMarks: testDetails.totalMarks,
                duration: testDetails.duration,
                testDate: testDetails.testDate,
                startTime: testDetails.startTime
            },
            message: "Test submissions fetched successfully"
        });

    } catch (error) {
        console.error('Error fetching test submissions:', error);
        return res.status(500).json({
            success: false,
            message: "Error fetching test submissions",
            error: error.message
        });
    }
};
