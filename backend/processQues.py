import re

def standardize_questions(input_text):
    """
    Standardize different question formats to the first format:
    question: [text]
    options:
    A) [option]
    B) [option]
    C) [option]
    D) [option]
    answer: [answer]
    question image description: [description]
    answer image description: [description]
    explanation: [explanation]
    """
    
    # Split the text into individual questions more carefully
    questions = []
    
    # First, split by "Question X (Category)" pattern to handle this format
    question_pattern = r'(Question\s+\d+\s*\([^)]+\))'
    parts = re.split(question_pattern, input_text, flags=re.IGNORECASE)
    
    if len(parts) > 1:
        # We found "Question X (Category)" patterns
        current_question = ""
        for i, part in enumerate(parts):
            part = part.strip()
            if not part:
                continue
                
            # Check if this is a question header
            if re.match(r'^Question\s+\d+\s*\([^)]+\)', part, re.IGNORECASE):
                # Save previous question if exists
                if current_question:
                    questions.append(current_question)
                current_question = part
            else:
                # This is content that belongs to the current question
                if current_question:
                    current_question += "\n" + part
                else:
                    current_question = part
        
        # Don't forget the last question
        if current_question:
            questions.append(current_question)
    else:
        # Fallback to original method if no "Question X (Category)" patterns found
        potential_blocks = re.split(r'\n\s*\n+', input_text.strip())
        
        current_question = ""
        for block in potential_blocks:
            block = block.strip()
            if not block:
                continue
                
            # Check if this block starts a new question
            if re.match(r'^\s*(?:question|Question):', block, re.IGNORECASE):
                # Save previous question if exists
                if current_question:
                    questions.append(current_question)
                current_question = block
            else:
                # Add to current question
                if current_question:
                    current_question += "\n\n" + block
                else:
                    current_question = block 
        
        # Don't forget the last question
        if current_question:
            questions.append(current_question)
    
    standardized_questions = []
    
    for i, question_block in enumerate(questions):
        question_block = question_block.strip() 
        if not question_block:
            continue
            
        try:
            parsed = parse_question_block(question_block) 
            # Ensure that a question was actually parsed before formatting
            if parsed and parsed.get('question'):
                formatted = format_question(parsed)
                standardized_questions.append(formatted)
            elif question_block: # If the block is not empty but couldn't be parsed as a question
                print(f"Warning: Could not parse question-like block {i+1} or question text is missing.")
                print(f"Block preview: {question_block[:150]}...")
        except Exception as e:
            print(f"Error parsing question block {i+1}: {e}")
            print(f"Block content: {question_block[:200]}...")
            continue 
    
    return '\n\n'.join(standardized_questions)

def parse_question_block(block):
    """Parse a question block and extract components"""
    
    question_text = ""
    options = []
    answer = ""
    question_image_desc = ""
    answer_image_desc = ""
    explanation = ""

    # Extract question text - handle both "question:" and "Question X (Category)" formats
    question_match = re.search(r'(?:question|Question):\s*(.*?)(?=\s*(?:options|Options|answer|Answer):|$)', block, re.IGNORECASE | re.DOTALL)
    if question_match:
        question_text = question_match.group(1).strip()
    else:
        # Handle "Question X (Category)" format
        question_header_match = re.match(r'^\s*Question\s+\d+\s*\([^)]+\)\s*\n*(.*?)(?=\s*(?:options|Options|answer|Answer):|$)', block, re.IGNORECASE | re.DOTALL)
        if question_header_match:
            question_text = question_header_match.group(1).strip()
        elif re.match(r'^\s*(?:question|Question):', block, re.IGNORECASE):
            parts = re.split(r'\s*(?:options|Options|answer|Answer):', block, 1, flags=re.IGNORECASE)
            q_text_candidate = re.sub(r'^\s*(?:question|Question):\s*', '', parts[0], flags=re.IGNORECASE).strip()
            question_text = q_text_candidate
    
    # Extract options - handle both uppercase and lowercase letters
    options_section_match = re.search(r'(?:options|Options):\s*(.*?)(?=\s*(?:answer|Answer|question image description|Question Image Description|explanation|Explanation):|$)', block, re.IGNORECASE | re.DOTALL)
    if options_section_match:
        options_text = options_section_match.group(1).strip()
        # First try to find options with any separator
        raw_options = re.findall(r'([A-Da-d])\s*[\)\.\uff09]\s*(.*?)(?=(?:\s*[A-Da-d]\s*[\)\.\uff09]|$))', options_text, re.DOTALL)
        
        # If that doesn't work, try with any non-alphanumeric separator
        if not raw_options: 
            raw_options = re.findall(r'([A-Da-d])\s*\W\s*(.*?)(?=(?:\s*[A-Da-d]\s*\W|$))', options_text, re.DOTALL)

        for letter, text in raw_options:
            clean_text = ' '.join(text.strip().split()) 
            if clean_text:
                options.append(f"{letter.upper()}) {clean_text}")
    else:
        # If no "options:" section found, try to extract options directly from the block
        # Look for lines that start with a letter followed by a separator
        option_lines = re.findall(r'^([A-Da-d])\s*[\)\.\uff09]\s*(.*?)$', block, re.MULTILINE | re.IGNORECASE)
        if not option_lines:
            option_lines = re.findall(r'^([A-Da-d])\s*\W\s*(.*?)$', block, re.MULTILINE | re.IGNORECASE)
        
        for letter, text in option_lines:
            clean_text = ' '.join(text.strip().split()) 
            if clean_text and not any(keyword in clean_text.lower() for keyword in ['answer:', 'explanation:', 'question image']):
                options.append(f"{letter.upper()}) {clean_text}")
    
    # Extract answer
    answer_match_regex = r'(?:answer|Answer):\s*(.*?)(?=\s*(?:question image|Question Image|answer image|Answer Image|explanation|Explanation|\*\*|$))'
    answer_match = re.search(answer_match_regex, block, re.IGNORECASE | re.DOTALL)
    
    if answer_match:
        raw_answer_from_line = answer_match.group(1).strip()
        answer = raw_answer_from_line # Initialize answer with the raw extracted value

        # First, try to strip common prefixes from answers that have them
        original_answer = answer
        
        # Pattern 1: Letter) Text (standard parenthesis)
        stripped_candidate = re.sub(r'^[a-dA-D]\s*\)\s*', '', raw_answer_from_line, flags=re.IGNORECASE).strip()
        if len(stripped_candidate) < len(raw_answer_from_line) and stripped_candidate:
            answer = stripped_candidate
        else:
            # Pattern 2: Letter. Text (dot)
            stripped_candidate = re.sub(r'^[a-dA-D]\s*\.\s*', '', raw_answer_from_line, flags=re.IGNORECASE).strip()
            if len(stripped_candidate) < len(raw_answer_from_line) and stripped_candidate:
                answer = stripped_candidate
            else:
                # Pattern 3: Letter）Text (full-width parenthesis)
                stripped_candidate = re.sub(r'^[a-dA-D]\s*\uff09\s*', '', raw_answer_from_line, flags=re.IGNORECASE).strip()
                if len(stripped_candidate) < len(raw_answer_from_line) and stripped_candidate:
                    answer = stripped_candidate
                else:
                    # Pattern 4: Letter [ANY NON-ALPHANUM] Text (more general)
                    if not (raw_answer_from_line.isalnum() and len(raw_answer_from_line) == 1):
                        stripped_candidate = re.sub(r'^[a-dA-D]\s*\W\s*', '', raw_answer_from_line, flags=re.IGNORECASE).strip()
                        if len(stripped_candidate) < len(raw_answer_from_line) and stripped_candidate:
                            answer = stripped_candidate

        # If after stripping we still have the original answer and it's just a single letter, 
        # then expand it using options
        if answer == original_answer and answer.upper() in ['A', 'B', 'C', 'D'] and len(answer) == 1 and options:
            option_letter_to_find = answer.upper()
            for opt_text in options: 
                if opt_text.upper().startswith(option_letter_to_find + ')'):
                    answer = opt_text[len(option_letter_to_find)+1:].strip() 
                    break
    
    # Extract question image description
    q_img_match = re.search(r'(?:question image description|Question Image Description):\s*(.*?)(?=\s*(?:answer image description|Answer Image Description|explanation|Explanation|\*\*|$))', block, re.IGNORECASE | re.DOTALL)
    if q_img_match:
        question_image_desc = q_img_match.group(1).strip()
    
    # Extract answer image description
    a_img_match = re.search(r'(?:answer image description|Answer Image Description):\s*(.*?)(?=\s*(?:explanation|Explanation|\*\*|$))', block, re.IGNORECASE | re.DOTALL)
    if a_img_match:
        answer_image_desc = a_img_match.group(1).strip()
        answer_image_desc = re.sub(r'\s*\*\*\s*$', '', answer_image_desc)
    
    # Extract explanation
    exp_match = re.search(r'(?:explanation|Explanation):\s*(.*?)(?:\s*\*\*\s*Bloom|\s*\(Bloom\'s Taxonomy:|$)', block, re.IGNORECASE | re.DOTALL)
    if exp_match:
        explanation_candidate = exp_match.group(1).strip()
        
        # Handle Bloom's taxonomy information
        bloom_info = ""
        end_of_exp_candidate_in_block = -1
        if explanation_candidate:
            try:
                expl_keyword_start = block.lower().find("explanation:")
                if expl_keyword_start != -1:
                    start_search_for_candidate = expl_keyword_start + len("explanation:")
                    pos_of_candidate = block.find(explanation_candidate, start_search_for_candidate)
                    if pos_of_candidate != -1:
                         end_of_exp_candidate_in_block = pos_of_candidate + len(explanation_candidate)
                    else:
                         end_of_exp_candidate_in_block = start_search_for_candidate + len(explanation_candidate)
            except:
                pass 

        search_for_bloom_in_block = block
        if end_of_exp_candidate_in_block != -1 and end_of_exp_candidate_in_block < len(block):
            search_for_bloom_in_block = block[end_of_exp_candidate_in_block:]

        bloom_match_in_block_remainder = re.search(r'(\(\s*Bloom\'s Taxonomy:.*?\)\s*)$', search_for_bloom_in_block, re.IGNORECASE | re.DOTALL)
        if not bloom_match_in_block_remainder: 
             bloom_match_in_block_remainder = re.search(r'(\(\s*Bloom\'s Taxonomy:.*?\)\s*)', block, re.IGNORECASE | re.DOTALL)

        if bloom_match_in_block_remainder:
            bloom_info = bloom_match_in_block_remainder.group(1).strip()
            if explanation_candidate.endswith(bloom_info):
                explanation = explanation_candidate 
            else:
                current_block_text_after_expl_keyword = ""
                expl_keyword_start = block.lower().find("explanation:")
                if expl_keyword_start != -1:
                    current_block_text_after_expl_keyword = block[expl_keyword_start + len("explanation:"):].strip()

                if bloom_info and (explanation_candidate + " " + bloom_info).strip() == current_block_text_after_expl_keyword:
                    explanation = (explanation_candidate + " " + bloom_info).strip()
                else: 
                    explanation = explanation_candidate
                    if bloom_info and not explanation_candidate.strip().endswith(bloom_info.strip()):
                         explanation = (explanation_candidate.strip() + " " + bloom_info.strip()).strip()
        else:
            explanation = explanation_candidate

        explanation = re.sub(r'^\s*\*\*', '', explanation).strip() 
        explanation = re.sub(r'\s*\*\*\s*$', '', explanation).strip()

    return {
        'question': question_text,
        'options': options,
        'answer': answer, 
        'question_image_desc': question_image_desc,
        'answer_image_desc': answer_image_desc,
        'explanation': explanation
    }

def format_question(parsed):
    """Format parsed question components into the standard format"""
    
    formatted = []
    
    if parsed.get('question'): 
        formatted.append(f"question: {parsed['question']}")
    
    if parsed.get('options'):
        formatted.append("options:")
        for option in parsed['options']:
            formatted.append(option)
    
    if parsed.get('answer'): 
        formatted.append(f"answer: {parsed['answer']}")
    
    if parsed.get('question_image_desc'):
        formatted.append(f"question image description: {parsed['question_image_desc']}")
    
    if parsed.get('answer_image_desc'):
        formatted.append(f"answer image description: {parsed['answer_image_desc']}")
    
    if parsed.get('explanation'):
        formatted.append(f"explanation: {parsed['explanation']}")
    
    return '\n'.join(formatted)

def main():
    """
    Main function to read questions from 'input_questions.txt',
    standardize them, and write to 'standardized_questions.txt'.
    """
    try:
        with open('input_questions.txt', 'r', encoding='utf-8') as file:
            input_text = file.read()
    except FileNotFoundError:
        print("Error: 'input_questions.txt' not found. Please create the file with your question data.")
        return
    
    if not input_text.strip():
        print("Warning: 'input_questions.txt' is empty. No output will be generated.")
        with open('standardized_questions.txt', 'w', encoding='utf-8') as file:
            file.write("")
        return

    standardized_text = standardize_questions(input_text)
    
    with open('standardized_questions.txt', 'w', encoding='utf-8') as file:
        file.write(standardized_text)
    
    print(f"Questions have been standardized and saved to 'standardized_questions.txt'")
    num_processed = len([q for q in standardized_text.split('\n\n') if q.strip()])
    print(f"Processed {num_processed} questions.")

if __name__ == "__main__":
    main()