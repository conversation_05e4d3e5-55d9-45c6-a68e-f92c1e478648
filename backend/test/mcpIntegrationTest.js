import mongoMcpService from '../services/mongoMcpService.js';
import { config } from 'dotenv';

config();

/**
 * Test script for MongoDB MCP integration
 * Run with: node test/mcpIntegrationTest.js
 */
async function testMcpIntegration() {
  console.log('🧪 Testing MongoDB MCP Integration...\n');

  try {
    // Test 1: Initialize MCP Service
    console.log('1️⃣ Testing MCP Service Initialization...');
    const initialized = await mongoMcpService.initialize();
    
    if (!initialized) {
      console.log('❌ MCP initialization failed - this is expected if MongoDB MCP server is not available');
      console.log('ℹ️  The system will automatically fall back to traditional data fetching');
      return;
    }
    
    console.log('✅ MCP Service initialized successfully');

    // Test 2: Check if service is ready
    console.log('\n2️⃣ Testing Service Readiness...');
    const isReady = mongoMcpService.isReady();
    console.log(`Service ready: ${isReady ? '✅' : '❌'}`);

    if (!isReady) {
      console.log('❌ Service not ready, skipping further tests');
      return;
    }

    // Test 3: Test basic database connection
    console.log('\n3️⃣ Testing Database Connection...');
    try {
      const dbList = await mongoMcpService.executeTool('list-databases');
      console.log('✅ Database connection successful');
      console.log('Available databases:', dbList.content?.map(db => db.name) || 'Unable to list');
    } catch (error) {
      console.log('❌ Database connection test failed:', error.message);
    }

    // Test 4: Test user knowledge graph retrieval (with mock data)
    console.log('\n4️⃣ Testing User Knowledge Graph Retrieval...');
    try {
      // Use a test student ID - replace with actual ID from your database
      const testStudentId = '507f1f77bcf86cd799439011'; // Mock ObjectId
      const testSubject = 'Mathematics';
      
      const userContext = await mongoMcpService.getUserKnowledgeGraph(testStudentId, testSubject);
      console.log('✅ User knowledge graph retrieval successful');
      console.log('Retrieved context keys:', Object.keys(userContext));
    } catch (error) {
      console.log('⚠️  User knowledge graph test failed (expected with mock data):', error.message);
    }

    // Test 5: Test enhanced prompt generation
    console.log('\n5️⃣ Testing Enhanced Prompt Generation...');
    try {
      const testStudentId = '507f1f77bcf86cd799439011'; // Mock ObjectId
      const testSubject = 'Mathematics';
      const testMessage = 'What should I study next?';
      const testHistory = [
        { type: 'user', content: 'Hello' },
        { type: 'assistant', content: 'Hi! How can I help you today?' }
      ];
      
      const enhancedPrompt = await mongoMcpService.generateEnhancedPrompt(
        testStudentId, 
        testSubject, 
        testMessage, 
        testHistory
      );
      
      console.log('✅ Enhanced prompt generation successful');
      console.log('Prompt length:', enhancedPrompt.length, 'characters');
      console.log('Contains database context:', enhancedPrompt.includes('STUDENT CONTEXT') ? '✅' : '❌');
    } catch (error) {
      console.log('⚠️  Enhanced prompt generation test failed:', error.message);
    }

    // Test 6: Cleanup
    console.log('\n6️⃣ Testing Service Cleanup...');
    await mongoMcpService.disconnect();
    console.log('✅ Service disconnected successfully');

  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }

  console.log('\n🏁 MCP Integration Test Complete');
  console.log('\n📋 Summary:');
  console.log('- If MCP initialization failed, the system will use traditional data fetching');
  console.log('- This ensures your chatbot continues to work regardless of MCP availability');
  console.log('- Check server logs for detailed MCP status information');
}

// Run the test
testMcpIntegration().catch(console.error);
