import mongoose from 'mongoose';
import { migrateHardcodedProficiencies } from '../services/proficiencyCalculationService.js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

/**
 * Migration script to update hardcoded proficiency values to calculated ones
 */
async function runMigration() {
    try {
        // Determine MongoDB URI based on environment
        const nodeEnv = process.env.NODE_ENV || 'development';
        let mongoUri;

        if (nodeEnv === 'production') {
            mongoUri = process.env.MONGO_URI_PROD;
        } else {
            mongoUri = process.env.MONGO_URI_TEST;
        }

        // Fallback to local MongoDB if no environment URI is found
        if (!mongoUri) {
            mongoUri = 'mongodb://localhost:27017/aegisscholar';
            console.log('Warning: No MongoDB URI found in environment variables, using local fallback');
        }

        console.log(`Connecting to MongoDB (${nodeEnv} environment)...`);
        await mongoose.connect(mongo<PERSON><PERSON>, {
            serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
            socketTimeoutMS: 45000, // Close sockets after 45s of inactivity
        });
        console.log('Connected to MongoDB successfully');

        // Parse command line arguments
        const args = process.argv.slice(2);
        const dryRun = args.includes('--dry-run');
        const batchSize = parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || 100;

        console.log(`Starting proficiency migration...`);
        console.log(`Dry run: ${dryRun}`);
        console.log(`Batch size: ${batchSize}`);

        // Run the migration
        const result = await migrateHardcodedProficiencies({
            dryRun,
            batchSize,
            weightingStrategy: 'nodeType',
            topicWeight: 1.2,
            subtopicWeight: 1.0
        });

        console.log('\nMigration Results:');
        console.log('==================');
        console.log(`Total students found: ${result.totalStudents}`);
        
        if (!dryRun) {
            console.log(`Successfully updated: ${result.updated}`);
            console.log(`Failed updates: ${result.failed}`);
        }
        
        console.log(`Message: ${result.message}`);

        if (dryRun) {
            console.log('\nThis was a dry run. No changes were made to the database.');
            console.log('Run without --dry-run to perform the actual migration.');
        }

    } catch (error) {
        console.error('Migration failed:', error.message);

        if (error.name === 'MongooseServerSelectionError') {
            console.error('\nConnection troubleshooting:');
            console.error('1. Check if MongoDB is running');
            console.error('2. Verify the connection string in your .env file');
            console.error('3. Check network connectivity');
            console.error('4. Ensure the database credentials are correct');
        }

        process.exit(1);
    } finally {
        // Close the database connection
        await mongoose.disconnect();
        console.log('\nDisconnected from MongoDB');
        process.exit(0);
    }
}

// Handle script execution
if (import.meta.url === `file://${process.argv[1]}`) {
    console.log('Proficiency Migration Script');
    console.log('============================');
    console.log('Usage: node migrateProficiencies.js [--dry-run] [--batch-size=100]');
    console.log('');
    
    runMigration();
}

export default runMigration;
