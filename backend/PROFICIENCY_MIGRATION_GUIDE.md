# Proficiency Migration Guide

## Overview

This guide explains how to migrate from hardcoded proficiency values (0.5) to dynamically calculated proficiency values based on actual curriculum node performance.

## Prerequisites

1. **Backend server must be running** on localhost:8080
2. **MongoDB connection** must be working
3. **Environment variables** properly configured in `.env` file

## Migration Options

### Option 1: API-Based Migration (Recommended)

The proficiency routes are now integrated into the main application. Use the API endpoints for migration:

#### Step 1: Test Connection
```bash
# Check if server is running
curl http://localhost:8080/api/auth/health
```

#### Step 2: Dry Run Migration
```bash
curl -X POST http://localhost:8080/api/proficiency/migrate \
     -H "Content-Type: application/json" \
     -d '{"dryRun": true, "batchSize": 10}'
```

Expected response:
```json
{
  "success": true,
  "data": {
    "totalStudents": 150,
    "dryRun": true,
    "message": "Dry run completed - no changes made"
  }
}
```

#### Step 3: Actual Migration
```bash
curl -X POST http://localhost:8080/api/proficiency/migrate \
     -H "Content-Type: application/json" \
     -d '{"dryRun": false, "batchSize": 50}'
```

Expected response:
```json
{
  "success": true,
  "data": {
    "totalStudents": 150,
    "updated": 145,
    "failed": 5,
    "message": "Migration completed: 145 updated, 5 failed"
  }
}
```

### Option 2: Standalone Script Migration

If you prefer to run the migration as a standalone script:

#### Step 1: Update Environment
Make sure your `.env` file has the correct MongoDB URIs:
```env
MONGO_URI_TEST=mongodb+srv://...
MONGO_URI_PROD=mongodb+srv://...
NODE_ENV=development
```

#### Step 2: Run Migration Script
```bash
# Navigate to backend directory
cd backend

# Dry run
node scripts/migrateProficiencies.js --dry-run

# Actual migration
node scripts/migrateProficiencies.js --batch-size=50
```

## API Endpoints

### 1. Migration Endpoint
**POST** `/api/proficiency/migrate`

**Body:**
```json
{
  "dryRun": false,
  "batchSize": 50,
  "weightingStrategy": "nodeType",
  "topicWeight": 1.2,
  "subtopicWeight": 1.0
}
```

### 2. Individual Recalculation
**PUT** `/api/proficiency/recalculate/:studentId/:subject`

**Body:**
```json
{
  "weightingStrategy": "nodeType",
  "topicWeight": 1.2,
  "subtopicWeight": 1.0
}
```

### 3. Batch Recalculation
**POST** `/api/proficiency/batch-recalculate`

**Body:**
```json
{
  "updates": [
    {"studentId": "student1", "subject": "mathematics"},
    {"studentId": "student2", "subject": "physics"}
  ],
  "weightingStrategy": "nodeType",
  "topicWeight": 1.2,
  "subtopicWeight": 1.0
}
```

### 4. Proficiency Statistics
**GET** `/api/proficiency/stats/:studentId/:subject`

**Response:**
```json
{
  "success": true,
  "data": {
    "studentId": "student1",
    "subject": "mathematics",
    "stats": {
      "totalNodes": 25,
      "averageProficiency": 78.5,
      "currentStoredProficiency": 75.0,
      "nodeBreakdown": {
        "chapters": 5,
        "subtopics": 20
      },
      "proficiencyDistribution": {
        "excellent": 5,
        "good": 8,
        "average": 7,
        "needsImprovement": 5
      }
    }
  }
}
```

## Weighting Strategies

### 1. Equal Weighting (default)
All curriculum nodes have equal importance.

### 2. Node Type Weighting
- Chapters: weight = 1.2 (higher importance)
- Subtopics: weight = 1.0 (standard importance)

### 3. Order Weighting
Later topics get higher weights based on complexity.

## Troubleshooting

### Connection Issues

**Error:** `MongooseServerSelectionError: connect ECONNREFUSED`

**Solutions:**
1. Check if MongoDB is running
2. Verify connection string in `.env` file
3. Check network connectivity
4. Ensure database credentials are correct

### Authentication Issues

**Error:** `401 Unauthorized`

**Solutions:**
1. Some endpoints might require authentication
2. Check if JWT middleware is properly configured
3. Use admin credentials if required

### Migration Failures

**Error:** Individual student updates failing

**Solutions:**
1. Check student data integrity
2. Verify curriculum progress exists
3. Check for missing node references
4. Review error logs for specific issues

## Verification

### 1. Check Migration Results
```bash
# Get stats for a specific student
curl http://localhost:8080/api/proficiency/stats/STUDENT_ID/mathematics
```

### 2. Compare Before/After
- Before: `overallProficiency` should be 0.5 (hardcoded)
- After: `overallProficiency` should be calculated value (0-1 range)

### 3. Database Verification
```javascript
// MongoDB query to check updated records
db.students.find({
  "subjects.overallProficiency": { $ne: 0.5 }
}).count()
```

## Rollback Plan

If migration causes issues:

1. **Stop the application**
2. **Restore database backup** (if available)
3. **Revert code changes** to use hardcoded values
4. **Investigate and fix issues**
5. **Re-run migration** with fixes

## Monitoring

After migration, monitor:

1. **Application performance** - Check if calculations impact response times
2. **Data accuracy** - Verify proficiency values make sense
3. **User feedback** - Check if analytics and progress tracking work correctly
4. **Error logs** - Monitor for calculation errors

## Support

If you encounter issues:

1. Check the logs in `backend/logs/` (if logging is configured)
2. Review the error messages in the API responses
3. Use the dry run mode to test before actual migration
4. Test with a small batch size first

## Next Steps

After successful migration:

1. **Remove hardcoded defaults** from new student registrations
2. **Update frontend** to use new proficiency values
3. **Monitor performance** and optimize if needed
4. **Consider implementing** real-time proficiency updates
