import distributedLock from './distributedLock.js';
import dataIntegrityChecker from './dataIntegrityChecker.js';
import fs from 'fs';
import path from 'path';

/**
 * Security System Initialization
 * Sets up all security components and performs initial checks
 */
class SecurityInitializer {
    
    async initialize() {
        console.log('🔒 Initializing AegisGrader Security Systems...');
        
        try {
            // 1. Initialize distributed lock system
            await this.initializeDistributedLocks();
            
            // 2. Create log directories
            await this.createLogDirectories();
            
            // 3. Run initial data integrity check
            await this.runInitialIntegrityCheck();
            
            // 4. Set up periodic cleanup tasks
            this.setupPeriodicTasks();
            
            console.log('✅ Security systems initialized successfully');
            
            return {
                success: true,
                message: 'All security systems operational'
            };
            
        } catch (error) {
            console.error('❌ Security initialization failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    async initializeDistributedLocks() {
        try {
            await distributedLock.initialize();
            console.log('✅ Distributed lock system initialized');
        } catch (error) {
            console.error('❌ Failed to initialize distributed locks:', error);
            throw error;
        }
    }
    
    async createLogDirectories() {
        try {
            const logDir = path.join(process.cwd(), 'logs');
            
            if (!fs.existsSync(logDir)) {
                fs.mkdirSync(logDir, { recursive: true });
                console.log('✅ Log directories created');
            } else {
                console.log('✅ Log directories already exist');
            }
        } catch (error) {
            console.error('❌ Failed to create log directories:', error);
            throw error;
        }
    }
    
    async runInitialIntegrityCheck() {
        try {
            console.log('🔍 Running initial data integrity check...');
            
            const integrityReport = await dataIntegrityChecker.runSystemIntegrityCheck();
            
            if (integrityReport.error) {
                console.warn('⚠️ Integrity check failed:', integrityReport.error);
                return;
            }
            
            console.log(`📊 Integrity Check Results:`);
            console.log(`   Total Users: ${integrityReport.totalUsers}`);
            console.log(`   Valid: ${integrityReport.validUsers}`);
            console.log(`   Invalid: ${integrityReport.invalidUsers}`);
            console.log(`   Integrity: ${integrityReport.integrityPercentage.toFixed(2)}%`);
            
            if (integrityReport.invalidUsers > 0) {
                console.warn(`⚠️ Found ${integrityReport.invalidUsers} users with integrity issues`);
                console.log('💡 Run dataIntegrityChecker.fixUserCreditIntegrity() for each affected user');
            } else {
                console.log('✅ All user data integrity checks passed');
            }
            
            // Check for orphaned transactions
            const orphanedTransactions = await dataIntegrityChecker.findOrphanedTransactions();
            if (orphanedTransactions.length > 0) {
                console.warn(`⚠️ Found ${orphanedTransactions.length} orphaned transactions`);
            } else {
                console.log('✅ No orphaned transactions found');
            }
            
            // Check for duplicate transactions
            const duplicateTransactions = await dataIntegrityChecker.findDuplicateTransactions();
            if (duplicateTransactions.length > 0) {
                console.warn(`⚠️ Found ${duplicateTransactions.length} potential duplicate transactions`);
            } else {
                console.log('✅ No duplicate transactions found');
            }
            
        } catch (error) {
            console.error('❌ Initial integrity check failed:', error);
            // Don't throw - this shouldn't prevent startup
        }
    }
    
    setupPeriodicTasks() {
        try {
            // Clean up expired locks every 5 minutes
            setInterval(async () => {
                try {
                    await distributedLock.cleanupExpiredLocks();
                } catch (error) {
                    console.error('Error in periodic lock cleanup:', error);
                }
            }, 5 * 60 * 1000);
            
            // Run integrity check every 24 hours
            setInterval(async () => {
                try {
                    console.log('🔍 Running scheduled integrity check...');
                    const report = await dataIntegrityChecker.runSystemIntegrityCheck();
                    
                    if (report.invalidUsers > 0) {
                        console.warn(`⚠️ Scheduled check found ${report.invalidUsers} users with integrity issues`);
                    }
                } catch (error) {
                    console.error('Error in scheduled integrity check:', error);
                }
            }, 24 * 60 * 60 * 1000);
            
            console.log('✅ Periodic security tasks scheduled');
            
        } catch (error) {
            console.error('❌ Failed to setup periodic tasks:', error);
            throw error;
        }
    }
    
    /**
     * Health check for all security systems
     */
    async healthCheck() {
        const health = {
            distributedLocks: false,
            auditLogging: false,
            dataIntegrity: false,
            overall: false
        };
        
        try {
            // Test distributed lock
            const testLock = await distributedLock.acquire('health_check_test', 1000);
            if (testLock) {
                await distributedLock.release('health_check_test');
                health.distributedLocks = true;
            }
            
            // Test audit logging
            const logDir = path.join(process.cwd(), 'logs');
            health.auditLogging = fs.existsSync(logDir);
            
            // Test data integrity checker
            const testCheck = await dataIntegrityChecker.findOrphanedTransactions();
            health.dataIntegrity = Array.isArray(testCheck);
            
            health.overall = health.distributedLocks && health.auditLogging && health.dataIntegrity;
            
        } catch (error) {
            console.error('Security health check failed:', error);
        }
        
        return health;
    }
}

export default new SecurityInitializer();
