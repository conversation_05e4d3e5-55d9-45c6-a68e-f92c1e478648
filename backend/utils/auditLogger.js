import fs from 'fs';
import path from 'path';

/**
 * Security Audit Logger for Payment System
 * Logs all security-relevant events for monitoring and compliance
 */
class AuditLogger {
    constructor() {
        this.logDir = path.join(process.cwd(), 'logs');
        this.ensureLogDirectory();
    }

    ensureLogDirectory() {
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
        }
    }

    formatLogEntry(level, event, data) {
        return JSON.stringify({
            timestamp: new Date().toISOString(),
            level,
            event,
            data,
            pid: process.pid,
            hostname: process.env.HOSTNAME || 'unknown'
        }) + '\n';
    }

    writeLog(filename, entry) {
        const logPath = path.join(this.logDir, filename);
        fs.appendFileSync(logPath, entry);
    }

    // Payment-specific audit events
    logPaymentOrderCreated(userId, userType, packageId, orderId, amount) {
        const entry = this.formatLogEntry('INFO', 'PAYMENT_ORDER_CREATED', {
            userId,
            userType,
            packageId,
            orderId,
            amount,
            ip: this.getClientIP()
        });
        this.writeLog('payment-audit.log', entry);
    }

    logPaymentVerificationAttempt(userId, orderId, paymentId, success, reason) {
        const entry = this.formatLogEntry(success ? 'INFO' : 'WARN', 'PAYMENT_VERIFICATION', {
            userId,
            orderId,
            paymentId,
            success,
            reason,
            ip: this.getClientIP()
        });
        this.writeLog('payment-audit.log', entry);
    }

    logSecurityViolation(type, details) {
        const entry = this.formatLogEntry('ERROR', 'SECURITY_VIOLATION', {
            type,
            details,
            ip: this.getClientIP(),
            userAgent: this.getUserAgent()
        });
        this.writeLog('security-violations.log', entry);
    }

    logWebhookReceived(event, orderId, signature, valid) {
        const entry = this.formatLogEntry(valid ? 'INFO' : 'WARN', 'WEBHOOK_RECEIVED', {
            event,
            orderId,
            signatureValid: valid,
            ip: this.getClientIP()
        });
        this.writeLog('webhook-audit.log', entry);
    }

    logCreditBalanceChange(userId, userType, oldBalance, newBalance, reason, transactionId) {
        const entry = this.formatLogEntry('INFO', 'CREDIT_BALANCE_CHANGE', {
            userId,
            userType,
            oldBalance,
            newBalance,
            change: newBalance - oldBalance,
            reason,
            transactionId
        });
        this.writeLog('credit-audit.log', entry);
    }

    // Helper methods (would be injected in real implementation)
    getClientIP() {
        return 'unknown'; // Would be injected from request
    }

    getUserAgent() {
        return 'unknown'; // Would be injected from request
    }
}

export default new AuditLogger();
