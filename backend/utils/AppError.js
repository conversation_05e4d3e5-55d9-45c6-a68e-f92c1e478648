class AppError extends Error {
  constructor(message, statusCode, errors = [], metadata = {}) {
    super(message);
    this.statusCode = statusCode;
    this.status = `${statusCode}`.startsWith('4') ? 'fail' : 'error';
    this.isOperational = true;
    this.errors = errors;
    this.metadata = metadata;

    Error.captureStackTrace(this, this.constructor);
  }

  static badRequest(message, errors = []) {
    return new AppError(message, 400, errors);
  }

  static unauthorized(message = 'Unauthorized access', metadata = {}) {
    return new AppError(message, 401, [], metadata);
  }

  static forbidden(message = 'Forbidden access', metadata = {}) {
    return new AppError(message, 403, [], metadata);
  }

  static notFound(message = 'Resource not found') {
    return new AppError(message, 404);
  }

  static serverError(message = 'Internal server error') {
    return new AppError(message, 500);
  }
  
  static internalError(message = 'Internal server error') {
    return new AppError(message, 500);
  }
  
  static validationError(message = 'Validation failed', errors = []) {
    return new AppError(message, 422, errors);
  }
  
  static conflict(message = 'Resource conflict') {
    return new AppError(message, 409);
  }
  
  static tooManyRequests(message = 'Too many requests') {
    return new AppError(message, 429);
  }
  
  static serviceUnavailable(message = 'Service unavailable') {
    return new AppError(message, 503);
  }
}

export default AppError;
