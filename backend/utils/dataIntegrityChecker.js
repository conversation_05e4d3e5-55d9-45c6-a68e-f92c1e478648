import Student from '../models/Student.js';
import Teacher from '../models/Teacher.js';
import CreditTransaction, { TRANSACTION_STATUS, TRANSACTION_TYPES } from '../models/CreditTransaction.js';

/**
 * Data Integrity Checker for Credit System
 * Validates consistency between user balances and transaction records
 */
class DataIntegrityChecker {
    
    /**
     * Verify user's credit balance matches transaction history
     * @param {string} userId - User ID
     * @param {string} userType - 'Student' or 'Teacher'
     * @returns {Promise<Object>} - Integrity check result
     */
    async verifyUserCreditIntegrity(userId, userType) {
        try {
            const UserModel = userType === 'Student' ? Student : Teacher;
            const user = await UserModel.findById(userId).select('credits billing');
            
            if (!user) {
                return { valid: false, error: 'User not found' };
            }

            // Calculate expected balance from transactions
            const transactions = await CreditTransaction.find({
                userId,
                userType,
                status: TRANSACTION_STATUS.COMPLETED
            }).sort({ createdAt: 1 });

            let calculatedBalance = 0;
            let calculatedEarned = 0;
            let calculatedSpent = 0;
            let calculatedAmountSpent = 0;

            for (const transaction of transactions) {
                if (transaction.type === TRANSACTION_TYPES.PURCHASE || 
                    transaction.type === TRANSACTION_TYPES.BONUS ||
                    transaction.type === TRANSACTION_TYPES.INITIAL_GRANT) {
                    calculatedBalance += transaction.creditAmount;
                    calculatedEarned += transaction.creditAmount;
                    
                    if (transaction.payment?.amount) {
                        calculatedAmountSpent += transaction.payment.amount;
                    }
                } else if (transaction.type === TRANSACTION_TYPES.USAGE) {
                    calculatedBalance -= transaction.creditAmount;
                    calculatedSpent += transaction.creditAmount;
                }
            }

            const currentBalance = user.credits?.balance || 0;
            const currentEarned = user.credits?.totalEarned || 0;
            const currentSpent = user.credits?.totalSpent || 0;
            const currentAmountSpent = user.billing?.totalAmountSpent || 0;

            const discrepancies = [];

            if (Math.abs(currentBalance - calculatedBalance) > 0.01) {
                discrepancies.push({
                    field: 'balance',
                    expected: calculatedBalance,
                    actual: currentBalance,
                    difference: currentBalance - calculatedBalance
                });
            }

            if (Math.abs(currentEarned - calculatedEarned) > 0.01) {
                discrepancies.push({
                    field: 'totalEarned',
                    expected: calculatedEarned,
                    actual: currentEarned,
                    difference: currentEarned - calculatedEarned
                });
            }

            if (Math.abs(currentSpent - calculatedSpent) > 0.01) {
                discrepancies.push({
                    field: 'totalSpent',
                    expected: calculatedSpent,
                    actual: currentSpent,
                    difference: currentSpent - calculatedSpent
                });
            }

            if (Math.abs(currentAmountSpent - calculatedAmountSpent) > 0.01) {
                discrepancies.push({
                    field: 'totalAmountSpent',
                    expected: calculatedAmountSpent,
                    actual: currentAmountSpent,
                    difference: currentAmountSpent - calculatedAmountSpent
                });
            }

            return {
                valid: discrepancies.length === 0,
                userId,
                userType,
                discrepancies,
                summary: {
                    balance: { expected: calculatedBalance, actual: currentBalance },
                    earned: { expected: calculatedEarned, actual: currentEarned },
                    spent: { expected: calculatedSpent, actual: currentSpent },
                    amountSpent: { expected: calculatedAmountSpent, actual: currentAmountSpent }
                }
            };
        } catch (error) {
            return { valid: false, error: error.message };
        }
    }

    /**
     * Fix user's credit balance based on transaction history
     * @param {string} userId - User ID
     * @param {string} userType - 'Student' or 'Teacher'
     * @returns {Promise<Object>} - Fix result
     */
    async fixUserCreditIntegrity(userId, userType) {
        const integrityCheck = await this.verifyUserCreditIntegrity(userId, userType);
        
        if (integrityCheck.valid) {
            return { fixed: false, message: 'No integrity issues found' };
        }

        if (integrityCheck.error) {
            return { fixed: false, error: integrityCheck.error };
        }

        try {
            const UserModel = userType === 'Student' ? Student : Teacher;
            const { summary } = integrityCheck;

            await UserModel.findByIdAndUpdate(userId, {
                $set: {
                    'credits.balance': summary.balance.expected,
                    'credits.totalEarned': summary.earned.expected,
                    'credits.totalSpent': summary.spent.expected,
                    'credits.lastUpdated': new Date(),
                    'billing.totalAmountSpent': summary.amountSpent.expected
                }
            });

            return {
                fixed: true,
                discrepancies: integrityCheck.discrepancies,
                correctedValues: {
                    balance: summary.balance.expected,
                    totalEarned: summary.earned.expected,
                    totalSpent: summary.spent.expected,
                    totalAmountSpent: summary.amountSpent.expected
                }
            };
        } catch (error) {
            return { fixed: false, error: error.message };
        }
    }

    /**
     * Run integrity check on all users
     * @returns {Promise<Object>} - System-wide integrity report
     */
    async runSystemIntegrityCheck() {
        const results = {
            totalUsers: 0,
            validUsers: 0,
            invalidUsers: 0,
            discrepancies: []
        };

        try {
            // Check all students
            const students = await Student.find({}).select('_id');
            for (const student of students) {
                results.totalUsers++;
                const check = await this.verifyUserCreditIntegrity(student._id, 'Student');
                
                if (check.valid) {
                    results.validUsers++;
                } else {
                    results.invalidUsers++;
                    results.discrepancies.push({
                        userId: student._id,
                        userType: 'Student',
                        issues: check.discrepancies || [check.error]
                    });
                }
            }

            // Check all teachers
            const teachers = await Teacher.find({}).select('_id');
            for (const teacher of teachers) {
                results.totalUsers++;
                const check = await this.verifyUserCreditIntegrity(teacher._id, 'Teacher');
                
                if (check.valid) {
                    results.validUsers++;
                } else {
                    results.invalidUsers++;
                    results.discrepancies.push({
                        userId: teacher._id,
                        userType: 'Teacher',
                        issues: check.discrepancies || [check.error]
                    });
                }
            }

            results.integrityPercentage = results.totalUsers > 0 
                ? (results.validUsers / results.totalUsers) * 100 
                : 100;

            return results;
        } catch (error) {
            return { error: error.message };
        }
    }

    /**
     * Check for orphaned transactions
     * @returns {Promise<Array>} - List of orphaned transactions
     */
    async findOrphanedTransactions() {
        try {
            const orphanedTransactions = [];
            
            const transactions = await CreditTransaction.find({}).select('userId userType transactionId');
            
            for (const transaction of transactions) {
                const UserModel = transaction.userType === 'Student' ? Student : Teacher;
                const user = await UserModel.findById(transaction.userId);
                
                if (!user) {
                    orphanedTransactions.push({
                        transactionId: transaction.transactionId,
                        userId: transaction.userId,
                        userType: transaction.userType
                    });
                }
            }

            return orphanedTransactions;
        } catch (error) {
            throw new Error(`Error finding orphaned transactions: ${error.message}`);
        }
    }

    /**
     * Check for duplicate transactions
     * @returns {Promise<Array>} - List of potential duplicates
     */
    async findDuplicateTransactions() {
        try {
            const duplicates = await CreditTransaction.aggregate([
                {
                    $group: {
                        _id: {
                            userId: '$userId',
                            'payment.razorpayPaymentId': '$payment.razorpayPaymentId'
                        },
                        count: { $sum: 1 },
                        transactions: { $push: '$transactionId' }
                    }
                },
                {
                    $match: {
                        count: { $gt: 1 },
                        '_id.payment.razorpayPaymentId': { $ne: null }
                    }
                }
            ]);

            return duplicates;
        } catch (error) {
            throw new Error(`Error finding duplicate transactions: ${error.message}`);
        }
    }
}

export default new DataIntegrityChecker();
