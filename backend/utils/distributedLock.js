import mongoose from 'mongoose';

/**
 * Distributed Lock Implementation using MongoDB
 * Prevents race conditions in payment processing
 */
class DistributedLock {
    constructor() {
        this.lockCollection = mongoose.connection.collection('payment_locks');
        this.defaultTTL = 30000; // 30 seconds
    }

    /**
     * Acquire a lock for a specific resource
     * @param {string} resource - Resource identifier (e.g., orderId)
     * @param {number} ttl - Time to live in milliseconds
     * @returns {Promise<boolean>} - True if lock acquired, false otherwise
     */
    async acquire(resource, ttl = this.defaultTTL) {
        const lockId = `payment_${resource}`;
        const expiresAt = new Date(Date.now() + ttl);

        try {
            // Try to insert a new lock document
            await this.lockCollection.insertOne({
                _id: lockId,
                resource,
                expiresAt,
                createdAt: new Date()
            });
            
            console.log(`Lock acquired for resource: ${resource}`);
            return true;
        } catch (error) {
            if (error.code === 11000) {
                // Duplicate key error - lock already exists
                console.log(`Lock already exists for resource: ${resource}`);
                return false;
            }
            throw error;
        }
    }

    /**
     * Release a lock for a specific resource
     * @param {string} resource - Resource identifier
     * @returns {Promise<boolean>} - True if lock released, false if not found
     */
    async release(resource) {
        const lockId = `payment_${resource}`;

        try {
            const result = await this.lockCollection.deleteOne({ _id: lockId });
            const released = result.deletedCount > 0;
            
            if (released) {
                console.log(`Lock released for resource: ${resource}`);
            } else {
                console.log(`No lock found to release for resource: ${resource}`);
            }
            
            return released;
        } catch (error) {
            console.error(`Error releasing lock for resource ${resource}:`, error);
            throw error;
        }
    }

    /**
     * Execute a function with a distributed lock
     * @param {string} resource - Resource identifier
     * @param {Function} fn - Function to execute
     * @param {number} ttl - Lock TTL in milliseconds
     * @returns {Promise<any>} - Result of the function
     */
    async withLock(resource, fn, ttl = this.defaultTTL) {
        const lockAcquired = await this.acquire(resource, ttl);
        
        if (!lockAcquired) {
            throw new Error(`Unable to acquire lock for resource: ${resource}`);
        }

        try {
            return await fn();
        } finally {
            await this.release(resource);
        }
    }

    /**
     * Clean up expired locks
     * Should be called periodically
     */
    async cleanupExpiredLocks() {
        try {
            const result = await this.lockCollection.deleteMany({
                expiresAt: { $lt: new Date() }
            });
            
            if (result.deletedCount > 0) {
                console.log(`Cleaned up ${result.deletedCount} expired locks`);
            }
        } catch (error) {
            console.error('Error cleaning up expired locks:', error);
        }
    }

    /**
     * Initialize the lock collection with TTL index
     */
    async initialize() {
        try {
            // Create TTL index for automatic cleanup
            await this.lockCollection.createIndex(
                { expiresAt: 1 },
                { expireAfterSeconds: 0 }
            );
            console.log('Distributed lock system initialized');
        } catch (error) {
            console.error('Error initializing distributed lock system:', error);
        }
    }
}

export default new DistributedLock();
