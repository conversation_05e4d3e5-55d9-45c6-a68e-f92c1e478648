// Simple test script to verify the chatbot endpoint works
import { getChatbotResponse } from './services/teachingAssistantService.js';

// Mock request and response objects
const mockReq = {
  body: {
    studentId: 'test-student-id',
    subject: 'Mathematics',
    message: 'What is algebra?',
    conversationHistory: []
  }
};

const mockRes = {
  json: (data) => {
    console.log('✅ Chatbot Response:', JSON.stringify(data, null, 2));
  },
  status: (code) => ({
    json: (data) => {
      console.log(`❌ Error Response (${code}):`, JSON.stringify(data, null, 2));
    }
  })
};

console.log('🧪 Testing Chatbot Endpoint...');
console.log('📝 Request:', JSON.stringify(mockReq.body, null, 2));

try {
  await getChatbotResponse(mockReq, mockRes);
} catch (error) {
  console.error('❌ Test failed:', error);
}
