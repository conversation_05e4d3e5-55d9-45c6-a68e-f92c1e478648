// Simple test script to verify the chat API endpoints work
import axios from 'axios';

const BASE_URL = 'http://localhost:8080';
const TEST_USER_ID = 'test-student-123';
const TEST_SUBJECT = 'Mathematics';

// Mock conversation data
const testConversation = {
  id: `conv_${Date.now()}_test`,
  userId: TEST_USER_ID,
  title: 'Test Math Chat',
  subject: TEST_SUBJECT,
  messages: [
    {
      id: 'msg1',
      type: 'user',
      content: 'What is algebra?',
      timestamp: new Date()
    },
    {
      id: 'msg2',
      type: 'assistant',
      content: 'Algebra is a branch of mathematics dealing with symbols and the rules for manipulating those symbols.',
      timestamp: new Date()
    }
  ]
};

async function testChatAPI() {
  try {
    console.log('🧪 Testing Chat API Endpoints...\n');

    // Test 1: Create a new conversation
    console.log('1️⃣ Testing conversation creation...');
    const createResponse = await axios.post(`${BASE_URL}/api/chat/conversations`, testConversation);
    console.log('✅ Conversation created successfully');
    console.log('Response:', createResponse.data);

    // Test 2: Get all conversations for user
    console.log('\n2️⃣ Testing get user conversations...');
    const getResponse = await axios.get(`${BASE_URL}/api/chat/conversations`, {
      params: { userId: TEST_USER_ID, subject: TEST_SUBJECT }
    });
    console.log('✅ Conversations retrieved successfully');
    console.log('Found conversations:', getResponse.data.length);

    // Test 3: Get specific conversation
    console.log('\n3️⃣ Testing get specific conversation...');
    const getOneResponse = await axios.get(`${BASE_URL}/api/chat/conversations/${testConversation.id}`, {
      params: { userId: TEST_USER_ID }
    });
    console.log('✅ Specific conversation retrieved successfully');
    console.log('Conversation title:', getOneResponse.data.title);

    // Test 4: Update conversation
    console.log('\n4️⃣ Testing conversation update...');
    const updatedMessages = [
      ...testConversation.messages,
      {
        id: 'msg3',
        type: 'user',
        content: 'Can you give me an example?',
        timestamp: new Date()
      }
    ];
    
    const updateResponse = await axios.put(`${BASE_URL}/api/chat/conversations/${testConversation.id}`, {
      userId: TEST_USER_ID,
      messages: updatedMessages,
      title: 'Updated Test Math Chat'
    });
    console.log('✅ Conversation updated successfully');
    console.log('Updated message count:', updateResponse.data.messages.length);

    // Test 5: Save conversation message (used by chat system)
    console.log('\n5️⃣ Testing save conversation message...');
    const saveResponse = await axios.post(`${BASE_URL}/api/chat/save-message`, {
      conversationId: testConversation.id,
      userId: TEST_USER_ID,
      subject: TEST_SUBJECT,
      messages: updatedMessages,
      title: 'Auto-saved Chat'
    });
    console.log('✅ Conversation message saved successfully');

    // Test 6: Delete conversation
    console.log('\n6️⃣ Testing conversation deletion...');
    const deleteResponse = await axios.delete(`${BASE_URL}/api/chat/conversations/${testConversation.id}`, {
      params: { userId: TEST_USER_ID }
    });
    console.log('✅ Conversation deleted successfully');
    console.log('Delete response:', deleteResponse.data.message);

    console.log('\n🎉 All chat API tests passed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
    if (error.response?.status === 401) {
      console.log('💡 Note: This might be due to JWT authentication. The endpoints require authentication in production.');
    }
  }
}

// Run the tests
testChatAPI();
