#!/usr/bin/env node

/**
 * Chat Persistence Test Script
 * Tests the fixed chat persistence functionality
 */

import axios from 'axios';

const BASE_URL = 'http://localhost:8080';
const TEST_USER_ID = 'test_user_123';
const TEST_SUBJECT = 'Mathematics';

// Mock authentication token (replace with actual token)
const AUTH_TOKEN = 'your_test_token_here';

const axiosInstance = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Authorization': `Bearer ${AUTH_TOKEN}`,
    'Content-Type': 'application/json'
  }
});

async function testChatPersistence() {
  console.log('🧪 Starting Chat Persistence Tests...\n');

  try {
    // Test 1: Create new conversation on login
    console.log('📝 Test 1: Create new conversation on login');
    const loginResponse = await axiosInstance.post('/api/chat/create-on-login', {
      userId: TEST_USER_ID,
      subject: TEST_SUBJECT
    });
    
    if (loginResponse.data.conversation) {
      console.log('✅ Login conversation created successfully');
      console.log(`   Conversation ID: ${loginResponse.data.conversation.id}`);
      console.log(`   Messages: ${loginResponse.data.conversation.messages.length}`);
    } else {
      console.log('❌ Failed to create login conversation');
    }

    // Test 2: Get user conversations
    console.log('\n📝 Test 2: Retrieve user conversations');
    const conversationsResponse = await axiosInstance.get('/api/chat/conversations', {
      params: { userId: TEST_USER_ID, subject: TEST_SUBJECT }
    });
    
    if (conversationsResponse.data && conversationsResponse.data.length > 0) {
      console.log('✅ Conversations retrieved successfully');
      console.log(`   Found ${conversationsResponse.data.length} conversation(s)`);
      conversationsResponse.data.forEach((conv, index) => {
        console.log(`   ${index + 1}. ${conv.title} (${conv.messages.length} messages)`);
      });
    } else {
      console.log('❌ No conversations found');
    }

    // Test 3: Create explicit new conversation
    console.log('\n📝 Test 3: Create explicit new conversation');
    const explicitResponse = await axiosInstance.post('/api/chat/create-new', {
      userId: TEST_USER_ID,
      subject: TEST_SUBJECT
    });
    
    if (explicitResponse.data.conversation) {
      console.log('✅ Explicit conversation created successfully');
      console.log(`   Conversation ID: ${explicitResponse.data.conversation.id}`);
    } else {
      console.log('❌ Failed to create explicit conversation');
    }

    // Test 4: Test chat response with caching
    console.log('\n📝 Test 4: Test chat response with caching');
    const conversationId = loginResponse.data.conversation.id;
    const testMessage = 'What is a quadratic equation?';
    
    // First request (should hit LLM)
    console.log('   Sending first request (should hit LLM)...');
    const startTime1 = Date.now();
    const chatResponse1 = await axiosInstance.post('/api/teaching-assistant/chat', {
      studentId: TEST_USER_ID,
      subject: TEST_SUBJECT,
      message: testMessage,
      conversationHistory: [],
      conversationId: conversationId
    });
    const responseTime1 = Date.now() - startTime1;
    
    if (chatResponse1.data.message) {
      console.log(`✅ First chat response received in ${responseTime1}ms`);
      console.log(`   Response length: ${chatResponse1.data.message.length} characters`);
    }

    // Second request (should hit cache)
    console.log('   Sending second identical request (should hit cache)...');
    const startTime2 = Date.now();
    const chatResponse2 = await axiosInstance.post('/api/teaching-assistant/chat', {
      studentId: TEST_USER_ID,
      subject: TEST_SUBJECT,
      message: testMessage,
      conversationHistory: [],
      conversationId: conversationId
    });
    const responseTime2 = Date.now() - startTime2;
    
    if (chatResponse2.data.message) {
      console.log(`✅ Second chat response received in ${responseTime2}ms`);
      console.log(`   Cache performance: ${responseTime2 < responseTime1 ? 'IMPROVED' : 'NO IMPROVEMENT'}`);
      console.log(`   Response identical: ${chatResponse1.data.message === chatResponse2.data.message ? 'YES' : 'NO'}`);
    }

    // Test 5: Test conversation analytics
    console.log('\n📝 Test 5: Test conversation analytics');
    const analyticsResponse = await axiosInstance.get('/api/chat/analytics', {
      params: { userId: TEST_USER_ID, timeRange: 7 }
    });
    
    if (analyticsResponse.data && analyticsResponse.data.length > 0) {
      console.log('✅ Analytics retrieved successfully');
      analyticsResponse.data.forEach(stat => {
        console.log(`   ${stat._id}: ${stat.totalConversations} conversations, ${stat.totalMessages} messages`);
      });
    } else {
      console.log('⚠️  No analytics data available yet');
    }

    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Status:', error.response.status);
      console.error('   Data:', error.response.data);
    }
  }
}

// Performance test function
async function testPerformance() {
  console.log('\n⚡ Performance Tests...\n');

  try {
    const testMessages = [
      'Explain derivatives',
      'What is integration?',
      'Solve x^2 + 5x + 6 = 0',
      'What are limits in calculus?',
      'Explain the chain rule'
    ];

    const conversationId = `perf_test_${Date.now()}`;
    const responseTimes = [];

    for (let i = 0; i < testMessages.length; i++) {
      const message = testMessages[i];
      console.log(`📝 Testing message ${i + 1}: "${message}"`);
      
      const startTime = Date.now();
      const response = await axiosInstance.post('/api/teaching-assistant/chat', {
        studentId: TEST_USER_ID,
        subject: TEST_SUBJECT,
        message: message,
        conversationHistory: [],
        conversationId: conversationId
      });
      const responseTime = Date.now() - startTime;
      
      responseTimes.push(responseTime);
      console.log(`   Response time: ${responseTime}ms`);
      
      if (response.data.message) {
        console.log(`   Response length: ${response.data.message.length} characters`);
      }
    }

    // Calculate statistics
    const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
    const maxResponseTime = Math.max(...responseTimes);
    const minResponseTime = Math.min(...responseTimes);

    console.log('\n📊 Performance Summary:');
    console.log(`   Average response time: ${avgResponseTime.toFixed(2)}ms`);
    console.log(`   Fastest response: ${minResponseTime}ms`);
    console.log(`   Slowest response: ${maxResponseTime}ms`);
    console.log(`   Target met (<2000ms): ${avgResponseTime < 2000 ? '✅ YES' : '❌ NO'}`);

  } catch (error) {
    console.error('❌ Performance test failed:', error.message);
  }
}

// Main execution
async function main() {
  console.log('🚀 Aegis AI Chat Persistence Test Suite\n');
  console.log('⚠️  Make sure the backend server is running on localhost:8080');
  console.log('⚠️  Update AUTH_TOKEN with a valid authentication token\n');

  await testChatPersistence();
  await testPerformance();
  
  console.log('\n✨ Test suite completed!');
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { testChatPersistence, testPerformance };
