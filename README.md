## **AegisScholar: Adaptive Learning Platform**


![<PERSON><PERSON><PERSON> Scholar-](https://github.com/user-attachments/assets/3692696f-4a12-4504-9c78-8784814ba26d)



### **Project Description**
**AegisScholar** is an adaptive learning platform designed to deliver personalized quizzes and tests based on users' proficiency and learning patterns. The platform leverages advanced algorithms, including **Item Response Theory (IRT)** and **Collaborative Filtering**, to dynamically adapt to the user’s strengths and weaknesses in real-time or batch modes, providing a tailored learning experience.

### **Key Features**
- **Adaptive Quiz Engine**: Questions are personalized based on user performance using IRT models.
- **Real-Time or Batch Processing**: Switches between real-time proficiency updates for small tests and batch processing for large-scale assessments.
- **Collaborative Filtering**: Recommends questions based on the performance of similar users to enhance learning.
- **Proficiency Updates**: Tracks user proficiency across different sub-skills, dynamically updating after each test/quiz.
- **Interactive Frontend**: Built using **React** and **TailwindCSS** for a sleek, modern interface.
- **Scalable Backend**: Developed in **Node.js** with support for **MongoDB/PostgreSQL**.

### **Technologies Used**
- **Backend**: Node.js, Express.js, MongoDB/PostgreSQL
- **Frontend**: React, TypeScript, TailwindCSS
- **Adaptive Algorithms**: Item Response Theory (IRT), Collaborative Filtering
- **Authentication**: JWT (JSON Web Tokens)
- **Testing**: Jest (for unit and integration tests)
- **Deployment**: Docker (for containerization), GitHub Actions (for CI/CD)

---

### **Table of Contents**
- [Features](#key-features)
- [Installation](#installation)
- [Usage](#usage)
- [Project Structure](#project-structure)
- [API Endpoints](#api-endpoints)
- [License](#license)

---

### **Installation**

#### **Backend Setup**
1. Clone the repository:
    ```bash
    git clone https://github.com/yourusername/AegisScholar.git
    cd AegisScholar/backend
    ```
2. Install the dependencies:
    ```bash
    npm install
    ```
3. Set up the environment variables:
    - Create a `.env` file in the `backend/` directory with the following:
      ```bash
      PORT=8080
      MONGO_URI=<your-mongo-uri>
      JWT_SECRET=<your-jwt-secret>
      ```

4. Run the backend server:
    ```bash
    npm run dev
    ```

#### **Frontend Setup**
1. Go to the frontend directory:
    ```bash
    cd ../frontend
    ```
2. Install the dependencies:
    ```bash
    npm install
    ```

3. Start the frontend:
    ```bash
    npm start
    ```

---

### **Usage**

#### **Running the Platform Locally**
1. Start both the **backend** and **frontend** as described in the setup instructions above.
2. Access the platform at `http://localhost:3000/` in your browser.
3. You can register and log in as a user, and the adaptive quiz will dynamically adjust based on your performance.

#### **API Endpoints**

##### **Authentication**
- `POST /api/auth/register`: Register a new user.
- `POST /api/auth/login`: Log in a user.

##### **User**
- `GET /api/users`: Get user details.
- `POST /api/users`: Create a new user.

##### **Test Submission**
- `POST /api/test/submit`: Submit test answers for real-time or batch processing.

---

### **Project Structure**

```bash
/AegisScholar
|-- /frontend               # React frontend
|   |-- /src
|       |-- /components     # React components
|       |-- /pages          # Pages (e.g., Login, Register, Homepage)
|       |-- /api            # API calls to backend
|-- /backend                # Node.js backend
|   |-- /routes             # Express routes (e.g., userRoutes, testRoutes)
|   |-- /controllers        # Controllers (testController, userController)
|   |-- /services           # Business logic (e.g., proficiency update, IRT calculations)
|   |-- /models             # MongoDB/PostgreSQL models (User, Test, etc.)
|   |-- /middleware         # Authentication (JWT), error handling
|-- /config                 # Environment configuration
|-- /scripts                # Deployment scripts
|-- /tests                  # Unit and integration tests (Jest)
|-- /docs                   # Documentation
|-- README.md               # Project README
|-- .env                    # Environment variables
```

---

### **License**
This project is licensed under the **MIT License**. See the `LICENSE` file for more details.

---

### **Future Enhancements**
- **AI-Powered Study Paths**: Automatically recommend entire study paths based on user performance.
- **Gamification**: Add badges, points, and leaderboards to improve engagement.
- **Mobile Support**: Implement responsive design for better mobile experiences.
- **Real-Time Collaboration**: Enable users to study together and share quizzes in real-time.

---
